<?php

namespace Tests\Unit\Services\Inventory;

use Tests\TestCase;
use App\Models\Users\User;
use App\Models\Inventory\Medicine;
use App\Models\Inventory\Supplier;
use App\Models\Inventory\Purchase;
use App\Models\Inventory\PurchaseItem;
use App\Models\Inventory\Inventory;
use App\Models\Inventory\Manufacturer;
use App\Models\Inventory\Category;
use App\Models\Inventory\UnitType;
use App\Services\Inventory\MedicinePurchaseService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;

class MedicinePurchaseServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $service;
    protected $user;
    protected $medicine;
    protected $supplier;
    protected $manufacturer;
    protected $category;
    protected $unitType;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create();
        Auth::login($this->user);

        // Create test manufacturer
        $this->manufacturer = Manufacturer::factory()->create([
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        // Create test category
        $this->category = Category::factory()->create([
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        // Create test unit type
        $this->unitType = UnitType::factory()->create([
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        // Create test supplier
        $this->supplier = Supplier::factory()->create([
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        // Create test medicine
        $this->medicine = Medicine::factory()->create([
            'manufacturer_id' => $this->manufacturer->id,
            'category_id' => $this->category->id,
            'unit_type_id' => $this->unitType->id,
            'supplier_price_unit' => 2.50,
            'retail_price_unit' => 3.00,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $this->service = new MedicinePurchaseService();
    }

    public function test_it_creates_initial_purchase_with_basic_data()
    {
        $data = [
            'supplier_id' => $this->supplier->id,
            'quantity' => 1000,
            'unit_price' => 2.50,
            'batch_number' => 'BATCH-001',
            'expiry_date' => now()->addYear(),
        ];

        $purchase = $this->service->createInitialPurchase($this->medicine, $data);

        // Assert purchase was created
        $this->assertInstanceOf(Purchase::class, $purchase);
        $this->assertEquals($this->supplier->id, $purchase->supplier_id);
        $this->assertEquals(2500.00, $purchase->total_amount);
        $this->assertEquals('received', $purchase->status);
        $this->assertEquals('paid', $purchase->payment_status);

        // Assert purchase item was created
        $purchaseItem = PurchaseItem::where('purchase_id', $purchase->id)->first();
        $this->assertNotNull($purchaseItem);
        $this->assertEquals($this->medicine->id, $purchaseItem->medicine_id);
        $this->assertEquals(1000, $purchaseItem->quantity);
        $this->assertEquals(2.50, $purchaseItem->unit_price);
        $this->assertEquals('BATCH-001', $purchaseItem->batch_number);

        // Assert inventory was created
        $inventory = Inventory::where('medicine_id', $this->medicine->id)
            ->where('batch_number', 'BATCH-001')
            ->first();
        $this->assertNotNull($inventory);
        $this->assertEquals(1000, $inventory->quantity);
        $this->assertEquals(2.50, $inventory->unit_price);
    }

    public function test_it_creates_purchase_with_tax_and_discount()
    {
        $data = [
            'supplier_id' => $this->supplier->id,
            'quantity' => 1000,
            'unit_price' => 2.50,
            'batch_number' => 'BATCH-002',
            'expiry_date' => now()->addYear(),
            'tax_percentage' => 10,
            'discount_percentage' => 5,
            'shipping_cost' => 50,
        ];

        $purchase = $this->service->createInitialPurchase($this->medicine, $data);

        // Calculate expected amounts
        $subtotal = 2500.00; // 1000 * 2.50
        $tax = 250.00; // 10% of 2500
        $discount = 125.00; // 5% of 2500
        $final = $subtotal + $tax - $discount + 50; // 2675.00

        // Assert purchase amounts
        $this->assertEquals($subtotal, $purchase->total_amount);
        $this->assertEquals(10, $purchase->tax_percentage);
        $this->assertEquals($tax, $purchase->tax_amount);
        $this->assertEquals(5, $purchase->discount_percentage);
        $this->assertEquals($discount, $purchase->discount_amount);
        $this->assertEquals(50, $purchase->shipping_cost);
        $this->assertEquals($final, $purchase->final_amount);

        // Assert purchase item amounts
        $purchaseItem = PurchaseItem::where('purchase_id', $purchase->id)->first();
        $this->assertEquals($subtotal, $purchaseItem->total_amount);
        $this->assertEquals(10, $purchaseItem->tax_percentage);
        $this->assertEquals($tax, $purchaseItem->tax_amount);
        $this->assertEquals(5, $purchaseItem->discount_percentage);
        $this->assertEquals($discount, $purchaseItem->discount_amount);
    }

    public function test_it_generates_unique_purchase_numbers()
    {
        $data = [
            'supplier_id' => $this->supplier->id,
            'quantity' => 100,
            'unit_price' => 2.50,
            'batch_number' => 'BATCH-003',
            'expiry_date' => now()->addYear(),
        ];

        // Create multiple purchases
        $purchase1 = $this->service->createInitialPurchase($this->medicine, $data);
        $data['batch_number'] = 'BATCH-004';
        $purchase2 = $this->service->createInitialPurchase($this->medicine, $data);
        $data['batch_number'] = 'BATCH-005';
        $purchase3 = $this->service->createInitialPurchase($this->medicine, $data);

        // Assert purchase numbers are unique and follow the pattern
        $this->assertMatchesRegularExpression('/^PO-\d{8}-\d{3}$/', $purchase1->purchase_number);
        $this->assertMatchesRegularExpression('/^PO-\d{8}-\d{3}$/', $purchase2->purchase_number);
        $this->assertMatchesRegularExpression('/^PO-\d{8}-\d{3}$/', $purchase3->purchase_number);
        $this->assertNotEquals($purchase1->purchase_number, $purchase2->purchase_number);
        $this->assertNotEquals($purchase2->purchase_number, $purchase3->purchase_number);
    }

    public function test_it_handles_transaction_rollback_on_error()
    {
        // Create data with invalid supplier to trigger error
        $data = [
            'supplier_id' => 999999, // Non-existent supplier
            'quantity' => 1000,
            'unit_price' => 2.50,
            'batch_number' => 'BATCH-006',
            'expiry_date' => now()->addYear(),
        ];

        $this->expectException(\Exception::class);

        try {
            $this->service->createInitialPurchase($this->medicine, $data);
        } catch (\Exception $e) {
            // Verify no records were created
            $this->assertEquals(0, Purchase::count());
            $this->assertEquals(0, PurchaseItem::count());
            $this->assertEquals(0, Inventory::count());
            throw $e;
        }
    }
} 