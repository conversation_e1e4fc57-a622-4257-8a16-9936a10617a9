<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\Inventory\ProfitLoss;
use App\Models\Inventory\Medicine;
use App\Models\Sales\Sale;
use App\Models\Sales\SaleItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProfitLossCalculationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $medicine;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->medicine = Medicine::factory()->create([
            'supplier_price_unit' => 80.00,
            'retail_price_unit' => 100.00
        ]);
    }

    /** @test */
    public function it_calculates_sale_profit_correctly_without_discount()
    {
        $profitLoss = ProfitLoss::create([
            'medicine_id' => $this->medicine->id,
            'reference_number' => 'TEST-001',
            'transaction_type' => 'sale',
            'quantity' => 2,
            'unit_cost' => 80.00,
            'unit_price' => 100.00,
            'total_revenue' => 200.00, // 2 * 100
            'discount_amount' => 0.00,
            'tax_amount' => 0.00,
            'transaction_date' => now(),
            'created_by' => $this->user->id,
        ]);

        $profitLoss->calculateProfits();

        $this->assertEquals(200.00, $profitLoss->total_revenue);
        $this->assertEquals(160.00, $profitLoss->total_cost); // 2 * 80
        $this->assertEquals(40.00, $profitLoss->gross_profit); // 200 - 160
        $this->assertEquals(40.00, $profitLoss->net_profit); // Same as gross since no discount
        $this->assertEquals(20.00, $profitLoss->profit_margin); // (40/200) * 100
    }

    /** @test */
    public function it_calculates_sale_profit_correctly_with_discount()
    {
        $profitLoss = ProfitLoss::create([
            'medicine_id' => $this->medicine->id,
            'reference_number' => 'TEST-002',
            'transaction_type' => 'sale',
            'quantity' => 2,
            'unit_cost' => 80.00,
            'unit_price' => 100.00,
            'total_revenue' => 180.00, // 200 - 20 discount (net revenue)
            'discount_amount' => 20.00,
            'tax_amount' => 0.00,
            'transaction_date' => now(),
            'created_by' => $this->user->id,
        ]);

        $profitLoss->calculateProfits();

        $this->assertEquals(180.00, $profitLoss->total_revenue); // Net revenue after discount
        $this->assertEquals(160.00, $profitLoss->total_cost); // 2 * 80
        $this->assertEquals(20.00, $profitLoss->gross_profit); // 180 - 160
        $this->assertEquals(20.00, $profitLoss->net_profit); // Same as gross since discount already applied
        $this->assertEquals(10.00, $profitLoss->profit_margin); // (20/200) * 100 (based on gross revenue)
    }

    /** @test */
    public function it_calculates_purchase_cost_correctly_with_shipping()
    {
        $profitLoss = ProfitLoss::create([
            'medicine_id' => $this->medicine->id,
            'reference_number' => 'PUR-001',
            'transaction_type' => 'purchase',
            'quantity' => 5,
            'unit_cost' => 75.00,
            'unit_price' => 0.00,
            'total_cost' => 375.00, // Will be recalculated
            'shipping_cost' => 25.00,
            'transaction_date' => now(),
            'created_by' => $this->user->id,
        ]);

        $profitLoss->calculateProfits();

        $this->assertEquals(400.00, $profitLoss->total_cost); // (5 * 75) + 25 shipping
        $this->assertEquals(0.00, $profitLoss->total_revenue);
        $this->assertEquals(0.00, $profitLoss->gross_profit);
        $this->assertEquals(0.00, $profitLoss->net_profit);
        $this->assertEquals(0.00, $profitLoss->profit_margin);
    }

    /** @test */
    public function it_handles_zero_revenue_gracefully()
    {
        $profitLoss = ProfitLoss::create([
            'medicine_id' => $this->medicine->id,
            'reference_number' => 'TEST-003',
            'transaction_type' => 'sale',
            'quantity' => 0,
            'unit_cost' => 80.00,
            'unit_price' => 100.00,
            'total_revenue' => 0.00,
            'discount_amount' => 0.00,
            'tax_amount' => 0.00,
            'transaction_date' => now(),
            'created_by' => $this->user->id,
        ]);

        $profitLoss->calculateProfits();

        $this->assertEquals(0.00, $profitLoss->total_revenue);
        $this->assertEquals(0.00, $profitLoss->total_cost);
        $this->assertEquals(0.00, $profitLoss->gross_profit);
        $this->assertEquals(0.00, $profitLoss->net_profit);
        $this->assertEquals(0.00, $profitLoss->profit_margin);
    }

    /** @test */
    public function it_calculates_summary_correctly()
    {
        // Create sale record
        ProfitLoss::create([
            'medicine_id' => $this->medicine->id,
            'reference_number' => 'SALE-001',
            'transaction_type' => 'sale',
            'quantity' => 2,
            'unit_cost' => 80.00,
            'unit_price' => 100.00,
            'total_revenue' => 180.00, // After discount
            'total_cost' => 160.00,
            'gross_profit' => 20.00,
            'net_profit' => 20.00,
            'discount_amount' => 20.00,
            'transaction_date' => now(),
            'created_by' => $this->user->id,
        ]);

        // Create purchase record
        ProfitLoss::create([
            'medicine_id' => $this->medicine->id,
            'reference_number' => 'PUR-001',
            'transaction_type' => 'purchase',
            'quantity' => 5,
            'unit_cost' => 75.00,
            'total_cost' => 400.00, // Including shipping
            'shipping_cost' => 25.00,
            'transaction_date' => now(),
            'created_by' => $this->user->id,
        ]);

        $summary = ProfitLoss::getSummary(now()->startOfDay(), now()->endOfDay());

        $this->assertEquals(180.00, $summary['total_revenue']);
        $this->assertEquals(560.00, $summary['total_cost']); // 160 (sale cost) + 400 (purchase cost)
        $this->assertEquals(160.00, $summary['sale_costs']);
        $this->assertEquals(400.00, $summary['purchase_costs']);
        $this->assertEquals(20.00, $summary['gross_profit']);
        $this->assertEquals(20.00, $summary['net_profit']);
        $this->assertEquals(1, $summary['sale_count']);
        $this->assertEquals(1, $summary['purchase_count']);
    }
}
