<?php

namespace Tests\Unit\Settings;

use App\Models\Setting;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Livewire\Pages\Settings\BaseSettings;
use Livewire\Component;
use PHPUnit\Framework\Attributes\Test;

class BaseSettingsTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_can_get_setting_value()
    {
        // Arrange
        Setting::create([
            'key' => 'test_key',
            'value' => 'test_value',
            'group' => 'general',
            'is_encrypted' => false
        ]);

        $component = new class extends BaseSettings {
            public function render() { return view('livewire.pages.settings.general-settings'); }
            public function getTestSetting($key) { return $this->getSetting($key); }
        };

        // Act
        $value = $component->getTestSetting('test_key');

        // Assert
        $this->assertEquals('test_value', $value);
    }

    #[Test]
    public function it_returns_default_value_when_setting_not_found()
    {
        // Arrange
        $component = new class extends BaseSettings {
            public function render() { return view('livewire.pages.settings.general-settings'); }
            public function getTestSetting($key, $default = null) { return $this->getSetting($key, $default); }
        };

        // Act
        $value = $component->getTestSetting('nonexistent_key', 'default_value');

        // Assert
        $this->assertEquals('default_value', $value);
    }

    #[Test]
    public function it_can_save_setting()
    {
        // Arrange
        $component = new class extends BaseSettings {
            public function render() { return view('livewire.pages.settings.general-settings'); }
            public function saveTestSetting($key, $value) { return $this->saveSetting($key, $value); }
        };

        // Act
        $component->saveTestSetting('new_key', 'new_value');

        // Assert
        $this->assertDatabaseHas('settings', [
            'key' => 'new_key',
            'value' => 'new_value'
        ]);
    }

    #[Test]
    public function it_can_update_existing_setting()
    {
        // Arrange
        Setting::create([
            'key' => 'existing_key',
            'value' => 'old_value',
            'group' => 'general',
            'is_encrypted' => false
        ]);

        $component = new class extends BaseSettings {
            public function render() { return view('livewire.pages.settings.general-settings'); }
            public function saveTestSetting($key, $value) { return $this->saveSetting($key, $value); }
        };

        // Act
        $component->saveTestSetting('existing_key', 'new_value');

        // Assert
        $this->assertDatabaseHas('settings', [
            'key' => 'existing_key',
            'value' => 'new_value',
            'is_encrypted' => false
        ]);
    }
}
