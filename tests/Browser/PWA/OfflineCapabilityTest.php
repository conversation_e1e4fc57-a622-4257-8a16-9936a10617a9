<?php

namespace Tests\Browser\PWA;

use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class OfflineCapabilityTest extends DuskTestCase
{
    /**
     * Test basic offline indicator functionality
     */
    public function test_offline_indicator()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/')
                   // Simulate online mode first
                   ->script("window.dispatchEvent(new Event('online'));")
                   ->pause(1000)
                   ->assertMissing('.offline-indicator')
                   
                   // Simulate offline mode
                   ->script("window.dispatchEvent(new Event('offline'));")
                   ->waitFor('.offline-indicator')
                   ->assertVisible('.offline-indicator')
                   ->assertSee("You're offline");
        });
    }

    /**
     * Test offline page access
     */
    public function test_offline_page_access()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/offline')
                   ->assertSee('PharmaDesk works offline')
                   ->assertSee('Continue working with your data')
                   ->assertPresent('.offline-actions');
        });
    }

    /**
     * Test offline data synchronization UI
     */
    public function test_offline_sync_ui()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/dashboard')
                   ->assertPresent('#sync-status')
                   ->assertPresent('.sync-button')
                   
                   // Test sync button is disabled when offline
                   ->script("window.dispatchEvent(new Event('offline'));")
                   ->pause(1000)
                   ->assertAttribute('.sync-button', 'disabled', 'true')
                   
                   // Test sync button is enabled when online
                   ->script("window.dispatchEvent(new Event('online'));")
                   ->pause(1000)
                   ->assertAttributeMissing('.sync-button', 'disabled');
        });
    }

    /**
     * Test IndexedDB initialization
     */
    public function test_indexeddb_initialization()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/')
                   ->waitForText('Offline database initialized', 10)
                   ->assertScript("return typeof window.OfflineDB !== 'undefined'")
                   ->assertScript("return typeof window.idb !== 'undefined' || typeof window.indexedDB !== 'undefined'");
        });
    }

    /**
     * Test offline data persistence
     */
    public function test_offline_data_persistence()
    {
        $this->browse(function (Browser $browser) {
            // First, ensure we're online and the DB is initialized
            $browser->visit('/')
                   ->waitForText('Offline database initialized', 10)
                   
                   // Store a test value in IndexedDB
                   ->script("
                       window.OfflineDB.saveSetting('test_key', 'test_value').then(() => {
                           window.testSaved = true;
                       });
                   ")
                   ->pause(1000)
                   ->assertScript("return window.testSaved === true")
                   
                   // Refresh the page
                   ->refresh()
                   ->waitForText('Offline database initialized', 10)
                   
                   // Check if the value persists
                   ->script("
                       window.OfflineDB.getSetting('test_key').then(value => {
                           window.retrievedValue = value;
                       });
                   ")
                   ->pause(1000)
                   ->assertScript("return window.retrievedValue === 'test_value'");
        });
    }

    /**
     * Test offline form submission
     */
    public function test_offline_form_submission()
    {
        $this->browse(function (Browser $browser) {
            // Go to a form page
            $browser->visit('/sales/create')
                   
                   // Simulate offline mode
                   ->script("window.dispatchEvent(new Event('offline'));")
                   ->pause(1000)
                   ->assertVisible('.offline-indicator')
                   
                   // Fill out the form
                   ->type('customer_name', 'Test Customer')
                   ->type('amount', '100')
                   
                   // Submit the form
                   ->press('Save')
                   
                   // Check for offline queue notification
                   ->waitForText('Saved offline', 10)
                   ->assertSee('Will be synchronized when online')
                   
                   // Check sync queue status
                   ->assertPresent('#pending-sync-count')
                   ->assertScript("return parseInt(document.querySelector('#pending-sync-count').textContent) > 0");
        });
    }

    /**
     * Test service worker sync event
     */
    public function test_service_worker_sync()
    {
        $this->browse(function (Browser $browser) {
            // First go offline and create data
            $browser->visit('/sales/create')
                   ->script("window.dispatchEvent(new Event('offline'));")
                   ->pause(1000)
                   ->type('customer_name', 'Sync Test Customer')
                   ->type('amount', '200')
                   ->press('Save')
                   ->waitForText('Saved offline', 10)
                   
                   // Now go online and check sync
                   ->script("window.dispatchEvent(new Event('online'));")
                   ->pause(1000)
                   
                   // Trigger sync manually
                   ->click('.sync-button')
                   ->waitForText('Sync completed', 10)
                   
                   // Verify sync status updated
                   ->assertSee('Last sync:')
                   ->assertScript("return document.querySelector('#pending-sync-count').textContent === '0'");
        });
    }

    /**
     * Test offline navigation
     */
    public function test_offline_navigation()
    {
        $this->browse(function (Browser $browser) {
            // First visit pages while online to cache them
            $browser->visit('/dashboard')
                   ->visit('/inventory')
                   ->visit('/sales')
                   ->visit('/customers')
                   
                   // Now go offline
                   ->script("window.dispatchEvent(new Event('offline'));")
                   ->pause(1000)
                   
                   // Try navigating to cached pages
                   ->clickLink('Inventory')
                   ->assertPathIs('/inventory')
                   ->assertSee('Inventory Management')
                   
                   ->clickLink('Sales')
                   ->assertPathIs('/sales')
                   ->assertSee('Sales Management')
                   
                   // Try navigating to a non-cached page
                   ->visit('/non-existent-page')
                   ->assertPathIs('/offline')
                   ->assertSee('PharmaDesk works offline');
        });
    }
} 