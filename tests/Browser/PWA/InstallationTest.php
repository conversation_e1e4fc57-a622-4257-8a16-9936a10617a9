<?php

namespace Tests\Browser\PWA;

use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class InstallationTest extends DuskTestCase
{
    /**
     * Test PWA installation prompt appears
     */
    public function test_pwa_installation_prompt_appears()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/')
                   ->waitFor('#pwa-install-prompt', 10)
                   ->assertVisible('#pwa-install-prompt')
                   ->assertSee('Add to Home Screen');
        });
    }

    /**
     * Test service worker registration
     */
    public function test_service_worker_registration()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/')
                   ->waitForText('ServiceWorker registration successful')
                   ->assertPresent('#service-worker-status')
                   ->assertSeeIn('#service-worker-status', 'Service Worker Active');
        });
    }

    /**
     * Test offline functionality
     */
    public function test_offline_functionality()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/')
                   // Simulate offline mode
                   ->script("window.dispatchEvent(new Event('offline'));")
                   ->waitFor('.offline-indicator')
                   ->assertVisible('.offline-indicator')
                   ->assertSee("You're offline")
                   // Test offline page access
                   ->visit('/offline')
                   ->assertSee('PharmaDesk works offline');
        });
    }

    /**
     * Test PWA icons are loaded correctly
     */
    public function test_pwa_icons_loaded()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/')
                   ->assertPresent("link[rel='apple-touch-icon'][sizes='152x152']")
                   ->assertPresent("link[rel='apple-touch-icon'][sizes='144x144']")
                   ->assertPresent("link[rel='apple-touch-icon'][sizes='120x120']")
                   ->assertPresent("link[rel='icon'][sizes='192x192']");
        });
    }

    /**
     * Test manifest is loaded correctly
     */
    public function test_manifest_loaded()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/')
                   ->assertPresent("link[rel='manifest']")
                   ->assertSourceHas('theme-color" content="#4F46E5"')
                   ->assertSourceHas('apple-mobile-web-app-capable" content="yes"');
        });
    }

    /**
     * Test PWA caching
     */
    public function test_pwa_caching()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/')
                   // First visit to cache assets
                   ->refresh()
                   // Second visit should be from cache
                   ->assertScript(
                       "performance.getEntriesByType('resource')
                           .some(e => e.transferSize === 0)"
                   );
        });
    }
} 