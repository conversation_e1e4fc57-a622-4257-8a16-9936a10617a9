<?php

namespace Tests\Browser\Reports;

use Tests\DuskTestCase;
use Laravel\Dusk\Browser;
use App\Models\Users\User;
use App\Models\Sales\Sale;
use App\Models\Sales\SaleItem;
use App\Models\Inventory\Medicine;
use App\Models\Inventory\Purchase;
use App\Models\Inventory\PurchaseItem;
use Illuminate\Foundation\Testing\DatabaseMigrations;

class ProfitLossTest extends DuskTestCase
{
    use DatabaseMigrations;

    protected $user;
    protected $medicine;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user with necessary permissions
        $this->user = User::factory()->create();
        $this->user->givePermissionTo('view reports');

        // Create test medicine
        $this->medicine = Medicine::factory()->create([
            'name' => 'Test Medicine',
            'unit_price' => 100,
            'supplier_price_unit' => 80,
        ]);

        // Create test data
        $this->createTestData();
    }

    protected function createTestData()
    {
        // Create sales
        for ($i = 0; $i < 3; $i++) {
            $sale = Sale::factory()->create([
                'total_amount' => 220,
                'discount_amount' => 20,
                'tax_amount' => 20,
                'created_by' => $this->user->id
            ]);

            SaleItem::factory()->create([
                'sale_id' => $sale->id,
                'medicine_id' => $this->medicine->id,
                'quantity' => 2,
                'unit_price' => 100,
            ]);
        }

        // Create purchases
        for ($i = 0; $i < 2; $i++) {
            $purchase = Purchase::factory()->create([
                'total_amount' => 160,
                'shipping_cost' => 10,
                'created_by' => $this->user->id
            ]);

            PurchaseItem::factory()->create([
                'purchase_id' => $purchase->id,
                'medicine_id' => $this->medicine->id,
                'quantity' => 2,
                'unit_price' => 80,
            ]);
        }
    }

    /** @test */
    public function user_can_view_profit_loss_report()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                   ->visit(route('reports.profit-loss'))
                   ->assertTitle('Profit & Loss Report - PharmaDesk')
                   ->assertSee('Profit & Loss Report')
                   ->assertSee('Total Revenue')
                   ->assertSee('Total Cost')
                   ->assertSee('Gross Profit')
                   ->assertSee('Net Profit')
                   ->assertPresent('.transaction-table');
        });
    }

    /** @test */
    public function user_can_filter_profit_loss_by_date_range()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                   ->visit(route('reports.profit-loss'))
                   ->type('start_date', now()->subDays(7)->format('Y-m-d'))
                   ->type('end_date', now()->format('Y-m-d'))
                   ->press('Filter')
                   ->waitForReload()
                   ->assertQueryStringHas('start_date')
                   ->assertQueryStringHas('end_date');
        });
    }

    /** @test */
    public function user_can_see_transaction_details()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                   ->visit(route('reports.profit-loss'))
                   ->assertSee('Test Medicine')
                   ->assertSee('INV-')  // Invoice number prefix
                   ->assertSee('PO-')   // Purchase order prefix
                   ->assertPresent('.transaction-type-sale')
                   ->assertPresent('.transaction-type-purchase');
        });
    }

    /** @test */
    public function profit_loss_calculations_are_displayed_correctly()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                   ->visit(route('reports.profit-loss'))
                   ->assertSee('600.00') // Total Revenue (3 sales * 200)
                   ->assertSee('480.00') // Total Cost (5 transactions * 160)
                   ->assertSee('120.00') // Gross Profit (600 - 480)
                   ->assertSee('60.00');  // Net Profit (120 - (3 * 20) discount)
        });
    }

    /** @test */
    public function profit_margins_are_color_coded()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                   ->visit(route('reports.profit-loss'))
                   ->assertPresent('.text-green-600') // Positive margins
                   ->assertPresent('.profit-margin');
        });
    }

    /** @test */
    public function pagination_works_correctly()
    {
        // Create additional test data
        for ($i = 0; $i < 25; $i++) {
            $sale = Sale::factory()->create();
            SaleItem::factory()->create([
                'sale_id' => $sale->id,
                'medicine_id' => $this->medicine->id
            ]);
        }

        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                   ->visit(route('reports.profit-loss'))
                   ->assertPresent('.pagination')
                   ->assertSee('Next')
                   ->click('.pagination .next-page')
                   ->waitForReload()
                   ->assertQueryStringHas('page', '2');
        });
    }

    /** @test */
    public function summary_cards_update_with_filters()
    {
        $this->browse(function (Browser $browser) {
            $initialRevenue = '';
            
            $browser->loginAs($this->user)
                   ->visit(route('reports.profit-loss'))
                   ->waitFor('.summary-card.revenue')
                   ->storeText('.summary-card.revenue', $initialRevenue)
                   ->type('start_date', now()->format('Y-m-d'))
                   ->type('end_date', now()->format('Y-m-d'))
                   ->press('Filter')
                   ->waitForReload()
                   ->assertSee('Total Revenue')
                   ->assertDontSee($initialRevenue);
        });
    }
} 