# PharmaDesh PWA Tests

This directory contains tests for the PWA (Progressive Web App) functionality of PharmaDesh.

## Test Structure

The PWA tests are organized into two main categories:

1. **Feature Tests** - These test the server-side components and file structure of the PWA
2. **Browser Tests** - These test the actual browser functionality using Laravel Dusk

### Feature Tests

Located in `tests/Feature/PWA/`, these tests verify:

- **ManifestTest.php** - Tests the web manifest file for proper configuration
- **ServiceWorkerTest.php** - Tests the service worker implementation
- **OfflineDbTest.php** - Tests the offline database configuration
- **IdbImportTest.php** - Tests the IndexedDB import functionality and fallbacks

### Browser Tests

Located in `tests/Browser/PWA/`, these tests verify:

- **InstallationTest.php** - Tests the PWA installation prompt and process
- **OfflineCapabilityTest.php** - Tests the application's behavior in offline mode

## Running the Tests

### Feature Tests

Run all PWA feature tests:

```bash
php artisan test tests/Feature/PWA
```

Run a specific feature test:

```bash
php artisan test tests/Feature/PWA/ServiceWorkerTest.php
```

### Browser Tests

Before running browser tests, ensure you have ChromeDriver installed:

```bash
php artisan dusk:chrome-driver
```

Run all PWA browser tests:

```bash
php artisan dusk tests/Browser/PWA
```

Run a specific browser test:

```bash
php artisan dusk tests/Browser/PWA/OfflineCapabilityTest.php
```

## Manual Offline Testing

For manual testing of offline functionality, we've provided a Python script:

```bash
# Make the script executable
chmod +x tools/offline_test.py

# Run the script
python tools/offline_test.py --url http://localhost:8000
```

The script will:
1. Access the site to cache resources
2. Simulate offline mode
3. Test navigation to cached pages
4. Test the offline indicator
5. Test the offline page

## Key PWA Components

1. **Service Worker** (`public/service-worker.js`)
   - Handles caching strategies
   - Manages offline requests
   - Synchronizes data when coming back online

2. **Web Manifest** (`public/manifest.json`)
   - Defines app name, icons, and colors
   - Controls the installation experience

3. **Offline Database** (`resources/js/offline-db.js`)
   - Implements IndexedDB storage
   - Provides data persistence when offline

4. **Sync Service** (`resources/js/sync-service.js`)
   - Manages synchronization of offline data
   - Handles conflict resolution

## Common Issues

1. **Service Worker Not Updating**
   - Clear the browser cache and reload
   - Check for errors in the browser console
   - Ensure the service worker is properly registered

2. **IndexedDB Issues**
   - Check browser support
   - Ensure proper fallbacks are in place
   - Verify the database schema

3. **Cache Issues**
   - Check the cache storage in DevTools
   - Verify the caching strategy in the service worker
   - Clear the cache if needed

## Additional Resources

- [MDN Progressive Web Apps Guide](https://developer.mozilla.org/en-US/docs/Web/Progressive_web_apps)
- [Google PWA Checklist](https://web.dev/pwa-checklist/)
- [IndexedDB Documentation](https://developer.mozilla.org/en-US/docs/Web/API/IndexedDB_API) 