/**
 * Offline Database Import Tests
 * 
 * Tests that verify we can import and use the offline database module
 */

// Import fake IndexedDB for testing environment
import 'fake-indexeddb/auto';

describe('Offline Database Import Tests', () => {
  let OfflineDB;

  beforeAll(async () => {
    // Mock the idb import since it's not available in test environment
    jest.doMock('idb', () => ({
      openDB: jest.fn().mockImplementation((name, version, options) => {
        return new Promise((resolve, reject) => {
          const request = indexedDB.open(name, version);
          request.onerror = () => reject(request.error);
          request.onsuccess = () => resolve(request.result);
          request.onupgradeneeded = (event) => {
            if (options && options.upgrade) {
              options.upgrade(event.target.result, event.oldVersion, event.newVersion, event.target.transaction);
            }
          };
        });
      })
    }));
  });

  test('should be able to create a basic database structure', async () => {
    // Test basic database operations without importing the full module
    const dbName = 'test-offline-db';
    const dbVersion = 1;
    
    const request = indexedDB.open(dbName, dbVersion);
    
    const db = await new Promise((resolve, reject) => {
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // Create stores similar to our offline-db structure
        if (!db.objectStoreNames.contains('sales')) {
          const salesStore = db.createObjectStore('sales', { keyPath: 'id', autoIncrement: true });
          salesStore.createIndex('customer_id', 'customer_id');
          salesStore.createIndex('invoice_number', 'invoice_number', { unique: true });
          salesStore.createIndex('created_at', 'created_at');
          salesStore.createIndex('sync_status', 'sync_status');
        }
        
        if (!db.objectStoreNames.contains('medicines')) {
          const medicinesStore = db.createObjectStore('medicines', { keyPath: 'id', autoIncrement: true });
          medicinesStore.createIndex('name', 'name');
          medicinesStore.createIndex('category_id', 'category_id');
          medicinesStore.createIndex('manufacturer_id', 'manufacturer_id');
          medicinesStore.createIndex('status', 'status');
          medicinesStore.createIndex('sync_status', 'sync_status');
        }
        
        if (!db.objectStoreNames.contains('inventory')) {
          const inventoryStore = db.createObjectStore('inventory', { keyPath: 'id', autoIncrement: true });
          inventoryStore.createIndex('medicine_id', 'medicine_id');
          inventoryStore.createIndex('batch_number', 'batch_number');
          inventoryStore.createIndex('location_id', 'location_id');
          inventoryStore.createIndex('medicine_batch_location', ['medicine_id', 'batch_number', 'location_id'], { unique: true });
          inventoryStore.createIndex('sync_status', 'sync_status');
        }
        
        if (!db.objectStoreNames.contains('customers')) {
          const customersStore = db.createObjectStore('customers', { keyPath: 'id', autoIncrement: true });
          customersStore.createIndex('name', 'name');
          customersStore.createIndex('email', 'email');
          customersStore.createIndex('phone', 'phone');
          customersStore.createIndex('sync_status', 'sync_status');
        }
        
        if (!db.objectStoreNames.contains('sync_queue')) {
          const syncQueueStore = db.createObjectStore('sync_queue', { keyPath: 'id', autoIncrement: true });
          syncQueueStore.createIndex('entity_type', 'entity_type');
          syncQueueStore.createIndex('operation', 'operation');
          syncQueueStore.createIndex('status', 'status');
          syncQueueStore.createIndex('priority', 'priority');
          syncQueueStore.createIndex('created_at', 'created_at');
        }
      };
    });
    
    expect(db).toBeDefined();
    expect(db.objectStoreNames.contains('sales')).toBe(true);
    expect(db.objectStoreNames.contains('medicines')).toBe(true);
    expect(db.objectStoreNames.contains('inventory')).toBe(true);
    expect(db.objectStoreNames.contains('customers')).toBe(true);
    expect(db.objectStoreNames.contains('sync_queue')).toBe(true);
    
    db.close();
  });

  test('should handle medicine operations', async () => {
    const dbName = 'medicine-test-db';
    const dbVersion = 1;
    
    const request = indexedDB.open(dbName, dbVersion);
    
    const db = await new Promise((resolve, reject) => {
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        if (!db.objectStoreNames.contains('medicines')) {
          const medicinesStore = db.createObjectStore('medicines', { keyPath: 'id', autoIncrement: true });
          medicinesStore.createIndex('name', 'name');
          medicinesStore.createIndex('sync_status', 'sync_status');
        }
      };
    });
    
    // Add a medicine
    const testMedicine = {
      name: 'Test Medicine',
      generic_name: 'Generic Name',
      category_id: 1,
      manufacturer_id: 1,
      unit_price: 10.50,
      status: 'active',
      sync_status: 'synced'
    };
    
    const tx = db.transaction(['medicines'], 'readwrite');
    const store = tx.objectStore('medicines');
    
    const addResult = await new Promise((resolve, reject) => {
      const request = store.add(testMedicine);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });
    
    expect(addResult).toBeDefined();
    
    // Retrieve the medicine
    const getResult = await new Promise((resolve, reject) => {
      const request = store.get(addResult);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });
    
    expect(getResult).toBeDefined();
    expect(getResult.name).toBe('Test Medicine');
    expect(getResult.unit_price).toBe(10.50);
    expect(getResult.sync_status).toBe('synced');
    
    db.close();
  });

  test('should handle inventory operations', async () => {
    const dbName = 'inventory-test-db';
    const dbVersion = 1;
    
    const request = indexedDB.open(dbName, dbVersion);
    
    const db = await new Promise((resolve, reject) => {
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        if (!db.objectStoreNames.contains('inventory')) {
          const inventoryStore = db.createObjectStore('inventory', { keyPath: 'id', autoIncrement: true });
          inventoryStore.createIndex('medicine_id', 'medicine_id');
          inventoryStore.createIndex('batch_number', 'batch_number');
          inventoryStore.createIndex('medicine_batch_location', ['medicine_id', 'batch_number', 'location_id'], { unique: true });
        }
      };
    });
    
    // Add inventory
    const testInventory = {
      medicine_id: 1,
      batch_number: 'BATCH001',
      quantity: 100,
      unit_price: 10.00,
      purchase_price: 8.00,
      expiry_date: '2024-12-31',
      location_id: 1
    };
    
    const tx = db.transaction(['inventory'], 'readwrite');
    const store = tx.objectStore('inventory');
    
    const addResult = await new Promise((resolve, reject) => {
      const request = store.add(testInventory);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });
    
    expect(addResult).toBeDefined();
    
    // Test quantity update simulation
    const getResult = await new Promise((resolve, reject) => {
      const request = store.get(addResult);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });
    
    expect(getResult.quantity).toBe(100);
    
    // Simulate sale (reduce quantity)
    getResult.quantity -= 10;
    
    const updateResult = await new Promise((resolve, reject) => {
      const request = store.put(getResult);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });
    
    // Verify update
    const updatedResult = await new Promise((resolve, reject) => {
      const request = store.get(addResult);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });
    
    expect(updatedResult.quantity).toBe(90);
    
    db.close();
  });

  test('should handle sync queue operations', async () => {
    const dbName = 'sync-queue-test-db';
    const dbVersion = 1;
    
    const request = indexedDB.open(dbName, dbVersion);
    
    const db = await new Promise((resolve, reject) => {
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        if (!db.objectStoreNames.contains('sync_queue')) {
          const syncQueueStore = db.createObjectStore('sync_queue', { keyPath: 'id', autoIncrement: true });
          syncQueueStore.createIndex('entity_type', 'entity_type');
          syncQueueStore.createIndex('status', 'status');
          syncQueueStore.createIndex('priority', 'priority');
        }
      };
    });
    
    // Add sync queue items with different priorities
    const syncItems = [
      {
        entity_type: 'sales',
        operation: 'create',
        data: { id: 1, total_amount: 100 },
        entity_id: 1,
        status: 'pending',
        priority: 1, // Critical
        created_at: new Date().toISOString()
      },
      {
        entity_type: 'customers',
        operation: 'update',
        data: { id: 2, name: 'Updated Customer' },
        entity_id: 2,
        status: 'pending',
        priority: 2, // High
        created_at: new Date().toISOString()
      },
      {
        entity_type: 'categories',
        operation: 'create',
        data: { id: 3, name: 'New Category' },
        entity_id: 3,
        status: 'pending',
        priority: 4, // Low
        created_at: new Date().toISOString()
      }
    ];
    
    const tx = db.transaction(['sync_queue'], 'readwrite');
    const store = tx.objectStore('sync_queue');
    
    // Add all items
    for (const item of syncItems) {
      await new Promise((resolve, reject) => {
        const request = store.add(item);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
      });
    }
    
    // Get all pending items
    const pendingItems = await new Promise((resolve, reject) => {
      const request = store.index('status').getAll('pending');
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });
    
    expect(pendingItems).toHaveLength(3);
    
    // Verify priority ordering (lower number = higher priority)
    const sortedItems = pendingItems.sort((a, b) => a.priority - b.priority);
    expect(sortedItems[0].entity_type).toBe('sales'); // Priority 1
    expect(sortedItems[1].entity_type).toBe('customers'); // Priority 2
    expect(sortedItems[2].entity_type).toBe('categories'); // Priority 4
    
    db.close();
  });
});
