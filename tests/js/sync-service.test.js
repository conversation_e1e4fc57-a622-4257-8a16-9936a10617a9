/**
 * Sync Service Integration Tests
 * 
 * Integration tests for sync processes and API interactions
 */

import 'fake-indexeddb/auto';
import * as SyncService from '../../resources/js/sync-service.js';
import * as OfflineDB from '../../resources/js/offline-db.js';

// Mock fetch for API calls
global.fetch = jest.fn();

describe('Sync Service Integration Tests', () => {
  beforeEach(async () => {
    // Clear database and reset mocks
    await OfflineDB.clearDatabase();
    fetch.mockClear();
  });

  describe('Data Fetching and Caching', () => {
    test('should fetch and cache medicines from API', async () => {
      const mockMedicines = {
        data: [
          {
            id: 1,
            name: 'Medicine 1',
            category: { id: 1, name: 'Category 1' },
            manufacturer: { id: 1, name: 'Manufacturer 1' }
          },
          {
            id: 2,
            name: 'Medicine 2',
            category: { id: 2, name: 'Category 2' },
            manufacturer: { id: 2, name: 'Manufacturer 2' }
          }
        ],
        meta: {
          count: 2,
          has_more: false
        }
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockMedicines
      });

      await SyncService.fetchMedicinesData('');

      const cachedMedicines = await OfflineDB.getAllMedicines();
      expect(cachedMedicines).toHaveLength(2);
      expect(cachedMedicines[0].name).toBe('Medicine 1');
    });

    test('should handle API errors gracefully', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        text: async () => 'Internal Server Error'
      });

      // Should not throw error, but log it
      await expect(SyncService.fetchMedicinesData('')).resolves.not.toThrow();
    });

    test('should fetch reference data', async () => {
      const mockCategories = {
        data: [
          { id: 1, name: 'Category 1', is_active: true },
          { id: 2, name: 'Category 2', is_active: true }
        ]
      };

      const mockManufacturers = {
        data: [
          { id: 1, name: 'Manufacturer 1', is_active: true },
          { id: 2, name: 'Manufacturer 2', is_active: true }
        ]
      };

      fetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockCategories
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockManufacturers
        });

      await SyncService.fetchReferenceData('');

      const cachedCategories = await OfflineDB.getAllCategories();
      const cachedManufacturers = await OfflineDB.getAllManufacturers();

      expect(cachedCategories).toHaveLength(2);
      expect(cachedManufacturers).toHaveLength(2);
    });
  });

  describe('Sync Queue Processing', () => {
    test('should process pending sync items', async () => {
      // Add test data to sync queue
      const testSale = {
        id: 1,
        customer_id: 1,
        total_amount: 100.00,
        payment_method: 'cash',
        items: [
          {
            medicine_id: 1,
            quantity: 2,
            unit_price: 50.00
          }
        ]
      };

      await OfflineDB.addToSyncQueue('sales', 'create', testSale, 1);

      // Mock successful API response
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          id: 123,
          status: 'success'
        })
      });

      await SyncService.processSyncQueue();

      // Verify sync item was processed
      const pendingItems = await OfflineDB.getPendingSyncItems();
      expect(pendingItems).toHaveLength(0);
    });

    test('should handle sync failures with retry', async () => {
      const testData = { id: 1, name: 'Test' };
      await OfflineDB.addToSyncQueue('medicines', 'create', testData, 1);

      // Mock API failure
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        text: async () => 'Server Error'
      });

      await SyncService.processSyncQueue();

      // Verify item is marked for retry
      const retryItems = await OfflineDB.getFailedSyncItems();
      expect(retryItems.length).toBeGreaterThan(0);
    });

    test('should respect sync priorities', async () => {
      // Add items with different priorities
      await OfflineDB.addToSyncQueue('categories', 'create', {}, 1); // Low priority
      await OfflineDB.addToSyncQueue('sales', 'create', {}, 2); // Critical priority

      const pendingItems = await OfflineDB.getPendingSyncItems();
      
      // Sales should come first due to higher priority
      expect(pendingItems[0].entity_type).toBe('sales');
      expect(pendingItems[1].entity_type).toBe('categories');
    });
  });

  describe('Conflict Resolution', () => {
    test('should detect and resolve conflicts', async () => {
      const localData = {
        id: 1,
        name: 'Local Name',
        updated_at: '2023-01-01T10:00:00Z'
      };

      const serverData = {
        id: 1,
        name: 'Server Name',
        updated_at: '2023-01-01T11:00:00Z'
      };

      // Mock server response with conflict
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => serverData
      });

      // Mock conflict resolution (server wins due to newer timestamp)
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          id: 1,
          status: 'success'
        })
      });

      await OfflineDB.addToSyncQueue('medicines', 'update', localData, 1);
      await SyncService.processSyncQueue();

      // Verify conflict was resolved
      const pendingItems = await OfflineDB.getPendingSyncItems();
      expect(pendingItems).toHaveLength(0);
    });
  });

  describe('Incremental Sync', () => {
    test('should perform incremental sync with timestamp', async () => {
      // Set last sync timestamp
      await OfflineDB.saveSetting('lastSuccessfulSync', '2023-01-01T00:00:00Z');

      const mockResponse = {
        data: [
          {
            id: 1,
            name: 'Updated Medicine',
            updated_at: '2023-01-01T12:00:00Z'
          }
        ],
        meta: { count: 1, has_more: false }
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      await SyncService.fetchMedicinesData('?since=2023-01-01T00%3A00%3A00Z');

      // Verify fetch was called with since parameter
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('since=2023-01-01T00%3A00%3A00Z')
      );
    });
  });

  describe('Batch Processing', () => {
    test('should process sync items in batches', async () => {
      // Add multiple items to sync queue
      for (let i = 1; i <= 10; i++) {
        await OfflineDB.addToSyncQueue('medicines', 'create', { id: i }, i);
      }

      // Mock successful responses
      for (let i = 0; i < 10; i++) {
        fetch.mockResolvedValueOnce({
          ok: true,
          json: async () => ({ id: i + 1, status: 'success' })
        });
      }

      await SyncService.processSyncQueue({ batchSize: 5 });

      // Verify all items were processed
      const pendingItems = await OfflineDB.getPendingSyncItems();
      expect(pendingItems).toHaveLength(0);
    });
  });

  describe('Network Status Handling', () => {
    test('should handle offline status', async () => {
      // Mock offline status
      Object.defineProperty(navigator, 'onLine', {
        writable: true,
        value: false
      });

      const result = await SyncService.isOnline();
      expect(result).toBe(false);

      // Should not attempt sync when offline
      await OfflineDB.addToSyncQueue('medicines', 'create', {}, 1);
      await SyncService.processSyncQueue();

      // Items should remain pending
      const pendingItems = await OfflineDB.getPendingSyncItems();
      expect(pendingItems).toHaveLength(1);
    });

    test('should resume sync when back online', async () => {
      // Start offline
      Object.defineProperty(navigator, 'onLine', {
        writable: true,
        value: false
      });

      await OfflineDB.addToSyncQueue('medicines', 'create', {}, 1);

      // Go online
      Object.defineProperty(navigator, 'onLine', {
        writable: true,
        value: true
      });

      // Mock successful sync
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ id: 1, status: 'success' })
      });

      await SyncService.processSyncQueue();

      const pendingItems = await OfflineDB.getPendingSyncItems();
      expect(pendingItems).toHaveLength(0);
    });
  });

  describe('Error Handling and Recovery', () => {
    test('should handle network timeouts', async () => {
      await OfflineDB.addToSyncQueue('medicines', 'create', {}, 1);

      // Mock network timeout
      fetch.mockRejectedValueOnce(new Error('Network timeout'));

      await SyncService.processSyncQueue();

      // Item should be marked for retry
      const failedItems = await OfflineDB.getFailedSyncItems();
      expect(failedItems.length).toBeGreaterThan(0);
    });

    test('should implement exponential backoff for retries', async () => {
      const syncId = await OfflineDB.addToSyncQueue('medicines', 'create', {}, 1);

      // Simulate multiple failures
      await OfflineDB.updateSyncItemStatus(syncId, 'failed', 'First failure');
      await OfflineDB.updateSyncItemStatus(syncId, 'failed', 'Second failure');

      const syncItem = await OfflineDB.getSyncItem(syncId);
      expect(syncItem.retry_count).toBe(2);
      expect(syncItem.next_retry_at).toBeDefined();
    });
  });
});
