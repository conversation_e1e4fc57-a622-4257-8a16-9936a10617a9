/**
 * Simple Test to verify test setup is working
 */

describe('Simple Test Suite', () => {
  test('should run basic test', () => {
    expect(1 + 1).toBe(2);
  });

  test('should have access to test utilities', () => {
    expect(global.testUtils).toBeDefined();
    expect(global.testUtils.createMockSale).toBeDefined();
  });

  test('should have mocked console', () => {
    console.log('test message');
    expect(console.log).toHaveBeenCalled();
  });

  test('should have fake IndexedDB', () => {
    expect(global.indexedDB).toBeDefined();
  });
});
