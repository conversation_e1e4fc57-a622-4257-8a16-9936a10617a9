/**
 * Offline Workflow Integration Tests
 * 
 * Tests that demonstrate complete offline sales workflows
 */

// Import fake IndexedDB for testing environment
import 'fake-indexeddb/auto';

describe('Offline Workflow Integration Tests', () => {
  let db;
  const dbName = 'workflow-test-db';
  const dbVersion = 1;

  beforeEach(async () => {
    // Create a fresh database for each test
    const request = indexedDB.open(dbName + Math.random(), dbVersion);
    
    db = await new Promise((resolve, reject) => {
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // Create all necessary stores
        const stores = [
          { name: 'medicines', keyPath: 'id', indexes: ['name', 'category_id', 'sync_status'] },
          { name: 'customers', keyPath: 'id', indexes: ['name', 'email', 'sync_status'] },
          { name: 'inventory', keyPath: 'id', indexes: ['medicine_id', 'batch_number', 'sync_status'] },
          { name: 'sales', keyPath: 'id', indexes: ['customer_id', 'invoice_number', 'sync_status'] },
          { name: 'sale_items', keyPath: 'id', indexes: ['sale_id', 'medicine_id'] },
          { name: 'sync_queue', keyPath: 'id', indexes: ['entity_type', 'status', 'priority'] }
        ];
        
        stores.forEach(storeConfig => {
          if (!db.objectStoreNames.contains(storeConfig.name)) {
            const store = db.createObjectStore(storeConfig.name, { 
              keyPath: storeConfig.keyPath, 
              autoIncrement: true 
            });
            
            storeConfig.indexes.forEach(indexName => {
              store.createIndex(indexName, indexName);
            });
          }
        });
      };
    });
  });

  afterEach(() => {
    if (db) {
      db.close();
    }
  });

  test('should complete a full offline sale workflow', async () => {
    // Step 1: Set up test data (medicines, customers, inventory)
    const testMedicine = {
      id: 1,
      name: 'Paracetamol 500mg',
      generic_name: 'Paracetamol',
      category_id: 1,
      manufacturer_id: 1,
      unit_price: 5.00,
      status: 'active',
      sync_status: 'synced'
    };

    const testCustomer = {
      id: 1,
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+1234567890',
      loyalty_points: 50,
      sync_status: 'synced'
    };

    const testInventory = {
      id: 1,
      medicine_id: 1,
      batch_number: 'BATCH001',
      quantity: 100,
      unit_price: 5.00,
      purchase_price: 3.00,
      expiry_date: '2024-12-31',
      location_id: 1,
      sync_status: 'synced'
    };

    // Insert test data
    const setupTx = db.transaction(['medicines', 'customers', 'inventory'], 'readwrite');
    
    await Promise.all([
      new Promise((resolve, reject) => {
        const request = setupTx.objectStore('medicines').add(testMedicine);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
      }),
      new Promise((resolve, reject) => {
        const request = setupTx.objectStore('customers').add(testCustomer);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
      }),
      new Promise((resolve, reject) => {
        const request = setupTx.objectStore('inventory').add(testInventory);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
      })
    ]);

    // Step 2: Create a sale
    const saleData = {
      customer_id: 1,
      invoice_number: 'INV-001',
      total_amount: 25.00,
      subtotal: 25.00,
      tax_amount: 0.00,
      discount_amount: 0.00,
      payment_method: 'cash',
      payment_status: 'paid',
      sale_date: new Date().toISOString(),
      created_at: new Date().toISOString(),
      sync_status: 'pending'
    };

    const saleItems = [
      {
        medicine_id: 1,
        quantity: 5,
        unit_price: 5.00,
        total_price: 25.00,
        batch_number: 'BATCH001',
        discount: 0.00,
        tax_rate: 0.00
      }
    ];

    // Step 3: Process the sale
    const saleTx = db.transaction(['sales', 'sale_items', 'inventory', 'sync_queue'], 'readwrite');
    
    // Save sale
    const saleId = await new Promise((resolve, reject) => {
      const request = saleTx.objectStore('sales').add(saleData);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });

    expect(saleId).toBeDefined();

    // Save sale items
    for (const item of saleItems) {
      item.sale_id = saleId;
      await new Promise((resolve, reject) => {
        const request = saleTx.objectStore('sale_items').add(item);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
      });
    }

    // Update inventory (reduce quantity)
    const inventoryItem = await new Promise((resolve, reject) => {
      const request = saleTx.objectStore('inventory').get(1);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });

    expect(inventoryItem.quantity).toBe(100);
    
    inventoryItem.quantity -= 5; // Sold 5 units
    inventoryItem.sync_status = 'pending';

    await new Promise((resolve, reject) => {
      const request = saleTx.objectStore('inventory').put(inventoryItem);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });

    // Add to sync queue
    const syncItems = [
      {
        entity_type: 'sales',
        operation: 'create',
        data: { ...saleData, id: saleId },
        entity_id: saleId,
        status: 'pending',
        priority: 1, // Critical
        created_at: new Date().toISOString()
      },
      {
        entity_type: 'inventory',
        operation: 'update',
        data: inventoryItem,
        entity_id: inventoryItem.id,
        status: 'pending',
        priority: 2, // High
        created_at: new Date().toISOString()
      }
    ];

    for (const syncItem of syncItems) {
      await new Promise((resolve, reject) => {
        const request = saleTx.objectStore('sync_queue').add(syncItem);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
      });
    }

    // Step 4: Verify the sale was processed correctly
    const verifyTx = db.transaction(['sales', 'sale_items', 'inventory', 'sync_queue'], 'readonly');
    
    // Check sale was saved
    const savedSale = await new Promise((resolve, reject) => {
      const request = verifyTx.objectStore('sales').get(saleId);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });

    expect(savedSale).toBeDefined();
    expect(savedSale.invoice_number).toBe('INV-001');
    expect(savedSale.total_amount).toBe(25.00);
    expect(savedSale.sync_status).toBe('pending');

    // Check sale items were saved
    const savedSaleItems = await new Promise((resolve, reject) => {
      const request = verifyTx.objectStore('sale_items').index('sale_id').getAll(saleId);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });

    expect(savedSaleItems).toHaveLength(1);
    expect(savedSaleItems[0].quantity).toBe(5);
    expect(savedSaleItems[0].total_price).toBe(25.00);

    // Check inventory was updated
    const updatedInventory = await new Promise((resolve, reject) => {
      const request = verifyTx.objectStore('inventory').get(1);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });

    expect(updatedInventory.quantity).toBe(95); // 100 - 5 = 95
    expect(updatedInventory.sync_status).toBe('pending');

    // Check sync queue has items
    const syncQueueItems = await new Promise((resolve, reject) => {
      const request = verifyTx.objectStore('sync_queue').index('status').getAll('pending');
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });

    expect(syncQueueItems).toHaveLength(2);
    
    // Verify priority ordering
    const sortedSyncItems = syncQueueItems.sort((a, b) => a.priority - b.priority);
    expect(sortedSyncItems[0].entity_type).toBe('sales'); // Priority 1
    expect(sortedSyncItems[1].entity_type).toBe('inventory'); // Priority 2
  });

  test('should handle insufficient stock validation', async () => {
    // Set up medicine and inventory with low stock
    const testMedicine = {
      id: 1,
      name: 'Low Stock Medicine',
      unit_price: 10.00,
      status: 'active'
    };

    const testInventory = {
      id: 1,
      medicine_id: 1,
      batch_number: 'BATCH001',
      quantity: 2, // Only 2 units available
      unit_price: 10.00
    };

    const setupTx = db.transaction(['medicines', 'inventory'], 'readwrite');
    
    await Promise.all([
      new Promise((resolve, reject) => {
        const request = setupTx.objectStore('medicines').add(testMedicine);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
      }),
      new Promise((resolve, reject) => {
        const request = setupTx.objectStore('inventory').add(testInventory);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
      })
    ]);

    // Try to sell more than available
    const checkTx = db.transaction(['inventory'], 'readonly');
    const availableInventory = await new Promise((resolve, reject) => {
      const request = checkTx.objectStore('inventory').index('medicine_id').getAll(1);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });

    const totalAvailable = availableInventory.reduce((sum, item) => sum + item.quantity, 0);
    const requestedQuantity = 5;

    // Validation should fail
    expect(totalAvailable).toBe(2);
    expect(totalAvailable).toBeLessThan(requestedQuantity);
    
    // This simulates the validation that would happen in the actual offline-db module
    const hasInsufficientStock = totalAvailable < requestedQuantity;
    expect(hasInsufficientStock).toBe(true);
  });

  test('should calculate loyalty points correctly', async () => {
    const saleAmount = 100.00;
    const loyaltyRate = 0.01; // 1% earning rate
    
    // Calculate points earned
    const pointsEarned = Math.floor(saleAmount * loyaltyRate);
    expect(pointsEarned).toBe(1);
    
    // Test points redemption
    const customerPoints = 50;
    const pointsToRedeem = 25;
    const pointValue = 0.01; // 1 point = $0.01
    
    // Validate redemption
    expect(customerPoints).toBeGreaterThanOrEqual(pointsToRedeem);
    
    const discountAmount = pointsToRedeem * pointValue;
    expect(discountAmount).toBe(0.25);
    
    const remainingPoints = customerPoints - pointsToRedeem + pointsEarned;
    expect(remainingPoints).toBe(26); // 50 - 25 + 1 = 26
  });

  test('should handle expiry date validation', async () => {
    const today = new Date();
    const expiredDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
    const validDate = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days from now
    
    // Test expired medicine
    const expiredInventory = {
      medicine_id: 1,
      batch_number: 'EXPIRED001',
      quantity: 10,
      expiry_date: expiredDate.toISOString().split('T')[0] // YYYY-MM-DD format
    };
    
    // Test valid medicine
    const validInventory = {
      medicine_id: 2,
      batch_number: 'VALID001',
      quantity: 10,
      expiry_date: validDate.toISOString().split('T')[0]
    };
    
    // Validation logic
    const isExpired = (expiryDate) => {
      const expiry = new Date(expiryDate);
      return expiry < today;
    };
    
    expect(isExpired(expiredInventory.expiry_date)).toBe(true);
    expect(isExpired(validInventory.expiry_date)).toBe(false);
    
    // Calculate days until expiry
    const daysUntilExpiry = (expiryDate) => {
      const expiry = new Date(expiryDate);
      const diffTime = expiry - today;
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    };
    
    expect(daysUntilExpiry(validInventory.expiry_date)).toBe(30);
    expect(daysUntilExpiry(expiredInventory.expiry_date)).toBeLessThan(0);
  });
});
