/**
 * End-to-End Tests for Offline Sales Workflows
 * 
 * These tests simulate complete offline sales scenarios from start to finish
 */

import 'fake-indexeddb/auto';
import * as OfflineDB from '../../resources/js/offline-db.js';
import * as SyncService from '../../resources/js/sync-service.js';
import * as DataIntegrity from '../../resources/js/data-integrity.js';

// Mock fetch for API calls
global.fetch = jest.fn();

describe('Offline Sales E2E Tests', () => {
  beforeEach(async () => {
    // Clear database and reset mocks
    await OfflineDB.clearDatabase();
    fetch.mockClear();
    
    // Set up test data
    await setupTestData();
  });

  async function setupTestData() {
    // Categories
    await OfflineDB.cacheCategories([
      { id: 1, name: 'Pain Relief', is_active: true },
      { id: 2, name: 'Antibiotics', is_active: true }
    ]);

    // Manufacturers
    await OfflineDB.cacheManufacturers([
      { id: 1, name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', is_active: true },
      { id: 2, name: 'MediLab', is_active: true }
    ]);

    // Medicines
    await OfflineDB.cacheMedicines([
      {
        id: 1,
        name: 'Paracetamol 500mg',
        generic_name: 'Paracetamol',
        category_id: 1,
        manufacturer_id: 1,
        unit_price: 5.00,
        status: 'active'
      },
      {
        id: 2,
        name: 'Amoxicillin 250mg',
        generic_name: 'Amoxicillin',
        category_id: 2,
        manufacturer_id: 2,
        unit_price: 15.00,
        status: 'active'
      }
    ]);

    // Inventory
    await OfflineDB.cacheInventory([
      {
        id: 1,
        medicine_id: 1,
        batch_number: 'PARA001',
        quantity: 100,
        unit_price: 5.00,
        purchase_price: 3.50,
        expiry_date: '2024-12-31',
        location_id: 1
      },
      {
        id: 2,
        medicine_id: 2,
        batch_number: 'AMOX001',
        quantity: 50,
        unit_price: 15.00,
        purchase_price: 12.00,
        expiry_date: '2024-06-30',
        location_id: 1
      }
    ]);

    // Customer
    const customerId = await OfflineDB.saveCustomer({
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '1234567890',
      address: '123 Main Street',
      loyalty_points: 100,
      status: 'active'
    });

    return { customerId };
  }

  describe('Complete Offline Sale Workflow', () => {
    test('should process complete sale with multiple items', async () => {
      const { customerId } = await setupTestData();

      // Create sale with multiple items
      const saleData = {
        customer_id: customerId,
        total_amount: 35.00,
        paid_amount: 35.00,
        due_amount: 0.00,
        discount_amount: 0.00,
        tax_amount: 0.00,
        payment_method: 'cash',
        payment_status: 'completed',
        items: [
          {
            medicine_id: 1,
            batch_number: 'PARA001',
            quantity: 2,
            unit_price: 5.00,
            location_id: 1
          },
          {
            medicine_id: 2,
            batch_number: 'AMOX001',
            quantity: 1,
            unit_price: 15.00,
            location_id: 1
          }
        ]
      };

      // Process sale offline
      const result = await OfflineDB.saveSale(saleData);

      expect(result.success).toBe(true);
      expect(result.saleId).toBeDefined();
      expect(result.totalAmount).toBe(35.00);

      // Verify sale was saved
      const savedSale = await OfflineDB.getSale(result.saleId);
      expect(savedSale.total_amount).toBe(35.00);
      expect(savedSale.customer_id).toBe(customerId);

      // Verify sale items were saved
      const saleItems = await OfflineDB.getSaleItemsBySale(result.saleId);
      expect(saleItems).toHaveLength(2);

      // Verify inventory was updated
      const inventory1 = await OfflineDB.getInventoryByMedicineBatch(1, 'PARA001', 1);
      const inventory2 = await OfflineDB.getInventoryByMedicineBatch(2, 'AMOX001', 1);
      expect(inventory1.quantity).toBe(98); // 100 - 2
      expect(inventory2.quantity).toBe(49); // 50 - 1

      // Verify sync queue entry was created
      const pendingItems = await OfflineDB.getPendingSyncItems();
      expect(pendingItems.length).toBeGreaterThan(0);
      expect(pendingItems.some(item => item.entity_type === 'sales')).toBe(true);
    });

    test('should handle sale with loyalty points redemption', async () => {
      const { customerId } = await setupTestData();

      const saleData = {
        customer_id: customerId,
        total_amount: 20.00,
        paid_amount: 15.00, // $5 discount from loyalty points
        due_amount: 0.00,
        discount_amount: 5.00,
        loyalty_points_to_redeem: 50, // 50 points = $5 discount
        payment_method: 'cash',
        payment_status: 'completed',
        items: [
          {
            medicine_id: 1,
            batch_number: 'PARA001',
            quantity: 4,
            unit_price: 5.00,
            location_id: 1
          }
        ]
      };

      const result = await OfflineDB.saveSale(saleData);
      expect(result.success).toBe(true);

      // Verify customer loyalty points were deducted
      const updatedCustomer = await OfflineDB.getCustomer(customerId);
      expect(updatedCustomer.loyalty_points).toBe(50); // 100 - 50

      // Verify loyalty transaction was recorded
      const loyaltyTransactions = await OfflineDB.getLoyaltyTransactionsByCustomer(customerId);
      expect(loyaltyTransactions.length).toBeGreaterThan(0);
    });

    test('should award loyalty points for sale', async () => {
      const { customerId } = await setupTestData();

      const saleData = {
        customer_id: customerId,
        total_amount: 100.00,
        paid_amount: 100.00,
        due_amount: 0.00,
        payment_method: 'cash',
        payment_status: 'completed',
        items: [
          {
            medicine_id: 1,
            batch_number: 'PARA001',
            quantity: 20,
            unit_price: 5.00,
            location_id: 1
          }
        ]
      };

      const result = await OfflineDB.saveSale(saleData);
      expect(result.success).toBe(true);

      // Verify loyalty points were awarded (1% of total)
      const updatedCustomer = await OfflineDB.getCustomer(customerId);
      expect(updatedCustomer.loyalty_points).toBe(101); // 100 + 1 (1% of $100)
    });

    test('should create profit/loss records', async () => {
      const saleData = {
        total_amount: 10.00,
        paid_amount: 10.00,
        due_amount: 0.00,
        payment_method: 'cash',
        payment_status: 'completed',
        items: [
          {
            medicine_id: 1,
            batch_number: 'PARA001',
            quantity: 2,
            unit_price: 5.00,
            location_id: 1
          }
        ]
      };

      const result = await OfflineDB.saveSale(saleData);
      expect(result.success).toBe(true);

      // Verify profit/loss record was created
      const profitLossRecords = await OfflineDB.getProfitLossBySale(result.saleId);
      expect(profitLossRecords).toHaveLength(1);
      
      const record = profitLossRecords[0];
      expect(record.total_cost).toBe(7.00); // 2 * 3.50 purchase price
      expect(record.total_revenue).toBe(10.00);
      expect(record.gross_profit).toBe(3.00); // 10.00 - 7.00
    });
  });

  describe('Validation and Error Handling', () => {
    test('should prevent sale with insufficient stock', async () => {
      const saleData = {
        total_amount: 1000.00,
        paid_amount: 1000.00,
        due_amount: 0.00,
        payment_method: 'cash',
        items: [
          {
            medicine_id: 1,
            batch_number: 'PARA001',
            quantity: 150, // More than available (100)
            unit_price: 5.00,
            location_id: 1
          }
        ]
      };

      await expect(OfflineDB.saveSale(saleData)).rejects.toThrow(/insufficient stock/i);
    });

    test('should prevent sale with expired medicine', async () => {
      // Add expired inventory
      await OfflineDB.cacheInventory([
        {
          id: 3,
          medicine_id: 1,
          batch_number: 'PARA_EXPIRED',
          quantity: 10,
          unit_price: 5.00,
          purchase_price: 3.50,
          expiry_date: '2020-01-01', // Expired
          location_id: 1
        }
      ]);

      const saleData = {
        total_amount: 5.00,
        paid_amount: 5.00,
        due_amount: 0.00,
        payment_method: 'cash',
        items: [
          {
            medicine_id: 1,
            batch_number: 'PARA_EXPIRED',
            quantity: 1,
            unit_price: 5.00,
            location_id: 1
          }
        ]
      };

      await expect(OfflineDB.saveSale(saleData)).rejects.toThrow(/expired/i);
    });

    test('should validate loyalty points redemption', async () => {
      const { customerId } = await setupTestData();

      const saleData = {
        customer_id: customerId,
        total_amount: 10.00,
        paid_amount: 0.00,
        due_amount: 0.00,
        loyalty_points_to_redeem: 200, // More than available (100)
        payment_method: 'loyalty',
        items: [
          {
            medicine_id: 1,
            batch_number: 'PARA001',
            quantity: 2,
            unit_price: 5.00,
            location_id: 1
          }
        ]
      };

      await expect(OfflineDB.saveSale(saleData)).rejects.toThrow(/insufficient loyalty points/i);
    });
  });

  describe('Sync and Conflict Resolution', () => {
    test('should sync sale to server when online', async () => {
      // Create offline sale
      const saleData = {
        total_amount: 10.00,
        paid_amount: 10.00,
        due_amount: 0.00,
        payment_method: 'cash',
        items: [
          {
            medicine_id: 1,
            batch_number: 'PARA001',
            quantity: 2,
            unit_price: 5.00,
            location_id: 1
          }
        ]
      };

      const result = await OfflineDB.saveSale(saleData);

      // Mock successful server sync
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          id: 123,
          invoice_number: 'INV-2023-001',
          status: 'success'
        })
      });

      // Process sync queue
      await SyncService.processSyncQueue();

      // Verify sync was successful
      const pendingItems = await OfflineDB.getPendingSyncItems();
      const salesSyncItems = pendingItems.filter(item => item.entity_type === 'sales');
      expect(salesSyncItems).toHaveLength(0);
    });

    test('should handle sync conflicts gracefully', async () => {
      const saleData = {
        total_amount: 10.00,
        paid_amount: 10.00,
        due_amount: 0.00,
        payment_method: 'cash',
        items: [
          {
            medicine_id: 1,
            batch_number: 'PARA001',
            quantity: 2,
            unit_price: 5.00,
            location_id: 1
          }
        ]
      };

      const result = await OfflineDB.saveSale(saleData);

      // Mock conflict response (409)
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 409,
        text: async () => JSON.stringify({
          error: 'Conflict',
          serverData: {
            id: result.saleId,
            total_amount: 12.00, // Different amount
            updated_at: new Date().toISOString()
          }
        })
      });

      // Mock successful resolution
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          id: 123,
          status: 'success'
        })
      });

      await SyncService.processSyncQueue();

      // Verify conflict was handled
      const pendingItems = await OfflineDB.getPendingSyncItems();
      const salesSyncItems = pendingItems.filter(item => item.entity_type === 'sales');
      expect(salesSyncItems).toHaveLength(0);
    });
  });

  describe('Data Integrity Checks', () => {
    test('should maintain data consistency during offline operations', async () => {
      // Perform multiple sales
      for (let i = 0; i < 5; i++) {
        const saleData = {
          total_amount: 5.00,
          paid_amount: 5.00,
          due_amount: 0.00,
          payment_method: 'cash',
          items: [
            {
              medicine_id: 1,
              batch_number: 'PARA001',
              quantity: 1,
              unit_price: 5.00,
              location_id: 1
            }
          ]
        };

        await OfflineDB.saveSale(saleData);
      }

      // Check data integrity
      const integrityResult = await OfflineDB.performIntegrityCheck();
      expect(integrityResult.consistent).toBe(true);
      expect(integrityResult.issues).toHaveLength(0);

      // Verify inventory consistency
      const inventory = await OfflineDB.getInventoryByMedicineBatch(1, 'PARA001', 1);
      expect(inventory.quantity).toBe(95); // 100 - 5
    });
  });
});
