/**
 * Offline Database Tests
 * 
 * Unit tests for IndexedDB operations and offline functionality
 */

// Mock IndexedDB for testing environment
import 'fake-indexeddb/auto';
import * as OfflineDB from '../../resources/js/offline-db.js';

describe('Offline Database Tests', () => {
  beforeEach(async () => {
    // Clear database before each test
    await OfflineDB.clearDatabase();
  });

  describe('Database Initialization', () => {
    test('should initialize database with correct stores', async () => {
      const db = await OfflineDB.initDB();
      
      expect(db).toBeDefined();
      expect(db.objectStoreNames.contains('sales')).toBe(true);
      expect(db.objectStoreNames.contains('sale_items')).toBe(true);
      expect(db.objectStoreNames.contains('inventory')).toBe(true);
      expect(db.objectStoreNames.contains('customers')).toBe(true);
      expect(db.objectStoreNames.contains('medicines')).toBe(true);
      expect(db.objectStoreNames.contains('categories')).toBe(true);
      expect(db.objectStoreNames.contains('manufacturers')).toBe(true);
      expect(db.objectStoreNames.contains('sync_queue')).toBe(true);
    });
  });

  describe('Medicine Operations', () => {
    test('should cache medicines successfully', async () => {
      const testMedicines = [
        {
          id: 1,
          name: 'Test Medicine 1',
          generic_name: 'Generic 1',
          category_id: 1,
          manufacturer_id: 1,
          unit_price: 10.50,
          status: 'active'
        },
        {
          id: 2,
          name: 'Test Medicine 2',
          generic_name: 'Generic 2',
          category_id: 2,
          manufacturer_id: 2,
          unit_price: 25.00,
          status: 'active'
        }
      ];

      await OfflineDB.cacheMedicines(testMedicines);
      const cachedMedicines = await OfflineDB.getAllMedicines();

      expect(cachedMedicines).toHaveLength(2);
      expect(cachedMedicines[0].name).toBe('Test Medicine 1');
      expect(cachedMedicines[1].name).toBe('Test Medicine 2');
    });

    test('should get medicine by ID', async () => {
      const testMedicine = {
        id: 1,
        name: 'Test Medicine',
        generic_name: 'Generic',
        category_id: 1,
        manufacturer_id: 1,
        unit_price: 15.75,
        status: 'active'
      };

      await OfflineDB.cacheMedicines([testMedicine]);
      const retrievedMedicine = await OfflineDB.getMedicine(1);

      expect(retrievedMedicine).toBeDefined();
      expect(retrievedMedicine.name).toBe('Test Medicine');
      expect(retrievedMedicine.unit_price).toBe(15.75);
    });
  });

  describe('Customer Operations', () => {
    test('should save and retrieve customer', async () => {
      const testCustomer = {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '1234567890',
        address: '123 Main St',
        loyalty_points: 100,
        status: 'active'
      };

      const customerId = await OfflineDB.saveCustomer(testCustomer);
      expect(customerId).toBeDefined();

      const retrievedCustomer = await OfflineDB.getCustomer(customerId);
      expect(retrievedCustomer.name).toBe('John Doe');
      expect(retrievedCustomer.email).toBe('<EMAIL>');
      expect(retrievedCustomer.loyalty_points).toBe(100);
    });

    test('should update customer loyalty points', async () => {
      const testCustomer = {
        name: 'Jane Doe',
        email: '<EMAIL>',
        loyalty_points: 50
      };

      const customerId = await OfflineDB.saveCustomer(testCustomer);
      
      // Update loyalty points
      await OfflineDB.updateCustomerLoyaltyPoints(customerId, 75);
      
      const updatedCustomer = await OfflineDB.getCustomer(customerId);
      expect(updatedCustomer.loyalty_points).toBe(75);
    });
  });

  describe('Inventory Operations', () => {
    test('should cache and retrieve inventory', async () => {
      const testInventory = [
        {
          id: 1,
          medicine_id: 1,
          batch_number: 'BATCH001',
          quantity: 100,
          unit_price: 10.00,
          purchase_price: 8.00,
          expiry_date: '2024-12-31',
          location_id: 1
        }
      ];

      await OfflineDB.cacheInventory(testInventory);
      const cachedInventory = await OfflineDB.getAllInventory();

      expect(cachedInventory).toHaveLength(1);
      expect(cachedInventory[0].batch_number).toBe('BATCH001');
      expect(cachedInventory[0].quantity).toBe(100);
    });

    test('should update inventory quantity', async () => {
      const testInventory = {
        id: 1,
        medicine_id: 1,
        batch_number: 'BATCH001',
        quantity: 100,
        location_id: 1
      };

      await OfflineDB.cacheInventory([testInventory]);
      
      // Update quantity (simulate sale of 10 units)
      const result = await OfflineDB.updateInventoryQuantity(
        null, // tx will be created internally
        1,    // medicine_id
        'BATCH001',
        1,    // location_id
        -10   // quantity change
      );

      expect(result.success).toBe(true);
      expect(result.newQuantity).toBe(90);
    });
  });

  describe('Sales Operations', () => {
    beforeEach(async () => {
      // Set up test data
      const testMedicine = {
        id: 1,
        name: 'Test Medicine',
        unit_price: 10.00
      };
      
      const testInventory = {
        id: 1,
        medicine_id: 1,
        batch_number: 'BATCH001',
        quantity: 100,
        unit_price: 10.00,
        purchase_price: 8.00,
        location_id: 1
      };

      const testCustomer = {
        id: 1,
        name: 'Test Customer',
        email: '<EMAIL>',
        loyalty_points: 50
      };

      await OfflineDB.cacheMedicines([testMedicine]);
      await OfflineDB.cacheInventory([testInventory]);
      await OfflineDB.saveCustomer(testCustomer);
    });

    test('should save sale with items', async () => {
      const testSale = {
        customer_id: 1,
        total_amount: 50.00,
        paid_amount: 50.00,
        due_amount: 0.00,
        discount_amount: 0.00,
        tax_amount: 0.00,
        payment_method: 'cash',
        payment_status: 'completed',
        items: [
          {
            medicine_id: 1,
            batch_number: 'BATCH001',
            quantity: 5,
            unit_price: 10.00,
            location_id: 1
          }
        ]
      };

      const result = await OfflineDB.saveSale(testSale);
      
      expect(result.success).toBe(true);
      expect(result.saleId).toBeDefined();
      expect(result.invoiceNumber).toContain('OFFLINE-');

      // Verify sale was saved
      const savedSale = await OfflineDB.getSale(result.saleId);
      expect(savedSale.total_amount).toBe(50.00);
      expect(savedSale.customer_id).toBe(1);

      // Verify inventory was updated
      const updatedInventory = await OfflineDB.getInventoryByMedicineBatch(1, 'BATCH001', 1);
      expect(updatedInventory.quantity).toBe(95); // 100 - 5
    });

    test('should validate insufficient stock', async () => {
      const testSale = {
        total_amount: 1000.00,
        paid_amount: 1000.00,
        due_amount: 0.00,
        payment_method: 'cash',
        items: [
          {
            medicine_id: 1,
            batch_number: 'BATCH001',
            quantity: 150, // More than available (100)
            unit_price: 10.00,
            location_id: 1
          }
        ]
      };

      await expect(OfflineDB.saveSale(testSale)).rejects.toThrow(/insufficient stock/i);
    });
  });

  describe('Sync Queue Operations', () => {
    test('should add items to sync queue', async () => {
      const testData = { id: 1, name: 'Test' };
      
      const syncId = await OfflineDB.addToSyncQueue(
        'medicines',
        'create',
        testData,
        1
      );

      expect(syncId).toBeDefined();

      const pendingItems = await OfflineDB.getPendingSyncItems();
      expect(pendingItems).toHaveLength(1);
      expect(pendingItems[0].entity_type).toBe('medicines');
      expect(pendingItems[0].operation).toBe('create');
    });

    test('should prioritize sync items correctly', async () => {
      // Add items with different priorities
      await OfflineDB.addToSyncQueue('sales', 'create', {}, 1); // Critical priority
      await OfflineDB.addToSyncQueue('categories', 'create', {}, 2); // Low priority
      await OfflineDB.addToSyncQueue('customers', 'create', {}, 3); // High priority

      const pendingItems = await OfflineDB.getPendingSyncItems();
      
      // Should be ordered by priority: sales (1), customers (2), categories (4)
      expect(pendingItems[0].entity_type).toBe('sales');
      expect(pendingItems[1].entity_type).toBe('customers');
      expect(pendingItems[2].entity_type).toBe('categories');
    });

    test('should update sync item status', async () => {
      const syncId = await OfflineDB.addToSyncQueue('medicines', 'create', {}, 1);
      
      await OfflineDB.updateSyncItemStatus(syncId, 'completed');
      
      const completedItems = await OfflineDB.getCompletedSyncItems();
      expect(completedItems).toHaveLength(1);
      expect(completedItems[0].status).toBe('completed');
    });
  });

  describe('Settings Operations', () => {
    test('should save and retrieve settings', async () => {
      await OfflineDB.saveSetting('lastSync', '2023-01-01T00:00:00Z');
      await OfflineDB.saveSetting('syncInterval', 300000);

      const lastSync = await OfflineDB.getSetting('lastSync');
      const syncInterval = await OfflineDB.getSetting('syncInterval');

      expect(lastSync).toBe('2023-01-01T00:00:00Z');
      expect(syncInterval).toBe(300000);
    });
  });

  describe('Data Integrity', () => {
    test('should perform basic integrity check', async () => {
      const result = await OfflineDB.performIntegrityCheck();
      
      expect(result).toBeDefined();
      expect(result.consistent).toBeDefined();
      expect(result.issues).toBeDefined();
      expect(result.timestamp).toBeDefined();
    });
  });
});
