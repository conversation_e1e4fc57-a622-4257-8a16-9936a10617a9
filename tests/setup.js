/**
 * Test Setup for PharmaDesk Offline Tests
 */

// Import fake IndexedDB for testing
import 'fake-indexeddb/auto';

// Polyfill for structuredClone if not available
if (!global.structuredClone) {
  global.structuredClone = (obj) => {
    return JSON.parse(JSON.stringify(obj));
  };
}

// Mock console methods to reduce noise in tests
const originalConsole = global.console;
global.console = {
  ...originalConsole,
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
  debug: jest.fn()
};

// Mock navigator.onLine
Object.defineProperty(navigator, 'onLine', {
  writable: true,
  value: true
});

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    href: 'http://localhost:3000',
    origin: 'http://localhost:3000',
    protocol: 'http:',
    host: 'localhost:3000',
    hostname: 'localhost',
    port: '3000',
    pathname: '/',
    search: '',
    hash: ''
  },
  writable: true
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn()
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn()
};
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock
});

// Mock document.querySelector for CSRF token
const originalQuerySelector = document.querySelector;
document.querySelector = jest.fn((selector) => {
  if (selector === 'meta[name="csrf-token"]') {
    return {
      getAttribute: jest.fn(() => 'mock-csrf-token')
    };
  }
  return originalQuerySelector.call(document, selector);
});

// Mock fetch globally
global.fetch = jest.fn();

// Mock CustomEvent for older environments
if (!global.CustomEvent) {
  global.CustomEvent = class CustomEvent extends Event {
    constructor(type, options = {}) {
      super(type, options);
      this.detail = options.detail;
    }
  };
}

// Mock performance.now for timing tests
if (!global.performance) {
  global.performance = {
    now: jest.fn(() => Date.now())
  };
}

// Setup test utilities
global.testUtils = {
  // Wait for async operations
  waitFor: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Create mock sale data
  createMockSale: (overrides = {}) => ({
    customer_id: 1,
    total_amount: 100.00,
    paid_amount: 100.00,
    due_amount: 0.00,
    discount_amount: 0.00,
    tax_amount: 0.00,
    payment_method: 'cash',
    payment_status: 'completed',
    items: [
      {
        medicine_id: 1,
        batch_number: 'BATCH001',
        quantity: 2,
        unit_price: 50.00,
        location_id: 1
      }
    ],
    ...overrides
  }),
  
  // Create mock medicine data
  createMockMedicine: (overrides = {}) => ({
    id: 1,
    name: 'Test Medicine',
    generic_name: 'Generic Name',
    category_id: 1,
    manufacturer_id: 1,
    unit_price: 10.00,
    status: 'active',
    ...overrides
  }),
  
  // Create mock inventory data
  createMockInventory: (overrides = {}) => ({
    id: 1,
    medicine_id: 1,
    batch_number: 'BATCH001',
    quantity: 100,
    unit_price: 10.00,
    purchase_price: 8.00,
    expiry_date: '2024-12-31',
    location_id: 1,
    ...overrides
  }),
  
  // Create mock customer data
  createMockCustomer: (overrides = {}) => ({
    id: 1,
    name: 'Test Customer',
    email: '<EMAIL>',
    phone: '1234567890',
    address: '123 Test Street',
    loyalty_points: 100,
    status: 'active',
    ...overrides
  })
};

// Setup and teardown hooks
beforeEach(() => {
  // Reset all mocks before each test
  jest.clearAllMocks();
  
  // Reset navigator.onLine to true
  Object.defineProperty(navigator, 'onLine', {
    writable: true,
    value: true
  });
  
  // Clear localStorage and sessionStorage
  localStorageMock.clear();
  sessionStorageMock.clear();
  
  // Reset console mocks
  if (console.log.mockClear) console.log.mockClear();
  if (console.warn.mockClear) console.warn.mockClear();
  if (console.error.mockClear) console.error.mockClear();
  if (console.info.mockClear) console.info.mockClear();
  if (console.debug.mockClear) console.debug.mockClear();
});

afterEach(() => {
  // Clean up any remaining timers
  jest.clearAllTimers();
});

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Suppress specific warnings in tests
const originalError = console.error;
console.error = (...args) => {
  if (
    typeof args[0] === 'string' &&
    (args[0].includes('Warning: ReactDOM.render is deprecated') ||
     args[0].includes('Warning: componentWillMount has been renamed'))
  ) {
    return;
  }
  originalError.call(console, ...args);
};
