# PharmaDesk Offline Functionality Tests

This directory contains comprehensive tests for the offline functionality of PharmaDesk, including unit tests, integration tests, and end-to-end tests.

## Test Structure

### Unit Tests
- **offline-db.test.js**: Tests for IndexedDB operations, data storage, and retrieval
- Tests individual functions and methods in isolation
- Covers database initialization, CRUD operations, and data validation

### Integration Tests
- **sync-service.test.js**: Tests for sync processes and API interactions
- Tests the integration between different modules
- Covers data fetching, caching, conflict resolution, and sync queue processing

### End-to-End Tests
- **offline-sales-e2e.test.js**: Complete offline sales workflow tests
- Tests entire user scenarios from start to finish
- Covers complete sales processes, validation, and data integrity

## Running Tests

### Prerequisites
Install test dependencies:
```bash
npm install
```

### Test Commands

Run all tests:
```bash
npm test
```

Run tests in watch mode (for development):
```bash
npm run test:watch
```

Run tests with coverage report:
```bash
npm run test:coverage
```

Run specific test suites:
```bash
# Offline database tests only
npm run test:offline

# Sync service tests only
npm run test:sync

# End-to-end tests only
npm run test:e2e
```

## Test Configuration

### Jest Configuration
- **jest.config.js**: Main Jest configuration
- **setup.js**: Test environment setup and mocks
- **babel.config.js**: Babel configuration for ES6+ support

### Coverage Thresholds
The tests maintain a minimum coverage threshold of 70% for:
- Branches
- Functions
- Lines
- Statements

## Test Environment

### Mocked Dependencies
- **IndexedDB**: Uses `fake-indexeddb` for testing
- **Fetch API**: Mocked for API call testing
- **Navigator**: Mocked for online/offline status
- **LocalStorage/SessionStorage**: Mocked for browser storage
- **Console**: Mocked to reduce test noise

### Test Utilities
The `setup.js` file provides utility functions for creating mock data:
- `testUtils.createMockSale()`: Creates mock sale data
- `testUtils.createMockMedicine()`: Creates mock medicine data
- `testUtils.createMockInventory()`: Creates mock inventory data
- `testUtils.createMockCustomer()`: Creates mock customer data

## Test Scenarios Covered

### Database Operations
- ✅ Database initialization and schema creation
- ✅ Medicine caching and retrieval
- ✅ Customer management
- ✅ Inventory tracking and updates
- ✅ Sales creation and item management
- ✅ Sync queue operations with priorities
- ✅ Settings storage and retrieval

### Business Logic
- ✅ Sale validation (stock, prices, customer data)
- ✅ Inventory constraint checking
- ✅ Loyalty points calculation and redemption
- ✅ Profit/loss record creation
- ✅ Prescription handling
- ✅ Business rule enforcement

### Sync Functionality
- ✅ Data fetching from APIs with pagination
- ✅ Incremental sync with timestamps
- ✅ Conflict detection and resolution
- ✅ Retry logic with exponential backoff
- ✅ Batch processing
- ✅ Priority-based synchronization
- ✅ Network status handling

### Error Handling
- ✅ Insufficient stock validation
- ✅ Expired medicine detection
- ✅ Invalid customer validation
- ✅ Network timeout handling
- ✅ API error responses
- ✅ Data integrity checks

### End-to-End Workflows
- ✅ Complete offline sale process
- ✅ Multi-item sales with different medicines
- ✅ Sales with loyalty points redemption
- ✅ Loyalty points awarding
- ✅ Profit/loss tracking
- ✅ Sync to server when online
- ✅ Conflict resolution during sync
- ✅ Data consistency maintenance

## Writing New Tests

### Test File Naming
- Unit tests: `*.test.js`
- Integration tests: `*-integration.test.js`
- End-to-end tests: `*-e2e.test.js`

### Test Structure
```javascript
describe('Feature Name', () => {
  beforeEach(async () => {
    // Setup test data
    await OfflineDB.clearDatabase();
  });

  describe('Specific Functionality', () => {
    test('should do something specific', async () => {
      // Arrange
      const testData = testUtils.createMockSale();
      
      // Act
      const result = await OfflineDB.saveSale(testData);
      
      // Assert
      expect(result.success).toBe(true);
    });
  });
});
```

### Best Practices
1. **Isolation**: Each test should be independent and not rely on other tests
2. **Cleanup**: Always clean up test data in `beforeEach` or `afterEach`
3. **Mocking**: Mock external dependencies (APIs, browser APIs)
4. **Assertions**: Use descriptive assertions and test both success and failure cases
5. **Coverage**: Aim for high test coverage but focus on critical paths
6. **Documentation**: Add comments for complex test scenarios

## Debugging Tests

### Running Individual Tests
```bash
# Run a specific test file
npx jest tests/js/offline-db.test.js

# Run a specific test case
npx jest -t "should save sale with items"
```

### Debug Mode
```bash
# Run tests in debug mode
node --inspect-brk node_modules/.bin/jest --runInBand
```

### Verbose Output
```bash
# Run with verbose output
npm test -- --verbose
```

## Continuous Integration

These tests are designed to run in CI/CD environments and provide:
- Fast execution (< 30 seconds for full suite)
- Reliable results with proper mocking
- Comprehensive coverage reporting
- Clear failure messages

## Contributing

When adding new offline functionality:
1. Write tests first (TDD approach)
2. Ensure all existing tests pass
3. Maintain or improve coverage thresholds
4. Update this README if adding new test categories
