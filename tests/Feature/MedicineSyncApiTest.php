<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;

class MedicineSyncApiTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_provides_medicines_api_endpoint()
    {
        $response = $this->getJson('/api/offline/medicines');
        
        // Should return 200 even with no data
        $response->assertStatus(200);
        
        // Should have proper JSON structure
        $response->assertJsonStructure([
            'data',
            'meta' => [
                'count',
                'limit', 
                'offset',
                'has_more'
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertIsArray($responseData['data']);
        $this->assertIsArray($responseData['meta']);
    }

    #[Test]
    public function it_supports_pagination_parameters()
    {
        $response = $this->getJson('/api/offline/medicines?limit=5&offset=10');
        
        $response->assertStatus(200);
        
        $responseData = $response->json();
        $this->assertEquals(5, $responseData['meta']['limit']);
        $this->assertEquals(10, $responseData['meta']['offset']);
    }

    #[Test]
    public function it_supports_since_parameter_for_incremental_sync()
    {
        $timestamp = now()->subHour()->toISOString();
        $response = $this->getJson('/api/offline/medicines?since=' . urlencode($timestamp));
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data',
            'meta'
        ]);
    }

    #[Test]
    public function it_provides_individual_medicine_endpoint()
    {
        // Test with non-existent medicine
        $response = $this->getJson('/api/offline/medicines/99999');
        
        $response->assertStatus(404);
        $response->assertJson(['error' => 'Medicine not found']);
    }

    #[Test]
    public function it_provides_categories_endpoint()
    {
        $response = $this->getJson('/api/offline/categories');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data',
            'meta' => [
                'timestamp'
            ]
        ]);
    }

    #[Test]
    public function it_provides_manufacturers_endpoint()
    {
        $response = $this->getJson('/api/offline/manufacturers');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data',
            'meta' => [
                'timestamp'
            ]
        ]);
    }

    #[Test]
    public function it_provides_locations_endpoint()
    {
        $response = $this->getJson('/api/offline/locations');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data',
            'meta' => [
                'timestamp'
            ]
        ]);
    }

    #[Test]
    public function it_handles_invalid_pagination_parameters()
    {
        // Test with negative limit
        $response = $this->getJson('/api/offline/medicines?limit=-5');
        $response->assertStatus(200); // Should handle gracefully
        
        // Test with very large limit
        $response = $this->getJson('/api/offline/medicines?limit=10000');
        $response->assertStatus(200); // Should handle gracefully
        
        // Test with negative offset
        $response = $this->getJson('/api/offline/medicines?offset=-10');
        $response->assertStatus(200); // Should handle gracefully
    }

    #[Test]
    public function it_handles_invalid_since_parameter()
    {
        // Test with invalid date format
        $response = $this->getJson('/api/offline/medicines?since=invalid-date');
        $response->assertStatus(200); // Should handle gracefully and ignore invalid since
        
        // Test with future date
        $futureDate = now()->addYear()->toISOString();
        $response = $this->getJson('/api/offline/medicines?since=' . urlencode($futureDate));
        $response->assertStatus(200);
    }

    #[Test]
    public function it_returns_proper_content_type()
    {
        $response = $this->getJson('/api/offline/medicines');
        
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/json');
    }

    #[Test]
    public function it_includes_timestamp_in_meta()
    {
        $response = $this->getJson('/api/offline/medicines');
        
        $response->assertStatus(200);
        
        $responseData = $response->json();
        $this->assertArrayHasKey('meta', $responseData);
        
        // For medicines endpoint, timestamp might be in different format
        // Just check that meta exists and is properly structured
        $this->assertIsArray($responseData['meta']);
    }

    #[Test]
    public function it_handles_cors_for_offline_access()
    {
        $response = $this->getJson('/api/offline/medicines', [
            'Origin' => 'http://localhost:3000'
        ]);
        
        $response->assertStatus(200);
        // CORS headers should be handled by middleware if configured
    }

    #[Test]
    public function it_provides_consistent_api_structure_across_endpoints()
    {
        $endpoints = [
            '/api/offline/medicines',
            '/api/offline/categories', 
            '/api/offline/manufacturers',
            '/api/offline/locations'
        ];
        
        foreach ($endpoints as $endpoint) {
            $response = $this->getJson($endpoint);
            $response->assertStatus(200);
            
            $responseData = $response->json();
            $this->assertArrayHasKey('data', $responseData);
            $this->assertArrayHasKey('meta', $responseData);
            $this->assertIsArray($responseData['data']);
            $this->assertIsArray($responseData['meta']);
        }
    }

    #[Test]
    public function it_handles_empty_database_gracefully()
    {
        // With fresh database (no seeded data), all endpoints should still work
        $response = $this->getJson('/api/offline/medicines');
        $response->assertStatus(200);
        
        $responseData = $response->json();
        $this->assertIsArray($responseData['data']);
        $this->assertCount(0, $responseData['data']); // Should be empty array
        $this->assertEquals(0, $responseData['meta']['count']);
    }

    #[Test]
    public function it_validates_medicine_id_parameter()
    {
        // Test with non-numeric ID
        $response = $this->getJson('/api/offline/medicines/abc');
        $response->assertStatus(404); // Should handle as not found
        
        // Test with zero ID
        $response = $this->getJson('/api/offline/medicines/0');
        $response->assertStatus(404);
        
        // Test with negative ID
        $response = $this->getJson('/api/offline/medicines/-1');
        $response->assertStatus(404);
    }

    #[Test]
    public function it_provides_proper_error_responses()
    {
        $response = $this->getJson('/api/offline/medicines/99999');
        
        $response->assertStatus(404);
        $response->assertJson(['error' => 'Medicine not found']);
        $response->assertHeader('Content-Type', 'application/json');
    }
}
