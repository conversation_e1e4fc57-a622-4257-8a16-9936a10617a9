<?php

namespace Tests\Feature\PWA;

use Tests\TestCase;
use Illuminate\Support\Facades\File;

class OfflineDbTest extends TestCase
{
    /**
     * Test that the offline-db.js file exists and is accessible
     */
    public function test_offline_db_file_exists_and_accessible()
    {
        $this->assertTrue(File::exists(resource_path('js/offline-db.js')), 'offline-db.js file does not exist');
        
        // Check if the file is included in the app.js bundle
        $appJsContent = File::get(resource_path('js/app.js'));
        $this->assertStringContainsString('offline-db', $appJsContent, 'offline-db.js is not imported in app.js');
    }

    /**
     * Test that the offline database has the required stores
     */
    public function test_offline_db_has_required_stores()
    {
        $offlineDbContent = File::get(resource_path('js/offline-db.js'));
        
        // Check for required stores
        $this->assertStringContainsString('SALES', $offlineDbContent);
        $this->assertStringContainsString('SALE_ITEMS', $offlineDbContent);
        $this->assertStringContainsString('INVENTORY', $offlineDbContent);
        $this->assertStringContainsString('CUSTOMERS', $offlineDbContent);
        $this->assertStringContainsString('SYNC_QUEUE', $offlineDbContent);
        $this->assertStringContainsString('MEDICINES', $offlineDbContent);
        $this->assertStringContainsString('SETTINGS', $offlineDbContent);
    }

    /**
     * Test that the offline database has the required methods
     */
    public function test_offline_db_has_required_methods()
    {
        $offlineDbContent = File::get(resource_path('js/offline-db.js'));
        
        // Check for required methods
        $this->assertStringContainsString('function initDB()', $offlineDbContent);
        $this->assertStringContainsString('function addToSyncQueue(', $offlineDbContent);
        $this->assertStringContainsString('function getPendingSyncItems()', $offlineDbContent);
        $this->assertStringContainsString('function updateSyncItemStatus(', $offlineDbContent);
        $this->assertStringContainsString('function saveSale(', $offlineDbContent);
        $this->assertStringContainsString('function saveCustomer(', $offlineDbContent);
        $this->assertStringContainsString('function cacheMedicines(', $offlineDbContent);
        $this->assertStringContainsString('function getSetting(', $offlineDbContent);
        $this->assertStringContainsString('function saveSetting(', $offlineDbContent);
    }

    /**
     * Test that the idb import is properly handled with fallbacks
     */
    public function test_idb_import_has_fallbacks()
    {
        $offlineDbContent = File::get(resource_path('js/offline-db.js'));
        
        // Check for fallback mechanisms
        $this->assertStringContainsString('try {', $offlineDbContent);
        $this->assertStringContainsString('catch (', $offlineDbContent);
        $this->assertStringContainsString('window.idb', $offlineDbContent);
    }

    /**
     * Test that the sync service properly integrates with the offline database
     */
    public function test_sync_service_integration()
    {
        $syncServiceContent = File::get(resource_path('js/sync-service.js'));
        
        // Check for integration with offline-db
        $this->assertStringContainsString('import * as OfflineDB from', $syncServiceContent);
        $this->assertStringContainsString('OfflineDB.initDB()', $syncServiceContent);
        $this->assertStringContainsString('OfflineDB.getPendingSyncItems()', $syncServiceContent);
        $this->assertStringContainsString('OfflineDB.updateSyncItemStatus(', $syncServiceContent);
    }

    /**
     * Test that the service worker properly handles sync events
     */
    public function test_service_worker_sync_handling()
    {
        $serviceWorkerContent = File::get(public_path('service-worker.js'));
        
        // Check for sync event handling
        $this->assertStringContainsString("self.addEventListener('sync'", $serviceWorkerContent);
        $this->assertStringContainsString("syncData()", $serviceWorkerContent);
        $this->assertStringContainsString("openOfflineDB()", $serviceWorkerContent);
    }

    /**
     * Test that the CDN fallback for idb is properly configured
     */
    public function test_cdn_fallback_configuration()
    {
        $appJsContent = File::get(resource_path('js/app.js'));
        $serviceWorkerContent = File::get(public_path('service-worker.js'));
        
        // Check for CDN fallback in app.js
        $this->assertStringContainsString('cdn.jsdelivr.net/npm/idb', $appJsContent);
        
        // Check that the CDN version is cached in the service worker
        $this->assertStringContainsString('cdn.jsdelivr.net/npm/idb', $serviceWorkerContent);
    }

    /**
     * Test that the Vite configuration properly handles the idb package
     */
    public function test_vite_configuration()
    {
        $viteConfigContent = File::get(base_path('vite.config.js'));
        
        // Check for idb configuration in Vite
        $this->assertStringContainsString("'idb'", $viteConfigContent);
        $this->assertStringContainsString("resolve", $viteConfigContent);
        $this->assertStringContainsString("optimizeDeps", $viteConfigContent);
    }
} 