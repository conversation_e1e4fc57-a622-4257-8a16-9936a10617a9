<?php

namespace Tests\Feature\PWA;

use Tests\TestCase;
use Illuminate\Support\Facades\File;

class ManifestTest extends TestCase
{
    public function test_manifest_file_exists_and_accessible()
    {
        $response = $this->get('/manifest.json');
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/json');
    }

    public function test_manifest_contains_required_fields()
    {
        $manifest = json_decode(File::get(public_path('manifest.json')), true);

        $this->assertArrayHasKey('name', $manifest);
        $this->assertArrayHasKey('short_name', $manifest);
        $this->assertArrayHasKey('start_url', $manifest);
        $this->assertArrayHasKey('display', $manifest);
        $this->assertArrayHasKey('background_color', $manifest);
        $this->assertArrayHasKey('theme_color', $manifest);
        $this->assertArray<PERSON>as<PERSON>ey('icons', $manifest);
    }

    public function test_manifest_icons_exist()
    {
        $manifest = json_decode(File::get(public_path('manifest.json')), true);
        
        foreach ($manifest['icons'] as $icon) {
            $iconPath = public_path(ltrim($icon['src'], '/'));
            $this->assertTrue(File::exists($iconPath), "Icon {$icon['src']} does not exist");
            
            // Verify image dimensions
            $imageSize = getimagesize($iconPath);
            $this->assertEquals($icon['sizes'], "{$imageSize[0]}x{$imageSize[1]}");
        }
    }

    public function test_service_worker_file_exists_and_accessible()
    {
        $response = $this->get('/service-worker.js');
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/javascript');
    }
} 