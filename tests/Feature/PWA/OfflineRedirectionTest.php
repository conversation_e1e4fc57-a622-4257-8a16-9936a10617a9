<?php

namespace Tests\Feature\PWA;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class OfflineRedirectionTest extends TestCase
{
    /**
     * Test that the fallback route redirects to the offline page when the X-Offline header is present.
     *
     * @return void
     */
    public function test_fallback_route_redirects_to_offline_page_with_header()
    {
        $response = $this->withHeaders([
            'X-Offline' => 'true',
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        ])->get('/non-existent-route-' . time());

        $response->assertStatus(200);
        $response->assertViewIs('offline');
    }

    /**
     * Test that the fallback route redirects to the offline page when the X-Offline-Check header is present.
     *
     * @return void
     */
    public function test_fallback_route_redirects_to_offline_page_with_check_header()
    {
        $response = $this->withHeaders([
            'X-Offline-Check' => 'true',
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        ])->get('/non-existent-route-' . time());

        $response->assertStatus(200);
        $response->assertViewIs('offline');
    }

    /**
     * Test that the fallback route redirects to the offline page when the offline_mode cookie is present.
     *
     * @return void
     */
    public function test_fallback_route_redirects_to_offline_page_with_cookie()
    {
        $response = $this->withCookie('offline_mode', 'true')
            ->withHeaders([
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            ])->get('/non-existent-route-' . time());

        $response->assertStatus(200);
        $response->assertViewIs('offline');
    }

    /**
     * Test that the fallback route returns a JSON response for AJAX requests.
     *
     * @return void
     */
    public function test_fallback_route_returns_json_for_ajax_requests()
    {
        $response = $this->withHeaders([
            'X-Requested-With' => 'XMLHttpRequest',
            'X-Offline' => 'true',
        ])->get('/non-existent-route-' . time());

        $response->assertStatus(503);
        $response->assertJson([
            'error' => 'offline',
            'message' => 'You are currently offline. Please check your connection and try again.'
        ]);
    }

    /**
     * Test that the fallback route returns a JSON response for API requests.
     *
     * @return void
     */
    public function test_fallback_route_returns_json_for_api_requests()
    {
        $response = $this->withHeaders([
            'X-Offline' => 'true',
        ])->get('/api/non-existent-route-' . time());

        $response->assertStatus(503);
        $response->assertJson([
            'error' => 'offline',
            'message' => 'You are currently offline. Please check your connection and try again.'
        ]);
    }

    /**
     * Test that the offline API check endpoint returns the correct response.
     *
     * @return void
     */
    public function test_offline_api_check_endpoint()
    {
        $response = $this->get('/api/offline/check');

        $response->assertStatus(200);
        $response->assertJson([
            'status' => 'offline_ready'
        ]);
    }

    /**
     * Test that the fallback route doesn't affect existing routes.
     *
     * @return void
     */
    public function test_fallback_route_doesnt_affect_existing_routes()
    {
        // Test the offline route directly
        $response = $this->get('/offline');

        $response->assertStatus(200);
        $response->assertViewIs('offline');

        // Test with offline header but to an existing route
        $response = $this->withHeaders([
            'X-Offline' => 'true',
        ])->get('/offline');

        $response->assertStatus(200);
        $response->assertViewIs('offline');
    }
} 