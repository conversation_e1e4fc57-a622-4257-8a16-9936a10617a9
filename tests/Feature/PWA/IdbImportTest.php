<?php

namespace Tests\Feature\PWA;

use Tests\TestCase;
use Illuminate\Support\Facades\File;

class IdbImportTest extends TestCase
{
    /**
     * Test that the idb package is properly listed in package.json
     */
    public function test_idb_package_in_package_json()
    {
        $packageJsonContent = File::get(base_path('package.json'));
        $packageJson = json_decode($packageJsonContent, true);
        
        $this->assertArrayHasKey('dependencies', $packageJson);
        $this->assertArrayHasKey('idb', $packageJson['dependencies']);
        $this->assertEquals('^7.1.1', $packageJson['dependencies']['idb']);
    }

    /**
     * Test that the offline-db.js file has proper import fallbacks
     */
    public function test_offline_db_import_fallbacks()
    {
        $offlineDbContent = File::get(resource_path('js/offline-db.js'));
        
        // Check for fallback mechanisms
        $this->assertStringContainsString('try {', $offlineDbContent);
        $this->assertStringContainsString('catch (', $offlineDbContent);
        $this->assertStringContainsString('openDB = require', $offlineDbContent);
        $this->assertStringContainsString('window.idb', $offlineDbContent);
    }

    /**
     * Test that the app.js file has proper dynamic import with CDN fallback
     */
    public function test_app_js_dynamic_import()
    {
        $appJsContent = File::get(resource_path('js/app.js'));
        
        // Check for dynamic import
        $this->assertStringContainsString('import(\'idb\')', $appJsContent);
        $this->assertStringContainsString('window.idb = module', $appJsContent);
        
        // Check for CDN fallback
        $this->assertStringContainsString('script.src = \'https://cdn.jsdelivr.net/npm/idb@7.1.1/build/umd.js\'', $appJsContent);
        $this->assertStringContainsString('document.head.appendChild(script)', $appJsContent);
    }

    /**
     * Test that the service worker caches the CDN version
     */
    public function test_service_worker_cdn_caching()
    {
        $serviceWorkerContent = File::get(public_path('service-worker.js'));
        
        // Check for CDN caching
        $this->assertStringContainsString('cdn.jsdelivr.net/npm/idb', $serviceWorkerContent);
    }

    /**
     * Test that the Vite configuration properly handles the idb package
     */
    public function test_vite_configuration()
    {
        $viteConfigContent = File::get(base_path('vite.config.js'));
        
        // Check for idb configuration in Vite
        $this->assertStringContainsString('resolve: {', $viteConfigContent);
        $this->assertStringContainsString('alias: {', $viteConfigContent);
        $this->assertStringContainsString('\'idb\':', $viteConfigContent);
        $this->assertStringContainsString('optimizeDeps: {', $viteConfigContent);
        $this->assertStringContainsString('include: [', $viteConfigContent);
    }

    /**
     * Test that the offline modules are properly imported after idb setup
     */
    public function test_offline_modules_import_sequence()
    {
        $appJsContent = File::get(resource_path('js/app.js'));
        
        // Check for proper import sequence
        $this->assertStringContainsString('const importOfflineModules = async () => {', $appJsContent);
        $this->assertStringContainsString('if (idbModule) await idbModule', $appJsContent);
        $this->assertStringContainsString('const OfflineDB = await import', $appJsContent);
        $this->assertStringContainsString('./offline-db', $appJsContent);
    }

    /**
     * Test that the global exports are properly set up
     */
    public function test_global_exports()
    {
        $appJsContent = File::get(resource_path('js/app.js'));
        
        // Check for global exports
        $this->assertStringContainsString('window.OfflineDB = OfflineDB', $appJsContent);
        $this->assertStringContainsString('window.initSyncService', $appJsContent);
        $this->assertStringContainsString('window.forceSync', $appJsContent);
        $this->assertStringContainsString('window.getSyncStatus', $appJsContent);
    }
} 