<?php

namespace Tests\Feature\PWA;

use Tests\TestCase;
use Illuminate\Support\Facades\File;

class ServiceWorkerTest extends TestCase
{
    public function test_service_worker_contains_required_event_listeners()
    {
        $serviceWorkerContent = File::get(public_path('service-worker.js'));

        // Check for required event listeners
        $this->assertStringContainsString("self.addEventListener('install'", $serviceWorkerContent);
        $this->assertStringContainsString("self.addEventListener('activate'", $serviceWorkerContent);
        $this->assertStringContainsString("self.addEventListener('fetch'", $serviceWorkerContent);
        $this->assertStringContainsString("self.addEventListener('push'", $serviceWorkerContent);
        $this->assertStringContainsString("self.addEventListener('sync'", $serviceWorkerContent);
    }

    public function test_offline_page_exists_and_accessible()
    {
        $response = $this->get('/offline');
        $response->assertStatus(200);
        $response->assertViewIs('offline');
    }

    public function test_cache_configuration()
    {
        $serviceWorkerContent = File::get(public_path('service-worker.js'));
        
        // Check cache name is defined
        $this->assertStringContainsString("const CACHE_NAME = 'pharmadesk-v1.2.0'", $serviceWorkerContent);
        
        // Check required assets are cached
        $this->assertStringContainsString("'/manifest.json'", $serviceWorkerContent);
        $this->assertStringContainsString("'/icons/icon-192x192.png'", $serviceWorkerContent);
        $this->assertStringContainsString("'/build/assets/app-", $serviceWorkerContent);
        
        // Check for idb library caching
        $this->assertStringContainsString("cdn.jsdelivr.net/npm/idb", $serviceWorkerContent);
    }

    public function test_caching_strategies()
    {
        $serviceWorkerContent = File::get(public_path('service-worker.js'));
        
        // Check for navigation request handling
        $this->assertStringContainsString("if (request.mode === 'navigate')", $serviceWorkerContent);
        $this->assertStringContainsString("if (request.method === 'GET')", $serviceWorkerContent);
        $this->assertStringContainsString("cache.put(request, networkResponse.clone())", $serviceWorkerContent);
    }

    public function test_notification_configuration()
    {
        $serviceWorkerContent = File::get(public_path('service-worker.js'));
        
        // Check notification setup
        $this->assertStringContainsString("self.addEventListener('push'", $serviceWorkerContent);
        $this->assertStringContainsString("self.addEventListener('notificationclick'", $serviceWorkerContent);
        $this->assertStringContainsString("showNotification('PharmaDesk Notification'", $serviceWorkerContent);
    }
    
    public function test_sync_functionality()
    {
        $serviceWorkerContent = File::get(public_path('service-worker.js'));
        
        // Check sync functionality
        $this->assertStringContainsString("self.addEventListener('sync'", $serviceWorkerContent);
        $this->assertStringContainsString("if (event.tag === 'sync-data')", $serviceWorkerContent);
        $this->assertStringContainsString("syncData()", $serviceWorkerContent);
    }
    
    public function test_indexeddb_integration()
    {
        $serviceWorkerContent = File::get(public_path('service-worker.js'));
        
        // Check IndexedDB integration
        $this->assertStringContainsString("openOfflineDB()", $serviceWorkerContent);
        $this->assertStringContainsString("indexedDB.open('pharmadesk-offline-db'", $serviceWorkerContent);
        $this->assertStringContainsString("tx.store.index('status').getAll('pending')", $serviceWorkerContent);
    }
    
    public function test_sync_notification()
    {
        $serviceWorkerContent = File::get(public_path('service-worker.js'));
        
        // Check sync notification
        $this->assertStringContainsString("type: 'sync-started'", $serviceWorkerContent);
        $this->assertStringContainsString("notifyClients('sync-completed'", $serviceWorkerContent);
        $this->assertStringContainsString("notifyClients('sync-error'", $serviceWorkerContent);
    }
    
    public function test_entity_sync_handlers()
    {
        $serviceWorkerContent = File::get(public_path('service-worker.js'));
        
        // Check entity sync handlers
        $this->assertStringContainsString("syncSaleToServer", $serviceWorkerContent);
        $this->assertStringContainsString("syncCustomerToServer", $serviceWorkerContent);
        $this->assertStringContainsString("syncInventoryToServer", $serviceWorkerContent);
    }
} 