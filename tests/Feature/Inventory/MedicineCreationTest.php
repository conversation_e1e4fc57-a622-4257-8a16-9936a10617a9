<?php

namespace Tests\Feature\Inventory;

use Tests\TestCase;
use App\Models\Users\User;
use App\Models\Inventory\Medicine;
use App\Models\Inventory\Supplier;
use App\Models\Inventory\Purchase;
use App\Models\Inventory\PurchaseItem;
use App\Models\Inventory\Inventory;
use App\Models\Inventory\Manufacturer;
use App\Models\Inventory\Category;
use App\Models\Inventory\UnitType;
use App\Models\Inventory\Location;
use App\Services\Inventory\MedicineService;
use App\Services\Inventory\MedicinePurchaseService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use PHPUnit\Framework\Attributes\Test;
use PHPUnit\Framework\Attributes\DataProvider;

class MedicineCreationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected $manufacturer;
    protected $category;
    protected $unitType;
    protected $supplier;
    protected $location;
    protected $medicineService;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        /** @var User $user */
        $user = User::factory()->create();
        $this->user = $user;
        $this->actingAs($this->user);

        // Create test manufacturer
        $this->manufacturer = Manufacturer::factory()->create([
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        // Create test category
        $this->category = Category::factory()->create([
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        // Create test unit type
        $this->unitType = UnitType::factory()->create([
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        // Create test supplier
        $this->supplier = Supplier::factory()->create([
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        // Create test location
        $this->location = Location::factory()->create([
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        // Initialize service
        $this->medicineService = new MedicineService(new MedicinePurchaseService());
    }

    #[Test]
    public function test_creates_medicine_with_consistent_data_across_tables()
    {
        $data = [
            'name' => 'Test Medicine',
            'generic_name' => 'Test Generic',
            'manufacturer_id' => $this->manufacturer->id,
            'category_id' => $this->category->id,
            'unit_type_id' => $this->unitType->id,
            'supplier_id' => $this->supplier->id,
            'location_id' => $this->location->id,
            'minimum_stock' => 10,
            'maximum_stock' => 100,
            'controlled_substance' => false,
            'prescription_required' => false,
            'supplier_price_unit' => 2.50,
            'retail_price_unit' => 3.00,
            'strips_per_box' => 10,
            'pieces_per_strip' => 10,
            'box_quantity' => 10,
            'initial_quantity' => 1000,
            'batch_number' => 'BATCH-001',
            'expiry_date' => now()->addYear(),
            'purchase_number' => 'PO-INIT-000001',
            'purchase_date' => now(),
            'unit_cost' => 2.50,
            'tax' => 10,
            'discount' => 5,
            'shipping_cost' => 50,
            'total_amount' => 2625.00,
            'purchase_notes' => 'Test purchase notes',
            'enabled_units' => ['unit'],
            'enabled_retail_units' => ['unit'],
            'movement_type' => 'addition',
        ];

        $medicine = $this->medicineService->createMedicine($data);

        // Assert medicine was created with correct data
        $this->assertDatabaseHas('medicines', [
            'id' => $medicine->id,
            'name' => 'Test Medicine',
            'generic_name' => 'Test Generic',
            'manufacturer_id' => $this->manufacturer->id,
            'category_id' => $this->category->id,
            'unit_type_id' => $this->unitType->id,
            'supplier_price_unit' => 2.50,
            'retail_price_unit' => 3.00,
        ]);

        // Assert supplier was attached with correct price
        $this->assertDatabaseHas('medicine_supplier', [
            'medicine_id' => $medicine->id,
            'supplier_id' => $this->supplier->id,
            'price' => 2.50,
            'is_default' => true,
        ]);

        // Assert inventory was created with correct data
        $this->assertDatabaseHas('inventories', [
            'medicine_id' => $medicine->id,
            'location_id' => $this->location->id,
            'batch_number' => 'BATCH-001',
            'quantity' => 1000,
            'unit_price' => 2.50,
        ]);

        // Assert purchase was created with correct data
        $purchase = Purchase::where('purchase_number', 'PO-INIT-000001')->first();
        $this->assertNotNull($purchase);
        $this->assertEquals(2500.00, $purchase->total_amount); // Base amount
        $this->assertEquals(10, $purchase->tax_percentage);
        $this->assertEquals(250.00, $purchase->tax_amount);
        $this->assertEquals(5, $purchase->discount_percentage);
        $this->assertEquals(125.00, $purchase->discount_amount);
        $this->assertEquals(50, $purchase->shipping_cost);
        $this->assertEquals(2675.00, $purchase->final_amount);

        // Assert purchase item was created with correct data
        $this->assertDatabaseHas('purchase_items', [
            'purchase_id' => $purchase->id,
            'medicine_id' => $medicine->id,
            'batch_number' => 'BATCH-001',
            'quantity' => 1000,
            'unit_price' => 2.50,
            'total_amount' => 2500.00,
        ]);
    }

    #[Test]
    public function test_rollbacks_transaction_on_invalid_manufacturer()
    {
        $data = [
            'name' => 'Test Medicine',
            'generic_name' => 'Test Generic',
            'manufacturer_id' => 99999, // Invalid manufacturer ID
            'category_id' => $this->category->id,
            'unit_type_id' => $this->unitType->id,
            'supplier_id' => $this->supplier->id,
            'location_id' => $this->location->id,
            'minimum_stock' => 10,
            'maximum_stock' => 100,
            'controlled_substance' => false,
            'prescription_required' => false,
            'supplier_price_unit' => 2.50,
            'retail_price_unit' => 3.00,
            'strips_per_box' => 10,
            'pieces_per_strip' => 10,
            'box_quantity' => 10,
            'initial_quantity' => 1000,
            'batch_number' => 'BATCH-002',
            'expiry_date' => now()->addYear(),
            'purchase_number' => 'PO-INIT-000002',
            'purchase_date' => now(),
            'unit_cost' => 2.50,
            'tax' => 10,
            'discount' => 5,
            'shipping_cost' => 50,
            'total_amount' => 2625.00,
            'purchase_notes' => 'Test purchase notes',
            'enabled_units' => ['unit'],
            'enabled_retail_units' => ['unit'],
            'movement_type' => 'addition',
        ];

        try {
            $this->medicineService->createMedicine($data);
            $this->fail('Expected exception was not thrown');
        } catch (\Exception $e) {
            // Assert no records were created
            $this->assertDatabaseMissing('medicines', ['name' => 'Test Medicine']);
            $this->assertDatabaseMissing('inventories', ['batch_number' => 'BATCH-002']);
            $this->assertDatabaseMissing('purchases', ['purchase_number' => 'PO-INIT-000002']);
            $this->assertDatabaseMissing('purchase_items', ['batch_number' => 'BATCH-002']);
        }
    }

    #[Test]
    public function test_rollbacks_transaction_on_invalid_location()
    {
        $data = [
            'name' => 'Test Medicine',
            'generic_name' => 'Test Generic',
            'manufacturer_id' => $this->manufacturer->id,
            'category_id' => $this->category->id,
            'unit_type_id' => $this->unitType->id,
            'supplier_id' => $this->supplier->id,
            'location_id' => 99999, // Invalid location ID
            'minimum_stock' => 10,
            'maximum_stock' => 100,
            'controlled_substance' => false,
            'prescription_required' => false,
            'supplier_price_unit' => 2.50,
            'retail_price_unit' => 3.00,
            'strips_per_box' => 10,
            'pieces_per_strip' => 10,
            'box_quantity' => 10,
            'initial_quantity' => 1000,
            'batch_number' => 'BATCH-003',
            'expiry_date' => now()->addYear(),
            'purchase_number' => 'PO-INIT-000003',
            'purchase_date' => now(),
            'unit_cost' => 2.50,
            'tax' => 10,
            'discount' => 5,
            'shipping_cost' => 50,
            'total_amount' => 2625.00,
            'purchase_notes' => 'Test purchase notes',
            'enabled_units' => ['unit'],
            'enabled_retail_units' => ['unit'],
            'movement_type' => 'addition',
        ];

        try {
            $this->medicineService->createMedicine($data);
            $this->fail('Expected exception was not thrown');
        } catch (\Exception $e) {
            // Assert no records were created
            $this->assertDatabaseMissing('medicines', ['name' => 'Test Medicine']);
            $this->assertDatabaseMissing('inventories', ['batch_number' => 'BATCH-003']);
            $this->assertDatabaseMissing('purchases', ['purchase_number' => 'PO-INIT-000003']);
            $this->assertDatabaseMissing('purchase_items', ['batch_number' => 'BATCH-003']);
        }
    }

    #[Test]
    public function test_handles_duplicate_batch_numbers()
    {
        // Create first medicine with batch number
        $data1 = [
            'name' => 'Test Medicine 1',
            'generic_name' => 'Test Generic 1',
            'manufacturer_id' => $this->manufacturer->id,
            'category_id' => $this->category->id,
            'unit_type_id' => $this->unitType->id,
            'supplier_id' => $this->supplier->id,
            'location_id' => $this->location->id,
            'minimum_stock' => 10,
            'maximum_stock' => 100,
            'controlled_substance' => false,
            'prescription_required' => false,
            'supplier_price_unit' => 2.50,
            'retail_price_unit' => 3.00,
            'strips_per_box' => 10,
            'pieces_per_strip' => 10,
            'box_quantity' => 10,
            'initial_quantity' => 1000,
            'batch_number' => 'BATCH-004',
            'expiry_date' => now()->addYear(),
            'purchase_number' => 'PO-INIT-000004',
            'purchase_date' => now(),
            'unit_cost' => 2.50,
            'tax' => 10,
            'discount' => 5,
            'shipping_cost' => 50,
            'total_amount' => 2625.00,
            'purchase_notes' => 'Test purchase notes',
            'enabled_units' => ['unit'],
            'enabled_retail_units' => ['unit'],
            'movement_type' => 'addition',
        ];

        $this->medicineService->createMedicine($data1);

        // Try to create second medicine with same batch number
        $data2 = $data1;
        $data2['name'] = 'Test Medicine 2';
        $data2['generic_name'] = 'Test Generic 2';
        $data2['purchase_number'] = 'PO-INIT-000005';

        try {
            $this->medicineService->createMedicine($data2);
            $this->fail('Expected exception was not thrown');
        } catch (\Exception $e) {
            // Assert only first medicine was created
            $this->assertDatabaseHas('medicines', ['name' => 'Test Medicine 1']);
            $this->assertDatabaseMissing('medicines', ['name' => 'Test Medicine 2']);
            $this->assertEquals(1, Inventory::where('batch_number', 'BATCH-004')->count());
            $this->assertEquals(1, PurchaseItem::where('batch_number', 'BATCH-004')->count());
        }
    }

    #[Test]
    public function test_verifies_price_consistency()
    {
        $data = [
            'name' => 'Test Medicine',
            'generic_name' => 'Test Generic',
            'manufacturer_id' => $this->manufacturer->id,
            'category_id' => $this->category->id,
            'unit_type_id' => $this->unitType->id,
            'supplier_id' => $this->supplier->id,
            'location_id' => $this->location->id,
            'minimum_stock' => 10,
            'maximum_stock' => 100,
            'controlled_substance' => false,
            'prescription_required' => false,
            'supplier_price_unit' => 2.50,
            'retail_price_unit' => 3.00,
            'strips_per_box' => 10,
            'pieces_per_strip' => 10,
            'box_quantity' => 10,
            'initial_quantity' => 1000,
            'batch_number' => 'BATCH-005',
            'expiry_date' => now()->addYear(),
            'purchase_number' => 'PO-INIT-000006',
            'purchase_date' => now(),
            'unit_cost' => 2.50, // Matches supplier_price_unit
            'tax' => 10,
            'discount' => 5,
            'shipping_cost' => 50,
            'total_amount' => 2625.00,
            'purchase_notes' => 'Test purchase notes',
            'enabled_units' => ['unit'],
            'enabled_retail_units' => ['unit'],
            'movement_type' => 'addition',
        ];

        $medicine = $this->medicineService->createMedicine($data);

        // Verify price consistency across all tables
        $this->assertEquals(2.50, $medicine->supplier_price_unit);
        
        $supplierPrice = DB::table('medicine_supplier')
            ->where('medicine_id', $medicine->id)
            ->where('supplier_id', $this->supplier->id)
            ->value('price');
        $this->assertEquals(2.50, $supplierPrice);

        $inventory = Inventory::where('medicine_id', $medicine->id)->first();
        $this->assertEquals(2.50, $inventory->unit_price);

        $purchaseItem = PurchaseItem::where('medicine_id', $medicine->id)->first();
        $this->assertEquals(2.50, $purchaseItem->unit_price);
    }

    public static function calculationDataProvider(): array
    {
        return [
            'basic_no_tax_no_discount' => [
                [
                    'quantity' => 100,
                    'unit_cost' => 10.00,
                    'tax' => 0,
                    'discount' => 0,
                    'shipping_cost' => 0,
                ],
                [
                    'subtotal' => 1000.00,
                    'tax_amount' => 0.00,
                    'discount_amount' => 0.00,
                    'final_amount' => 1000.00,
                ]
            ],
            'with_tax_only' => [
                [
                    'quantity' => 100,
                    'unit_cost' => 10.00,
                    'tax' => 10,
                    'discount' => 0,
                    'shipping_cost' => 0,
                ],
                [
                    'subtotal' => 1000.00,
                    'tax_amount' => 100.00,
                    'discount_amount' => 0.00,
                    'final_amount' => 1100.00,
                ]
            ],
            'with_discount_only' => [
                [
                    'quantity' => 100,
                    'unit_cost' => 10.00,
                    'tax' => 0,
                    'discount' => 5,
                    'shipping_cost' => 0,
                ],
                [
                    'subtotal' => 1000.00,
                    'tax_amount' => 0.00,
                    'discount_amount' => 50.00,
                    'final_amount' => 950.00,
                ]
            ],
            'with_tax_and_discount' => [
                [
                    'quantity' => 100,
                    'unit_cost' => 10.00,
                    'tax' => 10,
                    'discount' => 5,
                    'shipping_cost' => 0,
                ],
                [
                    'subtotal' => 1000.00,
                    'tax_amount' => 100.00,
                    'discount_amount' => 50.00,
                    'final_amount' => 1050.00,
                ]
            ],
            'with_shipping_cost' => [
                [
                    'quantity' => 100,
                    'unit_cost' => 10.00,
                    'tax' => 10,
                    'discount' => 5,
                    'shipping_cost' => 25.00,
                ],
                [
                    'subtotal' => 1000.00,
                    'tax_amount' => 100.00,
                    'discount_amount' => 50.00,
                    'final_amount' => 1075.00,
                ]
            ],
            'fractional_amounts' => [
                [
                    'quantity' => 33,
                    'unit_cost' => 12.99,
                    'tax' => 8.25,
                    'discount' => 3.5,
                    'shipping_cost' => 15.75,
                ],
                [
                    'subtotal' => 428.67,
                    'tax_amount' => 35.37,
                    'discount_amount' => 15.00,
                    'final_amount' => 464.79,
                ]
            ],
            'high_precision' => [
                [
                    'quantity' => 1000,
                    'unit_cost' => 0.99,
                    'tax' => 7.5,
                    'discount' => 2.25,
                    'shipping_cost' => 9.99,
                ],
                [
                    'subtotal' => 990.00,
                    'tax_amount' => 74.25,
                    'discount_amount' => 22.28,
                    'final_amount' => 1051.96,
                ]
            ],
        ];
    }

    #[Test]
    #[DataProvider('calculationDataProvider')]
    public function test_verifies_calculations(array $input, array $expected)
    {
        $baseData = [
            'name' => 'Test Medicine',
            'generic_name' => 'Test Generic',
            'manufacturer_id' => $this->manufacturer->id,
            'category_id' => $this->category->id,
            'unit_type_id' => $this->unitType->id,
            'supplier_id' => $this->supplier->id,
            'location_id' => $this->location->id,
            'minimum_stock' => 10,
            'maximum_stock' => 100,
            'controlled_substance' => false,
            'prescription_required' => false,
            'supplier_price_unit' => $input['unit_cost'],
            'retail_price_unit' => $input['unit_cost'] * 1.25, // 25% markup
            'strips_per_box' => 10,
            'pieces_per_strip' => 10,
            'box_quantity' => 10,
            'initial_quantity' => $input['quantity'],
            'batch_number' => 'BATCH-CALC-' . uniqid(),
            'expiry_date' => now()->addYear(),
            'purchase_number' => 'PO-CALC-' . uniqid(),
            'purchase_date' => now(),
            'unit_cost' => $input['unit_cost'],
            'tax' => $input['tax'],
            'discount' => $input['discount'],
            'shipping_cost' => $input['shipping_cost'],
            'total_amount' => $expected['final_amount'],
            'purchase_notes' => 'Calculation test',
            'enabled_units' => ['unit'],
            'enabled_retail_units' => ['unit'],
            'movement_type' => 'addition',
        ];

        $medicine = $this->medicineService->createMedicine($baseData);

        // Get the created purchase
        $purchase = Purchase::where('purchase_number', $baseData['purchase_number'])->first();
        $this->assertNotNull($purchase);

        // Verify all amounts with 2 decimal precision
        $this->assertEquals(
            round($expected['subtotal'], 2),
            round($purchase->total_amount, 2),
            "Subtotal calculation mismatch"
        );
        
        $this->assertEquals(
            round($expected['tax_amount'], 2),
            round($purchase->tax_amount, 2),
            "Tax amount calculation mismatch"
        );
        
        $this->assertEquals(
            round($expected['discount_amount'], 2),
            round($purchase->discount_amount, 2),
            "Discount amount calculation mismatch"
        );
        
        $this->assertEquals(
            round($expected['final_amount'], 2),
            round($purchase->final_amount, 2),
            "Final amount calculation mismatch"
        );

        // Verify purchase item calculations
        $purchaseItem = PurchaseItem::where('purchase_id', $purchase->id)->first();
        $this->assertNotNull($purchaseItem);
        
        $this->assertEquals(
            round($expected['subtotal'], 2),
            round($purchaseItem->total_amount, 2),
            "Purchase item total amount mismatch"
        );
        
        $this->assertEquals(
            round($expected['tax_amount'], 2),
            round($purchaseItem->tax_amount, 2),
            "Purchase item tax amount mismatch"
        );
        
        $this->assertEquals(
            round($expected['discount_amount'], 2),
            round($purchaseItem->discount_amount, 2),
            "Purchase item discount amount mismatch"
        );

        // Verify inventory unit price
        $inventory = Inventory::where('medicine_id', $medicine->id)
            ->where('batch_number', $baseData['batch_number'])
            ->first();
        $this->assertNotNull($inventory);
        $this->assertEquals(
            round($input['unit_cost'], 2),
            round($inventory->unit_price, 2),
            "Inventory unit price mismatch"
        );
    }

    #[Test]
    public function test_validates_maximum_tax_and_discount()
    {
        $data = [
            'name' => 'Test Medicine',
            'generic_name' => 'Test Generic',
            'manufacturer_id' => $this->manufacturer->id,
            'category_id' => $this->category->id,
            'unit_type_id' => $this->unitType->id,
            'supplier_id' => $this->supplier->id,
            'location_id' => $this->location->id,
            'minimum_stock' => 10,
            'maximum_stock' => 100,
            'controlled_substance' => false,
            'prescription_required' => false,
            'supplier_price_unit' => 10.00,
            'retail_price_unit' => 12.50,
            'strips_per_box' => 10,
            'pieces_per_strip' => 10,
            'box_quantity' => 10,
            'initial_quantity' => 100,
            'batch_number' => 'BATCH-MAX-' . uniqid(),
            'expiry_date' => now()->addYear(),
            'purchase_number' => 'PO-MAX-' . uniqid(),
            'purchase_date' => now(),
            'unit_cost' => 10.00,
            'tax' => 101, // Invalid tax percentage
            'discount' => 101, // Invalid discount percentage
            'shipping_cost' => 50,
            'total_amount' => 1000.00,
            'purchase_notes' => 'Maximum validation test',
            'enabled_units' => ['unit'],
            'enabled_retail_units' => ['unit'],
            'movement_type' => 'addition',
        ];

        try {
            $this->medicineService->createMedicine($data);
            $this->fail('Expected validation exception was not thrown');
        } catch (\Exception $e) {
            $this->assertStringContainsString('percentage', strtolower($e->getMessage()));
            
            // Verify no records were created
            $this->assertDatabaseMissing('medicines', ['name' => 'Test Medicine']);
            $this->assertDatabaseMissing('inventories', ['batch_number' => $data['batch_number']]);
            $this->assertDatabaseMissing('purchases', ['purchase_number' => $data['purchase_number']]);
        }
    }
} 