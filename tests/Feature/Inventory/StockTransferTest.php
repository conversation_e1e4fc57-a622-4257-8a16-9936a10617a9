<?php

namespace Tests\Feature\Inventory;

use Tests\TestCase;
use App\Models\Users\User;
use App\Models\Inventory\Medicine;
use App\Models\Inventory\Location;
use App\Models\Inventory\Inventory;
use App\Models\Inventory\StockMovement;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Carbon\Carbon;
use PHPUnit\Framework\Attributes\Test;
use App\Models\Inventory\Category;

class StockTransferTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected Location $sourceLocation;
    protected Location $destinationLocation;
    protected Medicine $medicine;
    protected $inventory;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create();
        
        // Create Sanctum token for API authentication
        $token = $this->user->createToken('test-token');
        $this->withHeaders([
            'Authorization' => 'Bearer ' . $token->plainTextToken,
            'Accept' => 'application/json'
        ]);
        
        // Create test locations
        $this->sourceLocation = Location::create([
            'name' => 'Source Warehouse',
            'type' => Location::TYPE_WAREHOUSE,
            'status' => 'active',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id
        ]);

        $this->destinationLocation = Location::create([
            'name' => 'Destination Store',
            'type' => Location::TYPE_STORE,
            'status' => 'active',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id
        ]);

        // Create test category
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id
        ]);

        // Create test unit type
        $unitType = \App\Models\Inventory\UnitType::factory()->create([
            'name' => 'Test Unit',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id
        ]);

        // Create test medicine
        $this->medicine = Medicine::create([
            'name' => 'Test Medicine',
            'generic_name' => 'Test Generic',
            'status' => 'active',
            'minimum_stock' => 10,
            'maximum_stock' => 100,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
            'category_id' => $category->id,
            'unit_type_id' => $unitType->id
        ]);

        // Create inventory with stock
        $this->inventory = Inventory::create([
            'medicine_id' => $this->medicine->id,
            'location_id' => $this->sourceLocation->id,
            'batch_number' => 'BATCH001',
            'quantity' => 50,
            'unit_price' => 10.00,
            'expiry_date' => Carbon::now()->addMonths(6),
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id
        ]);
    }

    #[Test]
    public function it_can_get_medicines_by_location()
    {
        $response = $this->getJson("/api/v1/inventory/locations/{$this->sourceLocation->id}/medicines");

        $response->assertStatus(200)
                ->assertJsonStructure(['data' => [['id', 'name', 'total_quantity']]])
                ->assertJsonCount(1, 'data')
                ->assertJson([
                    'data' => [
                        [
                            'id' => $this->medicine->id,
                            'name' => 'Test Medicine'
                        ]
                    ]
                ]);
    }

    #[Test]
    public function it_can_get_batches_by_location_and_medicine()
    {
        $response = $this->getJson("/api/v1/inventory/locations/{$this->sourceLocation->id}/medicines/{$this->medicine->id}/batches");

        $response->assertStatus(200)
                ->assertJsonStructure(['data' => [['id', 'batch_number', 'quantity', 'expiry_date']]])
                ->assertJsonCount(1, 'data')
                ->assertJson([
                    'data' => [
                        [
                            'batch_number' => 'BATCH001',
                            'quantity' => 50
                        ]
                    ]
                ]);
    }

    #[Test]
    public function it_can_transfer_stock_between_locations()
    {
        $response = $this->postJson('/api/v1/inventory/transfer', [
            'source_location' => $this->sourceLocation->id,
            'destination_location' => $this->destinationLocation->id,
            'medicine' => $this->medicine->id,
            'batch_number' => 'BATCH001',
            'quantity' => 20,
            'transfer_date' => Carbon::now()->format('Y-m-d'),
            'notes' => 'Test transfer'
        ]);

        $response->assertStatus(200)
                ->assertJson(['message' => 'Stock transfer has been recorded successfully']);

        // Check source location quantity is reduced
        $this->assertDatabaseHas('inventories', [
            'location_id' => $this->sourceLocation->id,
            'medicine_id' => $this->medicine->id,
            'batch_number' => 'BATCH001',
            'quantity' => 30
        ]);

        // Check destination location quantity is increased
        $this->assertDatabaseHas('inventories', [
            'location_id' => $this->destinationLocation->id,
            'medicine_id' => $this->medicine->id,
            'batch_number' => 'BATCH001',
            'quantity' => 20
        ]);
    }

    #[Test]
    public function it_cannot_transfer_expired_stock()
    {
        // Create expired inventory
        $expiredInventory = Inventory::create([
            'medicine_id' => $this->medicine->id,
            'location_id' => $this->sourceLocation->id,
            'batch_number' => 'EXPIRED001',
            'quantity' => 50,
            'unit_price' => 10.00,
            'expiry_date' => Carbon::now()->subMonth(),
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id
        ]);

        $response = $this->postJson('/api/v1/inventory/transfer', [
            'source_location' => $this->sourceLocation->id,
            'destination_location' => $this->destinationLocation->id,
            'medicine' => $this->medicine->id,
            'batch_number' => 'EXPIRED001',
            'quantity' => 20,
            'transfer_date' => Carbon::now()->format('Y-m-d'),
            'notes' => 'Test expired transfer'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['batch_number']);
    }

    #[Test]
    public function it_cannot_transfer_more_than_available_quantity()
    {
        $response = $this->postJson('/api/v1/inventory/transfer', [
            'source_location' => $this->sourceLocation->id,
            'destination_location' => $this->destinationLocation->id,
            'medicine' => $this->medicine->id,
            'batch_number' => 'BATCH001',
            'quantity' => 100, // More than available (50)
            'transfer_date' => Carbon::now()->format('Y-m-d'),
            'notes' => 'Test over quantity'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['batch_number']);
    }

    #[Test]
    public function it_cannot_transfer_to_same_location()
    {
        $response = $this->postJson('/api/v1/inventory/transfer', [
            'source_location' => $this->sourceLocation->id,
            'destination_location' => $this->sourceLocation->id,
            'medicine' => $this->medicine->id,
            'batch_number' => 'BATCH001',
            'quantity' => 20,
            'transfer_date' => Carbon::now()->format('Y-m-d'),
            'notes' => 'Test same location'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['destination_location']);
    }
}
