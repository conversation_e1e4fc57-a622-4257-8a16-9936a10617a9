<?php

namespace Tests\Feature\Inventory;

use Tests\TestCase;
use App\Models\Inventory\Medicine;
use App\Models\Inventory\Purchase;
use App\Models\Inventory\PurchaseItem;
use App\Models\Inventory\Inventory;
use App\Models\Inventory\Supplier;
use App\Models\Inventory\Category;
use App\Models\Inventory\Manufacturer;
use App\Models\Inventory\UnitType;
use App\Models\Inventory\Location;
use App\Models\Users\User;
use App\Services\Inventory\InventoryService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class MedicineArchitectureTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $supplier;
    protected $category;
    protected $manufacturer;
    protected $unitType;
    protected $location;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create();
        $this->actingAs($this->user);

        // Create supporting data
        $this->supplier = Supplier::factory()->create();
        $this->category = Category::factory()->create();
        $this->manufacturer = Manufacturer::factory()->create();
        $this->unitType = UnitType::factory()->create();
        $this->location = Location::factory()->create();
    }

    /** @test */
    public function medicine_can_be_created_without_inventory()
    {
        $medicineData = [
            'name' => 'Paracetamol',
            'generic_name' => 'Acetaminophen',
            'dosage' => '500mg',
            'description' => 'Pain reliever and fever reducer',
            'manufacturer_id' => $this->manufacturer->id,
            'category_id' => $this->category->id,
            'unit_type_id' => $this->unitType->id,
            'minimum_stock' => 100,
            'maximum_stock' => 1000,
            'controlled_substance' => false,
            'prescription_required' => false,
            'supplier_price_unit' => 5.00,
            'retail_price_unit' => 8.00,
            'status' => 'active',
            'strips_per_box' => 10,
            'pieces_per_strip' => 10,
            'box_quantity' => 1,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ];

        $medicine = Medicine::create($medicineData);

        $this->assertInstanceOf(Medicine::class, $medicine);
        $this->assertEquals('Paracetamol', $medicine->name);
        $this->assertEquals(0, $medicine->getCurrentStock());
        $this->assertTrue($medicine->needsReorder());
    }

    /** @test */
    public function medicine_can_have_suppliers()
    {
        $medicine = Medicine::factory()->create();
        
        $medicine->suppliers()->attach($this->supplier->id, [
            'price' => 4.50,
            'is_default' => true,
            'created_by' => $this->user->id,
        ]);

        $this->assertTrue($medicine->suppliers->contains($this->supplier));
        $this->assertEquals(4.50, $medicine->suppliers->first()->pivot->price);
        $this->assertTrue($medicine->suppliers->first()->pivot->is_default);
    }

    /** @test */
    public function purchase_order_workflow_creates_inventory()
    {
        $medicine = Medicine::factory()->create([
            'minimum_stock' => 100,
            'supplier_price_unit' => 5.00,
        ]);

        // Create purchase order
        $purchase = Purchase::create([
            'purchase_number' => 'PO-2025-001',
            'supplier_id' => $this->supplier->id,
            'status' => 'draft',
            'order_date' => now(),
            'expected_date' => now()->addDays(7),
            'created_by' => $this->user->id,
        ]);

        // Add purchase item
        $purchaseItem = PurchaseItem::create([
            'purchase_id' => $purchase->id,
            'medicine_id' => $medicine->id,
            'quantity_ordered' => 200,
            'quantity_received' => 0,
            'unit_price' => 5.00,
            'total_amount' => 1000.00,
            'created_by' => $this->user->id,
        ]);

        // Verify no inventory exists yet
        $this->assertEquals(0, $medicine->getCurrentStock());

        // Receive the goods
        $purchaseItem->update([
            'quantity_received' => 200,
            'batch_number' => 'BATCH-001',
            'expiry_date' => now()->addYears(2),
            'manufacture_date' => now()->subMonths(1),
            'location_id' => $this->location->id,
            'received_at' => now(),
        ]);

        // Create inventory from received purchase item
        $inventoryService = new InventoryService();
        $inventory = $inventoryService->createFromPurchaseItem($purchaseItem);

        // Verify inventory was created
        $this->assertInstanceOf(Inventory::class, $inventory);
        $this->assertEquals(200, $inventory->quantity);
        $this->assertEquals('BATCH-001', $inventory->batch_number);
        $this->assertEquals($medicine->id, $inventory->medicine_id);
        $this->assertEquals($purchaseItem->id, $inventory->purchase_item_id);

        // Verify medicine stock is updated
        $this->assertEquals(200, $medicine->fresh()->getCurrentStock());
        $this->assertFalse($medicine->fresh()->needsReorder());
    }

    /** @test */
    public function inventory_service_can_manage_stock_movements()
    {
        $medicine = Medicine::factory()->create();
        $purchase = Purchase::factory()->create(['supplier_id' => $this->supplier->id]);
        $purchaseItem = PurchaseItem::factory()->create([
            'purchase_id' => $purchase->id,
            'medicine_id' => $medicine->id,
            'quantity_received' => 100,
            'batch_number' => 'BATCH-001',
            'expiry_date' => now()->addYears(2),
            'location_id' => $this->location->id,
        ]);

        $inventoryService = new InventoryService();
        $inventory = $inventoryService->createFromPurchaseItem($purchaseItem);

        // Test quantity update
        $updatedInventory = $inventoryService->updateQuantity($inventory, -20, 'Sale');
        $this->assertEquals(80, $updatedInventory->quantity);

        // Test stock queries
        $this->assertEquals(80, $inventoryService->getCurrentStock($medicine->id));
        $this->assertCount(1, $inventoryService->getActiveBatches($medicine->id));
        $this->assertCount(0, $inventoryService->getExpiredBatches($medicine->id));
    }

    /** @test */
    public function purchase_item_has_proper_receiving_workflow()
    {
        $purchase = Purchase::factory()->create(['supplier_id' => $this->supplier->id]);
        $medicine = Medicine::factory()->create();

        $purchaseItem = PurchaseItem::create([
            'purchase_id' => $purchase->id,
            'medicine_id' => $medicine->id,
            'quantity_ordered' => 100,
            'quantity_received' => 0,
            'unit_price' => 5.00,
            'total_amount' => 500.00,
            'created_by' => $this->user->id,
        ]);

        // Initially not received
        $this->assertFalse($purchaseItem->isFullyReceived());
        $this->assertFalse($purchaseItem->isPartiallyReceived());
        $this->assertEquals(100, $purchaseItem->remaining_quantity);
        $this->assertFalse($purchaseItem->canCreateInventory());

        // Partially receive
        $purchaseItem->update([
            'quantity_received' => 50,
            'batch_number' => 'BATCH-001',
            'expiry_date' => now()->addYears(2),
            'location_id' => $this->location->id,
        ]);

        $this->assertFalse($purchaseItem->isFullyReceived());
        $this->assertTrue($purchaseItem->isPartiallyReceived());
        $this->assertEquals(50, $purchaseItem->remaining_quantity);
        $this->assertTrue($purchaseItem->canCreateInventory());

        // Fully receive
        $purchaseItem->update(['quantity_received' => 100]);

        $this->assertTrue($purchaseItem->isFullyReceived());
        $this->assertFalse($purchaseItem->isPartiallyReceived());
        $this->assertEquals(0, $purchaseItem->remaining_quantity);
    }

    /** @test */
    public function medicine_helper_methods_work_correctly()
    {
        $medicine = Medicine::factory()->create([
            'name' => 'Aspirin',
            'dosage' => '100mg',
            'generic_name' => 'Acetylsalicylic Acid',
            'supplier_price_unit' => 3.00,
            'retail_price_unit' => 5.00,
        ]);

        // Test display name
        $this->assertEquals('Aspirin (100mg)', $medicine->display_name);
        $this->assertEquals('Aspirin (100mg) - Acetylsalicylic Acid', $medicine->full_name);

        // Test pricing
        $this->assertTrue($medicine->hasPricing());
        $this->assertEquals(66.67, round($medicine->profit_margin, 2));

        // Test default supplier
        $medicine->suppliers()->attach($this->supplier->id, [
            'price' => 2.50,
            'is_default' => true,
            'created_by' => $this->user->id,
        ]);

        $defaultSupplier = $medicine->getDefaultSupplier();
        $this->assertEquals($this->supplier->id, $defaultSupplier->id);
    }
}
