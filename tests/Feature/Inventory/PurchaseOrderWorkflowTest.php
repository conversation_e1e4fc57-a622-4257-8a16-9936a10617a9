<?php

namespace Tests\Feature\Inventory;

use Tests\TestCase;
use App\Models\Users\User;
use App\Models\Inventory\Purchase;
use App\Models\Inventory\PurchaseItem;
use App\Models\Inventory\Medicine;
use App\Models\Inventory\Supplier;
use App\Models\Inventory\Inventory;
use App\Models\Inventory\StockMovement;
use App\Models\Inventory\Category;
use App\Models\Inventory\Manufacturer;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class PurchaseOrderWorkflowTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $supplier;
    protected $medicine;
    protected $purchase;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
        
        // Create test data
        $category = Category::factory()->create();
        $manufacturer = Manufacturer::factory()->create();
        
        $this->supplier = Supplier::factory()->create([
            'name' => 'Test Supplier',
            'is_active' => true
        ]);
        
        $this->medicine = Medicine::factory()->create([
            'name' => 'Test Medicine',
            'category_id' => $category->id,
            'manufacturer_id' => $manufacturer->id,
            'supplier_price_unit' => 10.00,
            'status' => 'active'
        ]);
        
        // Associate medicine with supplier
        $this->medicine->suppliers()->attach($this->supplier->id, ['price' => 12.00]);
    }

    /** @test */
    public function it_can_create_purchase_order_in_pending_status()
    {
        $response = $this->post(route('inventory.purchases.store'), [
            'supplier_id' => $this->supplier->id,
            'order_date' => now()->format('Y-m-d'),
            'expected_date' => now()->addDays(7)->format('Y-m-d'),
            'notes' => 'Test purchase order',
            'tax_percentage' => 10,
            'discount_percentage' => 5,
            'shipping_cost' => 50,
            'items' => [
                [
                    'medicine_id' => $this->medicine->id,
                    'quantity' => 100,
                    'unit_price' => 12.00,
                    'expiry_date' => now()->addYear()->format('Y-m-d'),
                    'batch_number' => 'BATCH001',
                    'total_amount' => 1200.00,
                    'tax_amount' => 120.00,
                    'discount_amount' => 60.00
                ]
            ]
        ]);

        $this->assertDatabaseHas('purchases', [
            'supplier_id' => $this->supplier->id,
            'status' => 'pending',
            'payment_status' => 'pending'
        ]);
    }

    /** @test */
    public function it_can_move_purchase_from_pending_to_ordered()
    {
        $purchase = Purchase::factory()->create([
            'supplier_id' => $this->supplier->id,
            'status' => 'pending',
            'created_by' => $this->user->id
        ]);

        $response = $this->post(route('inventory.purchases.order', $purchase));

        $response->assertRedirect(route('inventory.purchases.show', $purchase));
        $response->assertSessionHas('success', 'Purchase order has been placed successfully.');
        
        $purchase->refresh();
        $this->assertEquals('ordered', $purchase->status);
    }

    /** @test */
    public function it_can_receive_items_and_update_inventory()
    {
        // Create ordered purchase with items
        $purchase = Purchase::factory()->create([
            'supplier_id' => $this->supplier->id,
            'status' => 'ordered',
            'created_by' => $this->user->id
        ]);

        $purchaseItem = PurchaseItem::factory()->create([
            'purchase_id' => $purchase->id,
            'medicine_id' => $this->medicine->id,
            'quantity' => 100,
            'unit_price' => 12.00,
            'received_quantity' => 0,
            'expiry_date' => now()->addYear(),
            'batch_number' => 'BATCH001'
        ]);

        // Receive items
        $response = $this->post(route('inventory.purchases.receive', $purchase), [
            'items' => [
                $purchaseItem->id => [
                    'received_quantity' => 100,
                    'batch_number' => 'BATCH001',
                    'expiry_date' => now()->addYear()->format('Y-m-d')
                ]
            ]
        ]);

        $response->assertRedirect(route('inventory.purchases.show', $purchase));
        
        // Check purchase status updated
        $purchase->refresh();
        $this->assertEquals('received', $purchase->status);
        
        // Check item received quantity updated
        $purchaseItem->refresh();
        $this->assertEquals(100, $purchaseItem->received_quantity);
        
        // Check inventory was created/updated
        $this->assertDatabaseHas('inventories', [
            'medicine_id' => $this->medicine->id,
            'batch_number' => 'BATCH001',
            'quantity' => 100
        ]);
        
        // Check stock movement was recorded
        $this->assertDatabaseHas('stock_movements', [
            'medicine_id' => $this->medicine->id,
            'movement_type' => 'purchase',
            'quantity' => 100,
            'reference_type' => 'purchase',
            'reference_id' => $purchase->id
        ]);
    }

    /** @test */
    public function it_handles_partial_receiving_correctly()
    {
        // Create ordered purchase with items
        $purchase = Purchase::factory()->create([
            'supplier_id' => $this->supplier->id,
            'status' => 'ordered',
            'created_by' => $this->user->id
        ]);

        $purchaseItem = PurchaseItem::factory()->create([
            'purchase_id' => $purchase->id,
            'medicine_id' => $this->medicine->id,
            'quantity' => 100,
            'unit_price' => 12.00,
            'received_quantity' => 0,
            'expiry_date' => now()->addYear(),
            'batch_number' => 'BATCH001'
        ]);

        // Receive partial quantity
        $response = $this->post(route('inventory.purchases.receive', $purchase), [
            'items' => [
                $purchaseItem->id => [
                    'received_quantity' => 60,
                    'batch_number' => 'BATCH001',
                    'expiry_date' => now()->addYear()->format('Y-m-d')
                ]
            ]
        ]);

        $response->assertRedirect(route('inventory.purchases.show', $purchase));
        
        // Check purchase status is partially_received
        $purchase->refresh();
        $this->assertEquals('partially_received', $purchase->status);
        
        // Check item received quantity updated
        $purchaseItem->refresh();
        $this->assertEquals(60, $purchaseItem->received_quantity);
    }

    /** @test */
    public function it_prevents_receiving_more_than_ordered_quantity()
    {
        $purchase = Purchase::factory()->create([
            'supplier_id' => $this->supplier->id,
            'status' => 'ordered',
            'created_by' => $this->user->id
        ]);

        $purchaseItem = PurchaseItem::factory()->create([
            'purchase_id' => $purchase->id,
            'medicine_id' => $this->medicine->id,
            'quantity' => 100,
            'unit_price' => 12.00,
            'received_quantity' => 0,
            'expiry_date' => now()->addYear(),
            'batch_number' => 'BATCH001'
        ]);

        // Try to receive more than ordered
        $response = $this->post(route('inventory.purchases.receive', $purchase), [
            'items' => [
                $purchaseItem->id => [
                    'received_quantity' => 150, // More than ordered (100)
                    'batch_number' => 'BATCH001',
                    'expiry_date' => now()->addYear()->format('Y-m-d')
                ]
            ]
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('error');
        
        // Purchase status should remain unchanged
        $purchase->refresh();
        $this->assertEquals('ordered', $purchase->status);
    }

    /** @test */
    public function it_can_cancel_pending_or_ordered_purchases()
    {
        $purchase = Purchase::factory()->create([
            'supplier_id' => $this->supplier->id,
            'status' => 'pending',
            'created_by' => $this->user->id
        ]);

        $response = $this->post(route('inventory.purchases.cancel', $purchase));

        $response->assertRedirect(route('inventory.purchases.show', $purchase));
        $response->assertSessionHas('success', 'Purchase order cancelled successfully.');
        
        $purchase->refresh();
        $this->assertEquals('cancelled', $purchase->status);
    }
}
