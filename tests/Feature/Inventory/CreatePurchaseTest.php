<?php

namespace Tests\Feature\Inventory;

use Tests\TestCase;
use App\Models\Users\User;
use App\Models\Inventory\Supplier;
use App\Models\Inventory\Medicine;
use App\Models\Inventory\Manufacturer;
use App\Models\Inventory\Category;
use App\Models\Inventory\UnitType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Livewire\Livewire;
use App\Livewire\Inventory\CreatePurchase;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;

class CreatePurchaseTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $supplier;
    protected $medicine;
    protected $manufacturer;
    protected $category;
    protected $unitType;

    protected function setUp(): void
    {
        parent::setUp();

        // Create permissions
        Permission::create(['name' => 'view inventory']);
        Permission::create(['name' => 'create purchase']);
        Permission::create(['name' => 'edit purchase']);
        Permission::create(['name' => 'delete purchase']);

        // Create test user
        $this->user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'status' => 'active',
            'timezone' => 'UTC',
        ]);

        // Assign permissions to the user
        $this->user->givePermissionTo([
            'view inventory',
            'create purchase',
            'edit purchase',
            'delete purchase',
        ]);

        // Create manufacturer
        $this->manufacturer = Manufacturer::factory()->create([
            'name' => 'Test Manufacturer',
            'slug' => 'test-manufacturer',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'address' => '123 Test St',
            'website' => 'https://test.com',
            'is_active' => true,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        // Create category
        $this->category = Category::factory()->create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'description' => 'Test category description',
            'is_active' => true,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        // Create unit type
        $this->unitType = UnitType::factory()->create([
            'name' => 'Tablet',
            'slug' => 'tablet',
            'abbreviation' => 'TAB',
            'code' => 'TAB',
            'category' => 'unit',
            'description' => 'Tablet unit type',
            'is_active' => true,
            'is_base' => true,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        // Create test supplier
        $this->supplier = Supplier::factory()->create([
            'name' => 'Test Supplier',
            'email' => '<EMAIL>',
            'phone' => '0987654321',
            'address' => '456 Test Ave',
            'tax_number' => 'TAX-1234-5678-9012',
            'is_active' => true,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        // Create test medicine
        $this->medicine = Medicine::factory()->create([
            'name' => 'Test Medicine',
            'generic_name' => 'Test Generic',
            'description' => 'Test medicine description',
            'manufacturer_id' => $this->manufacturer->id,
            'category_id' => $this->category->id,
            'unit_type_id' => $this->unitType->id,
            'minimum_stock' => 10,
            'maximum_stock' => 100,
            'controlled_substance' => false,
            'prescription_required' => false,
            'supplier_price_carton' => 1000,
            'supplier_price_box' => 100,
            'supplier_price_strip' => 10,
            'supplier_price_unit' => 1,
            'retail_price_carton' => 2000,
            'retail_price_box' => 200,
            'retail_price_strip' => 20,
            'retail_price_unit' => 2,
            'unit_price' => 1,
            'enabled_units' => json_encode(['carton', 'box', 'strip', 'unit']),
            'status' => 'active',
            'strips_per_box' => 10,
            'pieces_per_strip' => 10,
            'is_active' => true,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);
    }

    #[Test]
    public function it_can_render_create_purchase_page()
    {
        $this->actingAs($this->user);
        Livewire::test(CreatePurchase::class)
            ->assertSuccessful();
    }

    #[Test]
    public function it_requires_supplier_selection()
    {
        Livewire::actingAs($this->user)
            ->test(CreatePurchase::class)
            ->set('supplier_id', '')
            ->call('save')
            ->assertHasErrors(['supplier_id' => 'required']);
    }

    #[Test]
    public function it_requires_order_date()
    {
        Livewire::actingAs($this->user)
            ->test(CreatePurchase::class)
            ->set('order_date', '')
            ->call('save')
            ->assertHasErrors(['order_date' => 'required']);
    }

    #[Test]
    public function it_requires_expected_date()
    {
        Livewire::actingAs($this->user)
            ->test(CreatePurchase::class)
            ->set('expected_date', '')
            ->call('save')
            ->assertHasErrors(['expected_date' => 'required']);
    }

    #[Test]
    public function it_can_add_purchase_item()
    {
        $component = Livewire::actingAs($this->user)
            ->test(CreatePurchase::class);

        // Clear items array first
        $component->set('items', []);

        // Add new item
        $component->call('addItem')
            ->assertSet('items.0', [
                'medicine_id' => '',
                'quantity' => 1,
                'unit_price' => 0,
                'total_amount' => 0,
                'tax_amount' => 0,
                'discount_amount' => 0,
                'batch_number' => '',
                'expiry_date' => now()->addYear()->format('Y-m-d'),
            ]);
    }

    #[Test]
    public function it_requires_medicine_selection_for_items()
    {
        Livewire::actingAs($this->user)
            ->test(CreatePurchase::class)
            ->call('addItem')
            ->set('items.0.medicine_id', '')
            ->set('items.0.quantity', 10)
            ->set('items.0.unit_price', 100)
            ->call('save')
            ->assertHasErrors(['items.0.medicine_id' => 'required']);
    }

    #[Test]
    public function it_requires_quantity_for_items()
    {
        Livewire::actingAs($this->user)
            ->test(CreatePurchase::class)
            ->call('addItem')
            ->set('items.0.medicine_id', $this->medicine->id)
            ->set('items.0.quantity', '')
            ->set('items.0.unit_price', 100)
            ->call('save')
            ->assertHasErrors(['items.0.quantity' => 'required']);
    }

    #[Test]
    public function it_requires_unit_price_for_items()
    {
        Livewire::actingAs($this->user)
            ->test(CreatePurchase::class)
            ->call('addItem')
            ->set('items.0.medicine_id', $this->medicine->id)
            ->set('items.0.quantity', 10)
            ->set('items.0.unit_price', '')
            ->call('save')
            ->assertHasErrors(['items.0.unit_price' => 'required']);
    }

    #[Test]
    public function it_can_calculate_item_total()
    {
        $component = Livewire::actingAs($this->user)
            ->test(CreatePurchase::class);

        // Clear items array first
        $component->set('items', []);

        // Add new item and set values
        $component
            ->call('addItem')
            ->set('items.0.medicine_id', $this->medicine->id)
            ->set('tax_percentage', 10)
            ->set('discount_percentage', 5)
            ->set('items.0.quantity', 10)
            ->set('items.0.unit_price', 100)
            ->call('calculateItemTotal', 0);

        // Get the item and verify calculations
        $item = $component->get('items.0');
        $this->assertEquals(1000, $item['quantity'] * $item['unit_price']); // Base amount
        $this->assertEquals(100, $item['tax_amount']); // 10% of 1000
        $this->assertEquals(50, $item['discount_amount']); // 5% of 1000
        $this->assertEquals(1050, $item['total_amount']); // 1000 + 100 - 50
    }

    #[Test]
    public function it_can_calculate_order_total_with_tax_and_discount()
    {
        $component = Livewire::actingAs($this->user)
            ->test(CreatePurchase::class);

        // Clear items array first
        $component->set('items', []);

        // Add new item and set values
        $component
            ->call('addItem')
            ->set('items.0.medicine_id', $this->medicine->id)
            ->set('tax_percentage', 10)
            ->set('discount_percentage', 5)
            ->set('items.0.quantity', 10)
            ->set('items.0.unit_price', 100)
            ->call('calculateItemTotal', 0)
            ->set('shipping_cost', 50);

        // Get the order total from the rendered view data
        $this->assertEquals(1100, $component->viewData('orderTotal'));
    }

    #[Test]
    public function it_can_create_purchase_order()
    {
        $component = Livewire::actingAs($this->user)
            ->test(CreatePurchase::class);

        // Clear items array first
        $component->set('items', []);

        // Set purchase data
        $component
            ->set('supplier_id', $this->supplier->id)
            ->set('order_date', now()->format('Y-m-d'))
            ->set('expected_date', now()->addDays(7)->format('Y-m-d'))
            ->call('addItem')
            ->set('items.0.medicine_id', $this->medicine->id)
            ->set('tax_percentage', 10)
            ->set('discount_percentage', 5)
            ->set('items.0.quantity', 10)
            ->set('items.0.unit_price', 100)
            ->set('items.0.expiry_date', now()->addYear()->format('Y-m-d'))
            ->set('items.0.batch_number', 'BATCH001')
            ->call('calculateItemTotal', 0)
            ->set('shipping_cost', 50)
            ->set('notes', 'Test purchase order');

        // Call save and check for validation errors
        $result = $component->call('save');
        if ($result->errors()->isNotEmpty()) {
            $this->fail('Validation errors: ' . json_encode($result->errors()));
        }

        // Check session for error messages
        if (session()->has('error')) {
            $this->fail('Save operation failed: ' . session('error'));
        }

        // Assert database records
        $this->assertDatabaseHas('purchases', [
            'supplier_id' => $this->supplier->id,
            'total_amount' => '1050.00', // quantity * unit_price
            'tax_percentage' => '10.00',
            'tax_amount' => '100.00',
            'discount_percentage' => '5.00',
            'discount_amount' => '50.00',
            'shipping_cost' => '50.00',
            'final_amount' => '1150.00', // 1050 (item total) + 50 (shipping) + 100 (tax) - 50 (discount)
            'status' => 'pending',
            'payment_status' => 'pending',
            'notes' => 'Test purchase order',
            'created_by' => $this->user->id,
        ]);

        $this->assertDatabaseHas('purchase_items', [
            'medicine_id' => $this->medicine->id,
            'quantity' => 10,
            'unit_price' => '100.00',
            'total_amount' => '1050.00', // 1000 + 100 - 50
            'batch_number' => 'BATCH001',
        ]);
    }

    #[Test]
    public function it_can_remove_purchase_item()
    {
        $component = Livewire::actingAs($this->user)
            ->test(CreatePurchase::class);

        // Clear items array first
        $component->set('items', []);

        // Add and remove an item
        $component
            ->call('addItem')
            ->assertSet('items', [
                0 => [
                    'medicine_id' => '',
                    'quantity' => 1,
                    'unit_price' => 0,
                    'total_amount' => 0,
                    'tax_amount' => 0,
                    'discount_amount' => 0,
                    'batch_number' => '',
                    'expiry_date' => now()->addYear()->format('Y-m-d'),
                ]
            ])
            ->call('removeItem', 0)
            ->assertSet('items', []);
    }
} 