<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Customers\Customer;
use App\Models\Users\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;

class CustomerSyncApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
    }

    #[Test]
    public function it_provides_customers_api_endpoint()
    {
        $response = $this->getJson('/api/offline/customers');
        
        // Should return 200 even with no data
        $response->assertStatus(200);
        
        // Should have proper JSON structure
        $response->assertJsonStructure([
            'data',
            'meta' => [
                'count',
                'total',
                'limit', 
                'offset',
                'has_more',
                'timestamp'
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertIsArray($responseData['data']);
        $this->assertIsArray($responseData['meta']);
    }

    #[Test]
    public function it_supports_pagination_parameters()
    {
        // Create test customers
        for ($i = 1; $i <= 15; $i++) {
            Customer::create([
                'name' => "Customer {$i}",
                'email' => "customer{$i}@example.com",
                'phone' => "123456789{$i}",
                'status' => 'active',
                'loyalty_points' => $i * 10,
                'created_by' => $this->user->id,
                'updated_by' => $this->user->id,
            ]);
        }

        $response = $this->getJson('/api/offline/customers?limit=5&offset=10');
        
        $response->assertStatus(200);
        
        $responseData = $response->json();
        $this->assertEquals(5, $responseData['meta']['count']);
        $this->assertEquals(5, $responseData['meta']['limit']);
        $this->assertEquals(10, $responseData['meta']['offset']);
        $this->assertEquals(15, $responseData['meta']['total']);
        $this->assertFalse($responseData['meta']['has_more']);
    }

    #[Test]
    public function it_supports_since_parameter_for_incremental_sync()
    {
        // Create a customer
        $customer = Customer::create([
            'name' => 'Test Customer',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'status' => 'active',
            'loyalty_points' => 100,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $timestamp = now()->subHour()->toISOString();
        $response = $this->getJson('/api/offline/customers?since=' . urlencode($timestamp));
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data',
            'meta'
        ]);

        $responseData = $response->json();
        $this->assertGreaterThan(0, $responseData['meta']['count']);
    }

    #[Test]
    public function it_supports_search_functionality()
    {
        // Create test customers
        Customer::create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'status' => 'active',
            'loyalty_points' => 100,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        Customer::create([
            'name' => 'Jane Smith',
            'email' => '<EMAIL>',
            'phone' => '0987654321',
            'status' => 'active',
            'loyalty_points' => 200,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        // Search by name
        $response = $this->getJson('/api/offline/customers?search=John');
        $response->assertStatus(200);
        
        $responseData = $response->json();
        $this->assertEquals(1, $responseData['meta']['count']);
        $this->assertEquals('John Doe', $responseData['data'][0]['name']);

        // Search by email
        $response = $this->getJson('/api/offline/customers?search=<EMAIL>');
        $response->assertStatus(200);
        
        $responseData = $response->json();
        $this->assertEquals(1, $responseData['meta']['count']);
        $this->assertEquals('Jane Smith', $responseData['data'][0]['name']);
    }

    #[Test]
    public function it_provides_individual_customer_endpoint()
    {
        // Create a customer
        $customer = Customer::create([
            'name' => 'Test Customer',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'status' => 'active',
            'loyalty_points' => 500,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $response = $this->getJson("/api/offline/customers/{$customer->id}");
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'email',
                'phone',
                'status',
                'loyalty_points',
                'loyalty_tier',
                'points_to_next_tier',
                'total_points_earned',
                'total_points_redeemed',
                'loyalty_transactions',
                'sales'
            ],
            'meta' => [
                'timestamp'
            ]
        ]);

        $customerData = $response->json('data');
        $this->assertEquals('Test Customer', $customerData['name']);
        $this->assertEquals('<EMAIL>', $customerData['email']);
        $this->assertEquals(500, $customerData['loyalty_points']);
    }

    #[Test]
    public function it_handles_non_existent_customer()
    {
        $response = $this->getJson('/api/offline/customers/99999');
        
        $response->assertStatus(404);
        $response->assertJson(['error' => 'Customer not found']);
    }

    #[Test]
    public function it_includes_computed_loyalty_attributes()
    {
        // Create a customer with high loyalty points
        $customer = Customer::create([
            'name' => 'VIP Customer',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'status' => 'active',
            'loyalty_points' => 1500,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $response = $this->getJson("/api/offline/customers/{$customer->id}");
        
        $response->assertStatus(200);
        
        $customerData = $response->json('data');
        $this->assertArrayHasKey('loyalty_tier', $customerData);
        $this->assertArrayHasKey('points_to_next_tier', $customerData);
        $this->assertArrayHasKey('total_points_earned', $customerData);
        $this->assertArrayHasKey('total_points_redeemed', $customerData);
    }

    #[Test]
    public function it_filters_by_status()
    {
        // Create active and inactive customers
        Customer::create([
            'name' => 'Active Customer',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'status' => 'active',
            'loyalty_points' => 100,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        Customer::create([
            'name' => 'Inactive Customer',
            'email' => '<EMAIL>',
            'phone' => '0987654321',
            'status' => 'inactive',
            'loyalty_points' => 50,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        // Default should only return active customers
        $response = $this->getJson('/api/offline/customers');
        $response->assertStatus(200);
        
        $responseData = $response->json();
        $this->assertEquals(1, $responseData['meta']['count']);
        $this->assertEquals('Active Customer', $responseData['data'][0]['name']);

        // Explicitly filter by status
        $response = $this->getJson('/api/offline/customers?status=inactive');
        $response->assertStatus(200);
        
        $responseData = $response->json();
        $this->assertEquals(1, $responseData['meta']['count']);
        $this->assertEquals('Inactive Customer', $responseData['data'][0]['name']);
    }

    #[Test]
    public function it_handles_invalid_pagination_parameters()
    {
        // Test with negative limit
        $response = $this->getJson('/api/offline/customers?limit=-5');
        $response->assertStatus(200); // Should handle gracefully
        
        // Test with very large limit
        $response = $this->getJson('/api/offline/customers?limit=10000');
        $response->assertStatus(200); // Should handle gracefully
        
        // Test with negative offset
        $response = $this->getJson('/api/offline/customers?offset=-10');
        $response->assertStatus(200); // Should handle gracefully
    }

    #[Test]
    public function it_provides_customer_sync_status_endpoint()
    {
        $response = $this->getJson('/api/offline/customers/sync-status');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'last_sync',
            'next_sync',
            'sync_interval',
            'total_customers',
            'pending_updates',
            'sync_enabled'
        ]);
        
        $data = $response->json();
        $this->assertContains($data['status'], ['idle', 'syncing', 'error']);
        $this->assertIsInt($data['sync_interval']);
        $this->assertIsInt($data['total_customers']);
        $this->assertIsInt($data['pending_updates']);
        $this->assertIsBool($data['sync_enabled']);
    }

    #[Test]
    public function it_handles_customer_sync_trigger()
    {
        // Create some test customers
        for ($i = 1; $i <= 5; $i++) {
            Customer::create([
                'name' => "Customer {$i}",
                'email' => "customer{$i}@example.com",
                'phone' => "123456789{$i}",
                'status' => 'active',
                'loyalty_points' => $i * 10,
                'created_by' => $this->user->id,
                'updated_by' => $this->user->id,
            ]);
        }

        $response = $this->postJson('/api/offline/customers/sync-trigger');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'message',
            'sync_id',
            'estimated_duration',
            'customers_to_sync',
            'force_full_sync'
        ]);
        
        $data = $response->json();
        $this->assertEquals('started', $data['status']);
        $this->assertIsString($data['sync_id']);
        $this->assertGreaterThan(0, $data['customers_to_sync']);
    }

    #[Test]
    public function it_prevents_concurrent_customer_sync_operations()
    {
        // Start first sync
        $response1 = $this->postJson('/api/offline/customers/sync-trigger');
        $response1->assertStatus(200);
        
        // Try to start second sync immediately
        $response2 = $this->postJson('/api/offline/customers/sync-trigger');
        $response2->assertStatus(409); // Conflict
        $response2->assertJson([
            'status' => 'error',
            'message' => 'Customer sync operation already in progress'
        ]);
    }

    #[Test]
    public function it_tracks_customer_sync_progress()
    {
        // Start sync
        $syncResponse = $this->postJson('/api/offline/customers/sync-trigger');
        $syncId = $syncResponse->json('sync_id');

        // Check progress
        $progressResponse = $this->getJson("/api/offline/customers/sync-progress/{$syncId}");
        
        $progressResponse->assertStatus(200);
        $progressResponse->assertJsonStructure([
            'sync_id',
            'status',
            'progress_percentage',
            'current_step',
            'customers_processed',
            'customers_total',
            'estimated_time_remaining',
            'errors'
        ]);
        
        $progress = $progressResponse->json();
        $this->assertEquals($syncId, $progress['sync_id']);
        $this->assertContains($progress['status'], ['running', 'completed', 'error']);
        $this->assertIsNumeric($progress['progress_percentage']);
        $this->assertGreaterThanOrEqual(0, $progress['progress_percentage']);
        $this->assertLessThanOrEqual(100, $progress['progress_percentage']);
    }

    #[Test]
    public function it_handles_customer_sync_cancellation()
    {
        // Start a sync
        $syncResponse = $this->postJson('/api/offline/customers/sync-trigger');
        $syncId = $syncResponse->json('sync_id');

        // Cancel the sync
        $cancelResponse = $this->deleteJson("/api/offline/customers/sync-cancel/{$syncId}");
        
        $cancelResponse->assertStatus(200);
        $cancelResponse->assertJson([
            'status' => 'cancelled',
            'message' => 'Customer sync operation cancelled successfully'
        ]);

        // Verify sync status is cancelled
        $statusResponse = $this->getJson("/api/offline/customers/sync-progress/{$syncId}");
        $statusResponse->assertStatus(200);
        $statusResponse->assertJsonPath('status', 'cancelled');
    }

    #[Test]
    public function it_returns_proper_content_type()
    {
        $response = $this->getJson('/api/offline/customers');
        
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/json');
    }

    #[Test]
    public function it_includes_timestamp_in_meta()
    {
        $response = $this->getJson('/api/offline/customers');
        
        $response->assertStatus(200);
        
        $responseData = $response->json();
        $this->assertArrayHasKey('meta', $responseData);
        $this->assertArrayHasKey('timestamp', $responseData['meta']);
        $this->assertIsString($responseData['meta']['timestamp']);
    }

    #[Test]
    public function it_handles_empty_database_gracefully()
    {
        // With fresh database (no seeded data), endpoint should still work
        $response = $this->getJson('/api/offline/customers');
        $response->assertStatus(200);
        
        $responseData = $response->json();
        $this->assertIsArray($responseData['data']);
        $this->assertCount(0, $responseData['data']); // Should be empty array
        $this->assertEquals(0, $responseData['meta']['count']);
        $this->assertEquals(0, $responseData['meta']['total']);
    }

    #[Test]
    public function it_validates_customer_id_parameter()
    {
        // Test with non-numeric ID
        $response = $this->getJson('/api/offline/customers/abc');
        $response->assertStatus(404); // Should handle as not found
        
        // Test with zero ID
        $response = $this->getJson('/api/offline/customers/0');
        $response->assertStatus(404);
        
        // Test with negative ID
        $response = $this->getJson('/api/offline/customers/-1');
        $response->assertStatus(404);
    }

    #[Test]
    public function it_supports_force_full_sync_parameter()
    {
        $response = $this->postJson('/api/offline/customers/sync-trigger', [
            'force_full_sync' => true
        ]);
        
        $response->assertStatus(200);
        $response->assertJsonPath('force_full_sync', true);
    }

    #[Test]
    public function it_handles_sync_errors_gracefully()
    {
        // Simulate error condition by using invalid sync ID
        $response = $this->getJson('/api/offline/customers/sync-progress/invalid-sync-id');

        $response->assertStatus(404);
        $response->assertJson([
            'status' => 'error',
            'message' => 'Customer sync operation not found'
        ]);
    }

    #[Test]
    public function it_provides_consistent_api_structure_for_customers()
    {
        // Create test customer
        $customer = Customer::create([
            'name' => 'API Test Customer',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'status' => 'active',
            'loyalty_points' => 300,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        // Test list endpoint
        $listResponse = $this->getJson('/api/offline/customers');
        $listResponse->assertStatus(200);

        $listData = $listResponse->json();
        $this->assertArrayHasKey('data', $listData);
        $this->assertArrayHasKey('meta', $listData);
        $this->assertIsArray($listData['data']);
        $this->assertIsArray($listData['meta']);

        // Test individual endpoint
        $detailResponse = $this->getJson("/api/offline/customers/{$customer->id}");
        $detailResponse->assertStatus(200);

        $detailData = $detailResponse->json();
        $this->assertArrayHasKey('data', $detailData);
        $this->assertArrayHasKey('meta', $detailData);
        $this->assertIsArray($detailData['data']);
        $this->assertIsArray($detailData['meta']);
    }
}
