<?php

namespace Tests\Feature\Settings;

use App\Models\Users\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class AdminPasswordVerificationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();

        // Create permissions
        Permission::create(['name' => 'manage settings']);
        
        // Create admin role
        $adminRole = Role::create(['name' => 'admin']);
        $adminRole->givePermissionTo('manage settings');

        // Create regular user with manage settings permission
        $this->user = User::factory()->create([
            'password' => Hash::make('password123')
        ]);
        $this->user->givePermissionTo('manage settings');

        // Create admin user
        $this->adminUser = User::factory()->create([
            'password' => Hash::make('admin123')
        ]);
        $this->adminUser->assignRole('admin');
    }

    /** @test */
    public function it_can_verify_correct_password_for_user_with_manage_settings_permission()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/admin/verify-password', [
                'password' => 'password123'
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Password verified successfully'
            ]);

        // Check session
        $this->assertNotNull(session('settings_password_verified_at'));
        $this->assertEquals($this->user->id, session('settings_password_verified_user'));
    }

    /** @test */
    public function it_can_verify_correct_password_for_admin_user()
    {
        $response = $this->actingAs($this->adminUser)
            ->postJson('/admin/verify-password', [
                'password' => 'admin123'
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Password verified successfully'
            ]);
    }

    /** @test */
    public function it_rejects_incorrect_password()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/admin/verify-password', [
                'password' => 'wrongpassword'
            ]);

        $response->assertStatus(401)
            ->assertJson([
                'success' => false,
                'message' => 'Invalid password'
            ]);
    }

    /** @test */
    public function it_rejects_user_without_permissions()
    {
        $unauthorizedUser = User::factory()->create([
            'password' => Hash::make('password123')
        ]);

        $response = $this->actingAs($unauthorizedUser)
            ->postJson('/admin/verify-password', [
                'password' => 'password123'
            ]);

        $response->assertStatus(403)
            ->assertJson([
                'success' => false,
                'message' => 'Unauthorized access'
            ]);
    }

    /** @test */
    public function it_can_check_verification_status()
    {
        // First verify password
        $this->actingAs($this->user)
            ->postJson('/admin/verify-password', [
                'password' => 'password123'
            ]);

        // Then check verification
        $response = $this->actingAs($this->user)
            ->getJson('/admin/check-verification');

        $response->assertStatus(200)
            ->assertJson([
                'verified' => true
            ])
            ->assertJsonStructure([
                'verified',
                'expires_at'
            ]);
    }

    /** @test */
    public function it_can_clear_verification()
    {
        // First verify password
        $this->actingAs($this->user)
            ->postJson('/admin/verify-password', [
                'password' => 'password123'
            ]);

        // Clear verification
        $response = $this->actingAs($this->user)
            ->postJson('/admin/clear-verification');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Verification cleared'
            ]);

        // Check that verification is cleared
        $this->assertNull(session('settings_password_verified_at'));
        $this->assertNull(session('settings_password_verified_user'));
    }

    /** @test */
    public function it_requires_password_field()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/admin/verify-password', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['password']);
    }
}
