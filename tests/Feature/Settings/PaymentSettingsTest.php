<?php

namespace Tests\Feature\Settings;

use App\Livewire\Pages\Settings\PaymentSettings;
use App\Models\Users\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Str;

class PaymentSettingsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create permission
        Permission::create(['name' => 'manage_settings']);
        
        // Create and authenticate a user with permissions
        $user = User::factory()->create();
        $user->givePermissionTo('manage_settings');
        $this->actingAs($user);
    }

    #[Test]
    public function it_can_render_payment_settings_page()
    {
        Livewire::test(PaymentSettings::class)
            ->assertViewIs('livewire.pages.settings.payment-settings')
            ->assertSuccessful();
    }

    #[Test]
    public function it_can_update_payment_methods()
    {
        $settings = [
            'defaultMethod' => 'cash',
            'cashEnabled' => true,
            'stripeEnabled' => false,
            'paypalEnabled' => false,
            'bankTransferEnabled' => false,
        ];

        $component = Livewire::test(PaymentSettings::class)
            ->set('defaultMethod', $settings['defaultMethod'])
            ->set('cashEnabled', $settings['cashEnabled'])
            ->set('stripeEnabled', $settings['stripeEnabled'])
            ->set('paypalEnabled', $settings['paypalEnabled'])
            ->set('bankTransferEnabled', $settings['bankTransferEnabled'])
            ->call('save')
            ->assertHasNoErrors();

        foreach ($settings as $key => $value) {
            $this->assertDatabaseHas('settings', [
                'key' => Str::snake($key),
                'value' => $value,
                'group' => 'payment'
            ]);
        }

        $component->assertSet('message', 'Settings saved successfully.');
    }

    #[Test]
    public function it_can_update_stripe_credentials()
    {
        $credentials = [
            'stripeKey' => 'pk_test_123',
            'stripeSecret' => 'sk_test_456'
        ];

        $component = Livewire::test(PaymentSettings::class)
            ->set('stripeEnabled', true)
            ->set('defaultMethod', 'stripe')
            ->set('stripeKey', $credentials['stripeKey'])
            ->set('stripeSecret', $credentials['stripeSecret'])
            ->call('save')
            ->assertHasNoErrors();

        foreach ($credentials as $key => $value) {
            $this->assertDatabaseHas('settings', [
                'key' => Str::snake($key),
                'value' => $value,
                'group' => 'payment',
                'is_encrypted' => 1
            ]);
        }

        $component->assertSet('message', 'Settings saved successfully.');
    }

    #[Test]
    public function it_validates_stripe_credentials_when_enabled()
    {
        Livewire::test(PaymentSettings::class)
            ->set('stripeEnabled', true)
            ->set('defaultMethod', 'stripe')
            ->set('stripeKey', '')
            ->set('stripeSecret', '')
            ->call('save')
            ->assertHasErrors([
                'stripeKey' => 'required',
                'stripeSecret' => 'required'
            ]);
    }

    #[Test]
    public function it_requires_at_least_one_payment_method()
    {
        Livewire::test(PaymentSettings::class)
            ->set('cashEnabled', false)
            ->set('stripeEnabled', false)
            ->set('paypalEnabled', false)
            ->set('bankTransferEnabled', false)
            ->call('save')
            ->assertHasErrors('defaultMethod');
    }

    #[Test]
    public function it_requires_default_method_to_be_enabled()
    {
        Livewire::test(PaymentSettings::class)
            ->set('defaultMethod', 'stripe')
            ->set('stripeEnabled', false)
            ->call('save')
            ->assertHasErrors('defaultMethod');
    }

    #[Test]
    public function it_requires_permission_to_access()
    {
        // Arrange
        $user = User::factory()->create();
        $this->actingAs($user);

        // Act & Assert
        $this->get(route('settings.payment'))
            ->assertStatus(403);
    }
}
