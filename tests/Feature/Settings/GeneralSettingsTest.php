<?php

namespace Tests\Feature\Settings;

use App\Livewire\Pages\Settings\GeneralSettings;
use App\Models\Users\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Str;

class GeneralSettingsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create permission
        Permission::create(['name' => 'manage_settings']);
        
        // Create and authenticate a user with permissions
        $user = User::factory()->create();
        $user->givePermissionTo('manage_settings');
        $this->actingAs($user);
    }

    #[Test]
    public function it_can_render_general_settings_page()
    {
        // Act & Assert
        Livewire::test(GeneralSettings::class)
            ->assertViewIs('livewire.pages.settings.general-settings')
            ->assertSuccessful();
    }

    #[Test]
    public function it_can_update_pharmacy_settings()
    {
        // Arrange
        $settings = [
            'pharmacyName' => 'Test Pharmacy',
            'address' => '123 Test Street',
            'phone' => '1234567890',
            'email' => '<EMAIL>',
            'currency' => 'USD',
            'timezone' => 'UTC'
        ];

        // Act & Assert
        $component = Livewire::test(GeneralSettings::class)
            ->set('pharmacyName', $settings['pharmacyName'])
            ->set('address', $settings['address'])
            ->set('phone', $settings['phone'])
            ->set('email', $settings['email'])
            ->set('currency', $settings['currency'])
            ->set('timezone', $settings['timezone'])
            ->call('save')
            ->assertHasNoErrors();

        // Assert database
        foreach ($settings as $key => $value) {
            $this->assertDatabaseHas('settings', [
                'key' => Str::snake($key),
                'value' => $value,
                'group' => 'general'
            ]);
        }

        // Assert session flash message
        $component->assertSet('message', 'Settings saved successfully.');
    }

    #[Test]
    public function it_validates_required_fields()
    {
        // Act & Assert
        Livewire::test(GeneralSettings::class)
            ->set('pharmacyName', '')
            ->set('address', '')
            ->set('phone', '')
            ->set('email', '')
            ->call('save')
            ->assertHasErrors([
                'pharmacyName' => 'required',
                'address' => 'required',
                'phone' => 'required',
                'email' => 'required'
            ]);
    }

    #[Test]
    public function it_validates_email_format()
    {
        // Act & Assert
        Livewire::test(GeneralSettings::class)
            ->set('email', 'invalid-email')
            ->call('save')
            ->assertHasErrors(['email' => 'email']);
    }

    #[Test]
    public function it_validates_phone_format()
    {
        // Act & Assert
        Livewire::test(GeneralSettings::class)
            ->set('phone', 'invalid-phone')
            ->call('save')
            ->assertHasErrors(['phone' => ['numeric', 'digits_between:10,15']]);
    }

    #[Test]
    public function it_requires_permission_to_access()
    {
        // Arrange
        $user = User::factory()->create();
        $this->actingAs($user);

        // Act & Assert
        $this->get(route('settings.general'))
            ->assertStatus(403);
    }
}
