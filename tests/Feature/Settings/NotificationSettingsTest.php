<?php

namespace Tests\Feature\Settings;

use App\Livewire\Pages\Settings\NotificationSettings;
use App\Models\Users\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;

class NotificationSettingsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create permission
        Permission::create(['name' => 'manage_settings']);
        
        // Create and authenticate a user with permissions
        /** @var User $user */
        $user = User::factory()->create();
        $user->givePermissionTo('manage_settings');
        $this->actingAs($user);
    }

    #[Test]
    public function it_can_render_notification_settings_page()
    {
        Livewire::test(NotificationSettings::class)
            ->assertViewIs('livewire.pages.settings.notification-settings')
            ->assertSuccessful();
    }

    #[Test]
    public function it_can_update_email_settings()
    {
        $settings = [
            'emailEnabled' => true,
            'smtpHost' => 'smtp.mailtrap.io',
            'smtpPort' => '2525',
            'smtpUsername' => 'test_user',
            'smtpPassword' => 'test_pass',
            'smtpEncryption' => 'tls',
            'fromAddress' => '<EMAIL>',
            'fromName' => 'Test Pharmacy'
        ];

        $component = Livewire::test(NotificationSettings::class)
            ->set('emailEnabled', $settings['emailEnabled'])
            ->set('smtpHost', $settings['smtpHost'])
            ->set('smtpPort', $settings['smtpPort'])
            ->set('smtpUsername', $settings['smtpUsername'])
            ->set('smtpPassword', $settings['smtpPassword'])
            ->set('smtpEncryption', $settings['smtpEncryption'])
            ->set('fromAddress', $settings['fromAddress'])
            ->set('fromName', $settings['fromName'])
            ->call('save')
            ->assertHasNoErrors();

        foreach ($settings as $key => $value) {
            $dbKey = Str::snake($key);
            $isEncrypted = in_array($key, ['smtpUsername', 'smtpPassword']);
            
            $setting = DB::table('settings')
                ->where('key', $dbKey)
                ->where('group', 'notification')
                ->where('is_encrypted', $isEncrypted)
                ->first();

            $this->assertNotNull($setting, "Setting {$dbKey} not found");

            if ($isEncrypted) {
                $decryptedValue = unserialize(Crypt::decryptString($setting->value));
                $this->assertEquals($value, $decryptedValue, "Encrypted value for {$dbKey} does not match");
            } else {
                $this->assertEquals($value, $setting->value, "Value for {$dbKey} does not match");
            }
        }

        $component->assertSet('message', 'Settings saved successfully.');
    }

    #[Test]
    public function it_can_update_sms_settings()
    {
        $settings = [
            'smsEnabled' => true,
            'smsProvider' => 'twilio',
            'smsAccountSid' => 'test_sid',
            'smsAuthToken' => 'test_token',
            'smsFromNumber' => '+**********'
        ];

        $component = Livewire::test(NotificationSettings::class)
            ->set('smsEnabled', $settings['smsEnabled'])
            ->set('smsProvider', $settings['smsProvider'])
            ->set('smsAccountSid', $settings['smsAccountSid'])
            ->set('smsAuthToken', $settings['smsAuthToken'])
            ->set('smsFromNumber', $settings['smsFromNumber'])
            ->call('save')
            ->assertHasNoErrors();

        foreach ($settings as $key => $value) {
            $dbKey = Str::snake($key);
            $isEncrypted = in_array($key, ['smsAccountSid', 'smsAuthToken']);
            
            $setting = DB::table('settings')
                ->where('key', $dbKey)
                ->where('group', 'notification')
                ->where('is_encrypted', $isEncrypted)
                ->first();

            $this->assertNotNull($setting, "Setting {$dbKey} not found");

            if ($isEncrypted) {
                $decryptedValue = unserialize(Crypt::decryptString($setting->value));
                $this->assertEquals($value, $decryptedValue, "Encrypted value for {$dbKey} does not match");
            } else {
                $this->assertEquals($value, $setting->value, "Value for {$dbKey} does not match");
            }
        }

        $component->assertSet('message', 'Settings saved successfully.');
    }

    #[Test]
    public function it_validates_email_settings_when_enabled()
    {
        Livewire::test(NotificationSettings::class)
            ->set('emailEnabled', true)
            ->set('smtpHost', '')
            ->set('smtpPort', '')
            ->set('smtpUsername', '')
            ->set('smtpPassword', '')
            ->set('fromAddress', '')
            ->set('fromName', '')
            ->call('save')
            ->assertHasErrors([
                'smtpHost' => 'required',
                'smtpPort' => 'required',
                'smtpUsername' => 'required',
                'smtpPassword' => 'required',
                'fromAddress' => 'required',
                'fromName' => 'required'
            ]);
    }

    #[Test]
    public function it_validates_sms_settings_when_enabled()
    {
        Livewire::test(NotificationSettings::class)
            ->set('smsEnabled', true)
            ->set('smsProvider', '')
            ->set('smsAccountSid', '')
            ->set('smsAuthToken', '')
            ->set('smsFromNumber', '')
            ->call('save')
            ->assertHasErrors([
                'smsProvider' => 'required',
                'smsAccountSid' => 'required',
                'smsAuthToken' => 'required',
                'smsFromNumber' => 'required'
            ]);
    }

    #[Test]
    public function it_validates_email_format()
    {
        Livewire::test(NotificationSettings::class)
            ->set('emailEnabled', true)
            ->set('fromAddress', 'invalid-email')
            ->call('save')
            ->assertHasErrors(['fromAddress' => 'email']);
    }

    #[Test]
    public function it_validates_sms_from_number_format()
    {
        Livewire::test(NotificationSettings::class)
            ->set('smsEnabled', true)
            ->set('smsFromNumber', 'invalid-number')
            ->call('save')
            ->assertHasErrors(['smsFromNumber' => 'regex']);
    }

    #[Test]
    public function it_requires_permission_to_access()
    {
        // Arrange
        /** @var User $user */
        $user = User::factory()->create();
        $this->actingAs($user);

        // Act & Assert
        $this->get(route('settings.notifications'))
            ->assertStatus(403);
    }
}
