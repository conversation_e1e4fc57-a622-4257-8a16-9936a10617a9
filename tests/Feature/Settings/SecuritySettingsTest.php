<?php

namespace Tests\Feature\Settings;

use App\Livewire\Pages\Settings\SecuritySettings;
use App\Models\Users\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class SecuritySettingsTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        Permission::create(['name' => 'manage_settings']);
        $this->user = User::factory()->create();
        $this->user->givePermissionTo('manage_settings');
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_can_render_security_settings_page()
    {
        $this->actingAs($this->user);

        $response = Livewire::test(SecuritySettings::class);
        $response->assertStatus(200);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_can_update_password_settings()
    {
        $this->actingAs($this->user);

        Livewire::test(SecuritySettings::class)
            ->set('minimumPasswordLength', 10)
            ->set('requireUppercase', true)
            ->set('requireNumbers', true)
            ->set('requireSpecialCharacters', true)
            ->set('passwordExpiryDays', 60)
            ->call('save')
            ->assertHasNoErrors()
            ->assertSet('minimumPasswordLength', 10)
            ->assertSet('passwordExpiryDays', 60);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_can_update_session_settings()
    {
        $this->actingAs($this->user);

        Livewire::test(SecuritySettings::class)
            ->set('sessionTimeout', 60)
            ->set('maxLoginAttempts', 3)
            ->set('lockoutDuration', 60)
            ->call('save')
            ->assertHasNoErrors()
            ->assertSet('sessionTimeout', 60)
            ->assertSet('maxLoginAttempts', 3)
            ->assertSet('lockoutDuration', 60);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_can_update_two_factor_settings()
    {
        $this->actingAs($this->user);

        Livewire::test(SecuritySettings::class)
            ->set('twoFactorEnabled', true)
            ->set('twoFactorMethod', 'email')
            ->set('twoFactorEnforced', true)
            ->set('twoFactorGracePeriod', 14)
            ->call('save')
            ->assertHasNoErrors()
            ->assertSet('twoFactorEnabled', true)
            ->assertSet('twoFactorMethod', 'email')
            ->assertSet('twoFactorGracePeriod', 14);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_can_update_ip_security_settings()
    {
        $this->actingAs($this->user);

        Livewire::test(SecuritySettings::class)
            ->set('ipWhitelisting', true)
            ->set('allowedIPs', ['***********', '********'])
            ->set('blockTorNetwork', true)
            ->set('blockVPNs', true)
            ->call('save')
            ->assertHasNoErrors()
            ->assertSet('ipWhitelisting', true)
            ->assertSet('blockTorNetwork', true)
            ->assertSet('blockVPNs', true);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_validates_password_length()
    {
        $this->actingAs($this->user);

        Livewire::test(SecuritySettings::class)
            ->set('minimumPasswordLength', 6)
            ->call('save')
            ->assertHasErrors(['minimumPasswordLength']);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_validates_session_timeout()
    {
        $this->actingAs($this->user);

        Livewire::test(SecuritySettings::class)
            ->set('sessionTimeout', 3)
            ->call('save')
            ->assertHasErrors(['sessionTimeout']);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_validates_ip_addresses()
    {
        $this->actingAs($this->user);

        Livewire::test(SecuritySettings::class)
            ->set('ipWhitelisting', true)
            ->set('allowedIPs', ['invalid-ip'])
            ->call('save')
            ->assertHasErrors(['allowedIPs.*']);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_requires_permission_to_access()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        Livewire::test(SecuritySettings::class)
            ->assertStatus(403);
    }
}
