<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Customers\Customer;
use App\Models\Customers\LoyaltyTransaction;
use App\Models\Sales\Sale;
use App\Models\Users\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;

class CustomerSyncIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
    }

    #[Test]
    public function it_can_sync_complete_customer_data_for_offline_sales()
    {
        // Create a customer with comprehensive data
        $customer = Customer::create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'address' => '123 Main St, City, State 12345',
            'date_of_birth' => '1985-06-15',
            'gender' => 'male',
            'status' => 'active',
            'loyalty_points' => 1250,
            'total_spent' => 2500.00,
            'preferred_contact_method' => 'email',
            'notes' => 'VIP customer, prefers morning appointments',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        // Create loyalty transactions
        LoyaltyTransaction::create([
            'customer_id' => $customer->id,
            'type' => 'earned',
            'points' => 100,
            'description' => 'Purchase reward',
            'created_by' => $this->user->id,
        ]);

        LoyaltyTransaction::create([
            'customer_id' => $customer->id,
            'type' => 'redeemed',
            'points' => 50,
            'description' => 'Discount applied',
            'created_by' => $this->user->id,
        ]);

        // Test the comprehensive customer API endpoint
        $response = $this->getJson("/api/offline/customers/{$customer->id}");
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'email',
                'phone',
                'address',
                'date_of_birth',
                'gender',
                'status',
                'loyalty_points',
                'total_spent',
                'preferred_contact_method',
                'notes',
                'loyalty_tier',
                'points_to_next_tier',
                'total_points_earned',
                'total_points_redeemed',
                'loyalty_transactions' => [
                    '*' => [
                        'id',
                        'type',
                        'points',
                        'description',
                        'created_at'
                    ]
                ],
                'sales'
            ],
            'meta' => [
                'timestamp'
            ]
        ]);
        
        $customerData = $response->json('data');
        
        // Verify all essential data for offline sales is present
        $this->assertEquals('John Doe', $customerData['name']);
        $this->assertEquals('<EMAIL>', $customerData['email']);
        $this->assertEquals('+1234567890', $customerData['phone']);
        $this->assertEquals(1250, $customerData['loyalty_points']);
        $this->assertEquals(2500.00, $customerData['total_spent']);
        
        // Verify computed loyalty attributes
        $this->assertArrayHasKey('loyalty_tier', $customerData);
        $this->assertArrayHasKey('points_to_next_tier', $customerData);
        $this->assertArrayHasKey('total_points_earned', $customerData);
        $this->assertArrayHasKey('total_points_redeemed', $customerData);
        
        // Verify loyalty transactions
        $this->assertCount(2, $customerData['loyalty_transactions']);
        $this->assertEquals('earned', $customerData['loyalty_transactions'][0]['type']);
        $this->assertEquals(100, $customerData['loyalty_transactions'][0]['points']);
    }

    #[Test]
    public function it_provides_paginated_customers_for_bulk_sync()
    {
        // Create multiple customers
        for ($i = 1; $i <= 25; $i++) {
            Customer::create([
                'name' => "Customer {$i}",
                'email' => "customer{$i}@example.com",
                'phone' => "+123456789{$i}",
                'status' => 'active',
                'loyalty_points' => $i * 50,
                'total_spent' => $i * 100.00,
                'created_by' => $this->user->id,
                'updated_by' => $this->user->id,
            ]);
        }
        
        // Test pagination
        $response = $this->getJson('/api/offline/customers?limit=10&offset=0');
        $response->assertStatus(200);
        
        $data = $response->json();
        $this->assertEquals(10, $data['meta']['count']);
        $this->assertEquals(10, $data['meta']['limit']);
        $this->assertEquals(0, $data['meta']['offset']);
        $this->assertEquals(25, $data['meta']['total']);
        $this->assertTrue($data['meta']['has_more']);
        
        // Test second page
        $response = $this->getJson('/api/offline/customers?limit=10&offset=10');
        $response->assertStatus(200);
        
        $data = $response->json();
        $this->assertEquals(10, $data['meta']['count']);
        $this->assertEquals(10, $data['meta']['limit']);
        $this->assertEquals(10, $data['meta']['offset']);
        $this->assertTrue($data['meta']['has_more']);
        
        // Test last page
        $response = $this->getJson('/api/offline/customers?limit=10&offset=20');
        $response->assertStatus(200);
        
        $data = $response->json();
        $this->assertEquals(5, $data['meta']['count']); // Remaining 5 customers
        $this->assertEquals(10, $data['meta']['limit']);
        $this->assertEquals(20, $data['meta']['offset']);
        $this->assertFalse($data['meta']['has_more']);
    }

    #[Test]
    public function it_supports_incremental_sync_for_updated_customers()
    {
        // Create a customer
        $customer = Customer::create([
            'name' => 'Original Customer',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'status' => 'active',
            'loyalty_points' => 100,
            'total_spent' => 200.00,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);
        
        $originalTimestamp = $customer->updated_at;
        
        // Wait a moment and update the customer
        sleep(1);
        $customer->update([
            'name' => 'Updated Customer',
            'loyalty_points' => 150,
            'total_spent' => 300.00,
        ]);
        
        // Test incremental sync - should only return updated customer
        $response = $this->getJson('/api/offline/customers?since=' . urlencode($originalTimestamp->toISOString()));
        $response->assertStatus(200);
        
        $data = $response->json();
        $this->assertEquals(1, $data['meta']['count']);
        $this->assertEquals('Updated Customer', $data['data'][0]['name']);
        $this->assertEquals(150, $data['data'][0]['loyalty_points']);
        $this->assertEquals(300.00, $data['data'][0]['total_spent']);
    }

    #[Test]
    public function it_handles_customers_with_complex_loyalty_scenarios()
    {
        // Create a customer with multiple loyalty transactions
        $customer = Customer::create([
            'name' => 'Loyalty Customer',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'status' => 'active',
            'loyalty_points' => 500,
            'total_spent' => 1000.00,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);
        
        // Create various loyalty transactions
        $transactions = [
            ['type' => 'earned', 'points' => 100, 'description' => 'Welcome bonus'],
            ['type' => 'earned', 'points' => 200, 'description' => 'Purchase reward'],
            ['type' => 'earned', 'points' => 150, 'description' => 'Birthday bonus'],
            ['type' => 'redeemed', 'points' => 75, 'description' => 'Discount applied'],
            ['type' => 'expired', 'points' => 25, 'description' => 'Points expired'],
        ];
        
        foreach ($transactions as $transaction) {
            LoyaltyTransaction::create([
                'customer_id' => $customer->id,
                'type' => $transaction['type'],
                'points' => $transaction['points'],
                'description' => $transaction['description'],
                'created_by' => $this->user->id,
            ]);
        }
        
        $response = $this->getJson("/api/offline/customers/{$customer->id}");
        $response->assertStatus(200);
        
        $customerData = $response->json('data');
        
        // Should include all loyalty transactions
        $this->assertCount(5, $customerData['loyalty_transactions']);
        
        // Verify transaction types are included
        $transactionTypes = array_column($customerData['loyalty_transactions'], 'type');
        $this->assertContains('earned', $transactionTypes);
        $this->assertContains('redeemed', $transactionTypes);
        $this->assertContains('expired', $transactionTypes);
    }

    #[Test]
    public function it_handles_customer_search_functionality()
    {
        // Create customers with different attributes
        $customers = [
            ['name' => 'Alice Johnson', 'email' => '<EMAIL>', 'phone' => '+1111111111'],
            ['name' => 'Bob Smith', 'email' => '<EMAIL>', 'phone' => '+2222222222'],
            ['name' => 'Charlie Brown', 'email' => '<EMAIL>', 'phone' => '+3333333333'],
            ['name' => 'Diana Prince', 'email' => '<EMAIL>', 'phone' => '+4444444444'],
        ];
        
        foreach ($customers as $customerData) {
            Customer::create([
                'name' => $customerData['name'],
                'email' => $customerData['email'],
                'phone' => $customerData['phone'],
                'status' => 'active',
                'loyalty_points' => 100,
                'created_by' => $this->user->id,
                'updated_by' => $this->user->id,
            ]);
        }
        
        // Search by name
        $response = $this->getJson('/api/offline/customers?search=Alice');
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertEquals(1, $data['meta']['count']);
        $this->assertEquals('Alice Johnson', $data['data'][0]['name']);
        
        // Search by email
        $response = $this->getJson('/api/offline/customers?search=<EMAIL>');
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertEquals(1, $data['meta']['count']);
        $this->assertEquals('Bob Smith', $data['data'][0]['name']);
        
        // Search by phone
        $response = $this->getJson('/api/offline/customers?search=+3333333333');
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertEquals(1, $data['meta']['count']);
        $this->assertEquals('Charlie Brown', $data['data'][0]['name']);
        
        // Partial search
        $response = $this->getJson('/api/offline/customers?search=Prin');
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertEquals(1, $data['meta']['count']);
        $this->assertEquals('Diana Prince', $data['data'][0]['name']);
    }

    #[Test]
    public function it_provides_comprehensive_sync_management()
    {
        // Create test customers
        for ($i = 1; $i <= 10; $i++) {
            Customer::create([
                'name' => "Sync Customer {$i}",
                'email' => "sync{$i}@example.com",
                'phone' => "+123456789{$i}",
                'status' => 'active',
                'loyalty_points' => $i * 25,
                'created_by' => $this->user->id,
                'updated_by' => $this->user->id,
            ]);
        }
        
        // Test sync status
        $statusResponse = $this->getJson('/api/offline/customers/sync-status');
        $statusResponse->assertStatus(200);
        $statusData = $statusResponse->json();
        $this->assertEquals(10, $statusData['total_customers']);
        $this->assertTrue($statusData['sync_enabled']);
        
        // Test sync trigger
        $triggerResponse = $this->postJson('/api/offline/customers/sync-trigger');
        $triggerResponse->assertStatus(200);
        $syncId = $triggerResponse->json('sync_id');
        $this->assertEquals(10, $triggerResponse->json('customers_to_sync'));
        
        // Test sync progress
        $progressResponse = $this->getJson("/api/offline/customers/sync-progress/{$syncId}");
        $progressResponse->assertStatus(200);
        $progressData = $progressResponse->json();
        $this->assertEquals($syncId, $progressData['sync_id']);
        $this->assertContains($progressData['status'], ['running', 'completed']);
        
        // Test sync cancellation
        $cancelResponse = $this->deleteJson("/api/offline/customers/sync-cancel/{$syncId}");
        $cancelResponse->assertStatus(200);
        $this->assertEquals('cancelled', $cancelResponse->json('status'));
    }

    #[Test]
    public function it_handles_customers_without_loyalty_data_gracefully()
    {
        // Create a customer without loyalty transactions
        $customer = Customer::create([
            'name' => 'Simple Customer',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'status' => 'active',
            'loyalty_points' => 0,
            'total_spent' => 0.00,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);
        
        $response = $this->getJson("/api/offline/customers/{$customer->id}");
        $response->assertStatus(200);
        
        $customerData = $response->json('data');
        
        // Should have empty arrays for related data
        $this->assertIsArray($customerData['loyalty_transactions']);
        $this->assertCount(0, $customerData['loyalty_transactions']);
        $this->assertIsArray($customerData['sales']);
        $this->assertCount(0, $customerData['sales']);
        
        // But should still have computed loyalty attributes
        $this->assertArrayHasKey('loyalty_tier', $customerData);
        $this->assertArrayHasKey('points_to_next_tier', $customerData);
        $this->assertEquals(0, $customerData['loyalty_points']);
    }

    #[Test]
    public function it_maintains_data_consistency_across_sync_operations()
    {
        // Create initial customers
        $customer1 = Customer::create([
            'name' => 'Consistency Test 1',
            'email' => '<EMAIL>',
            'phone' => '+1111111111',
            'status' => 'active',
            'loyalty_points' => 100,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);
        
        $customer2 = Customer::create([
            'name' => 'Consistency Test 2',
            'email' => '<EMAIL>',
            'phone' => '+2222222222',
            'status' => 'active',
            'loyalty_points' => 200,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);
        
        // Get initial data
        $initialResponse = $this->getJson('/api/offline/customers');
        $initialData = $initialResponse->json();
        $initialCount = $initialData['meta']['total'];
        
        // Trigger sync
        $syncResponse = $this->postJson('/api/offline/customers/sync-trigger');
        $syncResponse->assertStatus(200);
        
        // Get data after sync trigger
        $afterSyncResponse = $this->getJson('/api/offline/customers');
        $afterSyncData = $afterSyncResponse->json();
        
        // Data should remain consistent
        $this->assertEquals($initialCount, $afterSyncData['meta']['total']);
        $this->assertEquals($initialData['data'][0]['id'], $afterSyncData['data'][0]['id']);
        $this->assertEquals($initialData['data'][0]['name'], $afterSyncData['data'][0]['name']);
    }
}
