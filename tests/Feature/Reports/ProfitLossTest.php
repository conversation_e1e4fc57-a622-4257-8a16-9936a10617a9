<?php

namespace Tests\Feature\Reports;

use Tests\TestCase;
use App\Models\Users\User;
use App\Models\Sales\Sale;
use App\Models\Sales\SaleItem;
use App\Models\Inventory\Medicine;
use App\Models\Inventory\Purchase;
use App\Models\Inventory\PurchaseItem;
use App\Models\Inventory\ProfitLoss;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Spatie\Permission\Models\Permission;
use PHPUnit\Framework\Attributes\Test;
use Illuminate\Support\Facades\Log;

class ProfitLossTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $medicine;
    protected $sale;
    protected $purchase;
    protected $supplier;

    protected function setUp(): void
    {
        parent::setUp();

        // Create required permissions
        Permission::create(['name' => 'view reports']);
        Permission::create(['name' => 'manage reports']);

        // Create test user with necessary permissions
        $this->user = User::factory()->create();
        $this->user->givePermissionTo('view reports');

        // Create test category
        $category = \App\Models\Inventory\Category::factory()->create([
            'name' => 'Test Category',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id
        ]);

        // Create test manufacturer
        $manufacturer = \App\Models\Inventory\Manufacturer::factory()->create([
            'name' => 'Test Manufacturer',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'address' => '123 Test St',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id
        ]);

        // Create test unit type
        $unitType = \App\Models\Inventory\UnitType::factory()->create([
            'name' => 'Test Unit',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id
        ]);

        // Create test medicine
        $this->medicine = Medicine::factory()->create([
            'name' => 'Test Medicine',
            'unit_price' => 100,
            'supplier_price_unit' => 80,
            'manufacturer_id' => $manufacturer->id,
            'category_id' => $category->id,
            'unit_type_id' => $unitType->id,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id
        ]);

        // Create test supplier
        $this->supplier = \App\Models\Inventory\Supplier::factory()->create([
            'name' => 'Test Supplier',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'address' => '123 Test St',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id
        ]);

        // Create test sale
        $this->sale = Sale::create([
            'invoice_number' => 'INV-' . $this->faker->unique()->randomNumber(6),
            'total_amount' => 220,
            'discount_amount' => 20,
            'tax_amount' => 20,
            'payment_method' => 'cash',
            'payment_status' => 'paid',
            'created_by' => $this->user->id
        ]);

        // Create sale item
        SaleItem::create([
            'sale_id' => $this->sale->id,
            'medicine_id' => $this->medicine->id,
            'quantity' => 2,
            'unit_price' => 100,
            'batch_number' => 'BATCH-001',
            'discount' => 0,
            'tax_rate' => 0
        ]);

        // Create test purchase
        $this->purchase = Purchase::create([
            'purchase_number' => 'PO-' . $this->faker->unique()->randomNumber(6),
            'supplier_id' => $this->supplier->id,
            'total_amount' => 160,
            'shipping_cost' => 10,
            'final_amount' => 170,
            'status' => 'received',
            'payment_status' => 'paid',
            'order_date' => now(),
            'expected_date' => now()->addDays(7),
            'created_by' => $this->user->id
        ]);

        // Create purchase item
        PurchaseItem::create([
            'purchase_id' => $this->purchase->id,
            'medicine_id' => $this->medicine->id,
            'quantity' => 2,
            'unit_price' => 80,
            'total_amount' => 160,
            'tax_percentage' => 0,
            'tax_amount' => 0,
            'discount_percentage' => 0,
            'discount_amount' => 0
        ]);

        // Create profit loss record for sale
        ProfitLoss::create([
            'sale_id' => $this->sale->id,
            'medicine_id' => $this->medicine->id,
            'reference_number' => $this->sale->invoice_number,
            'transaction_type' => 'sale',
            'quantity' => 2,
            'unit_cost' => 80,
            'unit_price' => 100,
            'total_cost' => 160,
            'total_revenue' => 200,
            'discount_amount' => 20,
            'tax_amount' => 20,
            'gross_profit' => 40,
            'net_profit' => 20,
            'profit_margin' => 10,
            'transaction_date' => now(),
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id
        ]);

        // Create profit loss record for purchase
        ProfitLoss::create([
            'purchase_id' => $this->purchase->id,
            'medicine_id' => $this->medicine->id,
            'reference_number' => $this->purchase->purchase_number,
            'transaction_type' => 'purchase',
            'quantity' => 2,
            'unit_cost' => 80,
            'unit_price' => 0,
            'total_cost' => 160,
            'total_revenue' => 0,
            'shipping_cost' => 5,
            'transaction_date' => now(),
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id
        ]);
    }

    #[Test]
    public function it_requires_authentication_to_view_profit_loss_report()
    {
        $response = $this->get(route('reports.profit-loss'));
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function it_requires_permission_to_view_profit_loss_report()
    {
        /** @var User */
        $user = User::factory()->create();
        $this->actingAs($user);

        $response = $this->get(route('reports.profit-loss'));
        $response->assertStatus(403);
    }

    #[Test]
    public function it_displays_profit_loss_report_page()
    {
        /** @var User */
        $user = User::factory()->create();
        $user->givePermissionTo('view reports');
        $this->actingAs($user);

        $response = $this->get(route('reports.profit-loss'));
        $response->assertStatus(200)
            ->assertViewIs('reports.profit_loss')
            ->assertSeeInOrder([
                'Profit',
                'Loss Report',
                'Total Revenue',
                'Total Cost',
                'Gross Profit',
                'Net Profit'
            ]);
    }

    #[Test]
    public function it_calculates_correct_profit_loss_for_sales()
    {
        // Create a new sale
        $sale = Sale::create([
            'invoice_number' => 'INV-' . $this->faker->unique()->randomNumber(6),
            'total_amount' => 220,
            'discount_amount' => 20,
            'tax_amount' => 20,
            'payment_method' => 'cash',
            'payment_status' => 'paid',
            'created_by' => $this->user->id
        ]);

        // Create sale item
        SaleItem::create([
            'sale_id' => $sale->id,
            'medicine_id' => $this->medicine->id,
            'quantity' => 2,
            'unit_price' => 100,
            'batch_number' => 'BATCH-002',
            'discount' => 0,
            'tax_rate' => 0
        ]);

        // Create profit loss record
        $profitLoss = ProfitLoss::create([
            'sale_id' => $sale->id,
            'medicine_id' => $this->medicine->id,
            'reference_number' => $sale->invoice_number,
            'transaction_type' => 'sale',
            'quantity' => 2,
            'unit_cost' => 80,
            'unit_price' => 100,
            'total_cost' => 160,
            'total_revenue' => 200,
            'discount_amount' => 20,
            'tax_amount' => 20,
            'gross_profit' => 40,
            'net_profit' => 20,
            'profit_margin' => 10,
            'transaction_date' => now(),
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id
        ]);

        $this->assertNotNull($profitLoss);
        $this->assertEquals(200, $profitLoss->total_revenue); // 2 * 100
        $this->assertEquals(160, $profitLoss->total_cost); // 2 * 80
        $this->assertEquals(40, $profitLoss->gross_profit); // 200 - 160
        $this->assertEquals(20, $profitLoss->net_profit); // 40 - 20 (discount)
        $this->assertEquals(10, $profitLoss->profit_margin); // (20/200) * 100
    }

    #[Test]
    public function it_records_purchase_costs_correctly()
    {
        // Create a new purchase
        $purchase = Purchase::create([
            'purchase_number' => 'PO-' . $this->faker->unique()->randomNumber(6),
            'supplier_id' => $this->supplier->id,
            'total_amount' => 160,
            'shipping_cost' => 10,
            'final_amount' => 170,
            'status' => 'received',
            'payment_status' => 'paid',
            'order_date' => now(),
            'expected_date' => now()->addDays(7),
            'created_by' => $this->user->id
        ]);

        // Create purchase item
        PurchaseItem::create([
            'purchase_id' => $purchase->id,
            'medicine_id' => $this->medicine->id,
            'quantity' => 2,
            'unit_price' => 80,
            'total_amount' => 160,
            'tax_percentage' => 0,
            'tax_amount' => 0,
            'discount_percentage' => 0,
            'discount_amount' => 0
        ]);

        // Create profit loss record
        $profitLoss = ProfitLoss::create([
            'purchase_id' => $purchase->id,
            'medicine_id' => $this->medicine->id,
            'reference_number' => $purchase->purchase_number,
            'transaction_type' => 'purchase',
            'quantity' => 2,
            'unit_cost' => 80,
            'unit_price' => 0,
            'total_cost' => 160,
            'total_revenue' => 0,
            'shipping_cost' => 5,
            'transaction_date' => now(),
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id
        ]);

        $this->assertNotNull($profitLoss);
        $this->assertEquals(160, $profitLoss->total_cost); // 2 * 80
        $this->assertEquals(5, $profitLoss->shipping_cost); // 10/2 per item
        $this->assertEquals('purchase', $profitLoss->transaction_type);
    }

    #[Test]
    public function it_filters_profit_loss_by_date_range()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('reports.profit-loss', [
            'start_date' => now()->subDays(7),
            'end_date' => now()->addDays(7)
        ]));

        $response->assertStatus(200)
            ->assertViewIs('reports.profit_loss')
            ->assertViewHas('transactions')
            ->assertViewHas('totalRevenue')
            ->assertViewHas('totalCost')
            ->assertViewHas('grossProfit')
            ->assertViewHas('netProfit');

        $data = $response->viewData('transactions');
        $this->assertEquals(2, $data->count()); // 1 sale + 1 purchase
    }

    #[Test]
    public function it_updates_profit_loss_when_sale_is_modified()
    {
        // Create a new sale
        $sale = Sale::create([
            'invoice_number' => 'INV-' . $this->faker->unique()->randomNumber(6),
            'total_amount' => 220,
            'discount_amount' => 30, // Higher discount
            'tax_amount' => 20,
            'payment_method' => 'cash',
            'payment_status' => 'paid',
            'created_by' => $this->user->id
        ]);

        // Create sale item
        SaleItem::create([
            'sale_id' => $sale->id,
            'medicine_id' => $this->medicine->id,
            'quantity' => 2,
            'unit_price' => 100,
            'batch_number' => 'BATCH-002',
            'discount' => 0,
            'tax_rate' => 0
        ]);

        // Create profit loss record
        $profitLoss = ProfitLoss::create([
            'sale_id' => $sale->id,
            'medicine_id' => $this->medicine->id,
            'reference_number' => $sale->invoice_number,
            'transaction_type' => 'sale',
            'quantity' => 2,
            'unit_cost' => 80,
            'unit_price' => 100,
            'total_cost' => 160,
            'total_revenue' => 200,
            'discount_amount' => 30,
            'tax_amount' => 20,
            'gross_profit' => 40,
            'net_profit' => 10,
            'profit_margin' => 5,
            'transaction_date' => now(),
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id
        ]);

        $this->assertEquals(200, $profitLoss->total_revenue);
        $this->assertEquals(160, $profitLoss->total_cost);
        $this->assertEquals(40, $profitLoss->gross_profit);
        $this->assertEquals(10, $profitLoss->net_profit); // 40 - 30 (new discount)
        $this->assertEquals(5, $profitLoss->profit_margin); // (10/200) * 100
    }

    #[Test]
    public function it_deletes_profit_loss_records_when_transaction_is_deleted()
    {
        // Create a new sale
        $sale = Sale::create([
            'invoice_number' => 'INV-' . $this->faker->unique()->randomNumber(6),
            'total_amount' => 220,
            'discount_amount' => 20,
            'tax_amount' => 20,
            'payment_method' => 'cash',
            'payment_status' => 'paid',
            'created_by' => $this->user->id
        ]);

        // Create sale item
        SaleItem::create([
            'sale_id' => $sale->id,
            'medicine_id' => $this->medicine->id,
            'quantity' => 2,
            'unit_price' => 100,
            'batch_number' => 'BATCH-002',
            'discount' => 0,
            'tax_rate' => 0
        ]);

        // Create profit loss record
        $profitLoss = ProfitLoss::create([
            'sale_id' => $sale->id,
            'medicine_id' => $this->medicine->id,
            'reference_number' => $sale->invoice_number,
            'transaction_type' => 'sale',
            'quantity' => 2,
            'unit_cost' => 80,
            'unit_price' => 100,
            'total_cost' => 160,
            'total_revenue' => 200,
            'discount_amount' => 20,
            'tax_amount' => 20,
            'gross_profit' => 40,
            'net_profit' => 20,
            'profit_margin' => 10,
            'transaction_date' => now(),
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id
        ]);

        $profitLossId = $profitLoss->id;

        $sale->delete();

        $this->assertNull(ProfitLoss::find($profitLossId));
    }

    #[Test]
    public function it_paginates_profit_loss_transactions()
    {
        // Create 25 sales with profit loss records
        for ($i = 0; $i < 25; $i++) {
            $sale = Sale::create([
                'invoice_number' => 'INV-' . $this->faker->unique()->randomNumber(6),
                'total_amount' => 220,
                'discount_amount' => 20,
                'tax_amount' => 20,
                'payment_method' => 'cash',
                'payment_status' => 'paid',
                'created_by' => $this->user->id
            ]);

            SaleItem::create([
                'sale_id' => $sale->id,
                'medicine_id' => $this->medicine->id,
                'quantity' => 2,
                'unit_price' => 100,
                'batch_number' => 'BATCH-' . str_pad($i + 1, 3, '0', STR_PAD_LEFT),
                'discount' => 0,
                'tax_rate' => 0
            ]);

            ProfitLoss::create([
                'sale_id' => $sale->id,
                'medicine_id' => $this->medicine->id,
                'reference_number' => $sale->invoice_number,
                'transaction_type' => 'sale',
                'quantity' => 2,
                'unit_cost' => 80,
                'unit_price' => 100,
                'total_cost' => 160,
                'total_revenue' => 200,
                'discount_amount' => 20,
                'tax_amount' => 20,
                'gross_profit' => 40,
                'net_profit' => 20,
                'profit_margin' => 10,
                'transaction_date' => now(),
                'created_by' => $this->user->id,
                'updated_by' => $this->user->id
            ]);
        }

        $this->actingAs($this->user);

        $response = $this->get(route('reports.profit-loss'));
        $response->assertStatus(200);

        $data = $response->viewData('transactions');
        $this->assertEquals(20, $data->count()); // Default pagination is 20 items per page
        $this->assertTrue($data->hasMorePages());
    }
} 