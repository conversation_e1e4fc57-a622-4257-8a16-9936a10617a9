<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Inventory\Medicine;
use App\Models\Inventory\Category;
use App\Models\Inventory\Manufacturer;
use App\Models\Inventory\Inventory;
use App\Models\Inventory\Location;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class OfflineMedicineDetailTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->category = Category::factory()->create(['name' => 'Test Category']);
        $this->manufacturer = Manufacturer::factory()->create(['name' => 'Test Manufacturer']);
        $this->location = Location::factory()->create(['name' => 'Main Store']);
        
        $this->medicine = Medicine::factory()->create([
            'name' => 'Test Medicine',
            'generic_name' => 'Test Generic',
            'dosage' => '500mg',
            'category_id' => $this->category->id,
            'manufacturer_id' => $this->manufacturer->id,
            'retail_price_unit' => 10.50,
            'retail_price_strip' => 105.00,
            'retail_price_box' => 1050.00,
            'supplier_price_unit' => 8.00,
            'prescription_required' => true,
            'controlled_substance' => false,
        ]);
        
        // Create inventory batches
        Inventory::factory()->create([
            'medicine_id' => $this->medicine->id,
            'location_id' => $this->location->id,
            'batch_number' => 'BATCH001',
            'quantity' => 100,
            'unit_price' => 8.00,
            'expiry_date' => now()->addMonths(6),
        ]);
        
        Inventory::factory()->create([
            'medicine_id' => $this->medicine->id,
            'location_id' => $this->location->id,
            'batch_number' => 'BATCH002',
            'quantity' => 50,
            'unit_price' => 8.50,
            'expiry_date' => now()->addMonths(12),
        ]);
    }

    /** @test */
    public function it_displays_medicine_detail_page_with_offline_functionality()
    {
        $response = $this->get(route('inventory.medicines.show', $this->medicine->id));
        
        $response->assertStatus(200);
        $response->assertSee($this->medicine->name);
        $response->assertSee($this->medicine->generic_name);
        $response->assertSee($this->category->name);
        $response->assertSee($this->manufacturer->name);
        
        // Check for offline functionality elements
        $response->assertSee('medicineDetailOffline');
        $response->assertSee('Viewing Offline Data');
        $response->assertSee('Data May Be Outdated');
        $response->assertSee('Edit Medicine (Offline)');
        $response->assertSee('View History (Offline)');
    }

    /** @test */
    public function it_provides_offline_api_endpoint_for_medicine_details()
    {
        $response = $this->getJson("/api/offline/medicines/{$this->medicine->id}");
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'generic_name',
                'dosage',
                'category',
                'manufacturer',
                'retail_price_unit',
                'retail_price_strip',
                'retail_price_box',
                'supplier_price_unit',
                'prescription_required',
                'controlled_substance',
                'inventories' => [
                    '*' => [
                        'id',
                        'batch_number',
                        'quantity',
                        'unit_price',
                        'expiry_date',
                        'location'
                    ]
                ]
            ],
            'meta' => [
                'timestamp'
            ]
        ]);
        
        $responseData = $response->json('data');
        $this->assertEquals($this->medicine->name, $responseData['name']);
        $this->assertEquals($this->medicine->generic_name, $responseData['generic_name']);
        $this->assertEquals($this->category->name, $responseData['category']['name']);
        $this->assertEquals($this->manufacturer->name, $responseData['manufacturer']['name']);
        $this->assertCount(2, $responseData['inventories']);
    }

    /** @test */
    public function it_returns_404_for_non_existent_medicine_in_offline_api()
    {
        $response = $this->getJson('/api/offline/medicines/99999');
        
        $response->assertStatus(404);
        $response->assertJson(['error' => 'Medicine not found']);
    }

    /** @test */
    public function it_shows_proper_stock_levels_and_expiry_information()
    {
        $response = $this->get(route('inventory.medicines.show', $this->medicine->id));
        
        $response->assertStatus(200);
        
        // Check for batch information
        $response->assertSee('BATCH001');
        $response->assertSee('BATCH002');
        $response->assertSee('100'); // quantity
        $response->assertSee('50');  // quantity
        
        // Check for total stock (should be 150)
        $response->assertSee('150 units');
        
        // Check for location information
        $response->assertSee($this->location->name);
    }

    /** @test */
    public function it_handles_medicines_without_batches()
    {
        // Create a medicine without inventory
        $medicineWithoutStock = Medicine::factory()->create([
            'name' => 'No Stock Medicine',
            'category_id' => $this->category->id,
            'manufacturer_id' => $this->manufacturer->id,
        ]);
        
        $response = $this->get(route('inventory.medicines.show', $medicineWithoutStock->id));
        
        $response->assertStatus(200);
        $response->assertSee('No Stock Medicine');
        $response->assertSee('No batches found');
        $response->assertSee('0 units'); // total stock should be 0
    }

    /** @test */
    public function it_shows_prescription_and_controlled_substance_status()
    {
        $response = $this->get(route('inventory.medicines.show', $this->medicine->id));
        
        $response->assertStatus(200);
        
        // Check prescription required status (should show "Yes")
        $response->assertSee('Prescription Required');
        
        // Check controlled substance status (should show "No")
        $response->assertSee('Controlled Substance');
    }
}
