<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Inventory\Medicine;
use App\Models\Inventory\Category;
use App\Models\Inventory\Manufacturer;
use App\Models\Inventory\Inventory;
use App\Models\Inventory\Location;
use App\Models\Users\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;

class MedicineSyncIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        
        // Create test data
        $this->category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'is_active' => true,
        ]);
        
        $this->manufacturer = Manufacturer::create([
            'name' => 'Test Manufacturer',
            'slug' => 'test-manufacturer',
            'is_active' => true,
        ]);
        
        // Skip location creation for now due to model complexity
        $this->location = null;
    }

    #[Test]
    public function it_can_sync_complete_medicine_data_for_offline_sales()
    {
        // Create a medicine with comprehensive data (only using fillable fields)
        $medicine = Medicine::create([
            'name' => 'Test Medicine',
            'generic_name' => 'Test Generic',
            'category_id' => $this->category->id,
            'manufacturer_id' => $this->manufacturer->id,
            'unit_type' => 'tablet',
            'status' => 'active',
            'retail_price_unit' => 10.50,
            'retail_price_strip' => 105.00,
            'retail_price_box' => 1050.00,
            'supplier_price_unit' => 8.00,
            'supplier_price_strip' => 80.00,
            'supplier_price_box' => 800.00,
            'prescription_required' => true,
            'controlled_substance' => false,
            'minimum_stock' => 10,
            'maximum_stock' => 1000,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);
        
        // Skip inventory creation for now due to location dependency
        // Focus on testing medicine data sync without inventory
        
        // Test the comprehensive medicine API endpoint
        $response = $this->getJson("/api/offline/medicines/{$medicine->id}");
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'generic_name',
                'name',
                'category' => [
                    'id',
                    'name'
                ],
                'manufacturer' => [
                    'id',
                    'name'
                ],
                'retail_price_unit',
                'retail_price_strip',
                'retail_price_box',
                'supplier_price_unit',
                'supplier_price_strip',
                'supplier_price_box',
                'prescription_required',
                'controlled_substance',
                'minimum_stock',
                'maximum_stock',
                'status',
                'inventories'
            ],
            'meta' => [
                'timestamp'
            ]
        ]);
        
        $medicineData = $response->json('data');
        
        // Verify all essential data for offline sales is present
        $this->assertEquals('Test Medicine', $medicineData['name']);
        $this->assertEquals('Test Generic', $medicineData['generic_name']);
        $this->assertEquals(10.50, $medicineData['retail_price_unit']);
        $this->assertEquals(105.00, $medicineData['retail_price_strip']);
        $this->assertEquals(1050.00, $medicineData['retail_price_box']);
        $this->assertEquals(8.00, $medicineData['supplier_price_unit']);
        $this->assertTrue($medicineData['prescription_required']);
        $this->assertFalse($medicineData['controlled_substance']);
        
        // Verify category and manufacturer data
        $this->assertEquals('Test Category', $medicineData['category']['name']);
        $this->assertEquals('Test Manufacturer', $medicineData['manufacturer']['name']);
        
        // Verify inventory data (should be empty array without inventory)
        $this->assertIsArray($medicineData['inventories']);
        $this->assertCount(0, $medicineData['inventories']);
    }

    #[Test]
    public function it_provides_paginated_medicines_for_bulk_sync()
    {
        // Create multiple medicines
        for ($i = 1; $i <= 15; $i++) {
            Medicine::create([
                'name' => "Medicine {$i}",
                'generic_name' => "Generic {$i}",
                'category_id' => $this->category->id,
                'manufacturer_id' => $this->manufacturer->id,
                'unit_type' => 'tablet',
                'status' => 'active',
                'retail_price_unit' => 5.00 + $i,
                'created_by' => $this->user->id,
                'updated_by' => $this->user->id,
            ]);
        }
        
        // Test pagination
        $response = $this->getJson('/api/offline/medicines?limit=10&offset=0');
        $response->assertStatus(200);
        
        $data = $response->json();
        $this->assertEquals(10, $data['meta']['count']);
        $this->assertEquals(10, $data['meta']['limit']);
        $this->assertEquals(0, $data['meta']['offset']);
        $this->assertTrue($data['meta']['has_more']);
        
        // Test second page
        $response = $this->getJson('/api/offline/medicines?limit=10&offset=10');
        $response->assertStatus(200);
        
        $data = $response->json();
        $this->assertEquals(5, $data['meta']['count']); // Remaining 5 medicines
        $this->assertEquals(10, $data['meta']['limit']);
        $this->assertEquals(10, $data['meta']['offset']);
        $this->assertFalse($data['meta']['has_more']);
    }

    #[Test]
    public function it_supports_incremental_sync_for_updated_medicines()
    {
        // Create a medicine
        $medicine = Medicine::create([
            'name' => 'Original Medicine',
            'generic_name' => 'Original Generic',
            'category_id' => $this->category->id,
            'manufacturer_id' => $this->manufacturer->id,
            'unit_type' => 'capsule',
            'status' => 'active',
            'retail_price_unit' => 15.00,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);
        
        $originalTimestamp = $medicine->updated_at;
        
        // Wait a moment and update the medicine
        sleep(1);
        $medicine->update([
            'name' => 'Updated Medicine',
            'retail_price_unit' => 20.00,
        ]);
        
        // Test incremental sync - should only return updated medicine
        $response = $this->getJson('/api/offline/medicines?since=' . urlencode($originalTimestamp->toISOString()));
        $response->assertStatus(200);
        
        $data = $response->json();
        $this->assertEquals(1, $data['meta']['count']);
        $this->assertEquals('Updated Medicine', $data['data'][0]['name']);
        $this->assertEquals(20.00, $data['data'][0]['retail_price_unit']);
    }

    #[Test]
    public function it_provides_all_reference_data_for_offline_operations()
    {
        // Test categories endpoint
        $response = $this->getJson('/api/offline/categories');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'is_active'
                ]
            ],
            'meta' => [
                'timestamp'
            ]
        ]);
        
        // Test manufacturers endpoint
        $response = $this->getJson('/api/offline/manufacturers');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'is_active'
                ]
            ],
            'meta' => [
                'timestamp'
            ]
        ]);
        
        // Test locations endpoint
        $response = $this->getJson('/api/offline/locations');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'status'
                ]
            ],
            'meta' => [
                'timestamp'
            ]
        ]);
    }

    #[Test]
    public function it_handles_medicines_without_inventory_gracefully()
    {
        // Create a medicine without any inventory
        $medicine = Medicine::create([
            'name' => 'No Inventory Medicine',
            'generic_name' => 'No Inventory Generic',
            'category_id' => $this->category->id,
            'manufacturer_id' => $this->manufacturer->id,
            'unit_type' => 'tablet',
            'status' => 'active',
            'retail_price_unit' => 25.00,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $response = $this->getJson("/api/offline/medicines/{$medicine->id}");
        $response->assertStatus(200);

        $medicineData = $response->json('data');

        // Should have empty inventories array
        $this->assertIsArray($medicineData['inventories']);
        $this->assertCount(0, $medicineData['inventories']);

        // But should still have all medicine data
        $this->assertEquals('No Inventory Medicine', $medicineData['name']);
        $this->assertEquals(25.00, $medicineData['retail_price_unit']);
    }
}
