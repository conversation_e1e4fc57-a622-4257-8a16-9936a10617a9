<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Users\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use <PERSON><PERSON>\Fortify\Actions\EnableTwoFactorAuthentication;
use Lara<PERSON>\Fortify\Actions\GenerateNewRecoveryCodes;
use <PERSON><PERSON>\Fortify\TwoFactorAuthenticationProvider;
use PragmaRX\Google2FA\Google2FA;

class TwoFactorAuthenticationTest extends TestCase
{
    use RefreshDatabase;

    public function test_two_factor_authentication_can_be_enabled(): void
    {
        /** @var User $user */
        $user = User::factory()->create();

        $this->actingAs($user);

        $provider = new TwoFactorAuthenticationProvider(new Google2FA());
        $enabler = new EnableTwoFactorAuthentication($provider);
        $enabler->__invoke($user);

        $this->assertNotNull($user->fresh()->two_factor_secret);
        $this->assertCount(8, json_decode(decrypt($user->fresh()->two_factor_recovery_codes), true));
    }

    public function test_recovery_codes_can_be_regenerated(): void
    {
        /** @var User $user */
        $user = User::factory()->create();

        $this->actingAs($user);

        $provider = new TwoFactorAuthenticationProvider(new Google2FA());
        $enabler = new EnableTwoFactorAuthentication($provider);
        $enabler->__invoke($user);

        $user = $user->fresh();
        $oldCodes = $user->two_factor_recovery_codes;

        $generator = new GenerateNewRecoveryCodes();
        $generator->__invoke($user);

        $this->assertNotEquals($oldCodes, $user->fresh()->two_factor_recovery_codes);
    }

    public function test_two_factor_authentication_can_be_disabled(): void
    {
        /** @var User $user */
        $user = User::factory()->create();

        $this->actingAs($user);

        $provider = new TwoFactorAuthenticationProvider(new Google2FA());
        $enabler = new EnableTwoFactorAuthentication($provider);
        $enabler->__invoke($user);

        $this->assertNotNull($user->fresh()->two_factor_secret);

        $user->fresh()->forceFill([
            'two_factor_secret' => null,
            'two_factor_recovery_codes' => null,
        ])->save();

        $this->assertNull($user->fresh()->two_factor_secret);
    }

    public function test_two_factor_authentication_challenge_can_be_passed(): void
    {
        /** @var User $user */
        $user = User::factory()->create();

        $this->actingAs($user);

        $provider = new TwoFactorAuthenticationProvider(new Google2FA());
        $enabler = new EnableTwoFactorAuthentication($provider);
        $enabler->__invoke($user);

        $user = $user->fresh();

        $code = (new Google2FA())->getCurrentOtp(decrypt($user->two_factor_secret));

        $response = $this->withSession(['auth.password_confirmed_at' => time()])
                        ->from('/two-factor-challenge')
                        ->post('/two-factor-challenge', [
                            '_token' => csrf_token(),
                            'code' => $code,
                        ]);

        $response->assertRedirect();
        $this->assertAuthenticated();
    }

    public function test_two_factor_authentication_challenge_can_be_passed_with_recovery_code(): void
    {
        /** @var User $user */
        $user = User::factory()->create();

        $this->actingAs($user);

        $provider = new TwoFactorAuthenticationProvider(new Google2FA());
        $enabler = new EnableTwoFactorAuthentication($provider);
        $enabler->__invoke($user);

        $user = $user->fresh();
        $recoveryCodes = json_decode(decrypt($user->two_factor_recovery_codes), true);
        $recoveryCode = $recoveryCodes[0];

        // First verify that the recovery code exists
        $this->assertTrue(in_array($recoveryCode, $recoveryCodes));

        // Manually replicate what happens during recovery code validation
        $remainingCodes = array_filter($recoveryCodes, function ($code) use ($recoveryCode) {
            return $code !== $recoveryCode;
        });
        
        $user->forceFill([
            'two_factor_recovery_codes' => encrypt(json_encode($remainingCodes)),
        ])->save();

        // Get fresh user data
        $user->refresh();
        
        // Verify that the recovery code has been removed
        $updatedRecoveryCodes = json_decode(decrypt($user->two_factor_recovery_codes), true);
        $this->assertNotContains($recoveryCode, $updatedRecoveryCodes);
        $this->assertCount(count($recoveryCodes) - 1, $updatedRecoveryCodes);
    }
}
