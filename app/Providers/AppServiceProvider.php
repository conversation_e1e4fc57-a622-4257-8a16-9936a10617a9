<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Livewire\Livewire;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Livewire::component('security.two-factor-setup', \App\Http\Livewire\Security\TwoFactorSetup::class);
        Livewire::component('security.recovery-codes', \App\Http\Livewire\Security\RecoveryCodes::class);
        // Observers are registered in EventServiceProvider
    }
}
