<?php

namespace App\Services;

use App\Models\Users\UserActivity;
use App\Models\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ActivityLogger
{
    /**
     * Log a user activity.
     *
     * @param string $action
     * @param string $description
     * @param User|null $user
     * @param Model|null $model
     * @param array $properties
     * @param Request|null $request
     * @return UserActivity
     */
    public static function log(
        string $action,
        string $description,
        ?User $user = null,
        ?Model $model = null,
        array $properties = [],
        ?Request $request = null
    ): UserActivity {
        $user = $user ?? Auth::user();
        $request = $request ?? request();

        return UserActivity::create([
            'user_id' => $user?->id,
            'action' => $action,
            'description' => $description,
            'ip_address' => $request?->ip(),
            'user_agent' => $request?->userAgent(),
            'properties' => $properties,
            'model_type' => $model ? get_class($model) : null,
            'model_id' => $model?->id,
        ]);
    }

    /**
     * Log user login activity.
     */
    public static function logLogin(User $user, ?Request $request = null): UserActivity
    {
        return self::log(
            UserActivity::ACTION_LOGIN,
            'User logged in',
            $user,
            null,
            [
                'login_time' => now()->toDateTimeString(),
            ],
            $request
        );
    }

    /**
     * Log user logout activity.
     */
    public static function logLogout(User $user, ?Request $request = null): UserActivity
    {
        return self::log(
            UserActivity::ACTION_LOGOUT,
            'User logged out',
            $user,
            null,
            [
                'logout_time' => now()->toDateTimeString(),
            ],
            $request
        );
    }

    /**
     * Log profile update activity.
     */
    public static function logProfileUpdate(User $user, array $changes, ?Request $request = null): UserActivity
    {
        $changedFields = array_keys($changes);
        
        return self::log(
            UserActivity::ACTION_PROFILE_UPDATED,
            'Profile updated: ' . implode(', ', $changedFields),
            $user,
            $user,
            [
                'changed_fields' => $changedFields,
                'changes' => $changes,
            ],
            $request
        );
    }

    /**
     * Log password change activity.
     */
    public static function logPasswordChange(User $user, ?Request $request = null): UserActivity
    {
        return self::log(
            UserActivity::ACTION_PASSWORD_CHANGED,
            'Password changed',
            $user,
            $user,
            [
                'change_time' => now()->toDateTimeString(),
            ],
            $request
        );
    }

    /**
     * Log 2FA enabled activity.
     */
    public static function log2FAEnabled(User $user, ?Request $request = null): UserActivity
    {
        return self::log(
            UserActivity::ACTION_2FA_ENABLED,
            'Two-factor authentication enabled',
            $user,
            $user,
            [
                'enabled_time' => now()->toDateTimeString(),
            ],
            $request
        );
    }

    /**
     * Log 2FA disabled activity.
     */
    public static function log2FADisabled(User $user, ?Request $request = null): UserActivity
    {
        return self::log(
            UserActivity::ACTION_2FA_DISABLED,
            'Two-factor authentication disabled',
            $user,
            $user,
            [
                'disabled_time' => now()->toDateTimeString(),
            ],
            $request
        );
    }

    /**
     * Log avatar update activity.
     */
    public static function logAvatarUpdate(User $user, ?Request $request = null): UserActivity
    {
        return self::log(
            UserActivity::ACTION_AVATAR_UPDATED,
            'Profile avatar updated',
            $user,
            $user,
            [
                'update_time' => now()->toDateTimeString(),
            ],
            $request
        );
    }

    /**
     * Log settings update activity.
     */
    public static function logSettingsUpdate(User $user, array $settings, ?Request $request = null): UserActivity
    {
        return self::log(
            UserActivity::ACTION_SETTINGS_UPDATED,
            'Settings updated',
            $user,
            null,
            [
                'settings' => $settings,
                'update_time' => now()->toDateTimeString(),
            ],
            $request
        );
    }

    /**
     * Log sale creation activity.
     */
    public static function logSaleCreated(User $user, Model $sale, ?Request $request = null): UserActivity
    {
        return self::log(
            UserActivity::ACTION_SALE_CREATED,
            'Sale created: #' . ($sale->invoice_number ?? 'N/A'),
            $user,
            $sale,
            [
                'invoice_number' => $sale->invoice_number ?? 'N/A',
                'total_amount' => $sale->total_amount ?? 0,
            ],
            $request
        );
    }

    /**
     * Log medicine creation activity.
     */
    public static function logMedicineCreated(User $user, Model $medicine, ?Request $request = null): UserActivity
    {
        return self::log(
            UserActivity::ACTION_MEDICINE_CREATED,
            'Medicine created: ' . ($medicine->name ?? 'N/A'),
            $user,
            $medicine,
            [
                'medicine_name' => $medicine->name ?? 'N/A',
            ],
            $request
        );
    }

    /**
     * Log customer creation activity.
     */
    public static function logCustomerCreated(User $user, Model $customer, ?Request $request = null): UserActivity
    {
        return self::log(
            UserActivity::ACTION_CUSTOMER_CREATED,
            'Customer created: ' . ($customer->name ?? 'N/A'),
            $user,
            $customer,
            [
                'customer_name' => $customer->name ?? 'N/A',
            ],
            $request
        );
    }

    /**
     * Log inventory update activity.
     */
    public static function logInventoryUpdate(User $user, Model $inventory, array $changes, ?Request $request = null): UserActivity
    {
        $medicineName = 'N/A';
        if (isset($inventory->medicine) && $inventory->medicine) {
            $medicineName = $inventory->medicine->name ?? 'N/A';
        }

        return self::log(
            UserActivity::ACTION_INVENTORY_UPDATED,
            'Inventory updated for ' . $medicineName,
            $user,
            $inventory,
            [
                'medicine_name' => $medicineName,
                'changes' => $changes,
            ],
            $request
        );
    }

    /**
     * Get recent activities for a user.
     */
    public static function getRecentActivities(User $user, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return UserActivity::forUser($user->id)
            ->with('model')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get activity statistics for a user.
     */
    public static function getActivityStats(User $user, int $days = 30): array
    {
        $activities = UserActivity::forUser($user->id)
            ->recent($days)
            ->get();

        return [
            'total_activities' => $activities->count(),
            'login_count' => $activities->where('action', UserActivity::ACTION_LOGIN)->count(),
            'profile_updates' => $activities->where('action', UserActivity::ACTION_PROFILE_UPDATED)->count(),
            'sales_created' => $activities->where('action', UserActivity::ACTION_SALE_CREATED)->count(),
            'most_active_day' => $activities->groupBy(fn($activity) => $activity->created_at->format('Y-m-d'))
                ->sortByDesc(fn($group) => $group->count())
                ->keys()
                ->first(),
        ];
    }
}
