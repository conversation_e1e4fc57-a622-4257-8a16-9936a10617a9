<?php

namespace App\Services;

use App\Models\Customers\Customer;
use App\Models\Customers\LoyaltyTransaction;
use App\Models\Sales\Sale;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class LoyaltyService
{
    /**
     * Loyalty program configuration
     */
    const POINTS_PER_DOLLAR = 1; // 1 point per $1 spent
    const POINTS_TO_DOLLAR_RATIO = 100; // 100 points = $1 discount
    const POINTS_EXPIRY_MONTHS = 12; // Points expire after 12 months
    const GOLD_MEMBER_THRESHOLD = 1000; // Gold member at 1000 points

    /**
     * Award loyalty points for a sale
     */
    public function awardPoints(Sale $sale): ?LoyaltyTransaction
    {
        if (!$sale->customer_id) {
            return null;
        }

        try {
            DB::beginTransaction();

            $customer = $sale->customer;
            $pointsToAward = $this->calculatePointsForAmount($sale->total_amount);

            if ($pointsToAward <= 0) {
                DB::rollBack();
                return null;
            }

            // Create loyalty transaction
            $transaction = LoyaltyTransaction::create([
                'customer_id' => $customer->id,
                'sale_id' => $sale->id,
                'type' => LoyaltyTransaction::TYPE_EARNED,
                'points' => $pointsToAward,
                'amount_spent' => $sale->total_amount,
                'description' => "Points earned from purchase #{$sale->invoice_number}",
                'metadata' => [
                    'earning_rate' => self::POINTS_PER_DOLLAR,
                    'invoice_number' => $sale->invoice_number,
                    'sale_total' => $sale->total_amount
                ],
                'expires_at' => now()->addMonths(self::POINTS_EXPIRY_MONTHS),
                'created_by' => Auth::id() ?? $sale->created_by
            ]);

            // Update customer's loyalty points and last purchase date
            $customer->increment('loyalty_points', $pointsToAward);
            $customer->update(['last_purchase_date' => $sale->created_at]);

            DB::commit();

            Log::info('Loyalty points awarded', [
                'customer_id' => $customer->id,
                'sale_id' => $sale->id,
                'points_awarded' => $pointsToAward,
                'new_balance' => $customer->fresh()->loyalty_points
            ]);

            return $transaction;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to award loyalty points', [
                'customer_id' => $sale->customer_id,
                'sale_id' => $sale->id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Redeem loyalty points for discount
     */
    public function redeemPoints(Customer $customer, int $pointsToRedeem, ?Sale $sale = null): array
    {
        if ($pointsToRedeem <= 0) {
            return ['success' => false, 'message' => 'Invalid points amount'];
        }

        if ($customer->loyalty_points < $pointsToRedeem) {
            return ['success' => false, 'message' => 'Insufficient loyalty points'];
        }

        try {
            DB::beginTransaction();

            $discountAmount = $this->calculateDiscountForPoints($pointsToRedeem);

            // Create redemption transaction
            $transaction = LoyaltyTransaction::create([
                'customer_id' => $customer->id,
                'sale_id' => $sale?->id,
                'type' => LoyaltyTransaction::TYPE_REDEEMED,
                'points' => -$pointsToRedeem, // Negative for redemption
                'discount_applied' => $discountAmount,
                'description' => $sale 
                    ? "Points redeemed for discount on purchase #{$sale->invoice_number}"
                    : "Points redeemed for discount",
                'metadata' => [
                    'redemption_rate' => self::POINTS_TO_DOLLAR_RATIO,
                    'discount_amount' => $discountAmount,
                    'invoice_number' => $sale?->invoice_number
                ],
                'created_by' => Auth::id() ?? 1
            ]);

            // Update customer's loyalty points
            $customer->decrement('loyalty_points', $pointsToRedeem);

            DB::commit();

            Log::info('Loyalty points redeemed', [
                'customer_id' => $customer->id,
                'points_redeemed' => $pointsToRedeem,
                'discount_amount' => $discountAmount,
                'new_balance' => $customer->fresh()->loyalty_points
            ]);

            return [
                'success' => true,
                'transaction' => $transaction,
                'discount_amount' => $discountAmount,
                'points_redeemed' => $pointsToRedeem,
                'new_balance' => $customer->fresh()->loyalty_points
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to redeem loyalty points', [
                'customer_id' => $customer->id,
                'points_to_redeem' => $pointsToRedeem,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'message' => 'Failed to redeem points'];
        }
    }

    /**
     * Calculate points for a given amount
     */
    public function calculatePointsForAmount(float $amount): int
    {
        return (int) floor($amount * self::POINTS_PER_DOLLAR);
    }

    /**
     * Calculate discount for given points
     */
    public function calculateDiscountForPoints(int $points): float
    {
        return round($points / self::POINTS_TO_DOLLAR_RATIO, 2);
    }

    /**
     * Get customer's loyalty statistics
     */
    public function getCustomerStats(Customer $customer): array
    {
        $transactions = $customer->loyaltyTransactions();
        
        return [
            'total_points_earned' => $transactions->earned()->sum('points'),
            'total_points_redeemed' => abs($transactions->redeemed()->sum('points')),
            'current_balance' => $customer->loyalty_points,
            'total_spent' => $transactions->earned()->sum('amount_spent'),
            'total_saved' => $transactions->redeemed()->sum('discount_applied'),
            'member_since' => $customer->created_at,
            'last_activity' => $customer->last_purchase_date,
            'membership_tier' => $this->getMembershipTier($customer),
            'points_to_next_tier' => $this->getPointsToNextTier($customer)
        ];
    }

    /**
     * Get membership tier
     */
    public function getMembershipTier(Customer $customer): string
    {
        return $customer->loyalty_points >= self::GOLD_MEMBER_THRESHOLD ? 'Gold' : 'Regular';
    }

    /**
     * Get points needed for next tier
     */
    public function getPointsToNextTier(Customer $customer): int
    {
        if ($customer->loyalty_points >= self::GOLD_MEMBER_THRESHOLD) {
            return 0; // Already at highest tier
        }
        
        return self::GOLD_MEMBER_THRESHOLD - $customer->loyalty_points;
    }

    /**
     * Expire old points
     */
    public function expireOldPoints(): int
    {
        $expiredTransactions = LoyaltyTransaction::where('type', LoyaltyTransaction::TYPE_EARNED)
            ->where('expires_at', '<', now())
            ->whereNotExists(function($query) {
                $query->select(DB::raw(1))
                    ->from('loyalty_transactions as lt2')
                    ->where('lt2.type', LoyaltyTransaction::TYPE_EXPIRED)
                    ->whereColumn('lt2.sale_id', 'loyalty_transactions.sale_id')
                    ->whereColumn('lt2.customer_id', 'loyalty_transactions.customer_id');
            })
            ->get();

        $totalExpired = 0;

        foreach ($expiredTransactions as $transaction) {
            try {
                DB::beginTransaction();

                // Create expiration transaction
                LoyaltyTransaction::create([
                    'customer_id' => $transaction->customer_id,
                    'sale_id' => $transaction->sale_id,
                    'type' => LoyaltyTransaction::TYPE_EXPIRED,
                    'points' => -$transaction->points,
                    'description' => "Points expired from purchase #{$transaction->sale->invoice_number}",
                    'metadata' => [
                        'original_transaction_id' => $transaction->id,
                        'earned_date' => $transaction->created_at,
                        'expiry_date' => $transaction->expires_at
                    ],
                    'created_by' => 1 // System user
                ]);

                // Deduct points from customer
                $transaction->customer->decrement('loyalty_points', $transaction->points);
                $totalExpired += $transaction->points;

                DB::commit();

            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Failed to expire loyalty points', [
                    'transaction_id' => $transaction->id,
                    'customer_id' => $transaction->customer_id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $totalExpired;
    }
}
