<?php

namespace App\Services\Search;

use <PERSON><PERSON>\Scout\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class AdvancedSearchService
{
    /**
     * Perform an advanced search on a model.
     *
     * @param string $query The search query
     * @param string $model The model class name
     * @param array $options Additional search options
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function search(string $query, string $model, array $options = [])
    {
        $searchBuilder = $model::search($query);

        if (!empty($options['filters'])) {
            $this->applyFilters($searchBuilder, $options['filters']);
        }

        return $searchBuilder->get();
    }

    /**
     * Apply fuzzy matching to the search query.
     *
     * @param string $query
     * @return string
     */
    public function fuzzyMatch(string $query): string
    {
        // Split query into words
        $words = explode(' ', $query);
        $fuzzyWords = [];

        foreach ($words as $word) {
            // Add fuzzy variations
            $fuzzyWords[] = $word; // Exact match
            $fuzzyWords[] = $word . '*'; // Prefix match
            $fuzzyWords[] = '*' . $word . '*'; // Contains match
            
            // Add common misspellings
            $fuzzyWords = array_merge($fuzzyWords, $this->getCommonMisspellings($word));
        }

        return implode(' ', array_unique($fuzzyWords));
    }

    /**
     * Get phonetic variations of a word.
     *
     * @param string $word
     * @return array
     */
    public function getPhoneticVariations(string $word): array
    {
        $variations = [];
        
        // Add Soundex variation
        $variations[] = soundex($word);
        
        // Add Metaphone variation
        $variations[] = metaphone($word);
        
        return array_unique($variations);
    }

    /**
     * Get common misspellings for a word.
     *
     * @param string $word
     * @return array
     */
    protected function getCommonMisspellings(string $word): array
    {
        $misspellings = [];
        
        // Common character substitutions
        $substitutions = [
            'a' => ['e'],
            'e' => ['a', 'i'],
            'i' => ['e', 'y'],
            'o' => ['u'],
            'u' => ['o'],
            'ph' => ['f'],
            'f' => ['ph'],
            'c' => ['k', 's'],
            'k' => ['c'],
            's' => ['c'],
        ];

        // Generate variations
        foreach ($substitutions as $original => $replacements) {
            if (Str::contains($word, $original)) {
                foreach ($replacements as $replacement) {
                    $misspellings[] = str_replace($original, $replacement, $word);
                }
            }
        }

        return $misspellings;
    }

    /**
     * Apply filters to the search builder.
     *
     * @param Builder $builder
     * @param array $filters
     * @return void
     */
    protected function applyFilters(Builder $builder, array $filters): void
    {
        foreach ($filters as $field => $value) {
            if (is_array($value)) {
                $builder->whereIn($field, $value);
            } else {
                $builder->where($field, $value);
            }
        }
    }
} 