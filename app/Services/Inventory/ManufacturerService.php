<?php

namespace App\Services\Inventory;

use App\Models\Inventory\Manufacturer;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;

class ManufacturerService
{
    /**
     * Get paginated list of manufacturers with search and filter options
     *
     * @param Request|null $request
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getPaginatedManufacturers(Request $request = null, int $perPage = 15): LengthAwarePaginator
    {
        $query = Manufacturer::query();

        // Apply search if provided
        if ($request && $request->filled('search')) {
            $searchTerm = $request->input('search');
            $query->where(function($q) use ($searchTerm) {
                $q->where('name', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('email', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('phone', 'LIKE', "%{$searchTerm}%");
            });
        }

        // Apply status filter if provided
        if ($request && $request->filled('status')) {
            $status = $request->input('status');
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Apply sorting
        if ($request && $request->filled('sort')) {
            $sortField = 'created_at';
            $sortDirection = 'desc';

            switch ($request->input('sort')) {
                case 'name_asc':
                    $sortField = 'name';
                    $sortDirection = 'asc';
                    break;
                case 'name_desc':
                    $sortField = 'name';
                    $sortDirection = 'desc';
                    break;
                case 'newest':
                    $sortField = 'created_at';
                    $sortDirection = 'desc';
                    break;
                case 'oldest':
                    $sortField = 'created_at';
                    $sortDirection = 'asc';
                    break;
            }

            $query->orderBy($sortField, $sortDirection);
        } else {
            $query->latest();
        }

        return $query->paginate($perPage);
    }

    /**
     * Create a new manufacturer
     *
     * @param array $data
     * @return Manufacturer
     */
    public function createManufacturer(array $data): Manufacturer
    {
        return Manufacturer::create([
            'name' => $data['name'],
            'slug' => Str::slug($data['name']),
            'email' => $data['email'] ?? null,
            'phone' => $data['phone'] ?? null,
            'address' => $data['address'] ?? null,
            'website' => $data['website'] ?? null,
            'is_active' => $data['is_active'] ?? true,
        ]);
    }

    /**
     * Update an existing manufacturer
     *
     * @param Manufacturer $manufacturer
     * @param array $data
     * @return Manufacturer
     */
    public function updateManufacturer(Manufacturer $manufacturer, array $data): Manufacturer
    {
        $manufacturer->update([
            'name' => $data['name'],
            'slug' => Str::slug($data['name']),
            'email' => $data['email'] ?? null,
            'phone' => $data['phone'] ?? null,
            'address' => $data['address'] ?? null,
            'website' => $data['website'] ?? null,
            'is_active' => $data['is_active'] ?? true,
        ]);

        return $manufacturer->fresh();
    }

    /**
     * Delete a manufacturer
     *
     * @param Manufacturer $manufacturer
     * @return bool
     * @throws \Exception
     */
    public function deleteManufacturer(Manufacturer $manufacturer): bool
    {
        if ($manufacturer->medicines()->exists()) {
            throw new \Exception('Cannot delete manufacturer. It has associated medicines.');
        }

        return $manufacturer->delete();
    }
}
