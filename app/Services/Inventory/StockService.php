<?php

namespace App\Services\Inventory;

use App\Models\Inventory\Medicine;
use App\Models\Inventory\StockMovement;
use App\Models\Inventory\Location;
use App\Models\Inventory\Inventory;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use App\Services\Inventory\BatchHistoryService;

class StockService
{
    public function getStockSummary(): array
    {
        // Get total active medicines with their batches
        $totalmedicines = DB::table('inventories')
            ->join('medicines', 'inventories.medicine_id', '=', 'medicines.id')
            ->where('medicines.status', 'active')
            ->where('inventories.quantity', '>', 0)
            ->distinct('medicines.id')
            ->count();

        // Get medicines with quantity less than or equal to minimum stock
        $lowStockmedicines = DB::table('medicines')
            ->where('status', 'active')
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('inventories')
                    ->whereRaw('inventories.medicine_id = medicines.id')
                    ->groupBy('medicine_id')
                    ->havingRaw('SUM(inventories.quantity) <= medicines.minimum_stock');
            })
            ->count();

        // Get medicines with batches expiring in the next 3 months
        $expiringmedicines = DB::table('inventories')
            ->join('medicines', 'inventories.medicine_id', '=', 'medicines.id')
            ->where('medicines.status', 'active')
            ->where('inventories.quantity', '>', 0)
            ->whereNotNull('inventories.expiry_date')
            ->whereDate('inventories.expiry_date', '<=', Carbon::now()->addMonths(3))
            ->whereDate('inventories.expiry_date', '>=', Carbon::now())
            ->distinct('medicines.id')
            ->count();

        // Get recent stock movements
        $recentMovements = StockMovement::with([
                'medicine',
                'sourceLocation',
                'destinationLocation',
                'createdBy'
            ])
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        return compact('totalmedicines', 'lowStockmedicines', 'expiringmedicines', 'recentMovements');
    }

    public function getMedicinesByLocation(Location $location): Collection
    {
        try {
            $medicines = Medicine::join('inventories', 'medicines.id', '=', 'inventories.medicine_id')
                ->where('inventories.location_id', $location->id)
                ->where('inventories.quantity', '>', 0)
                ->where('medicines.status', 'active')
                ->where(function($query) {
                    $query->whereNull('inventories.expiry_date')
                          ->orWhere('inventories.expiry_date', '>', now());
                })
                ->select([
                    'medicines.id',
                    'medicines.name',
                    'medicines.dosage',
                    'medicines.category_id',
                    'medicines.manufacturer_id',
                    'medicines.unit_type_id',
                    DB::raw('CAST(SUM(inventories.quantity) AS INTEGER) as total_quantity')
                ])
                ->groupBy('medicines.id', 'medicines.name', 'medicines.dosage', 'medicines.category_id', 'medicines.manufacturer_id', 'medicines.unit_type_id')
                ->with(['category', 'manufacturer', 'unitType'])
                ->get();

            return $medicines->map(function($medicine) {
                return [
                    'id' => $medicine->id,
                    'name' => $medicine->name,
                    'dosage' => $medicine->dosage,
                    'total_quantity' => (int) $medicine->total_quantity
                ];
            });
        } catch (\Exception $e) {
            Log::error('Error getting medicines by location', [
                'location_id' => $location->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    public function getBatchesByLocationAndMedicine(Location $location, Medicine $medicine): Collection
    {
        try {
            return Inventory::where('location_id', $location->id)
                ->where('medicine_id', $medicine->id)
                ->where('quantity', '>', 0)
                ->where(function($query) {
                    $query->whereNull('expiry_date')
                          ->orWhere('expiry_date', '>', now());
                })
                ->select([
                    'id',
                    'batch_number',
                    'quantity',
                    'expiry_date',
                    'unit_price',
                    'created_at',
                    'updated_at'
                ])
                ->orderBy('expiry_date')
                ->get();
        } catch (\Exception $e) {
            Log::error('Error getting batches by location and medicine', [
                'location_id' => $location->id,
                'medicine_id' => $medicine->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    public function transferStock(array $data): void
    {
        DB::beginTransaction();
        try {
            // Get the authenticated user ID or default to 1 (admin) if not authenticated
            $userId = Auth::id() ?? 1;
            
            // Generate reference number
            $referenceNumber = 'TRF-' . date('Ymd') . '-' . str_pad(StockMovement::count() + 1, 6, '0', STR_PAD_LEFT);

            // Lock the source inventory for update to prevent race conditions
            $sourceInventory = Inventory::where('location_id', $data['source_location'])
                ->where('medicine_id', $data['medicine'])
                ->where('batch_number', $data['batch_number'])
                ->where('quantity', '>=', $data['quantity'])
                ->where(function($query) {
                    $query->whereNull('expiry_date')
                          ->orWhere('expiry_date', '>', now());
                })
                ->lockForUpdate()
                ->firstOrFail();

            // Decrease quantity from source location
            $sourceInventory->decrement('quantity', $data['quantity']);
            $sourceInventory->updated_by = $userId;
            $sourceInventory->save();

            // Check if destination already has this batch
            $destinationInventory = Inventory::firstOrNew([
                'location_id' => $data['destination_location'],
                'medicine_id' => $data['medicine'],
                'batch_number' => $data['batch_number']
            ]);

            // If it's a new inventory record, set the initial values
            if (!$destinationInventory->exists) {
                $destinationInventory->expiry_date = $sourceInventory->expiry_date;
                $destinationInventory->unit_price = $sourceInventory->unit_price;
                $destinationInventory->created_by = $userId;
                $destinationInventory->quantity = $data['quantity'];
            } else {
                // If it exists, increment the quantity
                $destinationInventory->increment('quantity', $data['quantity']);
            }
            
            $destinationInventory->updated_by = $userId;
            $destinationInventory->save();

            // Create stock movement record
            StockMovement::create([
                'medicine_id' => $data['medicine'],
                'source_location_id' => $data['source_location'],
                'destination_location_id' => $data['destination_location'],
                'quantity' => $data['quantity'],
                'batch_number' => $data['batch_number'],
                'movement_date' => $data['transfer_date'],
                'movement_type' => 'transfer',
                'reference_number' => $referenceNumber,
                'notes' => $data['notes'] ?? null,
                'created_by' => $userId,
                'updated_by' => $userId
            ]);

            // Create batch history records
            // 1. Record for source location (moved out)
            app(BatchHistoryService::class)->createBatchHistory([
                'medicine_id' => $data['medicine'],
                'batch_number' => $data['batch_number'],
                'expiry_date' => $sourceInventory->expiry_date,
                'action_type' => 'moved',
                'quantity' => -$data['quantity'], // Negative quantity for outgoing
                'location_id' => $data['source_location'],
                'unit_price' => $sourceInventory->unit_price,
                'notes' => "Transferred to " . Location::find($data['destination_location'])->name . " (Ref: {$referenceNumber})",
                'created_by' => $userId
            ]);

            // 2. Record for destination location (moved in)
            app(BatchHistoryService::class)->createBatchHistory([
                'medicine_id' => $data['medicine'],
                'batch_number' => $data['batch_number'],
                'expiry_date' => $sourceInventory->expiry_date,
                'action_type' => 'moved',
                'quantity' => $data['quantity'], // Positive quantity for incoming
                'location_id' => $data['destination_location'],
                'unit_price' => $sourceInventory->unit_price,
                'notes' => "Transferred from " . Location::find($data['source_location'])->name . " (Ref: {$referenceNumber})",
                'created_by' => $userId
            ]);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function createLocation(array $data): Location
    {
        return Location::create([
            'name' => $data['name'],
            'type' => $data['type'],
            'address' => $data['address'] ?? null,
            'status' => 'active',
            'created_by' => Auth::id()
        ]);
    }

    public function updateLocation(Location $location, array $data): void
    {
        $location->update([
            'name' => $data['name'],
            'type' => $data['type'],
            'address' => $data['address'] ?? null,
            'status' => $data['status'],
            'updated_by' => Auth::id()
        ]);
    }

    public function deleteLocation(Location $location): void
    {
        if ($location->stockMovementsFrom()->exists() || $location->stockMovementsTo()->exists()) {
            throw new \Exception('This location cannot be deleted as it has associated stock movements.');
        }
        $location->delete();
    }

    public function getLowStockItems(int $perPage = 15)
    {
        return DB::table('medicines')
            ->select([
                'medicines.id',
                'medicines.name',
                'medicines.generic_name',
                'medicines.dosage',
                'unit_types.code as unit_type',
                'medicines.minimum_stock',
                DB::raw('COALESCE((SELECT SUM(quantity) FROM inventories WHERE medicine_id = medicines.id), 0) as current_stock'),
                DB::raw('COALESCE((SELECT COUNT(DISTINCT warehouse_id) FROM inventories WHERE medicine_id = medicines.id AND quantity > 0), 0) as locations_count')
            ])
            ->leftJoin('unit_types', 'medicines.unit_type_id', '=', 'unit_types.id')
            ->where('medicines.status', 'active')
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('inventories')
                    ->whereRaw('inventories.medicine_id = medicines.id')
                    ->groupBy('medicine_id')
                    ->havingRaw('COALESCE(SUM(inventories.quantity), 0) <= medicines.minimum_stock');
            })
            ->orderBy('current_stock')
            ->paginate($perPage);
    }

    public function getExpiringItems(int $perPage = 15)
    {
        return DB::table('inventories')
            ->select([
                'inventories.id',
                'inventories.batch_number',
                'inventories.expiry_date',
                'inventories.quantity',
                'medicines.id as medicine_id',
                'medicines.name as medicine_name',
                'medicines.generic_name',
                'medicines.dosage',
                'unit_types.code as unit_type',
                'locations.name as location_name',
                'locations.location_code',
                'locations.type as location_type'
            ])
            ->join('medicines', 'inventories.medicine_id', '=', 'medicines.id')
            ->leftJoin('unit_types', 'medicines.unit_type_id', '=', 'unit_types.id')
            ->leftJoin('locations', function($join) {
                $join->on('inventories.location_id', '=', 'locations.id')
                     ->orOn('inventories.warehouse_id', '=', 'locations.id');
            })
            ->where('medicines.status', 'active')
            ->where(function($query) {
                $query->where('locations.status', 'active')
                      ->orWhereNull('locations.status')
                      ->orWhere('locations.is_active', 1);
            })
            ->where('inventories.quantity', '>', 0)
            ->whereNotNull('inventories.expiry_date')
            ->whereDate('inventories.expiry_date', '<=', now()->addMonths(3))
            ->whereDate('inventories.expiry_date', '>=', now())
            ->orderBy('inventories.expiry_date', 'asc')
            ->paginate($perPage);
    }

    public function getMedicineHistory(Medicine $medicine): array
    {
        // Get current stock summary
        $totalQuantity = DB::table('inventories')
            ->where('medicine_id', $medicine->id)
            ->where('quantity', '>', 0)
            ->sum('quantity');

        $activeBatches = DB::table('inventories')
            ->where('medicine_id', $medicine->id)
            ->where('quantity', '>', 0)
            ->count();

        $uniqueLocations = DB::table('inventories')
            ->where('medicine_id', $medicine->id)
            ->where('quantity', '>', 0)
            ->distinct('warehouse_id')
            ->count();

        $averagePrice = DB::table('inventories')
            ->where('medicine_id', $medicine->id)
            ->where('quantity', '>', 0)
            ->avg('unit_price');

        // Get current batches with location information
        $batches = DB::table('inventories')
            ->join('locations', 'inventories.warehouse_id', '=', 'locations.id')
            ->select(
                'inventories.*',
                'locations.name as location_name',
                'locations.type as location_type'
            )
            ->where('medicine_id', $medicine->id)
            ->orderBy('expiry_date')
            ->get();

        // Get movement history with pagination
        $movements = StockMovement::with([
                'medicine',
                'sourceLocation',
                'destinationLocation',
                'createdBy'
            ])
            ->where('medicine_id', $medicine->id)
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return compact(
            'totalQuantity',
            'activeBatches',
            'uniqueLocations',
            'averagePrice',
            'batches',
            'movements'
        );
    }

    /**
     * Handle expired inventory
     *
     * @return int Number of batches marked as expired
     */
    public function handleExpiredInventory(): int
    {
        $expiredCount = 0;
        $today = now()->format('Y-m-d');
        
        // Find all expired batches with quantity > 0
        $expiredBatches = Inventory::where('quantity', '>', 0)
            ->whereDate('expiry_date', '<', $today)
            ->get();
            
        foreach ($expiredBatches as $batch) {
            DB::beginTransaction();
            try {
                // Create batch history record for expired inventory
                app(BatchHistoryService::class)->createBatchHistory([
                    'medicine_id' => $batch->medicine_id,
                    'batch_number' => $batch->batch_number,
                    'expiry_date' => $batch->expiry_date,
                    'action_type' => 'expired',
                    'quantity' => $batch->quantity,
                    'location_id' => $batch->location_id,
                    'unit_price' => $batch->unit_price,
                    'notes' => 'Batch expired on ' . $batch->expiry_date->format('Y-m-d'),
                    'created_by' => Auth::id() ?? 1
                ]);
                
                // Set quantity to 0 for expired batch
                $batch->quantity = 0;
                $batch->save();
                
                $expiredCount++;
                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Error handling expired inventory: ' . $e->getMessage());
            }
        }
        
        return $expiredCount;
    }
}
