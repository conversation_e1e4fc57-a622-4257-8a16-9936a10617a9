<?php

namespace App\Services\Inventory;

use App\Models\Inventory\UnitType;
use App\Models\Inventory\UnitConversion;
use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class UnitConversionService
{
    /**
     * Get base unit types
     *
     * @return Collection
     */
    public function getBaseUnits(): Collection
    {
        return UnitType::where('is_base', true)->get();
    }

    /**
     * Get all unit types
     *
     * @return Collection
     */
    public function getAllUnits(): Collection
    {
        return UnitType::all();
    }

    /**
     * Get paginated conversion rules
     *
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getPaginatedConversionRules(int $perPage = 15): LengthAwarePaginator
    {
        return UnitConversion::with(['fromUnit', 'toUnit'])->paginate($perPage);
    }

    /**
     * Create a new unit conversion rule
     *
     * @param array $data
     * @param int $userId
     * @return array
     * @throws \Exception
     */
    public function createConversionRule(array $data, int $userId): array
    {
        try {
            DB::beginTransaction();

            // Create the direct conversion
            $directConversion = UnitConversion::create([
                'from_unit_id' => $data['from_unit_id'],
                'to_unit_id' => $data['to_unit_id'],
                'conversion_factor' => $data['conversion_factor'],
                'status' => 'active',
                'created_by' => $userId,
            ]);

            // Create the inverse conversion
            $inverseConversion = UnitConversion::create([
                'from_unit_id' => $data['to_unit_id'],
                'to_unit_id' => $data['from_unit_id'],
                'conversion_factor' => 1 / $data['conversion_factor'],
                'status' => 'active',
                'created_by' => $userId,
            ]);

            DB::commit();

            return [
                'direct' => $directConversion,
                'inverse' => $inverseConversion
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update an existing unit conversion rule
     *
     * @param UnitConversion $unitConversion
     * @param array $data
     * @param int $userId
     * @return array
     * @throws \Exception
     */
    public function updateConversionRule(UnitConversion $unitConversion, array $data, int $userId): array
    {
        try {
            DB::beginTransaction();

            $data['updated_by'] = $userId;
            $unitConversion->update($data);

            // Update the inverse conversion
            $inverseConversion = UnitConversion::where('from_unit_id', $unitConversion->to_unit_id)
                ->where('to_unit_id', $unitConversion->from_unit_id)
                ->first();

            if ($inverseConversion) {
                $inverseConversion->update([
                    'conversion_factor' => 1 / $data['conversion_factor'],
                    'status' => $data['status'],
                    'updated_by' => $userId,
                ]);
            }

            DB::commit();

            return [
                'direct' => $unitConversion->fresh(),
                'inverse' => $inverseConversion ? $inverseConversion->fresh() : null
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Delete a unit conversion rule
     *
     * @param UnitConversion $unitConversion
     * @return bool
     * @throws \Exception
     */
    public function deleteConversionRule(UnitConversion $unitConversion): bool
    {
        try {
            DB::beginTransaction();

            // Delete the inverse conversion
            UnitConversion::where('from_unit_id', $unitConversion->to_unit_id)
                ->where('to_unit_id', $unitConversion->from_unit_id)
                ->delete();

            // Delete the direct conversion
            $unitConversion->delete();

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Convert value between units
     *
     * @param array $data
     * @return array
     * @throws ValidationException
     */
    public function convertUnits(array $data): array
    {
        try {
            $conversion = UnitConversion::where('from_unit_id', $data['from_unit_id'])
                ->where('to_unit_id', $data['to_unit_id'])
                ->where('status', 'active')
                ->first();

            if (!$conversion) {
                throw ValidationException::withMessages([
                    'conversion' => ['No active conversion rule found between these units.'],
                ]);
            }

            $result = $data['value'] * $conversion->conversion_factor;

            return [
                'result' => $result,
                'from_unit' => $conversion->fromUnit->code,
                'to_unit' => $conversion->toUnit->code,
            ];
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw ValidationException::withMessages([
                'conversion' => ['Error converting units.'],
            ]);
        }
    }
}
