<?php

namespace App\Services\Inventory;

use App\Models\Inventory\Medicine;
use App\Models\Inventory\Purchase;
use App\Models\Inventory\PurchaseItem;
use App\Models\Inventory\Inventory;
use App\Models\Inventory\Supplier;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class MedicinePurchaseService
{
    /**
     * Create a purchase record for initial medicine stock
     *
     * @param Medicine $medicine
     * @param array $data
     * @return Purchase
     */
    public function createInitialPurchase(Medicine $medicine, array $data): Purchase
    {
        try {
            DB::beginTransaction();

            $subtotal = $data['quantity'] * $data['unit_price'];
            $taxAmount = $this->calculateTax($subtotal, $data['tax_percentage'] ?? 0);
            $discountAmount = $this->calculateDiscount($subtotal, $data['discount_percentage'] ?? 0);
            $shippingCost = $data['shipping_cost'] ?? 0;
            $finalAmount = $subtotal + $taxAmount - $discountAmount + $shippingCost;

            // Create purchase record
            $purchase = Purchase::create([
                'purchase_number' => $data['purchase_number'] ?? $this->generatePurchaseNumber(),
                'supplier_id' => $data['supplier_id'],
                'total_amount' => $subtotal,
                'tax_percentage' => $data['tax_percentage'] ?? 0,
                'tax_amount' => $taxAmount,
                'discount_percentage' => $data['discount_percentage'] ?? 0,
                'discount_amount' => $discountAmount,
                'shipping_cost' => $shippingCost,
                'final_amount' => $finalAmount,
                'status' => $data['status'] ?? 'received',
                'payment_status' => $data['payment_status'] ?? 'paid',
                'order_date' => $data['order_date'] ?? now(),
                'expected_date' => $data['expected_date'] ?? now(),
                'delivery_date' => $data['delivery_date'] ?? now(),
                'notes' => $data['notes'] ?? 'Initial stock entry',
                'created_by' => Auth::id(),
                'updated_by' => Auth::id(),
            ]);

            // Create purchase item
            $purchaseItem = PurchaseItem::create([
                'purchase_id' => $purchase->id,
                'medicine_id' => $medicine->id,
                'quantity' => $data['quantity'],
                'unit_price' => $data['unit_price'],
                'tax_percentage' => $data['tax_percentage'] ?? 0,
                'tax_amount' => $taxAmount,
                'discount_percentage' => $data['discount_percentage'] ?? 0,
                'discount_amount' => $discountAmount,
                'total_amount' => $subtotal, // Base amount without tax and discount
                'received_quantity' => $data['quantity'],
                'expiry_date' => $data['expiry_date'],
                'batch_number' => $data['batch_number'],
                'notes' => $data['notes'] ?? 'Initial stock entry',
            ]);

            // Create inventory record if not already created
            if (!isset($data['skip_inventory_creation']) || !$data['skip_inventory_creation']) {
                $inventory = Inventory::create([
                    'medicine_id' => $medicine->id,
                    'batch_number' => $data['batch_number'],
                    'expiry_date' => $data['expiry_date'],
                    'quantity' => $data['quantity'],
                    'unit_price' => $data['unit_price'], // Store supplier price
                    'location_id' => $data['location_id'] ?? null,
                    'rack_number' => $data['rack_number'] ?? null,
                    'bin_location' => $data['bin_location'] ?? null,
                    'warehouse_id' => $data['warehouse_id'] ?? null,
                    'temperature_requirement' => $data['temperature_requirement'] ?? null,
                    'notes' => $data['notes'] ?? null,
                    'created_by' => Auth::id(),
                    'updated_by' => Auth::id(),
                ]);
            }

            DB::commit();
            return $purchase;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating initial purchase: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Generate a unique purchase number
     *
     * @return string
     */
    protected function generatePurchaseNumber(): string
    {
        $prefix = 'PO-' . now()->format('Ymd');
        $lastPurchase = Purchase::where('purchase_number', 'like', $prefix . '%')
            ->orderBy('id', 'desc')
            ->first();

        if ($lastPurchase) {
            $lastNumber = (int) substr($lastPurchase->purchase_number, -3);
            $newNumber = str_pad($lastNumber + 1, 3, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '001';
        }

        return $prefix . '-' . $newNumber;
    }

    /**
     * Calculate tax amount
     *
     * @param float $amount
     * @param float $percentage
     * @return float
     */
    protected function calculateTax(float $amount, float $percentage): float
    {
        return round($amount * ($percentage / 100), 2);
    }

    /**
     * Calculate discount amount
     *
     * @param float $amount
     * @param float $percentage
     * @return float
     */
    protected function calculateDiscount(float $amount, float $percentage): float
    {
        return round($amount * ($percentage / 100), 2);
    }
} 