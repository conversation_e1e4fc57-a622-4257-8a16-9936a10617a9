<?php

namespace App\Services\Inventory;

use App\Models\Inventory\Supplier;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;

class SupplierService
{
    /**
     * Get paginated list of suppliers
     *
     * @param Request|null $request
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getPaginatedSuppliers($request = null, int $perPage = 10): LengthAwarePaginator
    {
        $query = Supplier::query();

        // Apply search if provided
        if ($request && $request->filled('search')) {
            $searchTerm = $request->input('search');
            $query->where(function($q) use ($searchTerm) {
                $q->where('name', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('contact_person', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('email', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('phone', 'LIKE', "%{$searchTerm}%");
            });
        }

        // Apply status filter if provided
        if ($request && $request->filled('status')) {
            $status = $request->input('status');
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Apply sorting
        if ($request && $request->filled('sort')) {
            $sort = $request->input('sort');
            switch ($sort) {
                case 'name_asc':
                    $query->orderBy('name', 'asc');
                    break;
                case 'name_desc':
                    $query->orderBy('name', 'desc');
                    break;
                case 'oldest':
                    $query->oldest();
                    break;
                case 'newest':
                default:
                    $query->latest();
                    break;
            }
        } else {
            $query->latest();
        }

        return $query->paginate($perPage)->withQueryString();
    }

    /**
     * Create a new supplier
     *
     * @param array $data
     * @return Supplier
     */
    public function createSupplier(array $data): Supplier
    {
        try {
            return Supplier::create([
                'name' => $data['name'],
                'contact_person' => $data['contact_person'] ?? null,
                'email' => $data['email'] ?? null,
                'phone' => $data['phone'] ?? null,
                'address' => $data['address'] ?? null,
                'tax_number' => $data['tax_number'] ?? null,
                'is_active' => $data['status'] === 'active',
                'created_by' => Auth::id(),
                'updated_by' => Auth::id(),
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating supplier: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update an existing supplier
     *
     * @param Supplier $supplier
     * @param array $data
     * @return Supplier
     */
    public function updateSupplier(Supplier $supplier, array $data): Supplier
    {
        try {
            $supplier->update([
                'name' => $data['name'],
                'contact_person' => $data['contact_person'] ?? null,
                'email' => $data['email'] ?? null,
                'phone' => $data['phone'] ?? null,
                'address' => $data['address'] ?? null,
                'tax_number' => $data['tax_number'] ?? null,
                'status' => $data['status'] ?? $supplier->status,
            ]);

            return $supplier->fresh();
        } catch (\Exception $e) {
            Log::error('Error updating supplier: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete a supplier
     *
     * @param Supplier $supplier
     * @return bool
     */
    public function deleteSupplier(Supplier $supplier): bool
    {
        try {
            // Check if supplier has any related records before deletion
            if ($supplier->purchases()->exists()) {
                throw new \Exception('Cannot delete supplier. It has associated purchase records.');
            }

            return $supplier->delete();
        } catch (\Exception $e) {
            Log::error('Error deleting supplier: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Find a supplier by ID
     *
     * @param mixed $id
     * @return Supplier
     */
    public function findSupplier($id): Supplier
    {
        return Supplier::findOrFail((int) $id);
    }
}
