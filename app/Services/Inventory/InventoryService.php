<?php

namespace App\Services\Inventory;

use App\Models\Inventory\Inventory;
use App\Models\Inventory\PurchaseItem;
use App\Models\Inventory\Location;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InventoryService
{
    /**
     * Create inventory from a received purchase item
     *
     * @param PurchaseItem $purchaseItem
     * @param float $quantity
     * @param int $locationId
     * @param string $batchNumber
     * @param string $expiryDate
     * @param string|null $manufactureDate
     * @param array $additionalData
     * @return Inventory
     */
    public function createFromPurchaseItem(
        PurchaseItem $purchaseItem,
        float $quantity,
        int $locationId,
        string $batchNumber,
        string $expiryDate,
        ?string $manufactureDate = null,
        array $additionalData = []
    ): Inventory
    {
        return DB::transaction(function () use ($purchaseItem, $quantity, $locationId, $batchNumber, $expiryDate, $manufactureDate, $additionalData) {
            // Create inventory record
            $inventory = Inventory::create([
                'medicine_id' => $purchaseItem->medicine_id,
                'purchase_item_id' => $purchaseItem->id,
                'batch_number' => $batchNumber,
                'expiry_date' => $expiryDate,
                'manufacture_date' => $manufactureDate,
                'quantity' => $quantity,
                'unit_cost' => $purchaseItem->unit_price,
                'location_id' => $locationId,
                'warehouse_id' => $additionalData['warehouse_id'] ?? null,
                'rack_number' => $additionalData['rack_number'] ?? null,
                'bin_location' => $additionalData['bin_location'] ?? null,
                'temperature_requirement' => $additionalData['temperature_requirement'] ?? null,
                'notes' => $additionalData['notes'] ?? "Created from purchase order {$purchaseItem->purchase->purchase_number}",
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            // Log the inventory creation
            Log::info('Inventory created from purchase item', [
                'inventory_id' => $inventory->id,
                'purchase_item_id' => $purchaseItem->id,
                'medicine_id' => $purchaseItem->medicine_id,
                'quantity' => $quantity,
                'batch_number' => $batchNumber,
            ]);

            return $inventory;
        });
    }

    /**
     * Update inventory quantity (for stock movements)
     * 
     * @param Inventory $inventory
     * @param int $quantityChange
     * @param string $reason
     * @return Inventory
     */
    public function updateQuantity(Inventory $inventory, int $quantityChange, string $reason = ''): Inventory
    {
        return DB::transaction(function () use ($inventory, $quantityChange, $reason) {
            $oldQuantity = $inventory->quantity;
            $newQuantity = $oldQuantity + $quantityChange;

            if ($newQuantity < 0) {
                throw new \InvalidArgumentException('Inventory quantity cannot be negative.');
            }

            $inventory->update([
                'quantity' => $newQuantity,
                'updated_by' => auth()->id(),
            ]);

            // Log the quantity change
            Log::info('Inventory quantity updated', [
                'inventory_id' => $inventory->id,
                'old_quantity' => $oldQuantity,
                'new_quantity' => $newQuantity,
                'change' => $quantityChange,
                'reason' => $reason,
            ]);

            return $inventory;
        });
    }

    /**
     * Get current stock for a medicine
     * 
     * @param int $medicineId
     * @return int
     */
    public function getCurrentStock(int $medicineId): int
    {
        return Inventory::where('medicine_id', $medicineId)
            ->sum('quantity');
    }

    /**
     * Get active batches for a medicine (non-expired with quantity > 0)
     * 
     * @param int $medicineId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getActiveBatches(int $medicineId)
    {
        return Inventory::where('medicine_id', $medicineId)
            ->where('quantity', '>', 0)
            ->where(function ($query) {
                $query->whereNull('expiry_date')
                      ->orWhere('expiry_date', '>', now());
            })
            ->orderBy('expiry_date')
            ->get();
    }

    /**
     * Get expiring batches for a medicine
     * 
     * @param int $medicineId
     * @param int $days
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getExpiringBatches(int $medicineId, int $days = 90)
    {
        return Inventory::where('medicine_id', $medicineId)
            ->where('quantity', '>', 0)
            ->whereNotNull('expiry_date')
            ->where('expiry_date', '>', now())
            ->where('expiry_date', '<=', now()->addDays($days))
            ->orderBy('expiry_date')
            ->get();
    }

    /**
     * Get expired batches for a medicine
     * 
     * @param int $medicineId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getExpiredBatches(int $medicineId)
    {
        return Inventory::where('medicine_id', $medicineId)
            ->where('quantity', '>', 0)
            ->whereNotNull('expiry_date')
            ->where('expiry_date', '<=', now())
            ->orderBy('expiry_date')
            ->get();
    }

    /**
     * Check if a medicine needs reordering
     * 
     * @param int $medicineId
     * @return bool
     */
    public function needsReorder(int $medicineId): bool
    {
        $medicine = \App\Models\Inventory\Medicine::find($medicineId);
        if (!$medicine) {
            return false;
        }

        $currentStock = $this->getCurrentStock($medicineId);
        return $currentStock <= $medicine->minimum_stock;
    }

    /**
     * Get inventory by location
     * 
     * @param int $locationId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getInventoryByLocation(int $locationId)
    {
        return Inventory::where('location_id', $locationId)
            ->where('quantity', '>', 0)
            ->with(['medicine', 'purchaseItem.purchase'])
            ->get();
    }

    /**
     * Transfer inventory between locations
     * 
     * @param Inventory $inventory
     * @param int $newLocationId
     * @param int $quantity
     * @param string $reason
     * @return Inventory|null
     */
    public function transferToLocation(Inventory $inventory, int $newLocationId, int $quantity, string $reason = ''): ?Inventory
    {
        if ($quantity > $inventory->quantity) {
            throw new \InvalidArgumentException('Transfer quantity cannot exceed available quantity.');
        }

        return DB::transaction(function () use ($inventory, $newLocationId, $quantity, $reason) {
            // Reduce quantity from source inventory
            $this->updateQuantity($inventory, -$quantity, "Transfer to location {$newLocationId}: {$reason}");

            // Create new inventory record at destination location
            $newInventory = Inventory::create([
                'medicine_id' => $inventory->medicine_id,
                'purchase_item_id' => $inventory->purchase_item_id,
                'batch_number' => $inventory->batch_number,
                'expiry_date' => $inventory->expiry_date,
                'manufacture_date' => $inventory->manufacture_date,
                'quantity' => $quantity,
                'unit_cost' => $inventory->unit_cost,
                'location_id' => $newLocationId,
                'warehouse_id' => $inventory->warehouse_id,
                'rack_number' => $inventory->rack_number,
                'bin_location' => $inventory->bin_location,
                'temperature_requirement' => $inventory->temperature_requirement,
                'notes' => "Transferred from location {$inventory->location_id}: {$reason}",
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            Log::info('Inventory transferred between locations', [
                'source_inventory_id' => $inventory->id,
                'destination_inventory_id' => $newInventory->id,
                'source_location_id' => $inventory->location_id,
                'destination_location_id' => $newLocationId,
                'quantity' => $quantity,
                'reason' => $reason,
            ]);

            return $newInventory;
        });
    }
}
