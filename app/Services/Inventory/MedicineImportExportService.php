<?php

namespace App\Services\Inventory;

use App\Exports\MedicineExport;
use App\Imports\MedicineImport;
use App\Models\Inventory\Medicine;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class MedicineImportExportService
{
    /**
     * Export medicines to Excel/CSV
     *
     * @param array $filters
     * @param string $format
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportMedicines(array $filters = [], string $format = 'xlsx')
    {
        // Validate format
        $allowedFormats = ['xlsx', 'xls', 'csv'];
        if (!in_array(strtolower($format), $allowedFormats)) {
            $format = 'xlsx'; // Default to xlsx if invalid format
        }

        $filename = 'medicines_export_' . now()->format('Y-m-d_H-i-s') . '.' . strtolower($format);

        try {
            return Excel::download(new MedicineExport($filters), $filename);
        } catch (\Exception $e) {
            Log::error('Medicine export error: ' . $e->getMessage());
            throw new \Exception('Failed to export medicines: ' . $e->getMessage());
        }
    }

    /**
     * Import medicines from Excel/CSV
     *
     * @param UploadedFile $file
     * @param bool $updateExisting
     * @return array
     */
    public function importMedicines(UploadedFile $file, bool $updateExisting = false): array
    {
        try {
            $import = new MedicineImport($updateExisting);
            
            Excel::import($import, $file);
            
            $stats = $import->getImportStats();
            $errors = $import->errors();
            $failures = $import->failures();
            
            // Format error messages
            $errorMessages = [];
            
            // Add validation errors
            foreach ($failures as $failure) {
                $errorMessages[] = "Row {$failure->row()}: " . implode(', ', $failure->errors());
            }
            
            // Add general errors
            foreach ($errors as $error) {
                $errorMessages[] = $error->getMessage();
            }
            
            $success = $stats['imported'] > 0 || $stats['updated'] > 0;
            
            return [
                'success' => $success,
                'message' => $this->getImportMessage($stats),
                'details' => $this->getImportDetails($stats),
                'errors' => $errorMessages,
                'stats' => $stats
            ];
            
        } catch (\Exception $e) {
            Log::error('Medicine import error: ' . $e->getMessage());
            
            return [
                'success' => false,
                'message' => 'Import failed: ' . $e->getMessage(),
                'details' => [],
                'errors' => [$e->getMessage()],
                'stats' => [
                    'imported' => 0,
                    'updated' => 0,
                    'skipped' => 0,
                    'errors' => 1,
                    'failures' => 0
                ]
            ];
        }
    }

    /**
     * Generate import template
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function generateTemplate()
    {
        $filename = 'medicine_import_template.xlsx';
        
        try {
            // Create sample data for template
            $sampleData = collect([
                [
                    'name' => 'Paracetamol',
                    'generic_name' => 'Acetaminophen',
                    'dosage' => '500mg',
                    'category' => 'Analgesics',
                    'manufacturer' => 'Sample Pharma',
                    'unit_type' => 'Tablet',
                    'minimum_stock' => 100,
                    'maximum_stock' => 1000,
                    'unit_price' => 2.50,
                    'supplier_price_carton' => 200.00,
                    'supplier_price_box' => 50.00,
                    'supplier_price_strip' => 5.00,
                    'supplier_price_unit' => 2.00,
                    'retail_price_carton' => 300.00,
                    'retail_price_box' => 75.00,
                    'retail_price_strip' => 7.50,
                    'retail_price_unit' => 2.50,
                    'strips_per_box' => 10,
                    'pieces_per_strip' => 10,
                    'box_quantity' => 1,
                    'controlled_substance' => 'No',
                    'prescription_required' => 'No',
                    'enabled_units' => 'box, strip, unit',
                    'enabled_retail_units' => 'box, strip, unit',
                    'status' => 'active'
                ]
            ]);
            
            // Create export with sample data
            $export = new class($sampleData) implements 
                \Maatwebsite\Excel\Concerns\FromCollection,
                \Maatwebsite\Excel\Concerns\WithHeadings,
                \Maatwebsite\Excel\Concerns\WithStyles,
                \Maatwebsite\Excel\Concerns\ShouldAutoSize
            {
                protected $data;
                
                public function __construct($data)
                {
                    $this->data = $data;
                }
                
                public function collection()
                {
                    return $this->data;
                }
                
                public function headings(): array
                {
                    return [
                        'name',
                        'generic_name',
                        'dosage',
                        'category',
                        'manufacturer',
                        'unit_type',
                        'minimum_stock',
                        'maximum_stock',
                        'unit_price',
                        'supplier_price_carton',
                        'supplier_price_box',
                        'supplier_price_strip',
                        'supplier_price_unit',
                        'retail_price_carton',
                        'retail_price_box',
                        'retail_price_strip',
                        'retail_price_unit',
                        'strips_per_box',
                        'pieces_per_strip',
                        'box_quantity',
                        'controlled_substance',
                        'prescription_required',
                        'enabled_units',
                        'enabled_retail_units',
                        'status'
                    ];
                }
                
                public function styles(\PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet)
                {
                    return [
                        1 => ['font' => ['bold' => true]],
                    ];
                }
            };
            
            return Excel::download($export, $filename);
            
        } catch (\Exception $e) {
            Log::error('Template generation error: ' . $e->getMessage());
            throw new \Exception('Failed to generate template: ' . $e->getMessage());
        }
    }

    /**
     * Get import message based on stats
     */
    protected function getImportMessage(array $stats): string
    {
        $total = $stats['imported'] + $stats['updated'];
        
        if ($total === 0) {
            return 'No medicines were imported.';
        }
        
        $message = "Successfully processed {$total} medicine(s).";
        
        if ($stats['imported'] > 0) {
            $message .= " {$stats['imported']} new medicine(s) created.";
        }
        
        if ($stats['updated'] > 0) {
            $message .= " {$stats['updated']} medicine(s) updated.";
        }
        
        if ($stats['skipped'] > 0) {
            $message .= " {$stats['skipped']} medicine(s) skipped.";
        }
        
        return $message;
    }

    /**
     * Get import details
     */
    protected function getImportDetails(array $stats): array
    {
        $details = [];
        
        if ($stats['imported'] > 0) {
            $details[] = "{$stats['imported']} new medicines imported";
        }
        
        if ($stats['updated'] > 0) {
            $details[] = "{$stats['updated']} medicines updated";
        }
        
        if ($stats['skipped'] > 0) {
            $details[] = "{$stats['skipped']} medicines skipped";
        }
        
        if ($stats['errors'] > 0) {
            $details[] = "{$stats['errors']} errors encountered";
        }
        
        if ($stats['failures'] > 0) {
            $details[] = "{$stats['failures']} validation failures";
        }
        
        return $details;
    }
}
