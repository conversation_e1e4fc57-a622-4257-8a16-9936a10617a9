<?php

namespace App\Services\Inventory;

use App\Models\Inventory\BatchHistory;
use App\Models\Inventory\Medicine;
use App\Models\Inventory\Location;
use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Auth;

class BatchHistoryService
{
    /**
     * Get all medicines for filtering
     *
     * @return Collection
     */
    public function getAllMedicines(): Collection
    {
        return Medicine::all();
    }

    /**
     * Get all locations for filtering
     *
     * @return Collection
     */
    public function getAllLocations(): Collection
    {
        return Location::all();
    }

    /**
     * Get filtered batch histories
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getFilteredBatchHistories(array $filters, int $perPage = 15): LengthAwarePaginator
    {
        $query = BatchHistory::with(['medicine', 'location', 'user'])
            ->when(isset($filters['medicine']), function ($query) use ($filters) {
                $query->where('medicine_id', $filters['medicine']);
            })
            ->when(isset($filters['location']), function ($query) use ($filters) {
                $query->where('location_id', $filters['location']);
            })
            ->when(isset($filters['action_type']), function ($query) use ($filters) {
                $query->where('action_type', $filters['action_type']);
            })
            ->when(isset($filters['date_range']), function ($query) use ($filters) {
                $dates = explode(' to ', $filters['date_range']);
                $query->whereBetween('created_at', $dates);
            });

        return $query->latest()->paginate($perPage);
    }

    /**
     * Get batch history details
     *
     * @param BatchHistory $history
     * @return BatchHistory
     */
    public function getBatchHistoryDetails(BatchHistory $history): BatchHistory
    {
        return $history->load(['medicine', 'location', 'user']);
    }

    /**
     * Export batch histories to CSV
     *
     * @param array $filters
     * @return array
     */
    public function exportBatchHistories(array $filters): array
    {
        $query = BatchHistory::with(['medicine', 'location', 'user'])
            ->when(isset($filters['medicine']), function ($query) use ($filters) {
                $query->where('medicine_id', $filters['medicine']);
            })
            ->when(isset($filters['location']), function ($query) use ($filters) {
                $query->where('location_id', $filters['location']);
            })
            ->when(isset($filters['action_type']), function ($query) use ($filters) {
                $query->where('action_type', $filters['action_type']);
            })
            ->when(isset($filters['date_range']), function ($query) use ($filters) {
                $dates = explode(' to ', $filters['date_range']);
                $query->whereBetween('created_at', $dates);
            });

        $histories = $query->latest()->get();

        $csvData = [];
        $csvData[] = [
            'Date',
            'Medicine',
            'Location',
            'Action Type',
            'Quantity',
            'Batch Number',
            'Expiry Date',
            'User'
        ];

        foreach ($histories as $history) {
            $csvData[] = [
                $history->created_at->format('Y-m-d H:i:s'),
                $history->medicine->name,
                $history->location->name,
                $history->action_type,
                $history->quantity,
                $history->batch_number,
                $history->expiry_date ? $history->expiry_date->format('Y-m-d') : '',
                $history->user->name
            ];
        }

        return [
            'data' => $csvData,
            'filename' => 'batch_history_' . now()->format('Y-m-d_H-i-s') . '.csv'
        ];
    }

    /**
     * Create a new batch history record
     *
     * @param array $data
     * @return BatchHistory
     */
    public function createBatchHistory(array $data): BatchHistory
    {
        // Ensure required fields are present
        $requiredFields = [
            'medicine_id',
            'batch_number',
            'expiry_date',
            'action_type',
            'quantity',
            'location_id'
        ];

        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                throw new \InvalidArgumentException("Missing required field: {$field}");
            }
        }

        // Validate action type
        $validActionTypes = ['created', 'moved', 'adjusted', 'expired'];
        if (!in_array($data['action_type'], $validActionTypes)) {
            throw new \InvalidArgumentException("Invalid action type: {$data['action_type']}");
        }

        // Add created_by if not set
        if (!isset($data['created_by'])) {
            $data['created_by'] = Auth::id();
        }

        return BatchHistory::create($data);
    }

    /**
     * Update a batch history record
     *
     * @param BatchHistory $history
     * @param array $data
     * @return BatchHistory
     */
    public function updateBatchHistory(BatchHistory $history, array $data): BatchHistory
    {
        // Validate action type if present
        if (isset($data['action_type'])) {
            $validActionTypes = ['created', 'moved', 'adjusted', 'expired'];
            if (!in_array($data['action_type'], $validActionTypes)) {
                throw new \InvalidArgumentException("Invalid action type: {$data['action_type']}");
            }
        }

        $history->update($data);
        return $history->fresh();
    }
}
