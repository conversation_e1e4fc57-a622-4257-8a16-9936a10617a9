<?php

namespace App\Services\Inventory;

use App\Models\Inventory\UnitType;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

class UnitTypeService
{
    /**
     * Get paginated list of unit types with search and filter options
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getPaginatedUnitTypes(array $filters = [], int $perPage = 10): LengthAwarePaginator
    {
        $query = UnitType::query();

        // Apply search if provided
        if (!empty($filters['search'])) {
            $query->search($filters['search']);
        }

        // Apply status filter if provided
        if (isset($filters['status']) && $filters['status'] !== '') {
            if ($filters['status'] === 'active') {
                $query->where('is_active', true);
            } elseif ($filters['status'] === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Apply category filter if provided
        if (isset($filters['category']) && $filters['category'] !== '') {
            $query->where('category', $filters['category']);
        }

        // Apply base unit filter if provided
        if (isset($filters['is_base']) && $filters['is_base'] !== '') {
            $query->where('is_base', $filters['is_base'] === 'yes');
        }

        // Apply sorting
        if (isset($filters['sort']) && $filters['sort'] !== '') {
            switch ($filters['sort']) {
                case 'name_asc':
                    $query->orderBy('name', 'asc');
                    break;
                case 'name_desc':
                    $query->orderBy('name', 'desc');
                    break;
                case 'oldest':
                    $query->oldest();
                    break;
                case 'newest':
                default:
                    $query->latest();
                    break;
            }
        } else {
            $query->latest();
        }

        return $query->paginate($perPage)->withQueryString();
    }

    /**
     * Search unit types by term
     *
     * @param string $term
     * @return Collection
     */
    public function searchUnitTypes(string $term): Collection
    {
        return UnitType::search($term)
            ->orderBy('name')
            ->limit(10)
            ->get();
    }

    /**
     * Get all active unit types
     *
     * @return Collection
     */
    public function getAllActiveUnitTypes(): Collection
    {
        return UnitType::where('is_active', true)
            ->orderBy('name')
            ->get();
    }

    /**
     * Create a new unit type
     *
     * @param array $data
     * @return UnitType
     */
    public function createUnitType(array $data): UnitType
    {
        try {
            return UnitType::create([
                'name' => $data['name'],
                'slug' => Str::slug($data['name']),
                'abbreviation' => $data['abbreviation'],
                'code' => $data['code'],
                'category' => $data['category'],
                'description' => $data['description'] ?? null,
                'is_active' => $data['is_active'] ?? true,
                'is_base' => $data['is_base'] ?? false
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating unit type: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update an existing unit type
     *
     * @param UnitType $unitType
     * @param array $data
     * @return UnitType
     */
    public function updateUnitType(UnitType $unitType, array $data): UnitType
    {
        try {
            $unitType->update([
                'name' => $data['name'],
                'slug' => Str::slug($data['name']),
                'abbreviation' => $data['abbreviation'],
                'description' => $data['description'] ?? null,
                'is_active' => $data['is_active'] ?? true,
            ]);

            return $unitType->fresh();
        } catch (\Exception $e) {
            Log::error('Error updating unit type: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete a unit type
     *
     * @param UnitType $unitType
     * @return bool
     * @throws \Exception
     */
    public function deleteUnitType(UnitType $unitType): bool
    {
        try {
            // Check for associated medicines
            if ($unitType->medicines()->exists()) {
                throw new \Exception('Cannot delete unit type. It has associated medicines.');
            }

            // Check for associated unit conversions
            if ($unitType->fromConversions()->exists() || $unitType->toConversions()->exists()) {
                throw new \Exception('Cannot delete unit type. It has associated unit conversions.');
            }

            return $unitType->delete();
        } catch (\Exception $e) {
            Log::error('Error deleting unit type: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Find a unit type by ID
     *
     * @param int $id
     * @return UnitType
     */
    public function findUnitType(int $id): UnitType
    {
        return UnitType::findOrFail($id);
    }
}
