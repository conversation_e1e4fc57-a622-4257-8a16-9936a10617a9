<?php

namespace App\Services\Inventory;

use App\Models\Inventory\Medicine;
use App\Models\Inventory\Location;
use App\Models\Inventory\Manufacturer;
use App\Models\Inventory\Inventory;
use App\Models\Inventory\Purchase;
use App\Models\Inventory\PurchaseItem;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Illuminate\Database\QueryException;

class MedicineService
{
    protected $user;
    protected $purchaseService;

    public function __construct(MedicinePurchaseService $purchaseService)
    {
        $this->user = Auth::id();
        $this->purchaseService = $purchaseService;
    }

    public function createMedicine(array $data)
    {
        DB::beginTransaction();
        try {
            // Check for duplicate batch number
            $existingInventory = Inventory::where('batch_number', $data['batch_number'])->first();
            if ($existingInventory) {
                throw new \Exception('Batch number already exists for this medicine');
            }

            // Validate tax and discount percentages
            if (isset($data['tax']) && $data['tax'] > 100) {
                throw new \InvalidArgumentException('Tax percentage cannot exceed 100%');
            }
            if (isset($data['discount']) && $data['discount'] > 100) {
                throw new \InvalidArgumentException('Discount percentage cannot exceed 100%');
            }

            // Extract inventory data
            $inventoryData = [
                'quantity' => $data['initial_quantity'],
                'expiry_date' => $data['expiry_date'],
                'unit_price' => $data['unit_cost'],
                'batch_number' => $data['batch_number']
            ];
            
            // Extract purchase data
            $purchaseData = [
                'purchase_number' => $data['purchase_number'],
                'order_date' => $data['purchase_date'],
                'tax_percentage' => $data['tax'] ?? 0,
                'discount_percentage' => $data['discount'] ?? 0,
                'shipping_cost' => $data['shipping_cost'] ?? 0,
                'notes' => $data['purchase_notes'] ?? null,
                'expected_date' => $data['purchase_date'],
                'unit_cost' => $data['unit_cost']  // Keep this for purchase item creation
            ];
            
            // Calculate purchase amounts
            $subtotal = $data['initial_quantity'] * $data['unit_cost'];
            $taxAmount = round($subtotal * ($purchaseData['tax_percentage'] / 100), 2);
            $discountAmount = round($subtotal * ($purchaseData['discount_percentage'] / 100), 2);
            $finalAmount = $subtotal + $taxAmount - $discountAmount + $purchaseData['shipping_cost'];

            $purchaseData['total_amount'] = $subtotal;
            $purchaseData['tax_amount'] = $taxAmount;
            $purchaseData['discount_amount'] = $discountAmount;
            $purchaseData['final_amount'] = $finalAmount;
            
            // Remove inventory and purchase fields from medicine data
            unset(
                $data['initial_quantity'],
                $data['expiry_date'],
                $data['unit_price'],
                $data['movement_type'],
                $data['batch_number'],
                $data['manufacture_date'],
                $data['purchase_number'],
                $data['purchase_date'],
                $data['unit_cost'],
                $data['tax'],
                $data['discount'],
                $data['shipping_cost'],
                $data['total_amount'],
                $data['purchase_notes']
            );
            
            // Verify manufacturer exists and is active
            $manufacturer = Manufacturer::findOrFail($data['manufacturer_id']);
            if (!$manufacturer->is_active) {
                throw new \Exception('Selected manufacturer is not active');
            }
            
            // Extract supplier ID before creating medicine
            $supplierId = $data['supplier_id'];
            unset($data['supplier_id']);

            // Handle enabled units
            if (isset($data['enabled_units']) && !is_array($data['enabled_units'])) {
                $data['enabled_units'] = [$data['enabled_units']];
            }

            // Set default values for price fields
            $this->setDefaultPrices($data);

            // Get location for inventory
            $location = Location::findOrFail($data['location_id']);
            $locationId = $data['location_id'];
            unset($data['location_id']);
            
            // Set unit_price from retail_price_unit
            $data['unit_price'] = $data['retail_price_unit'];
            
            // Create medicine
            $medicine = Medicine::create($data);

            // Attach supplier
            if ($supplierId) {
                $medicine->suppliers()->attach($supplierId, [
                    'price' => $data['supplier_price_unit'],
                    'is_default' => true,
                    'created_by' => $this->user
                ]);
            }

            // Create inventory record
            $inventoryData['medicine_id'] = $medicine->id;
            $inventoryData['location_id'] = $locationId;
            $inventoryData['created_by'] = $this->user;
            
            try {
                $inventory = Inventory::create($inventoryData);
            } catch (QueryException $e) {
                if (str_contains($e->getMessage(), 'inventories_medicine_batch_unique')) {
                    throw new \Exception('Batch number already exists for this medicine');
                }
                throw $e;
            }

            // Create purchase record
            $purchaseCreateData = array_merge(
                array_diff_key($purchaseData, ['unit_cost' => '']),
                [
                    'supplier_id' => $supplierId,
                    'created_by' => $this->user,
                    'updated_by' => $this->user
                ]
            );
            $purchase = Purchase::create($purchaseCreateData);

            // Create purchase item
            try {
                PurchaseItem::create([
                    'purchase_id' => $purchase->id,
                    'medicine_id' => $medicine->id,
                    'batch_number' => $inventoryData['batch_number'],
                    'quantity' => $inventoryData['quantity'],
                    'unit_price' => $purchaseData['unit_cost'],
                    'tax_percentage' => $purchaseData['tax_percentage'],
                    'tax_amount' => $purchaseData['tax_amount'],
                    'discount_percentage' => $purchaseData['discount_percentage'],
                    'discount_amount' => $purchaseData['discount_amount'],
                    'total_amount' => $purchaseData['total_amount'],
                    'expiry_date' => $inventoryData['expiry_date'],
                    'created_by' => $this->user
                ]);
            } catch (QueryException $e) {
                if (str_contains($e->getMessage(), 'purchase_items_medicine_batch_unique')) {
                    throw new \Exception('Batch number already exists for this medicine');
                }
                throw $e;
            }

            DB::commit();
            return $medicine;
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function updateMedicine(Medicine $medicine, array $data, array $inventoryData, string $movementType = 'addition')
    {
        DB::beginTransaction();

        try {
            // Remove inventory fields from medicine data
            unset(
                $data['quantity'], 
                $data['expiry_date'], 
                $data['unit_price'],
                $data['movement_type'],
                $data['batch_number'],
                $data['manufacture_date']  // Remove manufacture_date as it's not in medicines table
            );

            // Handle enabled units
            $data['enabled_units'] = $data['enabled_units'] ?? [];
            $data['enabled_retail_units'] = $data['enabled_retail_units'] ?? [];

            // Set default values for price fields
            $this->setDefaultPrices($data);

            // Get location for inventory
            $location = Location::findOrFail($data['location_id']);
            $locationId = $data['location_id'];
            unset($data['location_id']);

            // Get manufacturer - we only need manufacturer_id, not the name
            $manufacturer = Manufacturer::findOrFail($data['manufacturer_id']);
            
            // Extract supplier ID
            $supplierId = $data['supplier_id'];
            unset($data['supplier_id']);
            
            // Update medicine data
            $medicine->update($data);

            // Sync supplier
            $medicine->suppliers()->sync([$supplierId]);
            
            // Handle inventory update based on movement type
            $movementQuantity = $inventoryData['quantity'];
            if ($movementType === 'reduction') {
                $movementQuantity = -$movementQuantity;
            }
            
            // Prepare inventory data
            $inventoryRecord = array_merge($inventoryData, [
                'medicine_id' => $medicine->id,
                'quantity' => $movementQuantity,
                'location_id' => $locationId,
                'notes' => implode("\n", array_filter([
                    isset($data['manufacture_date']) ? "Manufacture Date: {$data['manufacture_date']}" : null,
                    "Manufacturer: {$manufacturer->name}"
                ])),
                'created_by' => $this->user,
                'updated_by' => $this->user
            ]);
            
            // Create inventory record
            $inventory = new Inventory($inventoryRecord);
            $inventory->save();
            
            // Create stock movement record
            $stockMovement = $medicine->stockMovements()->make([
                'quantity' => $movementQuantity,
                'movement_type' => $movementType,
                'movement_date' => now(),
                'reference_number' => $inventoryData['batch_number'],
                'batch_number' => $inventoryData['batch_number'],
                'notes' => "Stock {$movementType} via medicine update",
                'source_location_id' => $locationId,
                'destination_location_id' => $locationId
            ]);
            $stockMovement->save();

            DB::commit();
            return $medicine;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update only medicine information without affecting inventory data
     *
     * @param Medicine $medicine
     * @param array $data
     * @return Medicine
     */
    public function updateMedicineInfo(Medicine $medicine, array $data)
    {
        DB::beginTransaction();

        try {
            // Handle enabled units
            $data['enabled_units'] = $data['enabled_units'] ?? [];
            $data['enabled_retail_units'] = $data['enabled_retail_units'] ?? [];

            // Set default values for price fields
            $this->setDefaultPrices($data);

            // Extract supplier ID
            $supplierId = $data['supplier_id'] ?? null;
            unset($data['supplier_id']);
            
            // Update medicine data
            $medicine->update($data);

            // Sync supplier if provided
            if ($supplierId) {
                $medicine->suppliers()->sync([$supplierId]);
            }

            DB::commit();
            return $medicine;
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function deactivateMedicine(int $id)
    {
        DB::beginTransaction();
        try {
            $medicine = Medicine::findOrFail($id);
            $medicine->update(['status' => 'inactive']);
            $medicine->delete(); // Soft delete
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function getMedicineStats(Medicine $medicine)
    {
        // Calculate total quantity from inventories
        $totalQuantity = $medicine->inventories->sum('quantity');
        
        // Get active batches count
        $activeBatches = $medicine->inventories()
            ->where('quantity', '>', 0)
            ->where(function($query) {
                $query->whereNull('expiry_date')
                      ->orWhere('expiry_date', '>', now());
            })
            ->count();
        
        // Get unique locations count
        $uniqueLocations = $medicine->stockMovements()
            ->distinct()
            ->select('source_location_id', 'destination_location_id')
            ->get()
            ->flatMap(function ($movement) {
                return [$movement->source_location_id, $movement->destination_location_id];
            })
            ->unique()
            ->count();

        // Calculate average unit price from active inventories
        $activeInventories = $medicine->inventories()
            ->where('quantity', '>', 0)
            ->where(function($query) {
                $query->whereNull('expiry_date')
                      ->orWhere('expiry_date', '>', now());
            })
            ->get();

        $averagePrice = 0;
        if ($activeInventories->isNotEmpty()) {
            $totalValue = $activeInventories->sum(function ($inventory) {
                return $inventory->quantity * $inventory->unit_price;
            });
            $totalQuantityActive = $activeInventories->sum('quantity');
            $averagePrice = $totalQuantityActive > 0 ? $totalValue / $totalQuantityActive : 0;
        }

        return compact('totalQuantity', 'activeBatches', 'uniqueLocations', 'averagePrice');
    }

    public function getMedicineBatches(Medicine $medicine)
    {
        return $medicine->inventories()
            ->with('location')
            ->where('quantity', '>', 0)
            ->orderBy('expiry_date')
            ->get()
            ->map(function ($batch) {
                $batch->status = $this->getBatchStatus($batch);
                $batch->location_name = $batch->location ? $batch->location->name : 'Unknown';
                $batch->location_type = $batch->location ? $batch->location->type : 'Unknown';
                return $batch;
            });
    }

    private function getBatchStatus($batch)
    {
        if (!$batch->expiry_date) {
            return 'Expiry Not Set';
        }

        $today = now();
        $expiryDate = Carbon::parse($batch->expiry_date);
        $monthsUntilExpiry = $today->diffInMonths($expiryDate, false);

        if ($expiryDate < $today) {
            return 'Expired';
        } elseif ($monthsUntilExpiry <= 3) {
            return 'Expiring Soon';
        } else {
            return 'Valid';
        }
    }

    private function setDefaultPrices(array &$data)
    {
        $priceFields = [
            'supplier_price_carton', 'supplier_price_box', 'supplier_price_strip', 'supplier_price_unit',
            'retail_price_carton', 'retail_price_box', 'retail_price_strip', 'retail_price_unit'
        ];
        
        foreach ($priceFields as $field) {
            if (!isset($data[$field])) {
                $data[$field] = 0;
            }
        }
    }

    /**
     * Update only inventory information without modifying medicine data
     *
     * @param Medicine $medicine
     * @param array $inventoryData
     * @param string $movementType
     * @param int $locationId
     * @return Medicine
     */
    public function updateInventory(Medicine $medicine, array $inventoryData, string $movementType = 'addition', int $locationId = null)
    {
        DB::beginTransaction();

        try {
            // Ensure we have a valid locationId
            if (!$locationId) {
                // Try to get location from medicine's previous inventory
                $latestInventory = $medicine->inventories()->latest('created_at')->first();
                if ($latestInventory && $latestInventory->location_id) {
                    $locationId = $latestInventory->location_id;
                } else {
                    // If no previous inventory, use the default location
                    $defaultLocation = Location::where('is_default', true)->first();
                    $locationId = $defaultLocation ? $defaultLocation->id : 1; // Fallback to ID 1 if no default
                }
            }
            
            // Check for duplicate batch number
            $existingInventory = Inventory::where('batch_number', $inventoryData['batch_number'])
                ->where('medicine_id', $medicine->id)
                ->where('location_id', $locationId)
                ->first();
                
            if ($existingInventory) {
                throw new \Exception('This batch number already exists for this medicine at this location. Please use a different batch number.');
            }
            
            // Prepare inventory data
            $movementQuantity = $inventoryData['quantity'];
            if ($movementType === 'subtraction') {
                $movementQuantity = -$movementQuantity;
            }
            
            // Get manufacturer name for notes
            $manufacturer = $medicine->manufacturer;
            $manufacturerName = $manufacturer ? $manufacturer->name : 'Unknown';
            
            // Convert empty expiry_date string to null
            $expiryDate = isset($inventoryData['expiry_date']) && !empty($inventoryData['expiry_date']) 
                ? $inventoryData['expiry_date'] 
                : null;
            
            // Generate notes based on expiry date presence
            $expiryNote = $expiryDate 
                ? "" 
                : "No expiry tracking. ";
                
            // Create inventory record
            $inventory = new Inventory([
                'medicine_id' => $medicine->id,
                'quantity' => $movementQuantity,
                'location_id' => $locationId,
                'unit_price' => $inventoryData['unit_price'],
                'batch_number' => $inventoryData['batch_number'],
                'expiry_date' => $expiryDate,
                'notes' => $expiryNote . "Inventory {$movementType} via update form",
                'created_by' => $this->user,
                'updated_by' => $this->user
            ]);
            
            try {
                $inventory->save();
            } catch (QueryException $e) {
                // Check if this is a duplicate entry error
                if (str_contains($e->getMessage(), 'Duplicate entry') && 
                    str_contains($e->getMessage(), 'inventories_medicine_batch_location_unique')) {
                    throw new \Exception('This batch number already exists for this medicine at this location. Please use a different batch number.');
                }
                throw $e;
            }
            
            // Create stock movement record
            $stockMovement = $medicine->stockMovements()->make([
                'quantity' => $movementQuantity,
                'movement_type' => $movementType,
                'movement_date' => now(),
                'reference_number' => $inventoryData['batch_number'],
                'batch_number' => $inventoryData['batch_number'],
                'notes' => $expiryNote . "Stock {$movementType} via inventory update",
                'source_location_id' => $locationId,
                'destination_location_id' => $locationId
            ]);
            $stockMovement->save();

            // Create purchase order for inventory addition
            if ($movementType === 'addition' && $movementQuantity > 0) {
                // Get supplier ID from medicine's suppliers or null
                $supplierId = $medicine->suppliers->first()->id ?? null;
                
                // Prepare purchase data
                $purchaseData = [
                    'quantity' => $movementQuantity,
                    'unit_price' => $inventoryData['unit_price'],
                    'expiry_date' => $expiryDate,
                    'batch_number' => $inventoryData['batch_number'],
                    'supplier_id' => $supplierId,
                    'order_date' => $inventoryData['purchase_date'] ?? now(),
                    'expected_date' => $inventoryData['purchase_date'] ?? now(),
                    'delivery_date' => $inventoryData['purchase_date'] ?? now(),
                    'location_id' => $locationId,
                    'notes' => $inventoryData['purchase_notes'] ?? "Purchase created via inventory update for {$medicine->name}",
                    'status' => 'pending',
                    'payment_status' => 'pending',
                    'skip_inventory_creation' => true, // Skip inventory creation as we already created it
                    'tax_percentage' => $inventoryData['tax'] ?? 0,
                    'discount_percentage' => $inventoryData['discount'] ?? 0,
                    'shipping_cost' => $inventoryData['shipping_cost'] ?? 0,
                    'purchase_number' => $inventoryData['purchase_number'] ?? null
                ];
                
                // Create purchase order with pending status
                if (isset($this->purchaseService)) {
                    $purchase = $this->purchaseService->createInitialPurchase($medicine, $purchaseData);
                }
            }

            // Don't update medicine's location_id as the column doesn't exist in the medicines table
            // The location is stored in the inventory record instead
            
            DB::commit();
            return $medicine;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
