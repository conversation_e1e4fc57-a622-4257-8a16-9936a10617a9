<?php

namespace App\Services\Inventory;

use App\Models\Inventory\Category;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class CategoryService
{
    /**
     * Get paginated list of categories with search and filter options
     *
     * @param Request|null $request
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getPaginatedCategories(Request $request = null, int $perPage = 15): LengthAwarePaginator
    {
        $query = Category::with('parent');

        // Apply search if provided
        if ($request && $request->filled('search')) {
            $searchTerm = $request->input('search');
            $query->where(function($q) use ($searchTerm) {
                $q->where('name', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('description', 'LIKE', "%{$searchTerm}%")
                  ->orWhereHas('parent', function($subQuery) use ($searchTerm) {
                      $subQuery->where('name', 'LIKE', "%{$searchTerm}%");
                  });
            });
        }

        // Apply parent filter if provided
        if ($request && $request->filled('parent_id')) {
            $parentId = $request->input('parent_id');
            if ($parentId === 'none') {
                $query->whereNull('parent_id');
            } else {
                $query->where('parent_id', $parentId);
            }
        }

        // Apply status filter if provided
        if ($request && $request->filled('status')) {
            $status = $request->input('status');
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Apply sorting
        if ($request && $request->filled('sort')) {
            $sortField = 'created_at';
            $sortDirection = 'desc';

            switch ($request->input('sort')) {
                case 'name_asc':
                    $sortField = 'name';
                    $sortDirection = 'asc';
                    break;
                case 'name_desc':
                    $sortField = 'name';
                    $sortDirection = 'desc';
                    break;
                case 'newest':
                    $sortField = 'created_at';
                    $sortDirection = 'desc';
                    break;
                case 'oldest':
                    $sortField = 'created_at';
                    $sortDirection = 'asc';
                    break;
            }

            $query->orderBy($sortField, $sortDirection);
        } else {
            $query->latest();
        }

        return $query->paginate($perPage);
    }

    /**
     * Get parent categories
     *
     * @param Category|null $excludeCategory
     * @return Collection
     */
    public function getParentCategories(?Category $excludeCategory = null): Collection
    {
        $query = Category::where('parent_id', null);
        
        if ($excludeCategory) {
            $query->where('id', '!=', $excludeCategory->id);
        }

        return $query->get();
    }

    /**
     * Create a new category
     *
     * @param array $data
     * @return Category
     */
    public function createCategory(array $data): Category
    {
        try {
            return Category::create([
                'name' => $data['name'],
                'slug' => Str::slug($data['name']),
                'description' => $data['description'] ?? null,
                'parent_id' => $data['parent_id'] ?? null,
                'is_active' => $data['is_active'] ?? true,
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating category: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update an existing category
     *
     * @param Category $category
     * @param array $data
     * @return Category
     */
    public function updateCategory(Category $category, array $data): Category
    {
        try {
            $category->update([
                'name' => $data['name'],
                'slug' => Str::slug($data['name']),
                'description' => $data['description'] ?? null,
                'parent_id' => $data['parent_id'] ?? null,
                'is_active' => $data['is_active'] ?? true,
            ]);

            return $category->fresh();
        } catch (\Exception $e) {
            Log::error('Error updating category: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete a category
     *
     * @param Category $category
     * @return bool
     * @throws \Exception
     */
    public function deleteCategory(Category $category): bool
    {
        try {
            if ($category->medicines()->exists()) {
                throw new \Exception('Cannot delete category. It has associated medicines.');
            }

            if ($category->children()->exists()) {
                throw new \Exception('Cannot delete category. It has sub-categories.');
            }

            return $category->delete();
        } catch (\Exception $e) {
            Log::error('Error deleting category: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get category tree for hierarchical display
     *
     * @return Collection
     */
    public function getCategoryTree(): Collection
    {
        return Category::with('children')
            ->whereNull('parent_id')
            ->orderBy('name')
            ->get();
    }
}
