<?php

namespace App\Services\Inventory;

use App\Models\Inventory\Location;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class LocationService
{
    /**
     * Get paginated list of locations with optional filters
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getPaginatedLocations(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return Location::query()
            ->when(isset($filters['section']), function ($q) use ($filters) {
                $q->where('section', $filters['section']);
            })
            ->when(isset($filters['zone']), function ($q) use ($filters) {
                $q->where('zone', $filters['zone']);
            })
            ->when(isset($filters['temperature']), function ($q) use ($filters) {
                $q->where('temperature_requirement', $filters['temperature']);
            })
            ->when(isset($filters['search']), function ($q) use ($filters) {
                $search = $filters['search'];
                $q->where(function ($query) use ($search) {
                    $query->where('name', 'like', "%{$search}%")
                        ->orWhere('location_code', 'like', "%{$search}%")
                        ->orWhere('rack_number', 'like', "%{$search}%")
                        ->orWhere('bin_location', 'like', "%{$search}%");
                });
            })
            ->orderBy('path')
            ->paginate($perPage);
    }

    /**
     * Get parent locations for selection
     *
     * @param Location|null $excludeLocation
     * @return Collection
     */
    public function getParentLocations(?Location $excludeLocation = null): Collection
    {
        $query = Location::whereIn('type', ['warehouse', 'store'])
            ->orderBy('path');

        if ($excludeLocation) {
            $query->where('id', '!=', $excludeLocation->id);
        }

        return $query->get(['id', 'name', 'type', 'path']);
    }

    /**
     * Generate location code for shelf type locations
     *
     * @param array $data
     * @param Location|null $parent
     * @return string|null
     */
    private function generateLocationCode(array $data, ?Location $parent): ?string
    {
        if ($data['type'] !== 'shelf' || !$parent) {
            return null;
        }

        $prefix = substr($parent->name, 0, 3);
        $code = sprintf(
            '%s-%s-%s-%s-%s',
            strtoupper($prefix),
            $data['section'] ? substr($data['section'], 0, 3) : 'XXX',
            $data['zone'] ? substr(str_replace(' ', '', $data['zone']), 0, 3) : 'XXX',
            $data['aisle_number'] ?? 'XXX',
            $data['rack_number'] ?? 'XXX'
        );

        if (!empty($data['bin_location'])) {
            $code .= '-' . $data['bin_location'];
        }

        return $code;
    }

    /**
     * Create a new location
     *
     * @param array $data
     * @return Location
     * @throws \Exception
     */
    public function createLocation(array $data): Location
    {
        try {
            DB::beginTransaction();

            $parent = null;
            if (!empty($data['parent_id'])) {
                $parent = Location::findOrFail($data['parent_id']);
                $data['level'] = $parent->level + 1;
                $data['path'] = $parent->getFullPath();
            } else {
                $data['level'] = 0;
                $data['path'] = '';
            }

            $data['location_code'] = $this->generateLocationCode($data, $parent);
            $data['created_by'] = auth()->id();

            $location = Location::create($data);

            DB::commit();
            return $location;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating location: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update an existing location
     *
     * @param Location $location
     * @param array $data
     * @return Location
     * @throws \Exception
     */
    public function updateLocation(Location $location, array $data): Location
    {
        try {
            DB::beginTransaction();

            if ($location->type === 'shelf') {
                $data['location_code'] = $this->generateLocationCode(
                    array_merge(['type' => 'shelf'], $data),
                    $location->parent
                );
            }

            $data['updated_by'] = auth()->id();
            $location->update($data);

            DB::commit();
            return $location->fresh();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating location: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete a location
     *
     * @param Location $location
     * @return bool
     * @throws \Exception
     */
    public function deleteLocation(Location $location): bool
    {
        try {
            DB::beginTransaction();

            if ($location->children()->exists()) {
                throw new \Exception('Cannot delete location with child locations.');
            }

            if ($location->batchHistories()->exists()) {
                throw new \Exception('Cannot delete location with existing stock.');
            }

            $result = $location->delete();

            DB::commit();
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting location: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Find suitable locations based on criteria
     *
     * @param array $criteria
     * @return Collection
     */
    public function findSuitableLocations(array $criteria): Collection
    {
        return Location::query()
            ->availableForStorage()
            ->when(isset($criteria['temperature']), function ($q) use ($criteria) {
                $q->where('temperature_requirement', $criteria['temperature']);
            })
            ->when(isset($criteria['section']), function ($q) use ($criteria) {
                $q->where('section', $criteria['section']);
            })
            ->when(isset($criteria['controlled']) && $criteria['controlled'], function ($q) {
                $q->where('section', Location::SECTION_CTR);
            })
            ->get();
    }
}
