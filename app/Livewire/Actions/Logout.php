<?php

namespace App\Livewire\Actions;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class Logout
{
    /**
     * Log the current user out of the application.
     */
    public function __invoke(): void
    {
        // Get the current user to clear any relevant data
        $user = Auth::user();
        
        // Logout the user
        Auth::guard('web')->logout();
        
        // Invalidate the session
        Session::invalidate();
        
        // Regenerate the CSRF token
        Session::regenerateToken();
        
        // Set a session flag to indicate that the user has been logged out
        Session::put('auth.logout', true);
    }
}
