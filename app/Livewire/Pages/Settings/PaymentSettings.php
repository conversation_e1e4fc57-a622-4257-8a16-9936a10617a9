<?php

namespace App\Livewire\Pages\Settings;

class PaymentSettings extends BaseSettings
{
    public $defaultMethod = 'cash';
    public $stripeEnabled = false;
    public $stripeKey;
    public $stripeSecret;
    public $paypalEnabled = false;
    public $paypalClientId;
    public $paypalSecret;
    public $bankTransferEnabled = false;
    public $bankDetails;
    public $cashEnabled = true;
    public $message;

    public function mount()
    {
        $this->defaultMethod = $this->getSetting('default_method', 'cash');
        $this->stripeEnabled = filter_var($this->getSetting('stripe_enabled', false), FILTER_VALIDATE_BOOLEAN);
        $this->stripeKey = $this->getSetting('stripe_key');
        $this->stripeSecret = $this->getSetting('stripe_secret');
        $this->paypalEnabled = filter_var($this->getSetting('paypal_enabled', false), FILTER_VALIDATE_BOOLEAN);
        $this->paypalClientId = $this->getSetting('paypal_client_id');
        $this->paypalSecret = $this->getSetting('paypal_secret');
        $this->bankTransferEnabled = filter_var($this->getSetting('bank_transfer_enabled', false), FILTER_VALIDATE_BOOLEAN);
        $this->bankDetails = $this->getSetting('bank_details');
        $this->cashEnabled = filter_var($this->getSetting('cash_enabled', true), FILTER_VALIDATE_BOOLEAN);
    }

    protected function rules()
    {
        $rules = [
            'defaultMethod' => ['required', 'string', function($attribute, $value, $fail) {
                if (!$this->isMethodEnabled($value)) {
                    $fail('The default payment method must be enabled.');
                }
            }],
            'cashEnabled' => 'boolean',
            'stripeEnabled' => 'boolean',
            'paypalEnabled' => 'boolean',
            'bankTransferEnabled' => 'boolean',
        ];

        // Add conditional validation for payment method credentials
        if ($this->stripeEnabled) {
            $rules['stripeKey'] = 'required|string';
            $rules['stripeSecret'] = 'required|string';
        }

        if ($this->paypalEnabled) {
            $rules['paypalClientId'] = 'required|string';
            $rules['paypalSecret'] = 'required|string';
        }

        if ($this->bankTransferEnabled) {
            $rules['bankDetails'] = 'required|string';
        }

        return $rules;
    }

    protected function isMethodEnabled($method)
    {
        return match ($method) {
            'cash' => $this->cashEnabled,
            'stripe' => $this->stripeEnabled,
            'paypal' => $this->paypalEnabled,
            'bank_transfer' => $this->bankTransferEnabled,
            default => false,
        };
    }

    public function save()
    {
        $this->validate($this->rules());

        // Ensure at least one payment method is enabled
        if (!$this->cashEnabled && !$this->stripeEnabled && !$this->paypalEnabled && !$this->bankTransferEnabled) {
            $this->addError('defaultMethod', 'At least one payment method must be enabled.');
            return;
        }

        // Save payment method settings
        $this->saveSetting('default_method', $this->defaultMethod, 'payment');
        $this->saveSetting('cash_enabled', $this->cashEnabled, 'payment');
        $this->saveSetting('stripe_enabled', $this->stripeEnabled, 'payment');
        $this->saveSetting('paypal_enabled', $this->paypalEnabled, 'payment');
        $this->saveSetting('bank_transfer_enabled', $this->bankTransferEnabled, 'payment');

        // Save payment credentials with encryption
        if ($this->stripeEnabled) {
            $this->saveSetting('stripe_key', $this->stripeKey, 'payment', true);
            $this->saveSetting('stripe_secret', $this->stripeSecret, 'payment', true);
        }

        if ($this->paypalEnabled) {
            $this->saveSetting('paypal_client_id', $this->paypalClientId, 'payment', true);
            $this->saveSetting('paypal_secret', $this->paypalSecret, 'payment', true);
        }

        if ($this->bankTransferEnabled) {
            $this->saveSetting('bank_details', $this->bankDetails, 'payment');
        }

        $this->message = 'Settings saved successfully.';
    }

    public function render()
    {
        return view('livewire.pages.settings.payment-settings')
            ->layout('layouts.admin');
    }
}
