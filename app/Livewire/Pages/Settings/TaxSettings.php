<?php

namespace App\Livewire\Pages\Settings;

class TaxSettings extends BaseSettings
{
    public $enableTax = false;
    public $defaultTaxRate;
    public $taxNumber;
    public $taxRules = [];
    public $newRule = [
        'name' => '',
        'rate' => '',
        'applies_to' => 'all',
        'category_id' => null
    ];

    public function mount()
    {
        $this->enableTax = $this->getSetting('enable_tax', false);
        $this->defaultTaxRate = $this->getSetting('default_tax_rate', '0');
        $this->taxNumber = $this->getSetting('tax_number');
        $this->taxRules = json_decode($this->getSetting('tax_rules', '[]'), true);
    }

    public function addRule()
    {
        $this->validate([
            'newRule.name' => 'required|string|max:50',
            'newRule.rate' => 'required|numeric|min:0|max:100',
            'newRule.applies_to' => 'required|in:all,category',
            'newRule.category_id' => 'required_if:newRule.applies_to,category|nullable|exists:categories,id'
        ]);

        $this->taxRules[] = $this->newRule;
        $this->newRule = [
            'name' => '',
            'rate' => '',
            'applies_to' => 'all',
            'category_id' => null
        ];
    }

    public function removeRule($index)
    {
        unset($this->taxRules[$index]);
        $this->taxRules = array_values($this->taxRules);
    }

    public function save()
    {
        $this->validate([
            'defaultTaxRate' => 'required|numeric|min:0|max:100',
            'taxNumber' => 'nullable|string|max:50',
        ]);

        $this->saveSettings([
            'enable_tax' => $this->enableTax,
            'default_tax_rate' => $this->defaultTaxRate,
            'tax_number' => $this->taxNumber,
            'tax_rules' => json_encode($this->taxRules),
        ]);

        session()->flash('success', 'Tax settings updated successfully.');
    }

    public function render()
    {
        return view('livewire.pages.settings.tax-settings')
            ->layout('layouts.admin');
    }
}
