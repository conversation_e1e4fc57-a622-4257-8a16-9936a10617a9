<?php

namespace App\Livewire\Pages\Settings;

use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Livewire\WithPagination;

class BackupSettings extends BaseSettings
{
    use WithPagination;
    
    public $backupFrequency;
    public $backupRetentionDays;
    public $backupLocation;
    public $lastBackupDate;
    public $backupInProgress = false;
    public $backupEnabled;
    public $confirmingBackupDeletion = false;
    public $backupToDelete = null;
    public $searchBackup = '';
    
    protected $listeners = ['refreshBackups' => '$refresh'];

    public function mount()
    {
        $this->backupFrequency = $this->getSetting('backup_frequency', 'daily');
        $this->backupRetentionDays = $this->getSetting('backup_retention_days', '30');
        $this->backupLocation = $this->getSetting('backup_location', 'local');
        $this->lastBackupDate = $this->getSetting('last_backup_date');
        $this->backupEnabled = (bool) $this->getSetting('backup_enabled', true);
        
        // Ensure backup directory exists
        $backupPath = storage_path('app/backups');
        if (!is_dir($backupPath)) {
            mkdir($backupPath, 0755, true);
        }
    }

    public function save()
    {
        $this->validate([
            'backupFrequency' => 'required|in:daily,weekly,monthly',
            'backupRetentionDays' => 'required|integer|min:1|max:365',
            'backupLocation' => 'required|in:local,s3',
            'backupEnabled' => 'boolean',
        ]);

        $this->saveSettings([
            'backup_frequency' => $this->backupFrequency,
            'backup_retention_days' => $this->backupRetentionDays,
            'backup_location' => $this->backupLocation,
            'backup_enabled' => $this->backupEnabled,
        ]);

        session()->flash('success', 'Backup settings updated successfully.');
    }

    public function createBackup()
    {
        $this->backupInProgress = true;
        
        try {
            // Run backup command
            $output = Artisan::call('backup:run');
            $commandOutput = Artisan::output();
            
            // Save the last backup date
            $this->saveSetting('last_backup_date', now()->toDateTimeString());
            $this->lastBackupDate = now()->toDateTimeString();
            
            session()->flash('success', 'Backup created successfully.');
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to create backup: ' . $e->getMessage());
            \Log::error('Backup failed: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
        }

        $this->backupInProgress = false;
    }

    public function downloadLatestBackup()
    {
        try {
            $backupPath = storage_path('app/backups/latest.zip');
        
        if (!file_exists($backupPath)) {
            session()->flash('error', 'No backup file found.');
            return;
        }

            // Return the file as a download
            return response()->download($backupPath, 'pharmadesk-backup-' . now()->format('Y-m-d') . '.zip');
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to download backup: ' . $e->getMessage());
            \Log::error('Backup download failed: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
    
    public function downloadBackup($filename)
    {
        try {
            $backupPath = storage_path("app/backups/{$filename}");
            
            if (!file_exists($backupPath)) {
                session()->flash('error', 'Backup file not found.');
                return;
            }
            
            return response()->download($backupPath, $filename);
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to download backup: ' . $e->getMessage());
            \Log::error('Backup download failed: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
    
    public function confirmBackupDeletion($filename)
    {
        $this->confirmingBackupDeletion = true;
        $this->backupToDelete = $filename;
    }
    
    public function deleteBackup()
    {
        try {
            if (!$this->backupToDelete) {
                return;
            }
            
            $backupPath = storage_path("app/backups/{$this->backupToDelete}");
            
            if (file_exists($backupPath)) {
                File::delete($backupPath);
                session()->flash('success', 'Backup deleted successfully.');
            } else {
                session()->flash('error', 'Backup file not found.');
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to delete backup: ' . $e->getMessage());
            \Log::error('Backup deletion failed: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
        }
        
        $this->confirmingBackupDeletion = false;
        $this->backupToDelete = null;
    }
    
    public function toggleBackupStatus()
    {
        $this->backupEnabled = !$this->backupEnabled;
        $this->saveSettings([
            'backup_enabled' => $this->backupEnabled,
        ]);
        
        $status = $this->backupEnabled ? 'enabled' : 'disabled';
        session()->flash('success', "Automatic backups {$status} successfully.");
    }
    
    public function getBackupsProperty()
    {
        $backupPath = storage_path('app/backups');
        $files = [];
        
        if (File::isDirectory($backupPath)) {
            $allFiles = File::files($backupPath);
            
            foreach ($allFiles as $file) {
                $filename = $file->getFilename();
                
                // Skip the latest.zip file as it's just a copy of the most recent backup
                if ($filename === 'latest.zip') {
                    continue;
                }
                
                // Apply search filter if provided
                if ($this->searchBackup && !str_contains(strtolower($filename), strtolower($this->searchBackup))) {
                    continue;
                }
                
                $files[] = [
                    'name' => $filename,
                    'size' => $this->formatBytes($file->getSize()),
                    'date' => date('Y-m-d H:i:s', $file->getMTime()),
                    'path' => $file->getPathname(),
                ];
            }
            
            // Sort by date (newest first)
            usort($files, function($a, $b) {
                return strtotime($b['date']) - strtotime($a['date']);
            });
        }
        
        return $files;
    }
    
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= (1 << (10 * $pow));
        
        return round($bytes, $precision) . ' ' . $units[$pow];
    }

    public function render()
    {
        return view('livewire.pages.settings.backup-settings', [
            'backups' => $this->backups,
        ])->layout('layouts.admin');
    }
}
