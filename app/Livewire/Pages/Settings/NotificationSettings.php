<?php

namespace App\Livewire\Pages\Settings;

use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Twi<PERSON>\Rest\Client as TwilioClient;
use Vonage\Client as VonageClient;
use Vonage\Client\Credentials\Basic as VonageCredentials;
use Vonage\SMS\Message\SMS as VonageMessage;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\Attributes\Layout;
use App\Traits\HasSettings;

#[Layout('layouts.admin')]
class NotificationSettings extends Component
{
    use HasSettings;
    use WithFileUploads;

    // Email Settings
    public $emailEnabled = false;
    public $smtpHost;
    public $smtpPort;
    public $smtpUsername;
    public $smtpPassword;
    public $smtpEncryption = 'tls';
    public $fromAddress;
    public $fromName;

    // SMS Settings
    public $smsEnabled = false;
    public $smsProvider = 'twilio';
    public $smsAccountSid;
    public $smsAuthToken;
    public $smsFromNumber;

    // Test Configuration
    public $testEmailAddress;
    public $testPhoneNumber;

    public $message;

    public function mount()
    {
        // Load Email Settings
        $this->emailEnabled = filter_var($this->getSetting('email_enabled', false), FILTER_VALIDATE_BOOLEAN);
        $this->smtpHost = $this->getSetting('smtp_host');
        $this->smtpPort = $this->getSetting('smtp_port');
        $this->smtpUsername = $this->getSetting('smtp_username');
        $this->smtpPassword = $this->getSetting('smtp_password');
        $this->smtpEncryption = $this->getSetting('smtp_encryption', 'tls');
        $this->fromAddress = $this->getSetting('from_address');
        $this->fromName = $this->getSetting('from_name');

        // Load SMS Settings
        $this->smsEnabled = filter_var($this->getSetting('sms_enabled', false), FILTER_VALIDATE_BOOLEAN);
        $this->smsProvider = $this->getSetting('sms_provider', 'twilio');
        $this->smsAccountSid = $this->getSetting('sms_account_sid');
        $this->smsAuthToken = $this->getSetting('sms_auth_token');
        $this->smsFromNumber = $this->getSetting('sms_from_number');
    }

    protected function rules()
    {
        $rules = [
            'emailEnabled' => 'boolean',
            'smsEnabled' => 'boolean',
            'smtpEncryption' => 'string|in:tls,ssl',
            'smsProvider' => 'string|in:twilio,nexmo',
            'testEmailAddress' => '********|email',
            'testPhoneNumber' => ['********', 'string', 'regex:/^\+[1-9]\d{1,14}$/'],
        ];

        if ($this->emailEnabled) {
            $rules = array_merge($rules, [
                'smtpHost' => 'required|string',
                'smtpPort' => 'required|numeric',
                'smtpUsername' => 'required|string',
                'smtpPassword' => 'required|string',
                'fromAddress' => 'required|email',
                'fromName' => 'required|string',
            ]);
        }

        if ($this->smsEnabled) {
            $rules = array_merge($rules, [
                'smsProvider' => 'required|string',
                'smsAccountSid' => 'required|string',
                'smsAuthToken' => 'required|string',
                'smsFromNumber' => ['required', 'string', 'regex:/^\+[1-9]\d{1,14}$/'],
            ]);
        }

        return $rules;
    }

    public function save()
    {
        $this->validate($this->rules());

        // Save Email Settings
        $this->saveSetting('email_enabled', $this->emailEnabled, 'notification');
        
        if ($this->emailEnabled) {
            $this->saveSetting('smtp_host', $this->smtpHost, 'notification');
            $this->saveSetting('smtp_port', $this->smtpPort, 'notification');
            $this->saveSetting('smtp_username', $this->smtpUsername, 'notification', true);
            $this->saveSetting('smtp_password', $this->smtpPassword, 'notification', true);
            $this->saveSetting('smtp_encryption', $this->smtpEncryption, 'notification');
            $this->saveSetting('from_address', $this->fromAddress, 'notification');
            $this->saveSetting('from_name', $this->fromName, 'notification');
        }

        // Save SMS Settings
        $this->saveSetting('sms_enabled', $this->smsEnabled, 'notification');
        
        if ($this->smsEnabled) {
            $this->saveSetting('sms_provider', $this->smsProvider, 'notification');
            $this->saveSetting('sms_account_sid', $this->smsAccountSid, 'notification', true);
            $this->saveSetting('sms_auth_token', $this->smsAuthToken, 'notification', true);
            $this->saveSetting('sms_from_number', $this->smsFromNumber, 'notification');
        }

        $this->message = 'Settings saved successfully.';
    }

    public function sendTestEmail()
    {
        if (!$this->emailEnabled) {
            session()->flash('emailTestError', __('Email notifications are not enabled.'));
            return;
        }

        $this->validate([
            'testEmailAddress' => 'required|email',
            'smtpHost' => 'required|string',
            'smtpPort' => 'required|numeric',
            'smtpUsername' => 'required|string',
            'smtpPassword' => 'required|string',
            'fromAddress' => 'required|email',
            'fromName' => 'required|string',
        ]);

        try {
            // Create test email configuration
            $config = [
                'driver' => 'smtp',
                'host' => $this->smtpHost,
                'port' => $this->smtpPort,
                'username' => $this->smtpUsername,
                'password' => $this->smtpPassword,
                'encryption' => $this->smtpEncryption,
                'from' => [
                    'address' => $this->fromAddress,
                    'name' => $this->fromName,
                ],
                'timeout' => 30,
                'auth_mode' => null,
            ];

            // Set the temporary mail configuration
            config(['mail.mailers.smtp' => $config]);
            config(['mail.from' => ['address' => $this->fromAddress, 'name' => $this->fromName]]);

            // Send test email using the configuration
            Mail::mailer('smtp')->send([], [], function ($message) {
                $message->to($this->testEmailAddress)
                    ->subject(__('Test Email from :app', ['app' => config('app.name')]))
                    ->html(view('emails.test', [
                        'appName' => config('app.name'),
                        'testTime' => now()->format('Y-m-d H:i:s'),
                        'smtpHost' => $this->smtpHost,
                        'fromAddress' => $this->fromAddress,
                    ])->render());
            });

            session()->flash('emailTestSuccess', __('Test email sent successfully! Please check your inbox.'));
        } catch (\Exception $e) {
            session()->flash('emailTestError', __('Failed to send test email: ') . $e->getMessage());
            Log::error('Test email failed: ' . $e->getMessage(), [
                'host' => $this->smtpHost,
                'port' => $this->smtpPort,
                'username' => $this->smtpUsername,
                'encryption' => $this->smtpEncryption,
                'from_address' => $this->fromAddress,
                'from_name' => $this->fromName,
                'to' => $this->testEmailAddress,
            ]);
        }
    }

    public function sendTestSMS()
    {
        if (!$this->smsEnabled) {
            session()->flash('smsTestError', __('SMS notifications are not enabled.'));
            return;
        }

        $this->validate([
            'testPhoneNumber' => ['required', 'string', 'regex:/^\+[1-9]\d{1,14}$/'],
            'smsProvider' => 'required|string|in:twilio,nexmo',
            'smsAccountSid' => 'required|string',
            'smsAuthToken' => 'required|string',
            'smsFromNumber' => ['required', 'string', 'regex:/^\+[1-9]\d{1,14}$/'],
        ]);

        try {
            $message = __('This is a test message from your :app notification system.', ['app' => config('app.name')]);

            if ($this->smsProvider === 'twilio') {
                $twilio = new TwilioClient($this->smsAccountSid, $this->smsAuthToken);
                $twilio->messages->create(
                    $this->testPhoneNumber,
                    [
                        'from' => $this->smsFromNumber,
                        'body' => $message,
                    ]
                );
            } else {
                $vonage = new VonageClient(new VonageCredentials($this->smsAccountSid, $this->smsAuthToken));
                $vonage->sms()->send(
                    new VonageMessage(
                        $this->testPhoneNumber,
                        $this->smsFromNumber,
                        $message
                    )
                );
            }

            session()->flash('smsTestSuccess', __('Test SMS sent successfully! Please check your phone.'));
        } catch (\Exception $e) {
            session()->flash('smsTestError', __('Failed to send test SMS: ') . $e->getMessage());
            Log::error('Test SMS failed: ' . $e->getMessage(), [
                'provider' => $this->smsProvider,
                'account_sid' => $this->smsAccountSid,
                'from_number' => $this->smsFromNumber,
                'to_number' => $this->testPhoneNumber,
            ]);
        }
    }

    public function render()
    {
        return view('livewire.pages.settings.notification-settings');
    }
}
