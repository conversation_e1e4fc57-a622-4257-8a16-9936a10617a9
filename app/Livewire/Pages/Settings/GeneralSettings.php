<?php

namespace App\Livewire\Pages\Settings;

use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Storage;

class GeneralSettings extends BaseSettings
{
    use WithFileUploads;

    public $pharmacyName;
    public $address;
    public $phone;
    public $email;
    public $currency;
    public $timezone;
    public $logo;
    public $website;
    public $vatNumber;
    public $registrationNumber;
    public $message;

    public function mount()
    {
        $this->pharmacyName = $this->getSetting('pharmacy_name');
        $this->address = $this->getSetting('address');
        $this->phone = $this->getSetting('phone');
        $this->email = $this->getSetting('email');
        $this->currency = $this->getSetting('currency', 'USD');
        $this->timezone = $this->getSetting('timezone', 'UTC');
        $this->website = $this->getSetting('website');
        $this->vatNumber = $this->getSetting('vat_number');
        $this->registrationNumber = $this->getSetting('registration_number');
    }

    public function save()
    {
        $this->validate([
            'pharmacyName' => 'required|string|max:255',
            'address' => 'required|string|max:1000',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|max:255',
            'currency' => 'required|string|max:3',
            'timezone' => 'required|string|max:100',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:1024',
            'website' => 'nullable|url|max:255',
            'vatNumber' => 'nullable|string|max:100',
            'registrationNumber' => 'nullable|string|max:100',
        ], [
            'pharmacyName.required' => 'Pharmacy name is required.',
            'address.required' => 'Address is required.',
            'phone.required' => 'Phone number is required.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'currency.required' => 'Please select a currency.',
            'timezone.required' => 'Please select a timezone.',
            'logo.image' => 'Logo must be an image file.',
            'logo.mimes' => 'Logo must be a JPEG, PNG, JPG, or GIF file.',
            'logo.max' => 'Logo file size must not exceed 1MB.',
            'website.url' => 'Please enter a valid website URL.',
        ]);

        if ($this->logo) {
            // Delete old logo if exists
            $oldLogo = $this->getSetting('logo');
            if ($oldLogo && Storage::disk('public')->exists($oldLogo)) {
                Storage::disk('public')->delete($oldLogo);
            }

            $path = $this->logo->store('logos', 'public');
            $this->saveSetting('logo', $path);
        }

        $this->saveSetting('pharmacy_name', $this->pharmacyName);
        $this->saveSetting('address', $this->address);
        $this->saveSetting('phone', $this->phone);
        $this->saveSetting('email', $this->email);
        $this->saveSetting('currency', $this->currency);
        $this->saveSetting('timezone', $this->timezone);
        $this->saveSetting('website', $this->website);
        $this->saveSetting('vat_number', $this->vatNumber);
        $this->saveSetting('registration_number', $this->registrationNumber);

        $this->message = 'Settings saved successfully! Your changes have been applied.';

        // Clear the logo upload after saving
        $this->logo = null;
    }

    public function render()
    {
        return view('livewire.pages.settings.general-settings')
            ->layout('layouts.admin');
    }
}
