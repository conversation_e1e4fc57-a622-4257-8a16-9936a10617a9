<?php

namespace App\Livewire\Pages\Settings;

use Livewire\Component;
use App\Models\Setting;

abstract class BaseSettings extends Component
{
    protected function getSetting($key, $default = null, $group = 'general')
    {
        $setting = Setting::where('key', $key)
            ->where('group', $group)
            ->first();

        return $setting ? $setting->value : $default;
    }

    protected function saveSetting($key, $value, $group = 'general', $isEncrypted = false)
    {
        if (is_bool($value)) {
            $value = $value ? '1' : '0';
        }

        Setting::updateOrCreate(
            [
                'key' => $key,
                'group' => $group
            ],
            [
                'value' => $value,
                'is_encrypted' => $isEncrypted
            ]
        );
    }

    protected function saveSettings(array $settings, $group = 'general', $isEncrypted = false)
    {
        foreach ($settings as $key => $value) {
            $this->saveSetting($key, $value, $group, $isEncrypted);
        }
    }
}
