<?php

namespace App\Livewire\Pages\Settings;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

/**
 * Security Settings Component
 * 
 * Manages security-related settings including:
 * - Password policies
 * - Session management
 * - Two-factor authentication
 * - IP security
 * 
 * This component is part of the PharmaDesk settings management system
 * and follows PSR-12 coding standards.
 */
class SecuritySettings extends BaseSettings
{
    use AuthorizesRequests;

    // Password Settings
    public $minimumPasswordLength = 8;
    public $requireUppercase = true;
    public $requireNumbers = true;
    public $requireSpecialCharacters = true;
    public $passwordExpiryDays = 90;

    // Session Settings
    public $sessionTimeout = 30; // minutes
    public $maxLoginAttempts = 5;
    public $lockoutDuration = 30; // minutes

    // 2FA Settings
    public $twoFactorEnabled = false;
    public $twoFactorMethod = 'email'; // email, sms, authenticator
    public $twoFactorEnforced = false;
    public $twoFactorGracePeriod = 7; // days

    // IP Security
    public $ipWhitelisting = false;
    public $allowedIPs = [];
    public $blockTorNetwork = true;
    public $blockVPNs = true;

    public function mount()
    {
        $this->authorize('manage_settings');

        // Load Password Settings
        $this->minimumPasswordLength = (int) $this->getSetting('minimum_password_length', 8);
        $this->requireUppercase = filter_var($this->getSetting('require_uppercase', true), FILTER_VALIDATE_BOOLEAN);
        $this->requireNumbers = filter_var($this->getSetting('require_numbers', true), FILTER_VALIDATE_BOOLEAN);
        $this->requireSpecialCharacters = filter_var($this->getSetting('require_special_characters', true), FILTER_VALIDATE_BOOLEAN);
        $this->passwordExpiryDays = (int) $this->getSetting('password_expiry_days', 90);

        // Load Session Settings
        $this->sessionTimeout = (int) $this->getSetting('session_timeout', 30);
        $this->maxLoginAttempts = (int) $this->getSetting('max_login_attempts', 5);
        $this->lockoutDuration = (int) $this->getSetting('lockout_duration', 30);

        // Load 2FA Settings
        $this->twoFactorEnabled = filter_var($this->getSetting('two_factor_enabled', false), FILTER_VALIDATE_BOOLEAN);
        $this->twoFactorMethod = $this->getSetting('two_factor_method', 'email');
        $this->twoFactorEnforced = filter_var($this->getSetting('two_factor_enforced', false), FILTER_VALIDATE_BOOLEAN);
        $this->twoFactorGracePeriod = (int) $this->getSetting('two_factor_grace_period', 7);

        // Load IP Security Settings
        $this->ipWhitelisting = filter_var($this->getSetting('ip_whitelisting', false), FILTER_VALIDATE_BOOLEAN);
        $this->allowedIPs = explode(',', $this->getSetting('allowed_ips', ''));
        $this->blockTorNetwork = filter_var($this->getSetting('block_tor_network', true), FILTER_VALIDATE_BOOLEAN);
        $this->blockVPNs = filter_var($this->getSetting('block_vpns', true), FILTER_VALIDATE_BOOLEAN);
    }

    protected function rules()
    {
        return [
            'minimumPasswordLength' => 'required|integer|min:8|max:32',
            'requireUppercase' => 'boolean',
            'requireNumbers' => 'boolean',
            'requireSpecialCharacters' => 'boolean',
            'passwordExpiryDays' => 'required|integer|min:0|max:365',
            'sessionTimeout' => 'required|integer|min:5|max:1440',
            'maxLoginAttempts' => 'required|integer|min:1|max:10',
            'lockoutDuration' => 'required|integer|min:1|max:1440',
            'twoFactorEnabled' => 'boolean',
            'twoFactorMethod' => 'required_if:twoFactorEnabled,true|in:email,sms,authenticator',
            'twoFactorEnforced' => 'boolean',
            'twoFactorGracePeriod' => 'required_if:twoFactorEnforced,true|integer|min:1|max:30',
            'ipWhitelisting' => 'boolean',
            'allowedIPs' => 'array',
            'allowedIPs.*' => 'ip',
            'blockTorNetwork' => 'boolean',
            'blockVPNs' => 'boolean',
        ];
    }

    public function save()
    {
        $this->authorize('manage_settings');
        $this->validate();

        // Save Password Settings
        $this->saveSetting('minimum_password_length', $this->minimumPasswordLength, 'security');
        $this->saveSetting('require_uppercase', $this->requireUppercase, 'security');
        $this->saveSetting('require_numbers', $this->requireNumbers, 'security');
        $this->saveSetting('require_special_characters', $this->requireSpecialCharacters, 'security');
        $this->saveSetting('password_expiry_days', $this->passwordExpiryDays, 'security');

        // Save Session Settings
        $this->saveSetting('session_timeout', $this->sessionTimeout, 'security');
        $this->saveSetting('max_login_attempts', $this->maxLoginAttempts, 'security');
        $this->saveSetting('lockout_duration', $this->lockoutDuration, 'security');

        // Save 2FA Settings
        $this->saveSetting('two_factor_enabled', $this->twoFactorEnabled, 'security');
        $this->saveSetting('two_factor_method', $this->twoFactorMethod, 'security');
        $this->saveSetting('two_factor_enforced', $this->twoFactorEnforced, 'security');
        $this->saveSetting('two_factor_grace_period', $this->twoFactorGracePeriod, 'security');

        // Save IP Security Settings
        $this->saveSetting('ip_whitelisting', $this->ipWhitelisting, 'security');
        $this->saveSetting('allowed_ips', implode(',', array_filter($this->allowedIPs)), 'security');
        $this->saveSetting('block_tor_network', $this->blockTorNetwork, 'security');
        $this->saveSetting('block_vpns', $this->blockVPNs, 'security');

        session()->flash('success', 'Security settings updated successfully.');
    }

    public function render()
    {
        $this->authorize('manage_settings');
        return view('livewire.pages.settings.security-settings');
    }
}
