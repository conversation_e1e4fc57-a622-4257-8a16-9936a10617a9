<?php

namespace App\Livewire\Inventory;

use Livewire\Component;
use App\Models\Inventory\Purchase;
use App\Models\Inventory\PurchaseItem;
use App\Models\Inventory\Location;
use App\Services\Inventory\InventoryService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class ReceivePurchase extends Component
{
    public Purchase $purchase;
    public $receivingItems = [];
    public $locations = [];
    public $selectedLocation = null;
    public $notes = '';
    public $isPartialReceiving = false;

    protected $rules = [
        'selectedLocation' => 'required|exists:locations,id',
        'receivingItems.*.received_quantity' => 'required|numeric|min:0',
        'receivingItems.*.batch_number' => 'required|string|max:50',
        'receivingItems.*.expiry_date' => 'required|date|after_or_equal:today',
        'receivingItems.*.manufacture_date' => 'nullable|date|before_or_equal:today',
        'notes' => 'nullable|string|max:500',
    ];

    protected $messages = [
        'receivingItems.*.received_quantity.required' => 'Received quantity is required.',
        'receivingItems.*.received_quantity.min' => 'Received quantity must be at least 0.',
        'receivingItems.*.batch_number.required' => 'Batch number is required.',
        'receivingItems.*.expiry_date.required' => 'Expiry date is required.',
        'receivingItems.*.expiry_date.after_or_equal' => 'Expiry date must be today or in the future.',
        'receivingItems.*.manufacture_date.before_or_equal' => 'Manufacture date cannot be in the future.',
    ];

    public function mount(Purchase $purchase)
    {
        $this->purchase = $purchase->load(['items.medicine', 'supplier']);
        $this->locations = Location::where('is_active', true)->orderBy('name')->get();
        
        // Set default location to main warehouse if available
        $mainWarehouse = $this->locations->where('is_main_warehouse', true)->first();
        $this->selectedLocation = $mainWarehouse?->id ?? $this->locations->first()?->id;

        $this->initializeReceivingItems();
    }

    public function initializeReceivingItems()
    {
        $this->receivingItems = [];
        
        foreach ($this->purchase->items as $item) {
            $remainingQuantity = $item->quantity_ordered - $item->quantity_received;
            
            if ($remainingQuantity > 0) {
                $this->receivingItems[] = [
                    'purchase_item_id' => $item->id,
                    'medicine_name' => $item->medicine->getDisplayNameAttribute(),
                    'ordered_quantity' => $item->quantity_ordered,
                    'already_received' => $item->quantity_received,
                    'remaining_quantity' => $remainingQuantity,
                    'received_quantity' => $remainingQuantity, // Default to receiving all remaining
                    'batch_number' => $item->batch_number ?? '',
                    'expiry_date' => $item->expiry_date ? $item->expiry_date->format('Y-m-d') : '',
                    'manufacture_date' => $item->manufacture_date ? $item->manufacture_date->format('Y-m-d') : '',
                    'unit_price' => $item->unit_price,
                ];
            }
        }
    }

    public function updatedReceivingItems($value, $key)
    {
        $parts = explode('.', $key);
        if (count($parts) === 3) {
            $index = $parts[1];
            $field = $parts[2];
            
            if ($field === 'received_quantity') {
                $this->validateReceivedQuantity($index);
            }
        }
    }

    public function validateReceivedQuantity($index)
    {
        $item = $this->receivingItems[$index];
        $receivedQty = floatval($item['received_quantity']);
        $remainingQty = floatval($item['remaining_quantity']);
        
        if ($receivedQty > $remainingQty) {
            $this->receivingItems[$index]['received_quantity'] = $remainingQty;
            session()->flash('warning', 'Received quantity cannot exceed remaining quantity.');
        }
        
        if ($receivedQty < $remainingQty) {
            $this->isPartialReceiving = true;
        }
    }

    public function receiveAll()
    {
        foreach ($this->receivingItems as $index => $item) {
            $this->receivingItems[$index]['received_quantity'] = $item['remaining_quantity'];
        }
        $this->isPartialReceiving = false;
    }

    public function clearAll()
    {
        foreach ($this->receivingItems as $index => $item) {
            $this->receivingItems[$index]['received_quantity'] = 0;
        }
        $this->isPartialReceiving = true;
    }

    public function save()
    {
        $this->validate();

        try {
            DB::beginTransaction();

            $inventoryService = new InventoryService();
            $allReceived = true;
            $anyReceived = false;

            foreach ($this->receivingItems as $receivingItem) {
                $purchaseItem = PurchaseItem::findOrFail($receivingItem['purchase_item_id']);
                $receivedQty = floatval($receivingItem['received_quantity']);

                if ($receivedQty > 0) {
                    // Update purchase item with received information
                    $newReceivedQuantity = $purchaseItem->quantity_received + $receivedQty;
                    
                    $purchaseItem->update([
                        'quantity_received' => $newReceivedQuantity,
                        'batch_number' => $receivingItem['batch_number'],
                        'expiry_date' => $receivingItem['expiry_date'],
                        'manufacture_date' => $receivingItem['manufacture_date'] ?: null,
                        'location_id' => $this->selectedLocation,
                        'received_at' => now(),
                    ]);

                    // Create inventory record using the service
                    $inventoryService->createFromPurchaseItem(
                        $purchaseItem,
                        $receivedQty,
                        $this->selectedLocation,
                        $receivingItem['batch_number'],
                        $receivingItem['expiry_date'],
                        $receivingItem['manufacture_date'] ?: null
                    );

                    $anyReceived = true;
                }

                // Check if this item is fully received
                if ($purchaseItem->quantity_received < $purchaseItem->quantity_ordered) {
                    $allReceived = false;
                }
            }

            // Update purchase status
            if ($allReceived && $anyReceived) {
                $this->purchase->update([
                    'status' => 'received',
                    'delivery_date' => now(),
                ]);
            } elseif ($anyReceived) {
                $this->purchase->update([
                    'status' => 'partially_received',
                ]);
            }

            DB::commit();

            $message = $allReceived ? 'All items received successfully.' : 'Items partially received successfully.';
            session()->flash('success', $message);
            
            return redirect()->route('inventory.purchases.show', $this->purchase);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error receiving purchase order: ' . $e->getMessage(), [
                'purchase_id' => $this->purchase->id,
                'user_id' => Auth::id(),
                'receiving_items' => $this->receivingItems,
            ]);
            
            session()->flash('error', 'Error receiving items: ' . $e->getMessage());
        }
    }

    public function render()
    {
        return view('livewire.inventory.receive-purchase');
    }
}
