<?php

namespace App\Livewire\Inventory;

use Livewire\Component;
use App\Models\Inventory\Purchase;
use App\Models\Inventory\PurchaseItem;
use App\Models\Inventory\Medicine;
use App\Models\Inventory\Supplier;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class CreatePurchase extends Component
{
    public $purchase;
    public $items = [];
    public $supplier_id;
    public $order_date;
    public $expected_date;
    public $notes;
    public $tax_percentage = 0;
    public $discount_percentage = 0;
    public $shipping_cost = 0;
    public $loadingMedicines = false;

    protected $rules = [
        'supplier_id' => 'required|exists:suppliers,id',
        'order_date' => 'required|date',
        'expected_date' => 'required|date|after_or_equal:order_date',
        'notes' => 'nullable|string|max:500',
        'tax_percentage' => 'required|numeric|min:0|max:100',
        'discount_percentage' => 'required|numeric|min:0|max:100',
        'shipping_cost' => 'required|numeric|min:0',
        'items' => 'required|array|min:1',
        'items.*.medicine_id' => 'required|exists:medicines,id',
        'items.*.quantity_ordered' => 'required|numeric|min:1',
        'items.*.unit_price' => 'required|numeric|min:0',
    ];

    public function mount()
    {
        $this->order_date = now()->format('Y-m-d');
        $this->expected_date = now()->addDays(7)->format('Y-m-d');
        $this->addItem();
    }

    public function addItem()
    {
        $this->items[] = [
            'medicine_id' => '',
            'quantity_ordered' => 1,
            'unit_price' => 0,
            'tax_amount' => 0,
            'discount_amount' => 0,
            'total_amount' => 0,
            'price_editable' => false,
            'original_price' => 0,
        ];
    }

    public function removeItem($index)
    {
        unset($this->items[$index]);
        $this->items = array_values($this->items);
    }

    public function updatedItems($value, $key)
    {
        $parts = explode('.', $key);
        if (count($parts) === 3) {
            $index = $parts[1];
            $field = $parts[2];
            
            if ($field === 'medicine_id' && !empty($value) && !empty($this->supplier_id)) {
                // Set loading state to true
                $this->loadingMedicines = true;

                // Fetch the medicine's supplier price
                $medicine = Medicine::with(['suppliers' => function($query) {
                    $query->where('suppliers.id', $this->supplier_id);
                }])->find($value);

                if ($medicine) {
                    // Try to get the price from the pivot table first
                    $supplierPrice = $medicine->suppliers->first()?->pivot->price ?? null;

                    // If no price in pivot, use the medicine's supplier_price_unit
                    $finalPrice = $supplierPrice ?? $medicine->supplier_price_unit ?? 0;

                    // Set the price and mark as not editable initially
                    $this->items[$index]['unit_price'] = $finalPrice;
                    $this->items[$index]['price_editable'] = false;
                    $this->items[$index]['original_price'] = $finalPrice;

                    // Log for debugging
                    Log::info('Medicine selected', [
                        'medicine_id' => $value,
                        'name' => $medicine->name,
                        'supplier_price_unit' => $medicine->supplier_price_unit,
                        'pivot_price' => $supplierPrice,
                        'final_price' => $finalPrice
                    ]);

                    // Calculate the total after setting the price
                    $this->calculateItemTotal($index);
                }

                // Set loading state back to false
                $this->loadingMedicines = false;
            }
            
            if ($field === 'quantity_ordered' || $field === 'unit_price') {
                $this->calculateItemTotal($index);
            }
        }
    }

    public function updatedSupplierId($value)
    {
        $this->loadingMedicines = true;
        
        // Clear existing items when supplier changes
        $this->items = [];
        $this->addItem();
        
        $this->loadingMedicines = false;
    }

    public function calculateItemTotal($index)
    {
        if (empty($this->items[$index]['quantity_ordered']) || empty($this->items[$index]['unit_price'])) {
            return;
        }

        $quantity = floatval($this->items[$index]['quantity_ordered']);
        $unitPrice = floatval($this->items[$index]['unit_price']);
        $subtotal = $quantity * $unitPrice;

        // Calculate tax
        $taxAmount = $subtotal * ($this->tax_percentage / 100);
        $this->items[$index]['tax_amount'] = round($taxAmount, 2);

        // Calculate discount
        $discountAmount = $subtotal * ($this->discount_percentage / 100);
        $this->items[$index]['discount_amount'] = round($discountAmount, 2);

        // Calculate total
        $total = $subtotal + $taxAmount - $discountAmount;
        $this->items[$index]['total_amount'] = round($total, 2);
    }

    public function getSuppliers()
    {
        return Supplier::orderBy('name')->get();
    }

    public function getMedicines()
    {
        if (empty($this->supplier_id)) {
            return collect();
        }
        
        // Get medicines that are associated with the selected supplier
        $supplierMedicines = Medicine::with(['suppliers' => function($query) {
            $query->where('suppliers.id', $this->supplier_id);
        }])
        ->whereHas('suppliers', function($query) {
            $query->where('suppliers.id', $this->supplier_id);
        })
        ->orderBy('name')
        ->get();
        
        // If no specific medicines are associated with this supplier, return all active medicines
        if ($supplierMedicines->isEmpty()) {
            return Medicine::active()->orderBy('name')->get();
        }
        
        return $supplierMedicines;
    }

    public function getOrderTotal()
    {
        $subtotal = collect($this->items)->sum('total_amount');
        return $subtotal + floatval($this->shipping_cost);
    }

    public function save()
    {
        $this->validate();

        try {
            DB::beginTransaction();

            // Get the authenticated user's ID
            if (!Auth::check()) {
                throw new \Exception('User not authenticated');
            }
            $userId = Auth::id();

            // Create purchase order
            $purchase = Purchase::create([
                'supplier_id' => $this->supplier_id,
                'purchase_number' => 'PO-' . now()->format('Ymd') . '-' . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT),
                'order_date' => $this->order_date,
                'expected_date' => $this->expected_date,
                'status' => 'pending',
                'payment_status' => 'pending',
                'notes' => $this->notes,
                'tax_percentage' => $this->tax_percentage,
                'tax_amount' => collect($this->items)->sum('tax_amount'),
                'discount_percentage' => $this->discount_percentage,
                'discount_amount' => collect($this->items)->sum('discount_amount'),
                'shipping_cost' => $this->shipping_cost,
                'total_amount' => collect($this->items)->sum(function($item) {
                    return $item['quantity_ordered'] * $item['unit_price'];
                }),
                'final_amount' => $this->getOrderTotal(),
                'created_by' => $userId,
            ]);

            // Create purchase items
            foreach ($this->items as $item) {
                PurchaseItem::create([
                    'purchase_id' => $purchase->id,
                    'medicine_id' => $item['medicine_id'],
                    'quantity_ordered' => $item['quantity_ordered'],
                    'quantity_received' => 0, // Initially no items received
                    'unit_price' => $item['unit_price'],
                    'tax_percentage' => $this->tax_percentage,
                    'tax_amount' => $item['tax_amount'],
                    'discount_percentage' => $this->discount_percentage,
                    'discount_amount' => $item['discount_amount'],
                    'total_amount' => $item['total_amount'],
                    'created_by' => $userId,
                    // Expiry date and batch number will be set during receiving
                    'expiry_date' => null,
                    'batch_number' => null,
                ]);
            }

            DB::commit();

            session()->flash('success', 'Purchase order created successfully.');
            return redirect()->route('inventory.purchases.show', $purchase);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating purchase order: ' . $e->getMessage());
            throw $e;
        }
    }

    public function togglePriceEdit($index)
    {
        // If we have a medicine_id for this item, toggle the ability to edit the price
        if (!empty($this->items[$index]['medicine_id'])) {
            // Store the original price if not already stored
            if (!isset($this->items[$index]['original_price'])) {
                $this->items[$index]['original_price'] = $this->items[$index]['unit_price'];
            }
            
            // Toggle the editable state
            $this->items[$index]['price_editable'] = !($this->items[$index]['price_editable'] ?? false);
            
            // If we're turning off editing and the price was changed, log it
            if (!($this->items[$index]['price_editable'] ?? false) && 
                $this->items[$index]['original_price'] != $this->items[$index]['unit_price']) {
                Log::info('Price manually updated', [
                    'medicine_id' => $this->items[$index]['medicine_id'],
                    'original_price' => $this->items[$index]['original_price'],
                    'new_price' => $this->items[$index]['unit_price']
                ]);
            }
            
            // Recalculate totals
            $this->calculateItemTotal($index);
        }
    }

    public function render()
    {
        return view('livewire.inventory.create-purchase', [
            'suppliers' => $this->getSuppliers(),
            'medicines' => $this->getMedicines(),
            'orderTotal' => $this->getOrderTotal(),
        ]);
    }
}
