<?php

namespace App\Livewire\Inventory;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Inventory\Purchase;
use App\Models\Inventory\Supplier;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class PurchaseList extends Component
{
    use WithPagination;

    public $search = '';
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $filters = [
        'status' => '',
        'payment_status' => '',
        'supplier_id' => '',
        'start_date' => '',
        'end_date' => '',
    ];

    protected $queryString = [
        'search' => ['except' => ''],
        'sortField' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
        'filters',
    ];

    // Update the property to handle live updates
    protected $updatesQueryString = ['search', 'filters', 'sortField', 'sortDirection'];

    // Add listeners for real-time updates
    protected $listeners = ['updatedFilter'];

    protected function rules()
    {
        return [
            'filters.status' => ['nullable', Rule::in(['pending', 'ordered', 'received', 'partially_received', 'cancelled'])],
            'filters.payment_status' => ['nullable', Rule::in(['pending', 'partial', 'paid', 'overdue'])],
            'filters.supplier_id' => 'nullable|exists:suppliers,id',
            'filters.start_date' => 'nullable|date',
            'filters.end_date' => 'nullable|date|after_or_equal:filters.start_date',
        ];
    }

    public function updating($name, $value)
    {
        if (str_starts_with($name, 'filters.')) {
            $this->resetPage();
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedFilters($value, $key)
    {
        try {
            $this->validateFilters();
            $this->resetPage();
        } catch (\Illuminate\Validation\ValidationException $e) {
            // If date validation fails, reset the problematic filter
            if (str_contains($key, 'date')) {
                $this->filters[str_replace('filters.', '', $key)] = '';
            }
            $this->validateFilters();
        }
    }

    public function mount()
    {
        $this->validateFilters();
    }

    public function validateFilters()
    {
        try {
            $this->validateOnly('filters');
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Log validation errors for debugging
            Log::debug('Filter validation failed:', $e->errors());
            throw $e;
        }
    }

    public function resetFilters()
    {
        $this->reset(['search', 'filters', 'sortField', 'sortDirection']);
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
    }

    public function getSuppliers()
    {
        return Supplier::orderBy('name')
                      ->get(['id', 'name']);
    }

    protected function getFilteredQuery()
    {
        return Purchase::query()
            ->with(['supplier', 'items', 'payments'])
            ->when($this->search, function ($query) {
                $searchTerm = '%' . trim($this->search) . '%';
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('purchase_number', 'like', $searchTerm)
                      ->orWhere('notes', 'like', $searchTerm)
                      ->orWhereHas('supplier', function ($q) use ($searchTerm) {
                          $q->where('name', 'like', $searchTerm)
                            ->orWhere('contact_person', 'like', $searchTerm)
                            ->orWhere('email', 'like', $searchTerm)
                            ->orWhere('phone', 'like', $searchTerm);
                      })
                      ->orWhereHas('items.medicine', function ($q) use ($searchTerm) {
                          $q->where('name', 'like', $searchTerm)
                            ->orWhere('generic_name', 'like', $searchTerm);
                      });
                });
            })
            ->when($this->filters['status'], function ($query, $status) {
                $query->where('status', $status);
            })
            ->when($this->filters['payment_status'], function ($query, $paymentStatus) {
                $query->where('payment_status', $paymentStatus);
            })
            ->when($this->filters['supplier_id'], function ($query, $supplierId) {
                $query->where('supplier_id', $supplierId);
            })
            ->when($this->filters['start_date'] || $this->filters['end_date'], function ($query) {
                if ($this->filters['start_date'] && $this->filters['end_date']) {
                    $query->whereBetween(DB::raw('DATE(order_date)'), [
                        date('Y-m-d', strtotime($this->filters['start_date'])),
                        date('Y-m-d', strtotime($this->filters['end_date']))
                    ]);
                } elseif ($this->filters['start_date']) {
                    $query->whereDate('order_date', '>=', date('Y-m-d', strtotime($this->filters['start_date'])));
                } elseif ($this->filters['end_date']) {
                    $query->whereDate('order_date', '<=', date('Y-m-d', strtotime($this->filters['end_date'])));
                }
            });
    }

    public function render()
    {
        try {
            $query = $this->getFilteredQuery();
            
            // Add sorting
            if ($this->sortField === 'supplier_name') {
                $query->join('suppliers', 'purchases.supplier_id', '=', 'suppliers.id')
                      ->orderBy('suppliers.name', $this->sortDirection)
                      ->select('purchases.*');
            } else {
                $query->orderBy($this->sortField, $this->sortDirection);
            }

            $purchases = $query->paginate(10);

            return view('livewire.inventory.purchase-list', [
                'purchases' => $purchases,
                'suppliers' => $this->getSuppliers(),
            ]);
        } catch (\Exception $e) {
            Log::error('Error in PurchaseList: ' . $e->getMessage());
            session()->flash('error', 'An error occurred while loading the purchase orders.');
            return view('livewire.inventory.purchase-list', [
                'purchases' => collect([]),
                'suppliers' => collect([]),
            ]);
        }
    }
}
