<?php

namespace App\Livewire\Security;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use <PERSON><PERSON>\Fortify\Actions\ConfirmTwoFactorAuthentication;
use <PERSON><PERSON>\Fortify\TwoFactorAuthenticationProvider;

class TwoFactorChallenge extends Component
{
    public $code;
    public $recovery = false;
    public $rememberDevice = false;

    protected $rules = [
        'code' => 'required|string',
    ];

    public function mount()
    {
        if (! session('auth.password_confirmed_at')) {
            return redirect()->route('password.confirm');
        }
    }

    public function toggleRecovery()
    {
        $this->recovery = ! $this->recovery;
        $this->code = '';
        $this->resetErrorBag();
    }

    public function confirmTwoFactorAuthentication(TwoFactorAuthenticationProvider $provider)
    {
        $this->resetErrorBag();

        $user = Auth::user();
        
        if ($this->recovery) {
            $recovery_codes = json_decode(decrypt($user->two_factor_recovery_codes), true);

            if (! in_array($this->code, $recovery_codes)) {
                $this->addError('code', __('The recovery code entered was invalid.'));
                return;
            }

            // Remove used recovery code
            $recovery_codes = array_values(array_diff($recovery_codes, [$this->code]));
            $user->forceFill([
                'two_factor_recovery_codes' => encrypt(json_encode($recovery_codes)),
            ])->save();

        } else {
            try {
                if (! $provider->verify(decrypt($user->two_factor_secret), $this->code)) {
                    $this->addError('code', __('The provided two-factor authentication code was invalid.'));
                    return;
                }
            } catch (\Exception $e) {
                $this->addError('code', __('An error occurred while verifying your authentication code.'));
                return;
            }
        }

        if ($this->rememberDevice) {
            session(['auth.two_factor_remember' => true]);
        }

        $this->emit('twoFactorAuthenticationConfirmed');
        
        return redirect()->intended(route('dashboard'));
    }

    public function render()
    {
        return view('livewire.security.two-factor-challenge')->layout('layouts.guest');
    }
}
