namespace App\Livewire\Security;

use Livewire\Component;
use Illuminate\Support\Str;
use PragmaRX\Google2FA\Google2FA;
use BaconQrCode\Renderer\Color\Rgb;
use BaconQrCode\Renderer\ImageRenderer;
use BaconQrCode\Renderer\RendererStyle\Fill;
use BaconQrCode\Writer;
use BaconQrCode\Renderer\Image\SvgImageBackEnd;
use BaconQrCode\Renderer\RendererStyle\RendererStyle;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Str;
use App\Services\ActivityLogger;

class TwoFactorSetup extends Component
{
    public $showingQrCode = false;
    public $showingRecoveryCodes = false;
    public $showingConfirmation = false;
    public $code = '';
    public $secret;
    public $recoveryCodes;
    protected $google2fa;

    protected $rules = [
        'code' => ['required', 'string', 'min:6', 'max:6'],
    ];

    protected function ensureGoogle2faIsInitialized()
    {
        if (!$this->google2fa) {
            $this->google2fa = new Google2FA();
        }
    }

    public function boot()
    {
        $this->ensureGoogle2faIsInitialized();
    }

    public function mount()
    {
        $this->ensureGoogle2faIsInitialized();
        
        if (! auth()->user()->two_factor_enabled) {
            $this->generateNewSecret();
            $this->showingQrCode = true;
        }
    }

    public function generateNewSecret()
    {
        $this->secret = $this->google2fa->generateSecretKey();
        $this->showingQrCode = true;
        $this->showingConfirmation = false;
        $this->code = '';
    }

    public function getQrCodeSvgProperty()
    {
        $renderer = new ImageRenderer(
            new RendererStyle(192, 1, null, null, Fill::uniformColor(new Rgb(255, 255, 255), new Rgb(45, 55, 72))),
            new SvgImageBackEnd()
        );

        $writer = new Writer($renderer);

        $companyName = config('app.name');
        $userEmail = Auth::user()->email;
        
        return $writer->writeString(
            $this->google2fa->getQRCodeUrl(
                $companyName,
                $userEmail,
                $this->secret
            )
        );
    }

    public function enableTwoFactorAuth()
    {
        $this->resetErrorBag();

        try {
            $valid = $this->google2fa->verifyKey(
                $this->secret, $this->code
            );

            if (! $valid) {
                throw ValidationException::withMessages([
                    'code' => [__('The provided two-factor authentication code was invalid.')],
                ]);
            }
        } catch (\Exception $e) {
            throw ValidationException::withMessages([
                'code' => [__('The provided two-factor authentication code was invalid.')],
            ]);
        }

        // Generate recovery codes
        $this->recoveryCodes = collect(range(1, 8))->map(function () {
            return Str::random(10).'-'.Str::random(10);
        })->all();

        $user = Auth::user();
        
        $user->forceFill([
            'two_factor_secret' => encrypt($this->secret),
            'two_factor_recovery_codes' => encrypt(json_encode($this->recoveryCodes)),
            'two_factor_confirmed_at' => now(),
        ])->save();

        // Log 2FA enabled activity
        ActivityLogger::log2FAEnabled($user);

        $this->showingQrCode = false;
        $this->showingRecoveryCodes = true;
    }

    public function disableTwoFactorAuth()
    {
        $user = Auth::user();
        
        $user->forceFill([
            'two_factor_secret' => null,
            'two_factor_recovery_codes' => null,
            'two_factor_confirmed_at' => null,
        ])->save();

        // Log 2FA disabled activity
        ActivityLogger::log2FADisabled($user);

        $this->showingQrCode = false;
        $this->showingRecoveryCodes = false;
        $this->showingConfirmation = false;
        $this->code = '';
        $this->generateNewSecret();
    }

    public function confirmDisable()
    {
        $this->showingConfirmation = true;
    }

    public function render()
    {
        return view('livewire.security.two-factor-setup');
    }
}
