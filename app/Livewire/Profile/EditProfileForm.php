<?php

namespace App\Livewire\Profile;

use Livewire\Component;
use Livewire\WithFileUploads;
use App\Models\Users\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Services\ActivityLogger;

class EditProfileForm extends Component
{
    use WithFileUploads;

    public $user;
    public $name;
    public $email;
    public $phone;
    public $position;
    public $bio;
    public $timezone;
    public $avatar;
    public $showForm = false;

    protected $rules = [
        'name' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'phone' => 'nullable|string|max:20',
        'position' => 'nullable|string|max:100',
        'bio' => 'nullable|string|max:1000',
        'timezone' => 'nullable|string',
        'avatar' => 'nullable|image|max:2048',
    ];

    public function mount()
    {
        $this->user = Auth::user();
        $this->name = $this->user->name;
        $this->email = $this->user->email;
        $this->phone = $this->user->phone;
        $this->position = $this->user->position;
        $this->bio = $this->user->bio;
        $this->timezone = $this->user->timezone ?? 'UTC';
    }

    public function toggleForm()
    {
        $this->showForm = !$this->showForm;
        
        if (!$this->showForm) {
            $this->resetForm();
        }
    }

    public function resetForm()
    {
        $this->name = $this->user->name;
        $this->email = $this->user->email;
        $this->phone = $this->user->phone;
        $this->position = $this->user->position;
        $this->bio = $this->user->bio;
        $this->timezone = $this->user->timezone ?? 'UTC';
        $this->avatar = null;
        $this->resetValidation();
    }

    public function updateProfile()
    {
        $this->validate();

        try {
            // Handle avatar upload
            if ($this->avatar) {
                // Delete old avatar if exists
                if ($this->user->avatar) {
                    Storage::disk('public')->delete($this->user->avatar);
                }
                
                // Store new avatar
                $avatarPath = $this->avatar->store('avatars', 'public');
                $this->user->avatar = $avatarPath;
            }

            // Track changes for activity logging
            $originalData = $this->user->getOriginal();
            $changes = [];

            // Update user data
            $this->user->update([
                'name' => $this->name,
                'email' => $this->email,
                'phone' => $this->phone,
                'position' => $this->position,
                'bio' => $this->bio,
                'timezone' => $this->timezone,
            ]);

            // Determine what changed
            $newData = $this->user->fresh()->toArray();
            foreach (['name', 'email', 'phone', 'position', 'bio', 'timezone'] as $field) {
                if ($originalData[$field] !== $newData[$field]) {
                    $changes[$field] = [
                        'old' => $originalData[$field],
                        'new' => $newData[$field]
                    ];
                }
            }

            // Log avatar update separately if it was changed
            if ($this->avatar) {
                ActivityLogger::logAvatarUpdate($this->user);
            }

            // Log profile update if there were changes
            if (!empty($changes)) {
                ActivityLogger::logProfileUpdate($this->user, $changes);
            }

            $this->showForm = false;
            $this->dispatch('profile-updated');
            session()->flash('message', 'Profile updated successfully!');
            
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to update profile. Please try again.');
        }
    }

    public function render()
    {
        return view('livewire.profile.edit-profile-form');
    }
}
