<?php

namespace App\Livewire\Profile;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithFileUploads;

class UpdateProfileInformation extends Component
{
    use WithFileUploads;

    public $name;
    public $email;
    public $phone;
    public $avatar;
    public $position;
    public $bio;
    public $newAvatar;
    public $timezone;

    public function mount()
    {
        $user = Auth::user();
        $this->name = $user->name;
        $this->email = $user->email;
        $this->phone = $user->phone;
        $this->position = $user->position;
        $this->bio = $user->bio;
        $this->timezone = $user->timezone ?? config('app.timezone');
    }

    public function updateProfile()
    {
        $validatedData = $this->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255', 'unique:users,email,' . Auth::id()],
            'phone' => ['nullable', 'string', 'max:20'],
            'position' => ['nullable', 'string', 'max:100'],
            'bio' => ['nullable', 'string', 'max:500'],
            'newAvatar' => ['nullable', 'image', 'max:1024'], // 1MB max
            'timezone' => ['required', 'string', 'timezone'],
        ]);

        $user = Auth::user();

        if ($this->newAvatar) {
            $path = $this->newAvatar->store('avatars', 'public');
            $user->avatar = $path;
        }

        $user->fill([
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'position' => $this->position,
            'bio' => $this->bio,
            'timezone' => $this->timezone,
        ])->save();

        $this->dispatch('profile-updated');
    }

    public function render()
    {
        return view('livewire.profile.update-profile-information');
    }
}
