<?php

namespace App\Livewire\Profile;

use Livewire\Component;
use App\Models\Users\User;
use App\Models\Users\UserActivity;
use App\Services\ActivityLogger;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class ShowProfile extends Component
{
    public $user;
    public $activeTab = 'profile';
    public $has2FA = false;
    public $activeSessions = [];
    public $recentActivities = [];
    public $activityStats = [];

    // User preferences
    public $theme = 'light';
    public $language = 'en';
    public $timezone = 'UTC';
    public $emailNotifications = true;
    public $pushNotifications = true;

    protected $listeners = ['profile-updated' => 'refreshUser'];

    public function mount()
    {
        $this->user = User::find(Auth::id());
        $this->has2FA = $this->user->two_factor_enabled;
        $this->loadRecentActivities();
        $this->loadActivityStats();
        $this->loadUserPreferences();
    }

    protected function loadRecentActivities()
    {
        $this->recentActivities = ActivityLogger::getRecentActivities($this->user, 10);
    }

    protected function loadActivityStats()
    {
        $this->activityStats = ActivityLogger::getActivityStats($this->user, 30);
    }

    protected function loadUserPreferences()
    {
        // Load user preferences from user model or settings
        $this->theme = $this->user->theme ?? 'light';
        $this->language = $this->user->language ?? 'en';
        $this->timezone = $this->user->timezone ?? config('app.timezone');
        $this->emailNotifications = $this->user->email_notifications ?? true;
        $this->pushNotifications = $this->user->push_notifications ?? true;
    }

    public function getActivityIcon($activity)
    {
        $type = is_object($activity) ? $activity->action : $activity['type'];

        return match ($type) {
            UserActivity::ACTION_LOGIN, 'logged_in' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />',
            UserActivity::ACTION_LOGOUT => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />',
            UserActivity::ACTION_PROFILE_UPDATED, 'profile_updated' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />',
            UserActivity::ACTION_PASSWORD_CHANGED, 'password_changed' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />',
            UserActivity::ACTION_2FA_ENABLED, UserActivity::ACTION_2FA_DISABLED => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />',
            UserActivity::ACTION_AVATAR_UPDATED => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />',
            UserActivity::ACTION_SALE_CREATED => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />',
            'report_generated' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />',
            default => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />',
        };
    }

    public function formatActivityTime($time)
    {
        return Carbon::parse($time)->diffForHumans();
    }

    public function getActivityColor($activity)
    {
        $type = is_object($activity) ? $activity->action : $activity['type'];

        return match ($type) {
            UserActivity::ACTION_LOGIN, 'logged_in' => 'bg-green-500',
            UserActivity::ACTION_LOGOUT => 'bg-gray-500',
            UserActivity::ACTION_PROFILE_UPDATED, 'profile_updated' => 'bg-blue-500',
            UserActivity::ACTION_PASSWORD_CHANGED, 'password_changed' => 'bg-red-500',
            UserActivity::ACTION_2FA_ENABLED => 'bg-green-500',
            UserActivity::ACTION_2FA_DISABLED => 'bg-red-500',
            UserActivity::ACTION_AVATAR_UPDATED => 'bg-purple-500',
            UserActivity::ACTION_SALE_CREATED => 'bg-green-500',
            'report_generated' => 'bg-purple-500',
            default => 'bg-gray-500',
        };
    }

    public function getActivityBadgeColor($activity)
    {
        $type = is_object($activity) ? $activity->action : $activity['type'];

        return match ($type) {
            UserActivity::ACTION_LOGIN, 'logged_in' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
            UserActivity::ACTION_LOGOUT => 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
            UserActivity::ACTION_PROFILE_UPDATED, 'profile_updated' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
            UserActivity::ACTION_PASSWORD_CHANGED, 'password_changed' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
            UserActivity::ACTION_2FA_ENABLED => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
            UserActivity::ACTION_2FA_DISABLED => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
            UserActivity::ACTION_AVATAR_UPDATED => 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
            UserActivity::ACTION_SALE_CREATED => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
            'report_generated' => 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
            default => 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
        };
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;

        // Load data based on active tab
        switch ($tab) {
            case 'activity':
                $this->loadRecentActivities();
                $this->loadActivityStats();
                break;
            case 'security':
                $this->refreshUser();
                break;
            case 'preferences':
                $this->loadUserPreferences();
                break;
        }
    }

    public function refreshUser()
    {
        $this->user = User::find(Auth::id());
        $this->has2FA = $this->user->two_factor_enabled;
    }

    public function updatePreferences()
    {
        $this->validate([
            'theme' => 'required|in:light,dark',
            'language' => 'required|in:en,es,fr,de',
            'timezone' => 'required|timezone',
            'emailNotifications' => 'boolean',
            'pushNotifications' => 'boolean',
        ]);

        // Update user preferences (you may need to add these fields to users table)
        $this->user->update([
            'timezone' => $this->timezone,
        ]);

        // Log the activity
        ActivityLogger::logSettingsUpdate($this->user, [
            'theme' => $this->theme,
            'language' => $this->language,
            'timezone' => $this->timezone,
            'email_notifications' => $this->emailNotifications,
            'push_notifications' => $this->pushNotifications,
        ]);

        session()->flash('success', 'Preferences updated successfully.');
        $this->refreshUser();
    }

    public function refreshActivities()
    {
        $this->loadRecentActivities();
        $this->loadActivityStats();
        session()->flash('success', 'Activities refreshed.');
    }

    public function toggle2FA()
    {
        if ($this->has2FA) {
            // Disable 2FA
            $this->user->forceFill([
                'two_factor_secret' => null,
                'two_factor_recovery_codes' => null,
                'two_factor_confirmed_at' => null,
            ])->save();

            $this->has2FA = false;

            // Log the activity
            ActivityLogger::log2FADisabled($this->user);

            session()->flash('success', 'Two-factor authentication has been disabled.');
        } else {
            // Enable 2FA - redirect to setup
            session()->flash('show_2fa_setup', true);
        }
    }

    public function manageActiveSessions()
    {
        $this->dispatch('open-modal', 'manage-sessions');
    }

    public function viewLoginHistory()
    {
        $this->dispatch('open-modal', 'login-history');
    }

    public function render()
    {
        return view('livewire.profile.show-profile', [
            'user' => $this->user,
            'activeTab' => $this->activeTab,
            'has2FA' => $this->has2FA,
            'activeSessions' => $this->activeSessions,
            'recentActivities' => $this->recentActivities,
            'activityStats' => $this->activityStats,
        ])->layout('layouts.admin');
    }
}
