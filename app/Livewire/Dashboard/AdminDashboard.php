<?php

namespace App\Livewire\Dashboard;

use Livewire\Component;
use App\Models\Sales\Sale;
use App\Models\Inventory\Medicine;
use App\Models\Customers\Customer;
use App\Models\Inventory;
use App\Models\Category;
use App\Models\Supplier;
use App\Models\Manufacturer;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\Support\Facades\Schema;

class AdminDashboard extends Component
{
    public $salesStats;
    public $inventoryStats;
    public $customerStats;
    public $recentOrders;
    public $topProducts;
    public $financialStats;
    public $categoryStats;
    public $supplierStats;
    public $prescriptionStats;
    public $operationalStats;
    public $insuranceStats;

    protected $listeners = [
        'refreshDashboard' => '$refresh',
        'getSalesLineChartModel',
        'getPaymentMethodsPieChartModel'
    ];

    public function mount()
    {
        $this->loadDashboardData();
    }

    public function loadDashboardData()
    {
        $today = now()->startOfDay();
        $yesterday = now()->subDay()->startOfDay();
        $startOfWeek = now()->startOfWeek();
        $startOfMonth = now()->startOfMonth();
        $thirtyDaysAgo = now()->subDays(30)->startOfDay();

        // Load each stat set with error handling
        try {
            $this->salesStats = $this->getSalesStats($today, $yesterday, $startOfWeek, $startOfMonth);
        } catch (\Exception $e) {
            Log::error('Error loading sales stats: ' . $e->getMessage());
            $this->salesStats = [
                'today' => 0,
                'yesterday' => 0,
                'week' => 0,
                'month' => 0,
                'total_orders' => 0,
                'pending_orders' => 0
            ];
        }
        
        try {
            $this->inventoryStats = $this->getInventoryStats();
        } catch (\Exception $e) {
            Log::error('Error loading inventory stats: ' . $e->getMessage());
            $this->inventoryStats = [
                'total_medicines' => 0,
                'low_stock' => 0,
                'out_of_stock' => 0,
                'expiring_soon' => 0
            ];
        }
        
        try {
            $this->customerStats = $this->getCustomerStats($startOfMonth);
        } catch (\Exception $e) {
            Log::error('Error loading customer stats: ' . $e->getMessage());
            $this->customerStats = [
                'total_customers' => 0,
                'active_customers' => 0,
                'active_loyalty_members' => 0,
                'new_customers' => 0,
                'retention_rate' => 0,
                'avg_customer_value' => 0,
                'loyalty_percentage' => 0
            ];
        }
        
        try {
            $this->recentOrders = $this->getRecentOrders();
        } catch (\Exception $e) {
            Log::error('Error loading recent orders: ' . $e->getMessage());
            $this->recentOrders = collect();
        }
        
        try {
            $this->topProducts = $this->getTopProducts($thirtyDaysAgo);
        } catch (\Exception $e) {
            Log::error('Error loading top products: ' . $e->getMessage());
            $this->topProducts = collect();
        }
        
        // New statistics with error handling
        try {
            $this->financialStats = $this->getFinancialStats($startOfMonth);
        } catch (\Exception $e) {
            Log::error('Error loading financial stats: ' . $e->getMessage());
            $this->financialStats = [
                'gross_profit' => 0,
                'profit_margin' => 0,
                'tax_collected' => 0,
                'avg_order_value' => 0,
                'returns_value' => 0
            ];
        }
        
        try {
            $this->categoryStats = $this->getCategoryStats($startOfMonth);
        } catch (\Exception $e) {
            Log::error('Error loading category stats: ' . $e->getMessage());
            $this->categoryStats = [
                'top_categories' => collect(),
                'category_growth' => 0
            ];
        }
        
        try {
            $this->supplierStats = $this->getSupplierStats($startOfMonth);
        } catch (\Exception $e) {
            Log::error('Error loading supplier stats: ' . $e->getMessage());
            $this->supplierStats = [
                'top_suppliers' => collect(),
                'total_suppliers' => 0,
                'active_suppliers' => 0
            ];
        }
        
        try {
            $this->prescriptionStats = $this->getPrescriptionStats($startOfMonth);
        } catch (\Exception $e) {
            Log::error('Error loading prescription stats: ' . $e->getMessage());
            $this->prescriptionStats = [
                'total' => 0,
                'avg_items' => 0,
                'top_prescribers' => collect()
            ];
        }
        
        try {
            $this->operationalStats = $this->getOperationalStats($startOfMonth);
        } catch (\Exception $e) {
            Log::error('Error loading operational stats: ' . $e->getMessage());
            $this->operationalStats = [
                'avg_processing_time' => 0,
                'avg_delivery_time' => 0,
                'order_fulfillment_rate' => 0
            ];
        }
        
        try {
            $this->insuranceStats = $this->getInsuranceStats($startOfMonth);
        } catch (\Exception $e) {
            Log::error('Error loading insurance stats: ' . $e->getMessage());
            $this->insuranceStats = [
                'total_claims' => 0,
                'approved_claims' => 0,
                'rejected_claims' => 0,
                'pending_claims' => 0,
                'avg_claim_value' => 0
            ];
        }
    }

    public function getSalesLineChartModel()
    {
        try {
            $thirtyDaysAgo = now()->subDays(30)->startOfDay();
            $sales = DB::table('sales')
                ->select(
                    DB::raw('DATE(created_at) as date'),
                    DB::raw('COALESCE(SUM(total_amount), 0) as total_sales')
                )
                ->whereBetween('created_at', [$thirtyDaysAgo, now()])
                ->whereNull('deleted_at')
                ->groupBy('date')
                ->orderBy('date')
                ->get()
                ->keyBy('date');

            $data = [];
            $hasData = false;
            for ($date = clone $thirtyDaysAgo; $date <= now(); $date->addDay()) {
                $dateStr = $date->format('Y-m-d');
                $amount = isset($sales[$dateStr]) ? $sales[$dateStr]->total_sales : 0;
                if ($amount > 0) {
                    $hasData = true;
                }
                $data[] = [
                    'title' => $date->format('M d'),
                    'value' => (float) $amount
                ];
            }

            return [
                'title' => 'Daily Sales',
                'data' => $data,
                'hasData' => $hasData
            ];
        } catch (\Exception $e) {
            \Log::error('Error getting sales chart data: ' . $e->getMessage());
            // Return dummy data to prevent errors
            $data = [];
            $thirtyDaysAgo = now()->subDays(30);
            for ($i = 0; $i < 30; $i++) {
                $date = $thirtyDaysAgo->copy()->addDays($i);
                $data[] = [
                    'title' => $date->format('M d'),
                    'value' => 0
                ];
            }
            return [
                'title' => 'Daily Sales',
                'data' => $data,
                'hasData' => false
            ];
        }
    }

    public function getPaymentMethodsPieChartModel()
    {
        try {
            $startOfMonth = now()->startOfMonth();
            $methods = DB::table('sales')
                ->select('payment_method', DB::raw('COUNT(*) as count'))
                ->whereNotNull('payment_method')
                ->where('payment_method', '!=', '')
                ->whereNull('deleted_at')
                ->whereBetween('created_at', [$startOfMonth, now()])
                ->groupBy('payment_method')
                ->get();

            if ($methods->isEmpty()) {
                return [
                    'title' => 'Payment Methods',
                    'data' => [
                        [
                            'title' => 'Cash',
                            'value' => 0
                        ],
                        [
                            'title' => 'Card',
                            'value' => 0
                        ],
                        [
                            'title' => 'Mobile',
                            'value' => 0
                        ]
                    ],
                    'hasData' => false
                ];
            }

            return [
                'title' => 'Payment Methods',
                'data' => $methods->map(function ($method) {
                    return [
                        'title' => ucfirst(str_replace('_', ' ', $method->payment_method)),
                        'value' => (int) $method->count
                    ];
                })->toArray(),
                'hasData' => true
            ];
        } catch (\Exception $e) {
            \Log::error('Error getting payment methods chart data: ' . $e->getMessage());
            // Return dummy data to prevent errors
            return [
                'title' => 'Payment Methods',
                'data' => [
                    [
                        'title' => 'Cash',
                        'value' => 0
                    ],
                    [
                        'title' => 'Card',
                        'value' => 0
                    ],
                    [
                        'title' => 'Mobile',
                        'value' => 0
                    ]
                ],
                'hasData' => false
            ];
        }
    }

    private function getSalesStats($today, $yesterday, $startOfWeek, $startOfMonth): array
    {
        return [
            'today' => Sale::whereDate('created_at', $today)->sum('total_amount') ?? 0,
            'yesterday' => Sale::whereDate('created_at', $yesterday)->sum('total_amount') ?? 0,
            'week' => Sale::whereBetween('created_at', [$startOfWeek, now()])->sum('total_amount') ?? 0,
            'month' => Sale::whereBetween('created_at', [$startOfMonth, now()])->sum('total_amount') ?? 0,
            'total_orders' => Sale::count(),
            'pending_orders' => Sale::where('payment_status', 'pending')->count(),
        ];
    }

    private function getInventoryStats(): array
    {
        try {
            $thirtyDaysAgo = now()->subDays(30);
            $ninetyDaysAgo = now()->subDays(90);

            // Calculate turnover rate
            $beginningInventory = DB::table('inventories')
                ->whereDate('created_at', '<=', $thirtyDaysAgo)
                ->sum('quantity');
            
            $endingInventory = DB::table('inventories')
                ->whereDate('created_at', '<=', now())
                ->sum('quantity');

            $costOfGoodsSold = DB::table('sale_items')
                ->whereBetween('created_at', [$thirtyDaysAgo, now()])
                ->sum(DB::raw('quantity * unit_price'));

            $averageInventory = ($beginningInventory + $endingInventory) / 2;
            $turnoverRate = $averageInventory > 0 ? ($costOfGoodsSold / $averageInventory) : 0;

            // Calculate average storage days
            $avgStorageDays = DB::table('inventories')
                ->whereNotNull('created_at')
                ->where('quantity', '>', 0)
                ->avg(DB::raw('DATEDIFF(NOW(), created_at)')) ?? 0;

            // Get slow moving items (items not sold in last 90 days)
            $slowMovingItems = DB::table('medicines')
                ->leftJoin('inventories', 'medicines.id', '=', 'inventories.medicine_id')
                ->leftJoin('sale_items', function($join) use ($ninetyDaysAgo) {
                    $join->on('medicines.id', '=', 'sale_items.medicine_id')
                         ->where('sale_items.created_at', '>=', $ninetyDaysAgo);
                })
                ->whereNull('sale_items.id')
                ->where('inventories.quantity', '>', 0)
                ->select(
                    'medicines.name',
                    DB::raw('DATEDIFF(NOW(), inventories.created_at) as days_in_stock')
                )
                ->orderByDesc('days_in_stock')
                ->limit(5)
                ->get();

            return [
                'total_medicines' => Medicine::count(),
                'low_stock' => DB::table('medicines')
                    ->join('inventories', 'medicines.id', '=', 'inventories.medicine_id')
                    ->where('inventories.quantity', '<=', DB::raw('medicines.minimum_stock'))
                    ->distinct()
                    ->count('medicines.id'),
                'out_of_stock' => DB::table('medicines')
                    ->leftJoin('inventories', 'medicines.id', '=', 'inventories.medicine_id')
                    ->where(function($query) {
                        $query->whereNull('inventories.id')
                            ->orWhere('inventories.quantity', '=', 0);
                    })
                    ->distinct()
                    ->count('medicines.id'),
                'expiring_soon' => DB::table('medicines')
                    ->join('inventories', 'medicines.id', '=', 'inventories.medicine_id')
                    ->where('inventories.quantity', '>', 0)
                    ->whereDate('inventories.expiry_date', '<=', now()->addMonths(3))
                    ->whereDate('inventories.expiry_date', '>', now())
                    ->distinct()
                    ->count('medicines.id'),
                'turnover_rate' => $turnoverRate,
                'avg_storage_days' => round($avgStorageDays),
                'slow_moving' => $slowMovingItems
            ];
        } catch (\Exception $e) {
            Log::error('Error getting inventory stats: ' . $e->getMessage());
            return [
                'total_medicines' => 0,
                'low_stock' => 0,
                'out_of_stock' => 0,
                'expiring_soon' => 0,
                'turnover_rate' => 0,
                'avg_storage_days' => 0,
                'slow_moving' => collect()
            ];
        }
    }

    private function getCustomerStats($startOfMonth): array
    {
        try {
            $totalCustomers = Customer::count();
            
            // Check if loyalty_points column exists
            $activeLoyaltyMembers = 0;
            if (Schema::hasColumn('customers', 'loyalty_points')) {
                $activeLoyaltyMembers = Customer::where('status', 'active')
                    ->where('loyalty_points', '>', 0)
                    ->count();
            }
            
            // Check if last_purchase_date column exists
            $newCustomers = 0;
            if (Schema::hasColumn('customers', 'created_at')) {
                $newCustomers = Customer::where('created_at', '>=', $startOfMonth)->count();
            }
            
            // Calculate retention rate - customers who have purchased more than once
            $retentionRate = 0;
            
            // Calculate average customer value
            $avgCustomerValue = 0;
            if (Schema::hasTable('sales') && Schema::hasColumn('sales', 'customer_id')) {
                $avgCustomerValue = DB::table('sales')
                    ->whereNull('deleted_at')
                    ->whereNotNull('customer_id')
                    ->whereBetween('created_at', [$startOfMonth, now()])
                    ->avg('total_amount') ?? 0;
                    
                // Only calculate retention rate if we have the necessary columns
                if (Schema::hasColumn('customers', 'last_purchase_date')) {
                    $customersWithPurchases = DB::table('customers')
                        ->whereNotNull('last_purchase_date')
                        ->count();
                    
                    $retentionRate = $totalCustomers > 0 ? ($customersWithPurchases / $totalCustomers) * 100 : 0;
                } else {
                    // Fallback: Calculate based on sales data
                    $customersWithMultiplePurchases = DB::table('sales')
                        ->whereNull('deleted_at')
                        ->whereNotNull('customer_id')
                        ->groupBy('customer_id')
                        ->havingRaw('COUNT(*) > 1')
                        ->count('customer_id');
                    
                    $retentionRate = $totalCustomers > 0 ? ($customersWithMultiplePurchases / $totalCustomers) * 100 : 0;
                }
            }
            
            $loyaltyPercentage = $totalCustomers > 0 ? ($activeLoyaltyMembers / $totalCustomers) * 100 : 0;

            return [
                'total_customers' => $totalCustomers,
                'active_customers' => Customer::where('status', 'active')->count(),
                'active_loyalty_members' => $activeLoyaltyMembers,
                'new_customers' => $newCustomers,
                'retention_rate' => $retentionRate,
                'avg_customer_value' => $avgCustomerValue,
                'loyalty_percentage' => $loyaltyPercentage
            ];
        } catch (\Exception $e) {
            Log::error('Error getting customer stats: ' . $e->getMessage());
            return [
                'total_customers' => 0,
                'active_customers' => 0,
                'active_loyalty_members' => 0,
                'new_customers' => 0,
                'retention_rate' => 0,
                'avg_customer_value' => 0,
                'loyalty_percentage' => 0
            ];
        }
    }

    private function getRecentOrders()
    {
        return DB::table('sales')
            ->leftJoin('customers', 'sales.customer_id', '=', 'customers.id')
            ->select(
                'sales.id',
                'sales.invoice_number',
                'sales.total_amount',
                'sales.payment_status',
                'sales.payment_method',
                'sales.created_at',
                'customers.name as customer_name'
            )
            ->whereNull('sales.deleted_at')
            ->orderByDesc('sales.created_at')
            ->limit(5)
            ->get()
            ->map(function($order) {
                $order->created_at = Carbon::parse($order->created_at);
                return $order;
            });
    }

    private function getTopProducts($thirtyDaysAgo)
    {
        return DB::table('sale_items')
            ->join('medicines', 'sale_items.medicine_id', '=', 'medicines.id')
            ->leftJoin('categories', 'medicines.category_id', '=', 'categories.id')
            ->leftJoin('sales', 'sale_items.sale_id', '=', 'sales.id')
            ->whereNull('sales.deleted_at')
            ->select(
                'medicines.id as medicine_id',
                'medicines.name as medicine_name',
                'categories.name as category_name',
                DB::raw('SUM(sale_items.quantity) as total_quantity'),
                DB::raw('SUM(sale_items.quantity * sale_items.unit_price) as total_amount')
            )
            ->whereBetween('sale_items.created_at', [$thirtyDaysAgo, now()])
            ->groupBy('medicines.id', 'medicines.name', 'categories.name')
            ->orderByDesc('total_quantity')
            ->limit(5)
            ->get();
    }

    private function getFinancialStats($startOfMonth)
    {
        try {
            // Check if sales table exists
            if (!Schema::hasTable('sales')) {
                return [
                    'gross_profit' => 0,
                    'profit_margin' => 0,
                    'tax_collected' => 0,
                    'avg_order_value' => 0,
                    'returns_value' => 0
                ];
            }
            
            $sales = DB::table('sales')
                ->whereNull('deleted_at')
                ->whereBetween('created_at', [$startOfMonth, now()])
                ->get();

            $totalSales = $sales->sum('total_amount');
            
            // Check if total_cost column exists
            $totalCost = 0;
            if (Schema::hasColumn('sales', 'total_cost')) {
                $totalCost = $sales->sum('total_cost');
            } else {
                // Fallback: Estimate cost from sale items if the tables exist
                if (Schema::hasTable('sale_items') && Schema::hasTable('inventories')) {
                    try {
                        $totalCost = DB::table('sales')
                            ->join('sale_items', 'sales.id', '=', 'sale_items.sale_id')
                            ->join('inventories', function($join) {
                                $join->on('sale_items.medicine_id', '=', 'inventories.medicine_id');
                                if (Schema::hasColumn('sale_items', 'batch_number') && 
                                    Schema::hasColumn('inventories', 'batch_number')) {
                                    $join->on('sale_items.batch_number', '=', 'inventories.batch_number');
                                }
                            })
                            ->whereBetween('sales.created_at', [$startOfMonth, now()])
                            ->whereNull('sales.deleted_at')
                            ->sum(DB::raw('sale_items.quantity * inventories.unit_price'));
                    } catch (\Exception $e) {
                        Log::warning('Error calculating total cost from inventory: ' . $e->getMessage());
                        // If join fails, use a default margin
                        $totalCost = $totalSales * 0.7; // Assume 30% margin
                    }
                } else {
                    // If tables don't exist, use a default margin
                    $totalCost = $totalSales * 0.7; // Assume 30% margin
                }
            }
            
            $grossProfit = $totalSales - $totalCost;
            $profitMargin = $totalSales > 0 ? ($grossProfit / $totalSales) * 100 : 0;

            // Check if tax_amount column exists
            $taxCollected = 0;
            if (Schema::hasColumn('sales', 'tax_amount')) {
                $taxCollected = $sales->sum('tax_amount');
            }

            // Calculate average order value
            $avgOrderValue = $sales->count() > 0 ? $totalSales / $sales->count() : 0;

            // Check if status column exists for returns
            $returnsValue = 0;
            if (Schema::hasColumn('sales', 'status')) {
                $returnsValue = DB::table('sales')
                    ->whereNull('deleted_at')
                    ->where('status', 'returned')
                    ->whereBetween('created_at', [$startOfMonth, now()])
                    ->sum('total_amount');
            }

            return [
                'gross_profit' => $grossProfit,
                'profit_margin' => $profitMargin,
                'tax_collected' => $taxCollected,
                'avg_order_value' => $avgOrderValue,
                'returns_value' => $returnsValue
            ];
        } catch (\Exception $e) {
            Log::error('Error getting financial stats: ' . $e->getMessage());
            return [
                'gross_profit' => 0,
                'profit_margin' => 0,
                'tax_collected' => 0,
                'avg_order_value' => 0,
                'returns_value' => 0
            ];
        }
    }

    private function getCategoryStats($startOfMonth)
    {
        try {
            return DB::table('categories')
                ->leftJoin('medicines', 'categories.id', '=', 'medicines.category_id')
                ->leftJoin('sale_items', 'medicines.id', '=', 'sale_items.medicine_id')
                ->leftJoin('sales', 'sale_items.sale_id', '=', 'sales.id')
                ->whereNull('sales.deleted_at')
                ->whereBetween('sales.created_at', [$startOfMonth, now()])
                ->groupBy('categories.id', 'categories.name')
                ->select(
                    'categories.name',
                    DB::raw('COUNT(DISTINCT medicines.id) as total_items'),
                    DB::raw('SUM(sale_items.quantity * sale_items.unit_price) as revenue'),
                    DB::raw('0 as growth') // You'll need to calculate growth separately
                )
                ->orderByDesc('revenue')
                ->limit(5)
                ->get();
        } catch (\Exception $e) {
            Log::error('Error getting category stats: ' . $e->getMessage());
            return collect();
        }
    }

    private function getSupplierStats($startOfMonth)
    {
        try {
            return DB::table('suppliers')
                ->leftJoin('purchases', 'suppliers.id', '=', 'purchases.supplier_id')
                ->whereNull('purchases.deleted_at')
                ->where('purchases.status', 'received')
                ->groupBy('suppliers.id', 'suppliers.name')
                ->select(
                    'suppliers.name',
                    DB::raw('COUNT(purchases.id) as total_orders'),
                    DB::raw('AVG(CASE WHEN purchases.delivery_date <= purchases.expected_date THEN 100 ELSE 0 END) as on_time_delivery'),
                    DB::raw('AVG(COALESCE(purchases.quality_rating, 0)) as quality_rating')
                )
                ->orderByDesc('total_orders')
                ->limit(5)
                ->get();
        } catch (\Exception $e) {
            Log::error('Error getting supplier stats: ' . $e->getMessage());
            return collect();
        }
    }

    private function getPrescriptionStats($startOfMonth)
    {
        try {
            // Check if prescriptions table exists
            if (!Schema::hasTable('prescriptions')) {
                return [
                    'total' => 0,
                    'avg_items' => 0,
                    'top_prescribers' => collect()
                ];
            }
            
            $total = DB::table('prescriptions')
                ->when(Schema::hasColumn('prescriptions', 'deleted_at'), function($query) {
                    return $query->whereNull('deleted_at');
                })
                ->when(Schema::hasColumn('prescriptions', 'created_at'), function($query) use ($startOfMonth) {
                    return $query->whereBetween('created_at', [$startOfMonth, now()]);
                })
                ->count();

            // Check if prescription_items table exists
            $avgItems = 0;
            if (Schema::hasTable('prescription_items') && 
                Schema::hasColumn('prescription_items', 'prescription_id')) {
                try {
                    $prescriptionItemCounts = DB::table('prescriptions')
                        ->join('prescription_items', 'prescriptions.id', '=', 'prescription_items.prescription_id')
                        ->when(Schema::hasColumn('prescriptions', 'deleted_at'), function($query) {
                            return $query->whereNull('prescriptions.deleted_at');
                        })
                        ->when(Schema::hasColumn('prescriptions', 'created_at'), function($query) use ($startOfMonth) {
                            return $query->whereBetween('prescriptions.created_at', [$startOfMonth, now()]);
                        })
                        ->groupBy('prescriptions.id')
                        ->select(
                            'prescriptions.id',
                            DB::raw('COUNT(prescription_items.id) as item_count')
                        )
                        ->get();
                    
                    if ($prescriptionItemCounts->count() > 0) {
                        $avgItems = $prescriptionItemCounts->avg('item_count');
                    }
                } catch (\Exception $e) {
                    Log::warning('Error calculating average prescription items: ' . $e->getMessage());
                }
            }

            // Check if doctors table exists
            $topPrescribers = collect();
            if (Schema::hasTable('doctors') && Schema::hasColumn('prescriptions', 'doctor_id')) {
                try {
                    $topPrescribers = DB::table('prescriptions')
                        ->join('doctors', 'prescriptions.doctor_id', '=', 'doctors.id')
                        ->when(Schema::hasColumn('prescriptions', 'deleted_at'), function($query) {
                            return $query->whereNull('prescriptions.deleted_at');
                        })
                        ->when(Schema::hasColumn('prescriptions', 'created_at'), function($query) use ($startOfMonth) {
                            return $query->whereBetween('prescriptions.created_at', [$startOfMonth, now()]);
                        })
                        ->groupBy('doctors.id', 'doctors.name')
                        ->select(
                            'doctors.name',
                            DB::raw('COUNT(prescriptions.id) as prescription_count')
                        )
                        ->orderByDesc('prescription_count')
                        ->limit(5)
                        ->get();
                } catch (\Exception $e) {
                    Log::warning('Error getting top prescribers from doctors table: ' . $e->getMessage());
                }
            } else if (Schema::hasColumn('prescriptions', 'doctor_name')) {
                try {
                    // Fallback: Use doctor_name field if available
                    $topPrescribers = DB::table('prescriptions')
                        ->when(Schema::hasColumn('prescriptions', 'deleted_at'), function($query) {
                            return $query->whereNull('deleted_at');
                        })
                        ->when(Schema::hasColumn('prescriptions', 'created_at'), function($query) use ($startOfMonth) {
                            return $query->whereBetween('created_at', [$startOfMonth, now()]);
                        })
                        ->groupBy('doctor_name')
                        ->select(
                            'doctor_name as name',
                            DB::raw('COUNT(*) as prescription_count')
                        )
                        ->orderByDesc('prescription_count')
                        ->limit(5)
                        ->get();
                } catch (\Exception $e) {
                    Log::warning('Error getting top prescribers from doctor_name: ' . $e->getMessage());
                }
            }

            return [
                'total' => $total,
                'avg_items' => $avgItems,
                'top_prescribers' => $topPrescribers
            ];
        } catch (\Exception $e) {
            Log::error('Error getting prescription stats: ' . $e->getMessage());
            return [
                'total' => 0,
                'avg_items' => 0,
                'top_prescribers' => collect()
            ];
        }
    }

    private function getOperationalStats($startOfMonth)
    {
        try {
            $sales = Sale::whereNull('deleted_at')
                ->whereBetween('created_at', [$startOfMonth, now()])
                ->get();

            $totalSales = $sales->count();
            $returnedSales = $sales->where('status', 'returned')->count();
            $returnRate = $totalSales > 0 ? ($returnedSales / $totalSales) * 100 : 0;

            return [
                'avg_processing_time' => $sales->avg('processing_time') ?? 0,
                'return_rate' => $returnRate,
                'order_accuracy' => 100 - $returnRate,
                'staff_efficiency' => 95 // This should be calculated based on your business logic
            ];
        } catch (\Exception $e) {
            Log::error('Error getting operational stats: ' . $e->getMessage());
            return [
                'avg_processing_time' => 0,
                'return_rate' => 0,
                'order_accuracy' => 0,
                'staff_efficiency' => 0
            ];
        }
    }

    private function getInsuranceStats($startOfMonth)
    {
        try {
            // Check if insurance_claims table exists
            if (!Schema::hasTable('insurance_claims')) {
                return [
                    'pending_claims' => 0,
                    'approved_claims' => 0,
                    'rejected_claims' => 0,
                    'recent_claims' => collect()
                ];
            }
            
            $claims = DB::table('insurance_claims')
                ->whereNull('deleted_at')
                ->whereBetween('created_at', [$startOfMonth, now()]);

            $recentClaims = DB::table('insurance_claims')
                ->whereNull('deleted_at')
                ->orderByDesc('created_at')
                ->limit(5)
                ->get();

            return [
                'pending_claims' => $claims->where('status', 'pending')->count(),
                'approved_claims' => $claims->where('status', 'approved')->count(),
                'rejected_claims' => $claims->where('status', 'rejected')->count(),
                'recent_claims' => $recentClaims
            ];
        } catch (\Exception $e) {
            Log::error('Error getting insurance stats: ' . $e->getMessage());
            return [
                'pending_claims' => 0,
                'approved_claims' => 0,
                'rejected_claims' => 0,
                'recent_claims' => collect()
            ];
        }
    }

    public function render()
    {
        return view('livewire.dashboard.admin-dashboard');
    }
}
