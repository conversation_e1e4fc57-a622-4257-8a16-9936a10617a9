<?php

namespace App\Exports;

use App\Models\Inventory\Medicine;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Support\Collection;

class MedicineExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $filters;

    public function __construct(array $filters = [])
    {
        $this->filters = $filters;
    }

    /**
     * @return Collection
     */
    public function collection()
    {
        $query = Medicine::with(['category', 'manufacturer', 'unitType', 'inventories']);

        // Apply filters if provided
        if (!empty($this->filters['search'])) {
            $query->searchWithManufacturer($this->filters['search']);
        }

        if (!empty($this->filters['category_id'])) {
            $query->where('category_id', $this->filters['category_id']);
        }

        if (!empty($this->filters['manufacturer_id'])) {
            $query->where('manufacturer_id', $this->filters['manufacturer_id']);
        }

        if (!empty($this->filters['status'])) {
            if ($this->filters['status'] === 'active') {
                $query->active();
            } elseif ($this->filters['status'] === 'inactive') {
                $query->inactive();
            }
        }

        if (isset($this->filters['controlled_substance'])) {
            $query->where('controlled_substance', (bool) $this->filters['controlled_substance']);
        }

        if (isset($this->filters['prescription_required'])) {
            $query->where('prescription_required', (bool) $this->filters['prescription_required']);
        }

        $sort = $this->filters['sort'] ?? 'asc';
        return $query->orderBy('id', $sort)->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            'Name',
            'Generic Name',
            'Dosage',
            'Category',
            'Manufacturer',
            'Unit Type',
            'Minimum Stock',
            'Maximum Stock',
            'Current Stock',
            'Unit Price',
            'Supplier Price (Carton)',
            'Supplier Price (Box)',
            'Supplier Price (Strip)',
            'Supplier Price (Unit)',
            'Retail Price (Carton)',
            'Retail Price (Box)',
            'Retail Price (Strip)',
            'Retail Price (Unit)',
            'Strips Per Box',
            'Pieces Per Strip',
            'Box Quantity',
            'Controlled Substance',
            'Prescription Required',
            'Enabled Units',
            'Enabled Retail Units',
            'Status',
            'Created At',
            'Updated At'
        ];
    }

    /**
     * @param Medicine $medicine
     * @return array
     */
    public function map($medicine): array
    {
        return [
            $medicine->id,
            $medicine->name,
            $medicine->generic_name,
            $medicine->dosage,
            $medicine->category ? $medicine->category->name : '',
            $medicine->manufacturer ? $medicine->manufacturer->name : '',
            $medicine->unitType ? $medicine->unitType->name : '',
            $medicine->minimum_stock,
            $medicine->maximum_stock,
            $medicine->inventories->sum('quantity'),
            $medicine->unit_price,
            $medicine->supplier_price_carton,
            $medicine->supplier_price_box,
            $medicine->supplier_price_strip,
            $medicine->supplier_price_unit,
            $medicine->retail_price_carton,
            $medicine->retail_price_box,
            $medicine->retail_price_strip,
            $medicine->retail_price_unit,
            $medicine->strips_per_box,
            $medicine->pieces_per_strip,
            $medicine->box_quantity,
            $medicine->controlled_substance ? 'Yes' : 'No',
            $medicine->prescription_required ? 'Yes' : 'No',
            is_array($medicine->enabled_units) ? implode(', ', $medicine->enabled_units) : $medicine->enabled_units,
            is_array($medicine->enabled_retail_units) ? implode(', ', $medicine->enabled_retail_units) : $medicine->enabled_retail_units,
            $medicine->status ?? 'active',
            $medicine->created_at ? $medicine->created_at->format('Y-m-d H:i:s') : '',
            $medicine->updated_at ? $medicine->updated_at->format('Y-m-d H:i:s') : ''
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true]],
        ];
    }
}
