<?php

namespace App\Console\Commands;

use App\Services\LoyaltyService;
use Illuminate\Console\Command;

class ExpireLoyaltyPoints extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'loyalty:expire-points';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Expire old loyalty points that have passed their expiration date';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting loyalty points expiration process...');

        $loyaltyService = new LoyaltyService();
        $expiredPoints = $loyaltyService->expireOldPoints();

        if ($expiredPoints > 0) {
            $this->info("Successfully expired {$expiredPoints} loyalty points.");
        } else {
            $this->info('No loyalty points needed to be expired.');
        }

        return Command::SUCCESS;
    }
}
