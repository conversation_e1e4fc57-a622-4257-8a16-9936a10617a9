<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Str;
use ZipArchive;

class BackupRunCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:run {--only-db : Only backup database} {--only-files : Only backup files}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a backup of the application';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting backup process...');

        $timestamp = now()->format('Y-m-d-H-i-s');
        $backupFileName = "backup-{$timestamp}";
        $backupPath = storage_path("app/backups");
        $zipPath = "{$backupPath}/{$backupFileName}.zip";

        // Create backups directory if it doesn't exist
        if (!File::isDirectory($backupPath)) {
            File::makeDirectory($backupPath, 0755, true);
        }

        // Initialize ZIP archive
        $zip = new ZipArchive();
        if ($zip->open($zipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== true) {
            $this->error("Cannot create ZIP file {$zipPath}");
            return 1;
        }

        // Backup database if not only-files option
        if (!$this->option('only-files')) {
            $this->info('Backing up database...');
            $this->backupDatabase($zip, $timestamp);
        }

        // Backup files if not only-db option
        if (!$this->option('only-db')) {
            $this->info('Backing up files...');
            $this->backupFiles($zip);
        }

        // Close ZIP archive
        $zip->close();

        // Create a copy as latest.zip
        File::copy($zipPath, "{$backupPath}/latest.zip");

        $this->info('Backup completed successfully!');
        $this->info("Backup saved to: {$zipPath}");
        
        return 0;
    }

    /**
     * Backup database tables
     */
    protected function backupDatabase($zip, $timestamp)
    {
        $tables = [];
        $dbTables = DB::select('SHOW TABLES');
        
        foreach ($dbTables as $table) {
            $table = get_object_vars($table);
            $tables[] = reset($table);
        }

        $dbName = Config::get('database.connections.mysql.database');
        
        foreach ($tables as $table) {
            $this->info("Backing up table: {$table}");
            
            // Get table structure
            $structure = DB::select("SHOW CREATE TABLE `{$table}`");
            $createTable = get_object_vars($structure[0])['Create Table'] ?? '';
            
            if (empty($createTable)) {
                $this->warn("Could not get structure for table {$table}, skipping...");
                continue;
            }
            
            // Add DROP TABLE statement
            $createTable = "DROP TABLE IF EXISTS `{$table}`;\n\n{$createTable};";
            
            // Add to ZIP
            $zip->addFromString("database/{$table}_structure.sql", $createTable);
            
            // Get table data
            $rows = DB::table($table)->get();
            if ($rows->count() > 0) {
                $insertStatements = [];
                $insertHeader = "INSERT INTO `{$table}` VALUES ";
                $currentInsert = $insertHeader;
                $count = 0;
                
                foreach ($rows as $row) {
                    $values = [];
                    foreach ((array)$row as $value) {
                        if (is_null($value)) {
                            $values[] = 'NULL';
                        } elseif (is_numeric($value)) {
                            $values[] = $value;
                        } else {
                            $values[] = "'" . addslashes($value) . "'";
                        }
                    }
                    
                    $rowValues = '(' . implode(',', $values) . ')';
                    
                    // Start a new INSERT statement if the current one gets too large
                    if (strlen($currentInsert) + strlen($rowValues) + 1 > 1000000) {
                        $insertStatements[] = $currentInsert . ";";
                        $currentInsert = $insertHeader;
                    }
                    
                    if ($currentInsert != $insertHeader) {
                        $currentInsert .= ',';
                    }
                    
                    $currentInsert .= $rowValues;
                    $count++;
                }
                
                if ($currentInsert != $insertHeader) {
                    $insertStatements[] = $currentInsert . ";";
                }
                
                // Add data to ZIP
                foreach ($insertStatements as $index => $statement) {
                    $zip->addFromString("database/{$table}_data_{$index}.sql", $statement);
                }
                
                $this->info("  - {$count} rows backed up");
            } else {
                $this->info("  - Table is empty");
            }
        }
        
        // Create a combined SQL file with all tables
        $combinedSql = "-- PharmaDek Database Backup\n";
        $combinedSql .= "-- Generated: " . now()->toDateTimeString() . "\n";
        $combinedSql .= "-- Database: {$dbName}\n\n";
        $combinedSql .= "SET FOREIGN_KEY_CHECKS=0;\n\n";
        
        foreach ($tables as $table) {
            $combinedSql .= "-- Table structure for {$table}\n";
            $structure = DB::select("SHOW CREATE TABLE `{$table}`");
            $createTable = get_object_vars($structure[0])['Create Table'] ?? '';
            $combinedSql .= "DROP TABLE IF EXISTS `{$table}`;\n\n{$createTable};\n\n";
        }
        
        $zip->addFromString("database_backup_{$timestamp}.sql", $combinedSql);
        
        // Add a README file
        $readme = "PharmaDek Database Backup\n";
        $readme .= "Generated: " . now()->toDateTimeString() . "\n";
        $readme .= "Database: {$dbName}\n\n";
        $readme .= "This backup contains SQL files for each table structure and data.\n";
        $readme .= "To restore, import the database_backup_{$timestamp}.sql file or import each table file separately.\n";
        
        $zip->addFromString("database/README.txt", $readme);
    }

    /**
     * Backup important files
     */
    protected function backupFiles($zip)
    {
        // Directories to backup
        $directoriesToBackup = [
            'app',
            'config',
            'database/migrations',
            'public/images',
            'public/uploads',
            'resources/views',
            'routes',
        ];
        
        // Files to backup
        $filesToBackup = [
            '.env',
            'composer.json',
            'composer.lock',
            'package.json',
            'package-lock.json',
            'artisan',
        ];
        
        // Add directories
        foreach ($directoriesToBackup as $directory) {
            $path = base_path($directory);
            if (!File::isDirectory($path)) {
                $this->warn("Directory not found: {$directory}, skipping...");
                continue;
            }
            
            $this->info("Backing up directory: {$directory}");
            $files = File::allFiles($path);
            
            foreach ($files as $file) {
                $relativePath = 'files/' . $directory . '/' . $file->getRelativePathname();
                $zip->addFile($file->getRealPath(), $relativePath);
            }
        }
        
        // Add individual files
        foreach ($filesToBackup as $file) {
            $path = base_path($file);
            if (!File::exists($path)) {
                $this->warn("File not found: {$file}, skipping...");
                continue;
            }
            
            $this->info("Backing up file: {$file}");
            $zip->addFile($path, 'files/' . $file);
        }
        
        // Add a README file
        $readme = "PharmaDek Files Backup\n";
        $readme .= "Generated: " . now()->toDateTimeString() . "\n\n";
        $readme .= "This backup contains important application files.\n";
        $readme .= "To restore, extract the files to your application directory.\n";
        
        $zip->addFromString("files/README.txt", $readme);
    }
} 