<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Customers\Customer;
use App\Models\Sales\Sale;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateCustomerLastPurchaseDate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-customer-last-purchase-date';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update last_purchase_date field for existing customers';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to update last_purchase_date for customers...');
        
        $totalCustomers = Customer::count();
        $this->info("Found {$totalCustomers} customers to process");
        
        $bar = $this->output->createProgressBar($totalCustomers);
        $bar->start();
        
        $updated = 0;
        $errors = 0;
        
        Customer::chunk(100, function ($customers) use (&$updated, &$errors, $bar) {
            foreach ($customers as $customer) {
                try {
                    // Find the latest sale for this customer
                    $latestSale = Sale::where('customer_id', $customer->id)
                        ->orderBy('created_at', 'desc')
                        ->first();
                    
                    if ($latestSale) {
                        // Update the customer record
                        $customer->last_purchase_date = $latestSale->created_at;
                        $customer->save();
                        $updated++;
                    }
                } catch (\Exception $e) {
                    Log::error("Error updating customer ID {$customer->id}: " . $e->getMessage());
                    $errors++;
                }
                
                $bar->advance();
            }
        });
        
        $bar->finish();
        $this->newLine(2);
        
        $this->info("Update completed:");
        $this->info("- {$updated} customers updated successfully");
        $this->info("- {$errors} errors encountered");
        
        if ($errors > 0) {
            $this->warn("Check the logs for error details");
        }
        
        return Command::SUCCESS;
    }
}
