<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Users\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CheckUserStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:check-status {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check user status and enable if needed';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        // Check if users table has status column
        $this->info("Checking if users table has a status column...");
        $columns = Schema::getColumnListing('users');
        $this->info("Columns in users table: " . implode(', ', $columns));
        
        // Get the user
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("User not found with email: $email");
            return Command::FAILURE;
        }
        
        $this->info("User found with ID: {$user->id}, Name: {$user->name}");
        
        // Check for status column
        if (in_array('status', $columns)) {
            $this->info("Current status: " . ($user->status ?: 'NULL'));
            
            // Enable user by setting status to 'active'
            $user->status = 'active';
            $user->save();
            
            $this->info("User status updated to 'active'");
        } else {
            $this->info("No status column found, but we will try to update the email_verified_at column to ensure login works");
            $user->email_verified_at = now();
            $user->save();
            $this->info("Updated email_verified_at to: " . $user->email_verified_at);
        }
        
        // Ensure the user has the correct role
        $this->info("Current roles: " . implode(', ', $user->getRoleNames()->toArray()));
        
        return Command::SUCCESS;
    }
}
