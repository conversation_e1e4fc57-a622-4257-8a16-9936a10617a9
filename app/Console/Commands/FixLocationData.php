<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixLocationData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:location-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix location data by converting string NULL values to actual NULL values';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting location data fix...');
        
        // 1. Fix string 'NULL' values in locations table
        $this->info('Fixing string NULL values in locations table...');
        
        $locationColumns = [
            'section',
            'zone',
            'aisle_number',
            'address',
            'bin_location',
            'temperature_requirement'
        ];
        
        $locationCount = 0;
        
        foreach ($locationColumns as $column) {
            $affected = DB::table('locations')
                ->where($column, '=', 'NULL')
                ->update([$column => null]);
            
            $locationCount += $affected;
            $this->info("Fixed {$affected} records in locations.{$column}");
        }
        
        $this->info("Total fixed in locations table: {$locationCount}");
        
        // 2. Fix string 'NULL' values in inventories table
        $this->info('Fixing string NULL values in inventories table...');
        
        $inventoryColumns = [
            'rack_number',
            'bin_location',
            'temperature_requirement',
            'notes'
        ];
        
        $inventoryCount = 0;
        
        foreach ($inventoryColumns as $column) {
            $affected = DB::table('inventories')
                ->where($column, '=', 'NULL')
                ->update([$column => null]);
            
            $inventoryCount += $affected;
            $this->info("Fixed {$affected} records in inventories.{$column}");
        }
        
        $this->info("Total fixed in inventories table: {$inventoryCount}");
        
        // 3. Propagate rack_number from locations to inventories where missing
        $this->info('Propagating rack_number from locations to inventories...');
        
        $updated = DB::statement("
            UPDATE inventories i
            JOIN locations l ON i.location_id = l.id
            SET i.rack_number = l.rack_number
            WHERE i.rack_number IS NULL 
            AND l.rack_number IS NOT NULL
            AND l.rack_number != 'NULL'
        ");
        
        $this->info("Updated rack_number in inventories");
        
        // 4. Propagate bin_location from locations to inventories where missing
        $this->info('Propagating bin_location from locations to inventories...');
        
        $updated = DB::statement("
            UPDATE inventories i
            JOIN locations l ON i.location_id = l.id
            SET i.bin_location = l.bin_location
            WHERE i.bin_location IS NULL 
            AND l.bin_location IS NOT NULL
            AND l.bin_location != 'NULL'
        ");
        
        $this->info("Updated bin_location in inventories");
        
        // 5. Log a summary of the location data
        $this->info('Generating location data summary...');
        
        $locationStats = DB::select("
            SELECT 
                COUNT(*) as total_locations,
                SUM(CASE WHEN rack_number IS NOT NULL THEN 1 ELSE 0 END) as locations_with_rack,
                SUM(CASE WHEN bin_location IS NOT NULL THEN 1 ELSE 0 END) as locations_with_bin
            FROM locations
        ");
        
        $inventoryStats = DB::select("
            SELECT 
                COUNT(*) as total_inventories,
                SUM(CASE WHEN location_id IS NOT NULL THEN 1 ELSE 0 END) as inventories_with_location,
                SUM(CASE WHEN rack_number IS NOT NULL THEN 1 ELSE 0 END) as inventories_with_rack,
                SUM(CASE WHEN bin_location IS NOT NULL THEN 1 ELSE 0 END) as inventories_with_bin
            FROM inventories
        ");
        
        $this->info('Location statistics:');
        $this->table(
            ['Total Locations', 'With Rack', 'With Bin'],
            [[
                $locationStats[0]->total_locations,
                $locationStats[0]->locations_with_rack,
                $locationStats[0]->locations_with_bin
            ]]
        );
        
        $this->info('Inventory statistics:');
        $this->table(
            ['Total Inventories', 'With Location', 'With Rack', 'With Bin'],
            [[
                $inventoryStats[0]->total_inventories,
                $inventoryStats[0]->inventories_with_location,
                $inventoryStats[0]->inventories_with_rack,
                $inventoryStats[0]->inventories_with_bin
            ]]
        );
        
        $this->info('Location data fix completed!');
        
        return Command::SUCCESS;
    }
}
