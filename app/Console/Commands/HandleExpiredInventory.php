<?php

namespace App\Console\Commands;

use App\Services\Inventory\StockService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class HandleExpiredInventory extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'inventory:handle-expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handle expired inventory and create batch history records';

    /**
     * Execute the console command.
     */
    public function handle(StockService $stockService)
    {
        $this->info('Starting to handle expired inventory...');
        
        try {
            $expiredCount = $stockService->handleExpiredInventory();
            
            $this->info("Successfully processed {$expiredCount} expired batches.");
            Log::info("Expired inventory handler processed {$expiredCount} batches.");
        } catch (\Exception $e) {
            $this->error('Error handling expired inventory: ' . $e->getMessage());
            Log::error('Error in expired inventory command: ' . $e->getMessage());
        }
        
        return Command::SUCCESS;
    }
}
