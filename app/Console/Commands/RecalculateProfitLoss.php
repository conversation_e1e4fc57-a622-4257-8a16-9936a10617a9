<?php

namespace App\Console\Commands;

use App\Models\Inventory\ProfitLoss;
use Illuminate\Console\Command;

class RecalculateProfitLoss extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'profit-loss:recalculate 
                            {--dry-run : Show what would be recalculated without making changes}
                            {--from= : Start date (Y-m-d format)}
                            {--to= : End date (Y-m-d format)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Recalculate profit and loss records to fix any calculation issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $fromDate = $this->option('from');
        $toDate = $this->option('to');

        $this->info('Starting profit loss recalculation...');
        
        if ($dryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        // Build query
        $query = ProfitLoss::query();
        
        if ($fromDate) {
            $query->where('transaction_date', '>=', $fromDate);
            $this->info("Filtering from date: {$fromDate}");
        }
        
        if ($toDate) {
            $query->where('transaction_date', '<=', $toDate);
            $this->info("Filtering to date: {$toDate}");
        }

        $totalRecords = $query->count();
        $this->info("Found {$totalRecords} records to process");

        if ($totalRecords === 0) {
            $this->warn('No records found to process');
            return 0;
        }

        if (!$dryRun && !$this->confirm('Do you want to proceed with recalculation?')) {
            $this->info('Operation cancelled');
            return 0;
        }

        $processed = 0;
        $errors = 0;
        
        $progressBar = $this->output->createProgressBar($totalRecords);
        $progressBar->start();

        $query->chunk(100, function ($records) use (&$processed, &$errors, $progressBar, $dryRun) {
            foreach ($records as $record) {
                try {
                    if (!$dryRun) {
                        $record->calculateProfits();
                    }
                    $processed++;
                } catch (\Exception $e) {
                    $errors++;
                    $this->error("\nError processing record ID {$record->id}: " . $e->getMessage());
                }
                $progressBar->advance();
            }
        });

        $progressBar->finish();
        $this->newLine();

        if ($dryRun) {
            $this->info("DRY RUN COMPLETE: Would have processed {$processed} records");
        } else {
            $this->info("RECALCULATION COMPLETE: Processed {$processed} records");
        }

        if ($errors > 0) {
            $this->error("Encountered {$errors} errors during processing");
            return 1;
        }

        $this->info('All records processed successfully!');
        return 0;
    }
}
