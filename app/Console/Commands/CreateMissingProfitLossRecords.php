<?php

namespace App\Console\Commands;

use App\Models\Sales\Sale;
use App\Models\Inventory\ProfitLoss;
use App\Observers\SaleObserver;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CreateMissingProfitLossRecords extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'profit-loss:create-missing 
                            {--dry-run : Show what would be created without making changes}
                            {--force : Force recreation of existing records}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create missing profit loss records for existing sales';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        $this->info('Checking for missing profit loss records...');
        
        if ($dryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        // Get all sales
        $sales = Sale::with('items.medicine')->get();
        $this->info("Found {$sales->count()} sales to check");

        $created = 0;
        $skipped = 0;
        $errors = 0;

        foreach ($sales as $sale) {
            try {
                // Check if profit loss records exist for this sale
                $existingRecords = ProfitLoss::where('sale_id', $sale->id)->count();
                $expectedRecords = $sale->items->count();

                if ($existingRecords === $expectedRecords && !$force) {
                    $skipped++;
                    continue;
                }

                if ($force && $existingRecords > 0) {
                    $this->info("Deleting existing records for sale {$sale->invoice_number}");
                    if (!$dryRun) {
                        ProfitLoss::where('sale_id', $sale->id)->delete();
                    }
                }

                $this->info("Creating profit loss records for sale {$sale->invoice_number} ({$expectedRecords} items)");
                
                if (!$dryRun) {
                    // Manually trigger the observer logic
                    $this->createProfitLossRecords($sale);
                }
                
                $created++;
                
            } catch (\Exception $e) {
                $this->error("Error processing sale {$sale->invoice_number}: " . $e->getMessage());
                Log::error("Error creating profit loss for sale {$sale->id}", [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                $errors++;
            }
        }

        if ($dryRun) {
            $this->info("DRY RUN COMPLETE: Would have created records for {$created} sales");
        } else {
            $this->info("CREATION COMPLETE: Created records for {$created} sales");
        }
        
        $this->info("Skipped: {$skipped} sales (already have records)");
        
        if ($errors > 0) {
            $this->error("Encountered {$errors} errors during processing");
            return 1;
        }

        return 0;
    }

    /**
     * Create profit loss records for a sale (mimics SaleObserver logic)
     */
    private function createProfitLossRecords(Sale $sale): void
    {
        foreach ($sale->items as $item) {
            $medicine = $item->medicine;
            
            // Calculate base item total before any adjustments
            $itemTotal = round($item->quantity * $item->unit_price, 2);
            
            // Calculate proportional discount and tax for this item
            $allItemsTotal = $sale->items->sum(function($saleItem) {
                return $saleItem->quantity * $saleItem->unit_price;
            });
            
            $discountRatio = $allItemsTotal > 0 ? $sale->discount_amount / $allItemsTotal : 0;
            $taxRatio = $allItemsTotal > 0 ? $sale->tax_amount / $allItemsTotal : 0;
            
            $itemDiscount = round($itemTotal * $discountRatio, 2);
            $itemTax = round($itemTotal * $taxRatio, 2);
            
            // Calculate net revenue (after discount, before tax)
            $netRevenue = round($itemTotal - $itemDiscount, 2);

            // Validate supplier price exists
            $supplierPrice = $medicine->supplier_price_unit ?? 0;
            if ($supplierPrice <= 0) {
                $this->warn("Medicine {$medicine->name} has no supplier price set");
            }

            // Create profit loss record
            ProfitLoss::updateOrCreate(
                [
                    'sale_id' => $sale->id,
                    'medicine_id' => $item->medicine_id,
                    'reference_number' => $sale->invoice_number,
                    'transaction_type' => 'sale'
                ],
                [
                    'quantity' => $item->quantity,
                    'unit_cost' => $supplierPrice,
                    'unit_price' => $item->unit_price,
                    'total_cost' => round($item->quantity * $supplierPrice, 2),
                    'total_revenue' => $netRevenue,
                    'discount_amount' => $itemDiscount,
                    'tax_amount' => $itemTax,
                    'transaction_date' => $sale->created_at->toDateString(),
                    'created_by' => $sale->created_by,
                    'updated_by' => $sale->updated_by
                ]
            )->calculateProfits();
        }
    }
}
