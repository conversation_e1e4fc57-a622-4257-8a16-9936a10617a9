<?php

namespace App\Console\Commands;

use App\Models\Inventory\Medicine;
use Illuminate\Console\Command;

class BuildSearchIndex extends Command
{
    protected $signature = 'search:build-index';
    protected $description = 'Build the search index for medicines';

    public function handle()
    {
        $this->info('Building search index...');

        // Import all medicines to the search index
        Medicine::all()->searchable();

        $this->info('Search index built successfully!');
    }
} 