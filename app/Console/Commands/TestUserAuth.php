<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\Users\User;

class TestUserAuth extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-auth {email} {password}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test user authentication with email and password';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $password = $this->argument('password');
        
        // Attempt to find the user
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("User not found with email: $email");
            return Command::FAILURE;
        }
        
        $this->info("User found: " . $user->name);
        
        // Test password manually
        if (Hash::check($password, $user->password)) {
            $this->info("Password is correct!");
        } else {
            $this->error("Password is incorrect!");
            $this->info("Attempting to update password...");
            
            // Update the password
            $user->password = Hash::make($password);
            $user->save();
            
            $this->info("Password updated successfully!");
        }
        
        return Command::SUCCESS;
    }
}
