<?php

namespace App\Console\Commands;

use App\Models\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class CreateTestUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:test-user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a test user for development';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $user = User::where('email', '<EMAIL>')->first();
        
        if ($user) {
            $this->info('Test user already exists!');
            return;
        }
        
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);
        
        $this->info('Test user created successfully!');
        $this->info('Email: <EMAIL>');
        $this->info('Password: password');
    }
}
