<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Users\User;

class CheckUserExistence extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:check-user {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check if a user exists with the given email';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $user = User::where('email', $email)->first();
        
        if ($user) {
            $this->info("User found:");
            $this->info("ID: " . $user->id);
            $this->info("Name: " . $user->name);
            $this->info("Email: " . $user->email);
            $this->info("Roles: " . implode(', ', $user->getRoleNames()->toArray()));
        } else {
            $this->error("No user found with email: " . $email);
        }
        
        return Command::SUCCESS;
    }
}
