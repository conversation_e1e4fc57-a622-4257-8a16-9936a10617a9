<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\DB;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\BuildSearchIndex::class,
        Commands\HandleExpiredInventory::class,
        Commands\BackupRunCommand::class,
        Commands\RecalculateProfitLoss::class,
        Commands\CreateMissingProfitLossRecords::class,
        Commands\ExpireLoyaltyPoints::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * These schedules are used to run the console commands.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('inspire')->hourly();
        
        // Run expired inventory handler daily at midnight
        $schedule->command('inventory:handle-expired')
                ->daily()
                ->at('00:00')
                ->appendOutputTo(storage_path('logs/expired-inventory.log'));

        // Run loyalty points expiration daily at 3 AM
        $schedule->command('loyalty:expire-points')
                ->daily()
                ->at('03:00')
                ->appendOutputTo(storage_path('logs/loyalty-expiration.log'));

        // Run automatic backups based on settings
        $this->scheduleBackup($schedule);
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
    
    /**
     * Schedule automatic backups based on user settings
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function scheduleBackup(Schedule $schedule)
    {
        try {
            // Get backup settings from database
            $settings = DB::table('settings')
                ->whereIn('key', ['backup_frequency', 'backup_enabled', 'backup_retention_days'])
                ->get()
                ->keyBy('key')
                ->map(function ($item) {
                    return $item->value;
                })
                ->toArray();
            
            $backupEnabled = isset($settings['backup_enabled']) ? (bool) $settings['backup_enabled'] : true;
            $backupFrequency = $settings['backup_frequency'] ?? 'daily';
            $retentionDays = (int) ($settings['backup_retention_days'] ?? 30);
            
            if (!$backupEnabled) {
                return;
            }
            
            $command = $schedule->command('backup:run')
                ->appendOutputTo(storage_path('logs/backup.log'));
            
            switch ($backupFrequency) {
                case 'daily':
                    $command->daily()->at('01:00');
                    break;
                case 'weekly':
                    $command->weekly()->mondays()->at('01:00');
                    break;
                case 'monthly':
                    $command->monthly()->at('01:00');
                    break;
                default:
                    $command->daily()->at('01:00');
                    break;
            }
            
            // Clean old backups
            $schedule->call(function () use ($retentionDays) {
                $backupPath = storage_path('app/backups');
                if (is_dir($backupPath)) {
                    $files = glob("{$backupPath}/*.zip");
                    $now = time();
                    
                    foreach ($files as $file) {
                        // Skip the latest.zip file
                        if (basename($file) === 'latest.zip') {
                            continue;
                        }
                        
                        if (is_file($file)) {
                            if ($now - filemtime($file) >= 60 * 60 * 24 * $retentionDays) {
                                unlink($file);
                            }
                        }
                    }
                }
            })->daily()->at('02:00');
            
        } catch (\Exception $e) {
            \Log::error('Failed to schedule backup: ' . $e->getMessage());
        }
    }
} 