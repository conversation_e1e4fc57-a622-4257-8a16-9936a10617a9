<?php

namespace App\Models\Traits;

use App\Models\Users\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HasUserTracking
{
    protected static function bootHasUserTracking()
    {
        static::creating(function ($model) {
            if (auth()->check() && !isset($model->created_by)) {
                $model->created_by = auth()->id();
            }
            if (auth()->check() && !isset($model->updated_by)) {
                $model->updated_by = auth()->id();
            }
        });

        static::updating(function ($model) {
            if (auth()->check()) {
                $model->updated_by = auth()->id();
            }
        });
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
