<?php

namespace App\Models\Traits;

use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Builder;

trait Searchable
{
    /**
     * Scope a query to search for terms.
     *
     * @param Builder $query
     * @param string $term
     * @return Builder
     */
    public function scopeSearch(Builder $query, string $term): Builder
    {
        $searchableFields = $this->getSearchableFields();
        $terms = explode(' ', Str::lower($term));

        return $query->where(function ($query) use ($searchableFields, $terms) {
            foreach ($terms as $term) {
                $query->where(function ($query) use ($searchableFields, $term) {
                    foreach ($searchableFields as $field) {
                        $query->orWhere($field, 'LIKE', "%{$term}%");
                    }
                });
            }
        });
    }

    /**
     * Get the fields that should be searched.
     *
     * @return array
     */
    protected function getSearchableFields(): array
    {
        return $this->searchable ?? ['name'];
    }

    /**
     * Apply fuzzy search to a query.
     *
     * @param Builder $query
     * @param string $term
     * @return Builder
     */
    public function scopeFuzzySearch(Builder $query, string $term): Builder
    {
        $searchableFields = $this->getSearchableFields();
        $terms = explode(' ', Str::lower($term));
        $fuzzyTerms = $this->getFuzzyTerms($terms);

        return $query->where(function ($query) use ($searchableFields, $fuzzyTerms) {
            foreach ($fuzzyTerms as $term) {
                $query->orWhere(function ($query) use ($searchableFields, $term) {
                    foreach ($searchableFields as $field) {
                        $query->orWhere($field, 'LIKE', "%{$term}%");
                    }
                });
            }
        });
    }

    /**
     * Get fuzzy variations of search terms.
     *
     * @param array $terms
     * @return array
     */
    protected function getFuzzyTerms(array $terms): array
    {
        $fuzzyTerms = [];
        foreach ($terms as $term) {
            // Add exact term
            $fuzzyTerms[] = $term;

            // Add common misspellings
            $fuzzyTerms = array_merge($fuzzyTerms, $this->getCommonMisspellings($term));

            // Add phonetic variations
            $fuzzyTerms = array_merge($fuzzyTerms, $this->getPhoneticVariations($term));
        }

        return array_unique($fuzzyTerms);
    }

    /**
     * Get common misspellings for a term.
     *
     * @param string $term
     * @return array
     */
    protected function getCommonMisspellings(string $term): array
    {
        $misspellings = [];
        
        // Common character substitutions
        $substitutions = [
            'a' => ['e'],
            'e' => ['a', 'i'],
            'i' => ['e', 'y'],
            'o' => ['u'],
            'u' => ['o'],
            'ph' => ['f'],
            'f' => ['ph'],
            'c' => ['k', 's'],
            'k' => ['c'],
            's' => ['c'],
        ];

        // Generate variations
        foreach ($substitutions as $original => $replacements) {
            if (Str::contains($term, $original)) {
                foreach ($replacements as $replacement) {
                    $misspellings[] = str_replace($original, $replacement, $term);
                }
            }
        }

        return $misspellings;
    }

    /**
     * Get phonetic variations of a term.
     *
     * @param string $term
     * @return array
     */
    protected function getPhoneticVariations(string $term): array
    {
        $variations = [];
        
        // Add Soundex variation
        $variations[] = soundex($term);
        
        // Add Metaphone variation
        $variations[] = metaphone($term);
        
        return array_unique($variations);
    }
} 