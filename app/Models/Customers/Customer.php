<?php

namespace App\Models\Customers;

use App\Models\Sales\Sale;
use App\Models\Sales\Prescription;
use App\Models\Customers\LoyaltyTransaction;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Traits\HasUserTracking;
use App\Models\Traits\Searchable;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Customer extends Model
{
    use SoftDeletes, HasUserTracking, Searchable, HasFactory;

    /**
     * The fields that should be searched.
     *
     * @var array
     */
    protected $searchable = [
        'name',
        'email',
        'phone',
        'address'
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'date_of_birth',
        'gender',
        'loyalty_points',
        'total_spent',
        'preferred_contact_method',
        'notes',
        'status',
        'created_by',
        'updated_by',
        'last_purchase_date'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'loyalty_points' => 'integer',
        'total_spent' => 'decimal:2',
        'date_of_birth' => 'date',
        'email_verified_at' => 'datetime',
        'last_purchase_date' => 'datetime'
    ];

    /**
     * Get all of the sales for the customer.
     */
    public function sales(): HasMany
    {
        return $this->hasMany(Sale::class);
    }

    /**
     * Get all loyalty transactions for the customer.
     */
    public function loyaltyTransactions(): HasMany
    {
        return $this->hasMany(LoyaltyTransaction::class);
    }

    /**
     * Get all prescriptions for the customer through sales.
     */
    public function prescriptions(): HasManyThrough
    {
        return $this->hasManyThrough(
            Prescription::class,
            Sale::class,
            'customer_id', // Foreign key on sales table
            'sale_id',     // Foreign key on prescriptions table
            'id',          // Local key on customers table
            'id'           // Local key on sales table
        );
    }

    /**
     * Get the customer's loyalty tier
     */
    public function getLoyaltyTierAttribute(): string
    {
        return $this->loyalty_points >= 1000 ? 'Gold' : 'Regular';
    }

    /**
     * Get points needed for next tier
     */
    public function getPointsToNextTierAttribute(): int
    {
        return $this->loyalty_points >= 1000 ? 0 : (1000 - $this->loyalty_points);
    }

    /**
     * Get total points earned
     */
    public function getTotalPointsEarnedAttribute(): int
    {
        return $this->loyaltyTransactions()
            ->where('type', LoyaltyTransaction::TYPE_EARNED)
            ->sum('points');
    }

    /**
     * Get total points redeemed
     */
    public function getTotalPointsRedeemedAttribute(): int
    {
        return abs($this->loyaltyTransactions()
            ->where('type', LoyaltyTransaction::TYPE_REDEEMED)
            ->sum('points'));
    }
}
