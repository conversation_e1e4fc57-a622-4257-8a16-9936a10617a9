<?php

namespace App\Models\Inventory;

use App\Models\Users\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UnitConversion extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'from_unit_id',
        'to_unit_id',
        'conversion_factor',
        'notes',
        'status',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'conversion_factor' => 'decimal:6'
    ];

    // Relationships
    public function fromUnit()
    {
        return $this->belongsTo(UnitType::class, 'from_unit_id');
    }

    public function toUnit()
    {
        return $this->belongsTo(UnitType::class, 'to_unit_id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    // Helper methods
    public function convert($quantity)
    {
        return $quantity * $this->conversion_factor;
    }

    public function reverseConvert($quantity)
    {
        return $quantity / $this->conversion_factor;
    }
}
