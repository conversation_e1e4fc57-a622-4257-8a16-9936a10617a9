<?php

namespace App\Models\Inventory;

use App\Models\Sales\Sale;
use App\Models\Traits\HasUserTracking;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProfitLoss extends Model
{
    use SoftDeletes, HasUserTracking;

    protected $fillable = [
        'medicine_id',
        'sale_id',
        'purchase_id',
        'reference_number',
        'transaction_type',
        'quantity',
        'unit_cost',
        'unit_price',
        'total_cost',
        'total_revenue',
        'discount_amount',
        'tax_amount',
        'shipping_cost',
        'gross_profit',
        'net_profit',
        'profit_margin',
        'transaction_date',
        'notes',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'quantity' => 'integer',
        'unit_cost' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'total_revenue' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'shipping_cost' => 'decimal:2',
        'gross_profit' => 'decimal:2',
        'net_profit' => 'decimal:2',
        'profit_margin' => 'decimal:2',
        'transaction_date' => 'date',
    ];

    // Relationships
    public function medicine(): BelongsTo
    {
        return $this->belongsTo(Medicine::class);
    }

    public function sale(): BelongsTo
    {
        return $this->belongsTo(Sale::class);
    }

    public function purchase(): BelongsTo
    {
        return $this->belongsTo(Purchase::class);
    }

    // Helper Methods
    public function calculateProfits(): void
    {
        if ($this->transaction_type === 'sale') {
            $this->calculateSaleProfit();
        } else {
            $this->calculatePurchaseCost();
        }
    }

    protected function calculateSaleProfit(): void
    {
        // If total_revenue is already set (from observer), use it
        // Otherwise calculate from unit price and quantity
        if (!$this->total_revenue || $this->total_revenue == 0) {
            $this->total_revenue = round($this->quantity * $this->unit_price, 2);
        }

        // Calculate total cost
        $this->total_cost = round($this->quantity * $this->unit_cost, 2);

        // Calculate gross profit (Revenue - Cost)
        // Note: total_revenue should already be net of discounts from the observer
        $this->gross_profit = round($this->total_revenue - $this->total_cost, 2);

        // Calculate net profit (same as gross profit since discount is already applied to revenue)
        // Tax is not deducted from net profit as it's a pass-through to government
        $this->net_profit = $this->gross_profit;

        // Calculate profit margin as a percentage of gross revenue (before discount)
        $grossRevenue = round($this->quantity * $this->unit_price, 2);
        if ($grossRevenue > 0) {
            $this->profit_margin = round(($this->net_profit / $grossRevenue) * 100, 2);
        } else {
            $this->profit_margin = 0;
        }

        $this->save();
    }

    public function calculatePurchaseCost(): void
    {
        // For purchases, calculate total cost including shipping
        $baseCost = round($this->quantity * $this->unit_cost, 2);
        $this->total_cost = round($baseCost + ($this->shipping_cost ?? 0), 2);

        // Reset revenue and profit fields for purchases
        $this->total_revenue = 0;
        $this->gross_profit = 0;
        $this->net_profit = 0;
        $this->profit_margin = 0;

        $this->save();
    }

    // Scopes for common queries
    public function scopeSales($query)
    {
        return $query->where('transaction_type', 'sale');
    }

    public function scopePurchases($query)
    {
        return $query->where('transaction_type', 'purchase');
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('transaction_date', [$startDate, $endDate]);
    }

    public function scopeByMedicine($query, $medicineId)
    {
        return $query->where('medicine_id', $medicineId);
    }

    /**
     * Recalculate all profit loss records
     * Useful for fixing data inconsistencies
     */
    public static function recalculateAll(): int
    {
        $count = 0;
        self::chunk(100, function ($records) use (&$count) {
            foreach ($records as $record) {
                $record->calculateProfits();
                $count++;
            }
        });

        return $count;
    }

    /**
     * Get profit loss summary for a date range
     */
    public static function getSummary($startDate, $endDate): array
    {
        $summary = self::whereBetween('transaction_date', [$startDate, $endDate])
            ->selectRaw('
                SUM(CASE WHEN transaction_type = "sale" THEN total_revenue ELSE 0 END) as total_revenue,
                SUM(CASE WHEN transaction_type = "sale" THEN total_cost ELSE 0 END) as sale_costs,
                SUM(CASE WHEN transaction_type = "purchase" THEN total_cost ELSE 0 END) as purchase_costs,
                SUM(CASE WHEN transaction_type = "sale" THEN gross_profit ELSE 0 END) as gross_profit,
                SUM(CASE WHEN transaction_type = "sale" THEN net_profit ELSE 0 END) as net_profit,
                COUNT(CASE WHEN transaction_type = "sale" THEN 1 END) as sale_count,
                COUNT(CASE WHEN transaction_type = "purchase" THEN 1 END) as purchase_count
            ')
            ->first();

        return [
            'total_revenue' => $summary->total_revenue ?? 0,
            'total_cost' => ($summary->sale_costs ?? 0) + ($summary->purchase_costs ?? 0),
            'sale_costs' => $summary->sale_costs ?? 0,
            'purchase_costs' => $summary->purchase_costs ?? 0,
            'gross_profit' => $summary->gross_profit ?? 0,
            'net_profit' => $summary->net_profit ?? 0,
            'sale_count' => $summary->sale_count ?? 0,
            'purchase_count' => $summary->purchase_count ?? 0,
        ];
    }
}
