<?php

namespace App\Models\Inventory;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\Users\User;
use Illuminate\Support\Facades\Log;
use App\Models\Traits\HasUserTracking;

class Purchase extends Model
{
    use SoftDeletes, HasFactory, HasUserTracking;

    protected $fillable = [
        'purchase_number',
        'supplier_id',
        'total_amount',
        'tax_percentage',
        'tax_amount',
        'discount_percentage',
        'discount_amount',
        'shipping_cost',
        'final_amount',
        'paid_amount',
        'due_amount',
        'status',
        'payment_status',
        'order_date',
        'expected_date',
        'delivery_date',
        'quality_rating',
        'notes',
        'created_by',
        'updated_by',
        'purchase_date',
        'unit_cost',
    ];

    protected $casts = [
        'order_date' => 'date',
        'expected_date' => 'date',
        'delivery_date' => 'date',
        'total_amount' => 'decimal:2',
        'tax_percentage' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'shipping_cost' => 'decimal:2',
        'final_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'due_amount' => 'decimal:2',
        'quality_rating' => 'decimal:1',
    ];

    protected static function boot()
    {
        parent::boot();
    }

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(PurchaseItem::class);
    }

    public function payments(): HasMany
    {
        return $this->hasMany(PurchasePayment::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function calculateTotals(): void
    {
        $items = $this->items;
        
        $this->total_amount = $items->sum('total_amount');
        $this->tax_amount = $items->sum('tax_amount');
        $this->discount_amount = $items->sum('discount_amount');
        $this->final_amount = $this->total_amount + $this->tax_amount - $this->discount_amount + $this->shipping_cost;
        
        $this->save();
    }

    public function updatePaymentStatus(): void
    {
        $totalPaid = $this->payments->sum('amount');
        
        if ($totalPaid >= $this->final_amount) {
            $this->payment_status = 'paid';
        } elseif ($totalPaid > 0) {
            $this->payment_status = 'partial';
        } else {
            $this->payment_status = 'pending';
        }
        
        $this->save();
    }

    public function updateReceivingStatus(): void
    {
        $items = $this->items;
        $totalQuantity = $items->sum('quantity_ordered');
        $receivedQuantity = $items->sum('quantity_received');

        if ($receivedQuantity >= $totalQuantity) {
            $this->status = 'received';
            $this->delivery_date = now();
        } elseif ($receivedQuantity > 0) {
            $this->status = 'partially_received';
        }

        $this->save();
    }

    public function canBeReceived(): bool
    {
        return in_array($this->status, ['ordered', 'partially_received']);
    }

    public function isFullyReceived(): bool
    {
        return $this->status === 'received';
    }

    public function isPartiallyReceived(): bool
    {
        return $this->status === 'partially_received';
    }

    public function getTotalOrderedQuantityAttribute(): int
    {
        return $this->items->sum('quantity_ordered');
    }

    public function getTotalReceivedQuantityAttribute(): int
    {
        return $this->items->sum('quantity_received');
    }

    public function getRemainingQuantityAttribute(): int
    {
        return $this->total_ordered_quantity - $this->total_received_quantity;
    }

    public function getReceivingProgressAttribute(): float
    {
        if ($this->total_ordered_quantity == 0) {
            return 0;
        }
        return ($this->total_received_quantity / $this->total_ordered_quantity) * 100;
    }
} 