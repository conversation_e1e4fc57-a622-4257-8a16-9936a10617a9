<?php

namespace App\Models\Inventory;

use App\Models\Users\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Inventory\StockMovement;
use App\Models\Inventory\BatchHistory;
use App\Models\Inventory\Inventory;

class Location extends Model
{
    use HasFactory, SoftDeletes;

    // Location types
    const TYPE_WAREHOUSE = 'warehouse';
    const TYPE_STORE = 'store';
    const TYPE_RACK = 'rack';
    const TYPE_ZONE = 'zone';
    const TYPE_SHELF = 'shelf';
    const TYPE_BIN = 'bin';

    // Temperature requirements
    const TEMP_NORMAL = 'normal';
    const TEMP_COOL = 'cool';
    const TEMP_COLD = 'cold';

    // Sections
    const SECTION_GEN = 'general';
    const SECTION_CTR = 'controlled';
    const SECTION_RX = 'prescription';
    const SECTION_OTC = 'otc';

    protected $fillable = [
        'name',
        'type',
        'parent_id',
        'level',
        'path',
        'location_code',
        'section',
        'zone',
        'aisle_number',
        'rack_number',
        'bin_location',
        'temperature_requirement',
        'address',
        'status',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'level' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    // Relationships
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Location::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(Location::class, 'parent_id');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function batchHistories(): HasMany
    {
        return $this->hasMany(BatchHistory::class);
    }

    public function inventories(): HasMany
    {
        return $this->hasMany(Inventory::class);
    }

    public function movements(): HasMany
    {
        return $this->hasMany(StockMovement::class, 'from_location_id')
            ->orWhere('to_location_id', $this->id);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeAvailableForStorage($query)
    {
        return $query->where('type', self::TYPE_SHELF)
            ->where('status', 'active');
    }

    // Helper methods
    public function getFullPath(): string
    {
        if (!$this->parent_id) {
            return $this->id;
        }

        return $this->parent->path . '/' . $this->id;
    }

    public function isWarehouse(): bool
    {
        return $this->type === self::TYPE_WAREHOUSE;
    }

    public function isStore(): bool
    {
        return $this->type === self::TYPE_STORE;
    }

    public function isShelf(): bool
    {
        return $this->type === self::TYPE_SHELF;
    }

    public function isControlled(): bool
    {
        return $this->section === self::SECTION_CTR;
    }

    public function isPrescriptionOnly(): bool
    {
        return $this->section === self::SECTION_RX;
    }

    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($location) {
            if ($location->parent_id) {
                $parent = Location::find($location->parent_id);
                $location->level = $parent->level + 1;
                $location->path = $parent->path ? $parent->path . '/' . $parent->id : $parent->id;
            } else {
                $location->level = 0;
                $location->path = null;
            }
        });
    }
}
