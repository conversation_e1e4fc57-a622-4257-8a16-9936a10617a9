<?php

namespace App\Models\Inventory;

use App\Models\Inventory\Category;
use App\Models\Inventory\Manufacturer;
use App\Models\Inventory\Supplier;
use App\Models\Inventory\UnitType;
use App\Models\Users\User;
use App\Models\Inventory\PurchaseItem;
use App\Models\Inventory\Inventory;
use App\Models\Inventory\StockMovement;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Traits\HasUserTracking;
use App\Models\Traits\Searchable;

class Medicine extends Model
{
    use HasFactory, SoftDeletes, HasUserTracking, Searchable;

    /**
     * The fields that should be searched.
     *
     * @var array
     */
    protected $searchable = [
        'name',
        'generic_name'
    ];

    /**
     * Scope a query to search for terms including manufacturer name.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $term
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearchWithManufacturer($query, string $term)
    {
        // First, find medicines that match the search term
        $matchingMedicines = $query->where(function($query) use ($term) {
            // Exact name match gets highest priority
            $query->where('name', 'LIKE', $term)
                  // Similar name matches get second priority
                  ->orWhere('name', 'LIKE', "%{$term}%")
                  // Generic name matches get third priority
                  ->orWhere('generic_name', 'LIKE', "%{$term}%")
                  // Manufacturer name matches get fourth priority
                  ->orWhereHas('manufacturer', function($query) use ($term) {
                      $query->where('name', 'LIKE', "%{$term}%");
                  });
        })->get();

        // Get all generic names from matching medicines
        $genericNames = $matchingMedicines->pluck('generic_name')->unique()->filter();

        // Build the final query
        return $query->where(function($query) use ($term, $genericNames) {
            // Exact name match gets highest priority
            $query->where('name', 'LIKE', $term)
                  // Similar name matches get second priority
                  ->orWhere('name', 'LIKE', "%{$term}%")
                  // Generic name matches get third priority
                  ->orWhere('generic_name', 'LIKE', "%{$term}%")
                  // Manufacturer name matches get fourth priority
                  ->orWhereHas('manufacturer', function($query) use ($term) {
                      $query->where('name', 'LIKE', "%{$term}%");
                  })
                  // Include medicines with the same generic names
                  ->orWhereIn('generic_name', $genericNames);
        })->orderByRaw("CASE 
            WHEN name LIKE ? THEN 1
            WHEN name LIKE ? THEN 2
            WHEN generic_name LIKE ? THEN 3
            ELSE 4
        END", [$term, "%{$term}%", "%{$term}%"]);
    }

    /**
     * Get similar medicines based on generic name.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getSimilarMedicines()
    {
        if (!$this->generic_name) {
            return collect();
        }

        return static::where('generic_name', $this->generic_name)
            ->where('id', '!=', $this->id)
            ->get();
    }

    protected $fillable = [
        'name',
        'generic_name',
        'dosage',
        'description',
        'manufacturer_id',
        'category_id',
        'unit_type_id',
        'minimum_stock',
        'maximum_stock',
        'controlled_substance',
        'prescription_required',
        'supplier_price_carton',
        'supplier_price_box',
        'supplier_price_strip',
        'supplier_price_unit',
        'retail_price_carton',
        'retail_price_box',
        'retail_price_strip',
        'retail_price_unit',
        'enabled_units',
        'enabled_retail_units',
        'status',
        'strips_per_box',
        'pieces_per_strip',
        'box_quantity',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'enabled_units' => 'array',
        'enabled_retail_units' => 'array',
        'controlled_substance' => 'boolean',
        'prescription_required' => 'boolean',
        'supplier_price_carton' => 'decimal:2',
        'supplier_price_box' => 'decimal:2',
        'supplier_price_strip' => 'decimal:2',
        'supplier_price_unit' => 'decimal:2',
        'retail_price_carton' => 'decimal:2',
        'retail_price_box' => 'decimal:2',
        'retail_price_strip' => 'decimal:2',
        'retail_price_unit' => 'decimal:2',
        'deleted_at' => 'datetime'
    ];

    protected $with = ['category', 'manufacturer'];

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($medicine) {
            // If manufacturer_id is set but manufacturer name is empty
            if ($medicine->manufacturer_id && empty($medicine->manufacturer)) {
                $manufacturer = Manufacturer::find($medicine->manufacturer_id);
                if ($manufacturer) {
                    $medicine->manufacturer = $manufacturer->name;
                }
            }
            // If manufacturer name is provided but manufacturer_id is not set
            elseif (!$medicine->manufacturer_id && $medicine->manufacturer) {
                $manufacturer = Manufacturer::firstOrCreate(
                    ['name' => $medicine->manufacturer],
                    ['is_active' => true]
                );
                $medicine->manufacturer_id = $manufacturer->id;
            }

            // Set default status if not set
            if (!$medicine->status) {
                $medicine->status = 'active';
            }
        });
    }

    public function manufacturer()
    {
        return $this->belongsTo(Manufacturer::class, 'manufacturer_id');
    }

    public function suppliers()
    {
        return $this->belongsToMany(
            Supplier::class,
            'medicine_supplier',
            'medicine_id',
            'supplier_id'
        )->withPivot('price', 'is_default', 'created_by')->withTimestamps();
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function unitType()
    {
        return $this->belongsTo(UnitType::class, 'unit_type_id');
    }

    public function purchaseItems()
    {
        return $this->hasMany(PurchaseItem::class);
    }

    public function purchases()
    {
        return $this->hasManyThrough(
            Purchase::class,
            PurchaseItem::class,
            'medicine_id',
            'id',
            'id',
            'purchase_id'
        );
    }

    public function inventories()
    {
        return $this->hasMany(Inventory::class);
    }

    public function stockMovements()
    {
        return $this->hasMany(StockMovement::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    public function scopeControlledSubstances($query)
    {
        return $query->where('controlled_substance', true);
    }

    public function scopePrescriptionRequired($query)
    {
        return $query->where('prescription_required', true);
    }

    // Helper Methods
    public function getCurrentStock()
    {
        // Get current stock from inventory table via purchase items
        return \App\Models\Inventory\Inventory::where('medicine_id', $this->id)
            ->sum('quantity');
    }

    public function getStockQuantityAttribute()
    {
        return $this->getCurrentStock();
    }

    public function needsReorder()
    {
        return $this->getCurrentStock() <= $this->minimum_stock;
    }

    public function getLatestPurchasePrice()
    {
        $latestPurchaseItem = $this->purchaseItems()
            ->whereHas('purchase', function($query) {
                $query->where('status', 'received');
            })
            ->latest('created_at')
            ->first();

        return $latestPurchaseItem ? $latestPurchaseItem->unit_price : $this->supplier_price_unit;
    }

    public function getTotalPurchased()
    {
        return $this->purchaseItems()
            ->whereHas('purchase', function($query) {
                $query->where('status', 'received');
            })
            ->sum('quantity_received');
    }

    public function getDefaultSupplier()
    {
        return $this->suppliers()
            ->wherePivot('is_default', true)
            ->first();
    }

    public function getActiveBatches()
    {
        return $this->inventories()
            ->with('location')
            ->where('quantity', '>', 0)
            ->where(function($query) {
                $query->whereNull('expiry_date')
                      ->orWhere('expiry_date', '>', now());
            })
            ->orderBy('expiry_date')
            ->get();
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        $array = $this->toArray();

        // Customize array to include related data
        $array = array_merge($array, [
            'category_name' => $this->category ? $this->category->name : null,
            'manufacturer_name' => $this->manufacturer ? $this->manufacturer->name : null,
            'unit_type_name' => $this->unitType ? $this->unitType->name : null,
            'searchable_text' => implode(' ', [
                $this->name,
                $this->generic_name,
                $this->category ? $this->category->name : '',
                $this->manufacturer ? $this->manufacturer->name : '',
                $this->unitType ? $this->unitType->name : '',
            ])
        ]);

        return $array;
    }

    /**
     * Get the formatted display name for the medicine
     */
    public function getDisplayNameAttribute()
    {
        $name = $this->name;
        if ($this->dosage) {
            $name .= ' (' . $this->dosage . ')';
        }
        return $name;
    }

    /**
     * Get the full medicine name with generic name
     */
    public function getFullNameAttribute()
    {
        $name = $this->display_name;
        if ($this->generic_name && $this->generic_name !== $this->name) {
            $name .= ' - ' . $this->generic_name;
        }
        return $name;
    }

    /**
     * Check if medicine has valid pricing information
     */
    public function hasPricing()
    {
        return $this->supplier_price_unit > 0 || $this->retail_price_unit > 0;
    }

    /**
     * Get the profit margin percentage
     */
    public function getProfitMarginAttribute()
    {
        if ($this->supplier_price_unit > 0 && $this->retail_price_unit > 0) {
            return (($this->retail_price_unit - $this->supplier_price_unit) / $this->supplier_price_unit) * 100;
        }
        return 0;
    }
}
