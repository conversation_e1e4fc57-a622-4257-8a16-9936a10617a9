<?php

namespace App\Models\Inventory;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Users\User;

class PurchasePayment extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'purchase_id',
        'amount',
        'payment_method',
        'reference_number',
        'payment_date',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_date' => 'date',
    ];

    public function purchase(): BelongsTo
    {
        return $this->belongsTo(Purchase::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    protected static function booted()
    {
        static::created(function ($payment) {
            $payment->purchase->updatePaymentStatus();
        });

        static::updated(function ($payment) {
            $payment->purchase->updatePaymentStatus();
        });

        static::deleted(function ($payment) {
            $payment->purchase->updatePaymentStatus();
        });
    }
} 