<?php

namespace App\Models\Inventory;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\Users\User;
use App\Models\Traits\HasUserTracking;
use App\Models\Inventory\Location;
use App\Models\Inventory\Medicine;

class StockMovement extends Model
{
    use HasFactory, HasUserTracking;

    protected $fillable = [
        'medicine_id',
        'inventory_id',
        'quantity',
        'movement_type',
        'movement_date',
        'reference_number',
        'reference_type',
        'reference_id',
        'batch_number',
        'expiry_date',
        'unit_price',
        'total_cost',
        'notes',
        'source_location_id',
        'destination_location_id',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'quantity' => 'integer',
        'movement_type' => 'string',
        'movement_date' => 'datetime',
        'reference_number' => 'string',
        'reference_type' => 'string',
        'reference_id' => 'integer',
        'batch_number' => 'string',
        'expiry_date' => 'date',
        'unit_price' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'source_location_id' => 'integer',
        'destination_location_id' => 'integer'
    ];

    public function medicine()
    {
        return $this->belongsTo(Medicine::class);
    }

    public function sourceLocation()
    {
        return $this->belongsTo(Location::class, 'source_location_id');
    }

    public function destinationLocation()
    {
        return $this->belongsTo(Location::class, 'destination_location_id');
    }

    public function creator()
    {
        return $this->belongsTo(\App\Models\Users\User::class, 'created_by');
    }
}
