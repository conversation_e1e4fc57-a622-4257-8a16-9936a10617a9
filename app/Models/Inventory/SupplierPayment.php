<?php

namespace App\Models\Inventory;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Users\User;
use App\Models\Traits\HasUserTracking;

class SupplierPayment extends Model
{
    use SoftDeletes, HasUserTracking;

    protected $fillable = [
        'supplier_id',
        'purchase_id',
        'amount',
        'payment_method',
        'reference_number',
        'payment_date',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_date' => 'date',
    ];

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    public function purchase(): BelongsTo
    {
        return $this->belongsTo(Purchase::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    protected static function booted()
    {
        static::created(function ($payment) {
            // Update purchase payment status if purchase is associated
            if ($payment->purchase) {
                $payment->purchase->updatePaymentStatus();
            }
        });

        static::updated(function ($payment) {
            // Update purchase payment status if purchase is associated
            if ($payment->purchase) {
                $payment->purchase->updatePaymentStatus();
            }
        });

        static::deleted(function ($payment) {
            // Update purchase payment status if purchase is associated
            if ($payment->purchase) {
                $payment->purchase->updatePaymentStatus();
            }
        });
    }
} 