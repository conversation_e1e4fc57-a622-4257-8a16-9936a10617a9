<?php

namespace App\Models\Inventory;

use App\Models\Inventory\Medicine;
use App\Models\Inventory\Location;
use App\Models\Users\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class BatchHistory extends Model
{
    use HasFactory, SoftDeletes;

    // Action types
    const ACTION_CREATED = 'created';
    const ACTION_MOVED = 'moved';
    const ACTION_ADJUSTED = 'adjusted';
    const ACTION_EXPIRED = 'expired';

    protected $fillable = [
        'medicine_id',
        'batch_number',
        'expiry_date',
        'action_type',
        'quantity',
        'location_id',
        'unit_price',
        'notes',
        'created_by'
    ];

    protected $casts = [
        'expiry_date' => 'date',
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    // Relationships
    public function medicine(): BelongsTo
    {
        return $this->belongsTo(Medicine::class);
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeCreated($query)
    {
        return $query->where('action_type', self::ACTION_CREATED);
    }

    public function scopeMoved($query)
    {
        return $query->where('action_type', self::ACTION_MOVED);
    }

    public function scopeAdjusted($query)
    {
        return $query->where('action_type', self::ACTION_ADJUSTED);
    }

    public function scopeExpired($query)
    {
        return $query->where('action_type', self::ACTION_EXPIRED);
    }

    // Helper methods
    public function isPositiveAction(): bool
    {
        return in_array($this->action_type, [self::ACTION_CREATED, self::ACTION_MOVED]);
    }

    public function isNegativeAction(): bool
    {
        return in_array($this->action_type, [self::ACTION_ADJUSTED, self::ACTION_EXPIRED]);
    }

    /**
     * Get the valid action types
     *
     * @return array
     */
    public static function getValidActionTypes(): array
    {
        return [
            self::ACTION_CREATED,
            self::ACTION_MOVED,
            self::ACTION_ADJUSTED,
            self::ACTION_EXPIRED
        ];
    }
}
