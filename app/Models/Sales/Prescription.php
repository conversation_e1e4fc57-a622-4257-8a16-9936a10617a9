<?php

namespace App\Models\Sales;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Prescription extends Model
{
    protected $fillable = [
        'sale_id',
        'doctor_name',
        'hospital_name',
        'prescription_date',
        'prescription_image',
    ];

    protected $casts = [
        'prescription_date' => 'date',
    ];

    public function sale(): BelongsTo
    {
        return $this->belongsTo(Sale::class);
    }

    public function customerPrescriptions(): HasMany
    {
        return $this->hasMany(CustomerPrescription::class);
    }
}
