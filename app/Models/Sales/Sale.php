<?php

namespace App\Models\Sales;

use App\Models\Users\User;
use App\Models\Customers\Customer;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use App\Models\Sales\SalePayment;
use App\Models\Sales\Prescription;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Traits\HasUserTracking;

class Sale extends Model
{
    use HasFactory, SoftDeletes, HasUserTracking;

    protected $fillable = [
        'invoice_number',
        'customer_id',
        'total_amount',
        'total_cost',
        'paid_amount',
        'due_amount',
        'discount_amount',
        'tax_amount',
        'payment_method',
        'payment_status',
        'due_date',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'total_amount' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'due_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'due_date' => 'date',
    ];

    protected static function boot()
    {
        parent::boot();
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    public function items(): HasMany
    {
        return $this->hasMany(SaleItem::class);
    }

    public function prescription(): HasOne
    {
        return $this->hasOne(Prescription::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function payments(): HasMany
    {
        return $this->hasMany(SalePayment::class);
    }

    public function getRemainingBalanceAttribute(): float
    {
        return max(0, $this->total_amount - $this->paid_amount);
    }

    public function getPaymentStatusTextAttribute(): string
    {
        if ($this->payment_status === 'completed') {
            return 'Paid';
        }
        
        if ($this->paid_amount > 0) {
            return 'Partial';
        }
        
        return 'Due';
    }

    public function getPaymentStatusColorAttribute(): string
    {
        return match($this->payment_status_text) {
            'Paid' => 'success',
            'Partial' => 'warning',
            'Due' => 'danger',
            default => 'secondary'
        };
    }
}
