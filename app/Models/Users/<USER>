<?php

namespace App\Models\Users;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserActivity extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'user_id',
        'action',
        'description',
        'ip_address',
        'user_agent',
        'properties',
        'model_type',
        'model_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'properties' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Activity types constants
     */
    const ACTION_LOGIN = 'login';
    const ACTION_LOGOUT = 'logout';
    const ACTION_PROFILE_UPDATED = 'profile_updated';
    const ACTION_PASSWORD_CHANGED = 'password_changed';
    const ACTION_2FA_ENABLED = '2fa_enabled';
    const ACTION_2FA_DISABLED = '2fa_disabled';
    const ACTION_AVATAR_UPDATED = 'avatar_updated';
    const ACTION_SETTINGS_UPDATED = 'settings_updated';
    const ACTION_SALE_CREATED = 'sale_created';
    const ACTION_MEDICINE_CREATED = 'medicine_created';
    const ACTION_CUSTOMER_CREATED = 'customer_created';
    const ACTION_INVENTORY_UPDATED = 'inventory_updated';

    /**
     * Get the user that performed the activity.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the model that the activity was performed on.
     */
    public function model(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope to get activities for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get activities of a specific type.
     */
    public function scopeOfType($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope to get recent activities.
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Get formatted activity description with context.
     */
    public function getFormattedDescriptionAttribute(): string
    {
        $description = $this->description;

        if ($this->properties) {
            foreach ($this->properties as $key => $value) {
                // Convert value to string if it's not already
                $stringValue = is_array($value) ? json_encode($value) : (string) $value;
                $description = str_replace("{{$key}}", $stringValue, $description);
            }
        }

        return $description;
    }

    /**
     * Get activity icon based on action type.
     */
    public function getIconAttribute(): string
    {
        return match($this->action) {
            self::ACTION_LOGIN => 'login',
            self::ACTION_LOGOUT => 'logout',
            self::ACTION_PROFILE_UPDATED => 'user-edit',
            self::ACTION_PASSWORD_CHANGED => 'key',
            self::ACTION_2FA_ENABLED, self::ACTION_2FA_DISABLED => 'shield',
            self::ACTION_AVATAR_UPDATED => 'camera',
            self::ACTION_SETTINGS_UPDATED => 'cog',
            self::ACTION_SALE_CREATED => 'shopping-cart',
            self::ACTION_MEDICINE_CREATED => 'plus-circle',
            self::ACTION_CUSTOMER_CREATED => 'user-plus',
            self::ACTION_INVENTORY_UPDATED => 'package',
            default => 'activity',
        };
    }

    /**
     * Get activity color based on action type.
     */
    public function getColorAttribute(): string
    {
        return match($this->action) {
            self::ACTION_LOGIN => 'green',
            self::ACTION_LOGOUT => 'gray',
            self::ACTION_PROFILE_UPDATED => 'blue',
            self::ACTION_PASSWORD_CHANGED => 'yellow',
            self::ACTION_2FA_ENABLED => 'green',
            self::ACTION_2FA_DISABLED => 'red',
            self::ACTION_AVATAR_UPDATED => 'purple',
            self::ACTION_SETTINGS_UPDATED => 'indigo',
            self::ACTION_SALE_CREATED => 'green',
            self::ACTION_MEDICINE_CREATED => 'blue',
            self::ACTION_CUSTOMER_CREATED => 'indigo',
            self::ACTION_INVENTORY_UPDATED => 'orange',
            default => 'gray',
        };
    }
}
