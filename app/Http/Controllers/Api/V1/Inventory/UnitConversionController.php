<?php

namespace App\Http\Controllers\Api\V1\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Inventory\UnitConversion;
use App\Services\Inventory\UnitConversionService;
use App\Http\Resources\Api\V1\UnitConversionResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Validation\ValidationException;

class UnitConversionController extends Controller
{
    protected $unitConversionService;

    public function __construct(UnitConversionService $unitConversionService)
    {
        $this->unitConversionService = $unitConversionService;
    }

    /**
     * Get unit conversion list with filter options
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $baseUnits = $this->unitConversionService->getBaseUnits();
        $allUnits = $this->unitConversionService->getAllUnits();
        $conversionRules = $this->unitConversionService->getPaginatedConversionRules();

        return response()->json([
            'base_units' => $baseUnits,
            'all_units' => $allUnits,
            'conversion_rules' => UnitConversionResource::collection($conversionRules)
        ]);
    }

    /**
     * Create a new unit conversion rule
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'from_unit_id' => 'required|exists:unit_types,id',
            'to_unit_id' => 'required|exists:unit_types,id|different:from_unit_id',
            'conversion_factor' => 'required|numeric|gt:0',
        ]);

        try {
            $conversions = $this->unitConversionService->createConversionRule(
                $validated,
                auth()->id()
            );

            return response()->json([
                'message' => 'Unit conversion created successfully',
                'data' => [
                    'direct' => new UnitConversionResource($conversions['direct']),
                    'inverse' => new UnitConversionResource($conversions['inverse'])
                ]
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error creating unit conversion',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update an existing unit conversion rule
     *
     * @param Request $request
     * @param UnitConversion $unitConversion
     * @return JsonResponse
     */
    public function update(Request $request, UnitConversion $unitConversion): JsonResponse
    {
        $validated = $request->validate([
            'conversion_factor' => 'required|numeric|gt:0',
            'status' => 'required|in:active,inactive',
        ]);

        try {
            $conversions = $this->unitConversionService->updateConversionRule(
                $unitConversion,
                $validated,
                auth()->id()
            );

            return response()->json([
                'message' => 'Unit conversion updated successfully',
                'data' => [
                    'direct' => new UnitConversionResource($conversions['direct']),
                    'inverse' => $conversions['inverse'] ? new UnitConversionResource($conversions['inverse']) : null
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error updating unit conversion',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a unit conversion rule
     *
     * @param UnitConversion $unitConversion
     * @return JsonResponse
     */
    public function destroy(UnitConversion $unitConversion): JsonResponse
    {
        try {
            $this->unitConversionService->deleteConversionRule($unitConversion);

            return response()->json([
                'message' => 'Unit conversion deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error deleting unit conversion',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Convert value between units
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function convert(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'value' => 'required|numeric',
            'from_unit_id' => 'required|exists:unit_types,id',
            'to_unit_id' => 'required|exists:unit_types,id',
        ]);

        try {
            $result = $this->unitConversionService->convertUnits($validated);

            return response()->json($result);
        } catch (ValidationException $e) {
            return response()->json([
                'message' => $e->getMessage(),
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error converting units',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
