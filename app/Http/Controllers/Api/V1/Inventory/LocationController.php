<?php

namespace App\Http\Controllers\Api\V1\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Inventory\Location;
use App\Services\Inventory\LocationService;
use App\Http\Resources\Api\V1\LocationResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Validation\Rule;

class LocationController extends Controller
{
    protected $locationService;

    public function __construct(LocationService $locationService)
    {
        $this->locationService = $locationService;
    }

    /**
     * Get paginated list of locations
     *
     * @param Request $request
     * @return ResourceCollection
     */
    public function index(Request $request): ResourceCollection
    {
        $filters = $request->only(['section', 'zone', 'temperature', 'search']);
        $locations = $this->locationService->getPaginatedLocations(
            $filters,
            $request->input('per_page', 15)
        );

        return LocationResource::collection($locations);
    }

    /**
     * Create a new location
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'type' => ['required', Rule::in(['warehouse', 'store', 'shelf'])],
            'parent_id' => ['nullable', 'exists:locations,id'],
            'section' => ['nullable', 'string', 'max:255'],
            'zone' => ['nullable', 'string', 'max:255'],
            'aisle_number' => ['nullable', 'string', 'max:50'],
            'rack_number' => ['nullable', 'string', 'max:50'],
            'bin_location' => ['nullable', 'string', 'max:50'],
            'temperature_requirement' => ['nullable', 'string', 'max:50'],
            'address' => ['nullable', 'string', 'max:255'],
            'status' => ['required', Rule::in(['active', 'inactive'])],
        ]);

        try {
            $location = $this->locationService->createLocation($validated);
            
            return response()->json([
                'message' => 'Location created successfully',
                'data' => new LocationResource($location)
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error creating location',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific location
     *
     * @param Location $location
     * @return LocationResource
     */
    public function show(Location $location): LocationResource
    {
        return new LocationResource($location->load([
            'parent',
            'creator',
            'updater',
            'batchHistories.medicine',
            'movements' => function ($query) {
                $query->with(['medicine', 'creator'])
                    ->latest()
                    ->take(50);
            }
        ]));
    }

    /**
     * Update an existing location
     *
     * @param Request $request
     * @param Location $location
     * @return JsonResponse
     */
    public function update(Request $request, Location $location): JsonResponse
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'section' => ['nullable', 'string', 'max:255'],
            'zone' => ['nullable', 'string', 'max:255'],
            'aisle_number' => ['nullable', 'string', 'max:50'],
            'rack_number' => ['nullable', 'string', 'max:50'],
            'bin_location' => ['nullable', 'string', 'max:50'],
            'temperature_requirement' => ['nullable', 'string', 'max:50'],
            'address' => ['nullable', 'string', 'max:255'],
            'status' => ['required', Rule::in(['active', 'inactive'])],
        ]);

        try {
            $location = $this->locationService->updateLocation($location, $validated);
            
            return response()->json([
                'message' => 'Location updated successfully',
                'data' => new LocationResource($location)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error updating location',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a location
     *
     * @param Location $location
     * @return JsonResponse
     */
    public function destroy(Location $location): JsonResponse
    {
        try {
            $this->locationService->deleteLocation($location);
            
            return response()->json([
                'message' => 'Location deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error deleting location',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * Find suitable locations based on criteria
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function findSuitable(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'temperature' => ['nullable', 'string'],
            'section' => ['nullable', 'string'],
            'controlled' => ['nullable', 'boolean'],
        ]);

        $locations = $this->locationService->findSuitableLocations($validated);
        
        return response()->json([
            'data' => LocationResource::collection($locations)
        ]);
    }
}
