<?php

namespace App\Http\Controllers\Api\V1\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Inventory\BatchHistory;
use App\Services\Inventory\BatchHistoryService;
use App\Http\Resources\Api\V1\BatchHistoryResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Support\Facades\Response;

class BatchHistoryController extends Controller
{
    protected $batchHistoryService;

    public function __construct(BatchHistoryService $batchHistoryService)
    {
        $this->batchHistoryService = $batchHistoryService;
    }

    /**
     * Get filtered batch histories with filter options
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $filters = $request->only(['medicine', 'location', 'action_type', 'date_range']);
        $perPage = $request->input('per_page', 15);

        $batchHistories = $this->batchHistoryService->getFilteredBatchHistories($filters, $perPage);
        $medicines = $this->batchHistoryService->getAllMedicines();
        $locations = $this->batchHistoryService->getAllLocations();

        return response()->json([
            'medicines' => $medicines,
            'locations' => $locations,
            'batch_histories' => BatchHistoryResource::collection($batchHistories)
        ]);
    }

    /**
     * Get specific batch history details
     *
     * @param BatchHistory $history
     * @return BatchHistoryResource
     */
    public function show(BatchHistory $history): BatchHistoryResource
    {
        $history = $this->batchHistoryService->getBatchHistoryDetails($history);
        return new BatchHistoryResource($history);
    }

    /**
     * Export batch histories to CSV
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function export(Request $request)
    {
        $filters = $request->only(['medicine', 'location', 'action_type', 'date_range']);
        $exportData = $this->batchHistoryService->exportBatchHistories($filters);

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $exportData['filename'] . '"'
        ];

        $callback = function() use ($exportData) {
            $file = fopen('php://output', 'w');
            foreach ($exportData['data'] as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    /**
     * Get filter options
     *
     * @return JsonResponse
     */
    public function filterOptions(): JsonResponse
    {
        return response()->json([
            'medicines' => $this->batchHistoryService->getAllMedicines(),
            'locations' => $this->batchHistoryService->getAllLocations(),
            'action_types' => [
                'purchase',
                'sale',
                'adjustment',
                'transfer',
                'return',
                'expired'
            ]
        ]);
    }
}
