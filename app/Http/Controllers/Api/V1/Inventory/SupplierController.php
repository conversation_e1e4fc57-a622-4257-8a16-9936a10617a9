<?php

namespace App\Http\Controllers\Api\V1\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Inventory\Supplier;
use App\Services\Inventory\SupplierService;
use App\Http\Resources\Api\V1\SupplierResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\ResourceCollection;

class SupplierController extends Controller
{
    protected $supplierService;

    public function __construct(SupplierService $supplierService)
    {
        $this->supplierService = $supplierService;
    }

    /**
     * Get paginated list of suppliers
     *
     * @param Request $request
     * @return ResourceCollection
     */
    public function index(Request $request): ResourceCollection
    {
        $suppliers = $this->supplierService->getPaginatedSuppliers(
            $request->input('per_page', 10)
        );

        return SupplierResource::collection($suppliers);
    }

    /**
     * Create a new supplier
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'contact_person' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'tax_number' => 'nullable|string|max:50',
            'status' => 'nullable|string|in:active,inactive',
        ]);

        try {
            $supplier = $this->supplierService->createSupplier($validatedData);
            
            return response()->json([
                'message' => 'Supplier created successfully',
                'data' => new SupplierResource($supplier)
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error creating supplier',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific supplier
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $supplier = $this->supplierService->findSupplier($id);
            return response()->json([
                'data' => new SupplierResource($supplier->loadCount('purchases'))
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Supplier not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update an existing supplier
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'contact_person' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'tax_number' => 'nullable|string|max:50',
            'status' => 'nullable|string|in:active,inactive',
        ]);

        try {
            $supplier = $this->supplierService->findSupplier($id);
            $updatedSupplier = $this->supplierService->updateSupplier($supplier, $validatedData);
            
            return response()->json([
                'message' => 'Supplier updated successfully',
                'data' => new SupplierResource($updatedSupplier)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error updating supplier',
                'error' => $e->getMessage()
            ], $e instanceof \Illuminate\Database\Eloquent\ModelNotFoundException ? 404 : 500);
        }
    }

    /**
     * Delete a supplier
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $supplier = $this->supplierService->findSupplier($id);
            $this->supplierService->deleteSupplier($supplier);
            
            return response()->json([
                'message' => 'Supplier deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error deleting supplier',
                'error' => $e->getMessage()
            ], $e instanceof \Illuminate\Database\Eloquent\ModelNotFoundException ? 404 : 422);
        }
    }
}
