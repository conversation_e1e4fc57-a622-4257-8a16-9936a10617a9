<?php

namespace App\Http\Controllers\Api\V1\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Inventory\Manufacturer;
use App\Services\Inventory\ManufacturerService;
use App\Http\Resources\Api\V1\ManufacturerResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\ResourceCollection;

class ManufacturerController extends Controller
{
    protected $manufacturerService;

    public function __construct(ManufacturerService $manufacturerService)
    {
        $this->manufacturerService = $manufacturerService;
    }

    /**
     * Get paginated list of manufacturers
     *
     * @param Request $request
     * @return ResourceCollection
     */
    public function index(Request $request): ResourceCollection
    {
        $manufacturers = $this->manufacturerService->getPaginatedManufacturers(
            $request->input('per_page', 15)
        );

        return ManufacturerResource::collection($manufacturers);
    }

    /**
     * Create a new manufacturer
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:manufacturers,name',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'website' => 'nullable|url|max:255',
            'is_active' => 'boolean',
        ]);

        try {
            $manufacturer = $this->manufacturerService->createManufacturer($validatedData);
            
            return response()->json([
                'message' => 'Manufacturer created successfully',
                'data' => new ManufacturerResource($manufacturer)
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error creating manufacturer',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific manufacturer
     *
     * @param Manufacturer $manufacturer
     * @return ManufacturerResource
     */
    public function show(Manufacturer $manufacturer): ManufacturerResource
    {
        return new ManufacturerResource($manufacturer->loadCount('medicines'));
    }

    /**
     * Update an existing manufacturer
     *
     * @param Request $request
     * @param Manufacturer $manufacturer
     * @return JsonResponse
     */
    public function update(Request $request, Manufacturer $manufacturer): JsonResponse
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:manufacturers,name,' . $manufacturer->id,
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'website' => 'nullable|url|max:255',
            'is_active' => 'boolean',
        ]);

        try {
            $manufacturer = $this->manufacturerService->updateManufacturer($manufacturer, $validatedData);
            
            return response()->json([
                'message' => 'Manufacturer updated successfully',
                'data' => new ManufacturerResource($manufacturer)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error updating manufacturer',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a manufacturer
     *
     * @param Manufacturer $manufacturer
     * @return JsonResponse
     */
    public function destroy(Manufacturer $manufacturer): JsonResponse
    {
        try {
            $this->manufacturerService->deleteManufacturer($manufacturer);
            
            return response()->json([
                'message' => 'Manufacturer deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error deleting manufacturer',
                'error' => $e->getMessage()
            ], 422);
        }
    }
}
