<?php

namespace App\Http\Controllers\Api\V1\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Inventory\Medicine;
use App\Models\Inventory\Location;
use App\Models\Inventory\Inventory;
use App\Services\Inventory\StockService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Resources\Api\V1\LocationResource;
use App\Http\Resources\Api\V1\MedicineResource;
use App\Http\Resources\Api\V1\StockMovementResource;
use App\Http\Resources\Api\V1\InventoryResource;
use Illuminate\Support\Facades\Log;

class StockController extends Controller
{
    protected $stockService;

    public function __construct(StockService $stockService)
    {
        $this->stockService = $stockService;
    }

    /**
     * Get stock summary including total medicines, low stock items, and expiring items.
     *
     * @return JsonResponse
     */
    public function summary(): JsonResponse
    {
        $summary = $this->stockService->getStockSummary();
        
        return response()->json([
            'data' => [
                'total_medicines' => $summary['totalmedicines'],
                'low_stock_medicines' => $summary['lowStockmedicines'],
                'expiring_medicines' => $summary['expiringmedicines'],
                'recent_movements' => StockMovementResource::collection($summary['recentMovements'])
            ]
        ]);
    }

    /**
     * Get medicines available in a specific location.
     *
     * @param Location $location
     * @return JsonResponse
     */
    public function medicinesByLocation(Location $location): JsonResponse
    {
        try {
            $medicines = $this->stockService->getMedicinesByLocation($location);
            
            return response()->json([
                'data' => $medicines
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting medicines by location', [
                'location_id' => $location->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'message' => 'An error occurred while getting medicines',
                'error' => config('app.debug') ? $e->getMessage() : 'Please try again later'
            ], 500);
        }
    }

    /**
     * Get batches of a medicine in a specific location.
     *
     * @param Location $location
     * @param Medicine $medicine
     * @return JsonResponse
     */
    public function batchesByLocationAndMedicine(Location $location, Medicine $medicine): JsonResponse
    {
        try {
            $batches = $this->stockService->getBatchesByLocationAndMedicine($location, $medicine);
            
            return response()->json([
                'data' => InventoryResource::collection($batches)
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting batches by location and medicine', [
                'location_id' => $location->id,
                'medicine_id' => $medicine->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'message' => 'An error occurred while getting batches',
                'error' => config('app.debug') ? $e->getMessage() : 'Please try again later'
            ], 500);
        }
    }

    /**
     * Transfer stock between locations.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function transfer(Request $request): JsonResponse
    {
        try {
            $validatedData = $request->validate([
                'source_location' => 'required|exists:locations,id',
                'destination_location' => 'required|exists:locations,id|different:source_location',
                'medicine' => 'required|exists:medicines,id',
                'quantity' => 'required|integer|min:1',
                'batch_number' => [
                    'required',
                    'string',
                    function ($attribute, $value, $fail) use ($request) {
                        // Check if batch exists in source location with sufficient quantity
                        $inventory = Inventory::where('location_id', $request->source_location)
                            ->where('medicine_id', $request->medicine)
                            ->where('batch_number', $value)
                            ->where('quantity', '>=', $request->quantity)
                            ->where(function($query) {
                                $query->whereNull('expiry_date')
                                    ->orWhere('expiry_date', '>', now());
                            })
                            ->first();

                        if (!$inventory) {
                            $fail('The selected batch is not available in sufficient quantity or has expired.');
                        }
                    },
                ],
                'transfer_date' => 'required|date|before_or_equal:' . now()->format('Y-m-d'),
                'notes' => 'nullable|string|max:500'
            ]);

            $this->stockService->transferStock($validatedData);
            
            return response()->json([
                'message' => 'Stock transfer has been recorded successfully'
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Stock transfer failed', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);
            
            return response()->json([
                'message' => 'An error occurred while processing the transfer',
                'error' => config('app.debug') ? $e->getMessage() : 'Please try again later'
            ], 500);
        }
    }

    /**
     * Get list of locations.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function locations(Request $request): JsonResponse
    {
        $locations = Location::where(function($query) {
            $query->where('status', 'active')
                  ->orWhereNull('status');
        })
        ->orderBy('name')
        ->paginate($request->input('per_page', 15));
        
        return response()->json([
            'data' => LocationResource::collection($locations),
            'meta' => [
                'current_page' => $locations->currentPage(),
                'last_page' => $locations->lastPage(),
                'per_page' => $locations->perPage(),
                'total' => $locations->total()
            ]
        ]);
    }

    /**
     * Create a new location.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function storeLocation(Request $request): JsonResponse
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:locations,name',
            'type' => 'required|in:warehouse,store,shelf',
            'address' => 'nullable|string'
        ]);

        try {
            $location = $this->stockService->createLocation($validatedData);
            
            return response()->json([
                'message' => 'Location has been created successfully',
                'data' => new LocationResource($location)
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'An error occurred while creating the location',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update an existing location.
     *
     * @param Request $request
     * @param Location $location
     * @return JsonResponse
     */
    public function updateLocation(Request $request, Location $location): JsonResponse
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:locations,name,' . $location->id,
            'type' => 'required|in:warehouse,store,shelf',
            'address' => 'nullable|string',
            'status' => 'required|in:active,inactive'
        ]);

        try {
            $this->stockService->updateLocation($location, $validatedData);
            
            return response()->json([
                'message' => 'Location has been updated successfully',
                'data' => new LocationResource($location->fresh())
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'An error occurred while updating the location',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a location.
     *
     * @param Location $location
     * @return JsonResponse
     */
    public function destroyLocation(Location $location): JsonResponse
    {
        try {
            $this->stockService->deleteLocation($location);
            
            return response()->json([
                'message' => 'Location has been deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'An error occurred while deleting the location',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get list of low stock items.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function lowStock(Request $request): JsonResponse
    {
        $items = $this->stockService->getLowStockItems($request->input('per_page', 15));
        
        return response()->json([
            'data' => $items->items(),
            'meta' => [
                'current_page' => $items->currentPage(),
                'last_page' => $items->lastPage(),
                'per_page' => $items->perPage(),
                'total' => $items->total()
            ]
        ]);
    }

    /**
     * Get list of expiring items.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function expiring(Request $request): JsonResponse
    {
        $items = $this->stockService->getExpiringItems($request->input('per_page', 15));
        
        return response()->json([
            'data' => $items->items(),
            'meta' => [
                'current_page' => $items->currentPage(),
                'last_page' => $items->lastPage(),
                'per_page' => $items->perPage(),
                'total' => $items->total()
            ]
        ]);
    }

    /**
     * Get medicine history including stock summary and movements.
     *
     * @param Medicine $medicine
     * @return JsonResponse
     */
    public function medicineHistory(Medicine $medicine): JsonResponse
    {
        $history = $this->stockService->getMedicineHistory($medicine);
        
        return response()->json([
            'data' => [
                'medicine' => new MedicineResource($medicine),
                'total_quantity' => $history['totalQuantity'],
                'active_batches' => $history['activeBatches'],
                'unique_locations' => $history['uniqueLocations'],
                'average_price' => $history['averagePrice'],
                'batches' => InventoryResource::collection($history['batches']),
                'movements' => [
                    'data' => StockMovementResource::collection($history['movements']),
                    'meta' => [
                        'current_page' => $history['movements']->currentPage(),
                        'last_page' => $history['movements']->lastPage(),
                        'per_page' => $history['movements']->perPage(),
                        'total' => $history['movements']->total()
                    ]
                ]
            ]
        ]);
    }
}
