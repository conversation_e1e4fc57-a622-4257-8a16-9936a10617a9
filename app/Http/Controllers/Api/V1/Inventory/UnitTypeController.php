<?php

namespace App\Http\Controllers\Api\V1\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Inventory\UnitType;
use App\Services\Inventory\UnitTypeService;
use App\Http\Resources\Api\V1\UnitTypeResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\ResourceCollection;

class UnitTypeController extends Controller
{
    protected $unitTypeService;

    public function __construct(UnitTypeService $unitTypeService)
    {
        $this->unitTypeService = $unitTypeService;
    }

    /**
     * Get paginated list of unit types
     *
     * @param Request $request
     * @return ResourceCollection
     */
    public function index(Request $request): ResourceCollection
    {
        $unitTypes = $this->unitTypeService->getPaginatedUnitTypes(
            $request->input('per_page', 10)
        );

        return UnitTypeResource::collection($unitTypes);
    }

    /**
     * Create a new unit type
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:unit_types,name',
            'abbreviation' => 'required|string|max:10|unique:unit_types,abbreviation',
            'description' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ]);

        try {
            $unitType = $this->unitTypeService->createUnitType($validatedData);
            
            return response()->json([
                'message' => 'Unit type created successfully',
                'data' => new UnitTypeResource($unitType)
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error creating unit type',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific unit type
     *
     * @param UnitType $unitType
     * @return UnitTypeResource
     */
    public function show(UnitType $unitType): UnitTypeResource
    {
        return new UnitTypeResource(
            $unitType->loadCount('medicines')
        );
    }

    /**
     * Update an existing unit type
     *
     * @param Request $request
     * @param UnitType $unitType
     * @return JsonResponse
     */
    public function update(Request $request, UnitType $unitType): JsonResponse
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:unit_types,name,' . $unitType->id,
            'abbreviation' => 'required|string|max:10|unique:unit_types,abbreviation,' . $unitType->id,
            'description' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ]);

        try {
            $unitType = $this->unitTypeService->updateUnitType($unitType, $validatedData);
            
            return response()->json([
                'message' => 'Unit type updated successfully',
                'data' => new UnitTypeResource($unitType)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error updating unit type',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a unit type
     *
     * @param UnitType $unitType
     * @return JsonResponse
     */
    public function destroy(UnitType $unitType): JsonResponse
    {
        try {
            $this->unitTypeService->deleteUnitType($unitType);
            
            return response()->json([
                'message' => 'Unit type deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error deleting unit type',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * Get all active unit types
     *
     * @return JsonResponse
     */
    public function active(): JsonResponse
    {
        $unitTypes = $this->unitTypeService->getAllActiveUnitTypes();
        
        return response()->json([
            'data' => UnitTypeResource::collection($unitTypes)
        ]);
    }
}
