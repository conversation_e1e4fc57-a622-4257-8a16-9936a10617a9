<?php

namespace App\Http\Controllers\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Inventory\Medicine;
use App\Models\Inventory\Category;
use App\Models\Inventory\Location;
use App\Models\Inventory\Warehouse;
use App\Models\Inventory\Manufacturer;
use App\Models\Inventory\Supplier;
use App\Services\Inventory\MedicineService;
use App\Services\Inventory\MedicineImportExportService;
use App\Http\Requests\Inventory\StoreMedicineRequest;
use App\Http\Requests\Inventory\UpdateMedicineRequest;
use App\Http\Requests\Inventory\ImportMedicineRequest;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use App\Models\Inventory\StockMovement;
use App\Models\Inventory\Inventory;
use App\Models\Inventory\UnitType;
use App\Services\Search\AdvancedSearchService;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\Auth;

class MedicineController extends Controller
{
    protected $medicineService;
    protected $searchService;
    protected $importExportService;

    public function __construct(
        MedicineService $medicineService,
        AdvancedSearchService $searchService,
        MedicineImportExportService $importExportService
    ) {
        $this->medicineService = $medicineService;
        $this->searchService = $searchService;
        $this->importExportService = $importExportService;
    }

    public function index(Request $request)
    {
        $sort = $request->input('sort', 'asc');
        $search = $request->input('search');
        $filters = $request->only(['category_id', 'manufacturer_id', 'status', 'controlled_substance', 'prescription_required']);
        
        // Get categories and manufacturers for filters
        $categories = Category::where('is_active', true)->orderBy('name')->get();
        $manufacturers = Manufacturer::where('is_active', true)->orderBy('name')->get();
        
        $query = Medicine::with(['category', 'manufacturer', 'inventories' => function($query) {
            $query->select('id', 'medicine_id', 'quantity', 'unit_price', 'expiry_date');
        }]);

        // Apply search if provided
        if ($search) {
            $query->searchWithManufacturer($search);
        }

        // Apply filters
        foreach ($filters as $field => $value) {
            if ($value !== null && $value !== '') {
                $query->where($field, $value);
            }
        }

        // Get all results first if searching
        if ($search) {
            $allMedicines = $query->get();
            $groupedMedicines = collect();
            
            // Group by generic name while maintaining order
            $allMedicines->each(function ($medicine) use ($groupedMedicines) {
                if (!$groupedMedicines->contains('generic_name', $medicine->generic_name)) {
                    $groupedMedicines->push($medicine);
                    // Add similar medicines right after the main one
                    $similarMedicines = $medicine->getSimilarMedicines();
                    $groupedMedicines->push(...$similarMedicines);
                }
            });
            
            // Manually paginate the grouped results
            $page = $request->input('page', 1);
            $perPage = 15;
            $items = $groupedMedicines->forPage($page, $perPage);
            $medicines = new \Illuminate\Pagination\LengthAwarePaginator(
                $items,
                $groupedMedicines->count(),
                $perPage,
                $page,
                ['path' => $request->url()]
            );
        } else {
            // Regular pagination for non-search results
            $medicines = $query->paginate(15);
        }

        // Append query parameters to pagination links
        $medicines->appends(['sort' => $sort, 'search' => $search] + $filters);
            
        return view('inventory.medicines.index', compact(
            'medicines', 
            'sort', 
            'search', 
            'filters', 
            'categories', 
            'manufacturers'
        ));
    }

    public function create()
    {
        $categories = Category::where('is_active', 1)->get();
        $manufacturers = Manufacturer::where('is_active', 1)->get();
        $suppliers = Supplier::where('is_active', 1)->get();
        $locations = Location::where('is_active', 1)->get();
        $warehouses = Warehouse::where('is_active', 1)->get();
        $unitTypes = UnitType::where('is_active', 1)->get();

        return view('inventory.medicines.create', compact(
            'categories',
            'manufacturers',
            'suppliers',
            'locations',
            'warehouses',
            'unitTypes'
        ));
    }

    public function store(Request $request)
    {
        try {
            // Check if we're only saving medicine info (without inventory)
            if ($request->has('save_medicine_info')) {
                return $this->storeMedicineOnly($request);
            }
            
            // Check if we're in the inventory update tab
            if ($request->input('activeTab') === 'inventory-update') {
                // Make sure we have the essential medicine info fields
                if (empty($request->input('name')) || empty($request->input('manufacturer_id'))) {
                    return redirect()
                        ->back()
                        ->withInput()
                        ->with('error', 'Please fill in the required medicine information in the first tab before adding inventory.');
                }
            }
            
            // Regular validation using StoreMedicineRequest
            $validator = app(StoreMedicineRequest::class);
            $validated = $request->validate($validator->rules(), $validator->messages());
            
            // Create medicine with inventory
            $medicine = $this->medicineService->createMedicine($validated);
            
            if ($request->has('save_and_add')) {
                return redirect()
                    ->route('inventory.medicines.create')
                    ->with('success', 'Medicine added successfully with inventory. Add another one!');
            }
            
            return redirect()
                ->route('inventory.medicines.index')
                ->with('success', 'Medicine added successfully with inventory');
                
        } catch (\Exception $e) {
            // Log the full technical error for debugging
            Log::error('Error creating medicine: ' . $e->getMessage());
            
            // Prepare a user-friendly error message
            $errorMessage = 'Error creating medicine. Please check your input and try again.';
            
            // Check for known error patterns
            if (str_contains($e->getMessage(), 'batch number already exists')) {
                $errorMessage = 'This batch number already exists. Please use a different batch number.';
            } else if ($e instanceof QueryException && 
                      (str_contains($e->getMessage(), 'inventories_medicine_batch_unique') || 
                       str_contains($e->getMessage(), 'inventories_medicine_batch_location_unique'))) {
                $errorMessage = 'This batch number already exists. Please use a different batch number.';
            }
            
            return redirect()
                ->back()
                ->withInput()
                ->with('error', $errorMessage);
        }
    }
    
    /**
     * Store medicine information only without inventory data
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    protected function storeMedicineOnly(Request $request)
    {
        try {
            // Validate medicine info only
            $validated = $request->validate([
                'name' => ['required', 'string', 'max:255'],
                'generic_name' => ['nullable', 'string', 'max:255'],
                'dosage' => ['nullable', 'string', 'max:255'],
                'manufacturer_id' => ['required', 'exists:manufacturers,id'],
                'supplier_id' => ['nullable', 'exists:suppliers,id'],
                'category_id' => ['nullable', 'exists:categories,id'],
                'unit_type_id' => ['nullable', 'exists:unit_types,id'],
                'controlled_substance' => ['nullable'],
                'prescription_required' => ['nullable'],
                'enabled_units' => ['nullable', 'array'],
                'enabled_units.*' => ['string', 'in:carton,box,strip,unit'],
                'enabled_retail_units' => ['nullable', 'array'],
                'enabled_retail_units.*' => ['string', 'in:carton,box,strip,unit'],
                'supplier_price_carton' => ['nullable', 'numeric', 'min:0'],
                'supplier_price_box' => ['nullable', 'numeric', 'min:0'],
                'supplier_price_strip' => ['nullable', 'numeric', 'min:0'],
                'supplier_price_unit' => ['nullable', 'numeric', 'min:0'],
                'retail_price_carton' => ['nullable', 'numeric', 'min:0'],
                'retail_price_box' => ['nullable', 'numeric', 'min:0'],
                'retail_price_strip' => ['nullable', 'numeric', 'min:0'],
                'retail_price_unit' => ['nullable', 'numeric', 'min:0'],
            ]);
            
            // Process checkbox values - ensure they're properly converted to boolean
            if (is_array($request->input('controlled_substance'))) {
                // If it's an array (which happens in some cases), take the first value
                $validated['controlled_substance'] = !empty($request->input('controlled_substance')[0]);
            } else {
                $validated['controlled_substance'] = $request->boolean('controlled_substance');
            }
            
            if (is_array($request->input('prescription_required'))) {
                // If it's an array (which happens in some cases), take the first value
                $validated['prescription_required'] = !empty($request->input('prescription_required')[0]);
            } else {
                $validated['prescription_required'] = $request->boolean('prescription_required');
            }
            
            // Log the boolean values for debugging
            Log::debug('Boolean values for medicine:', [
                'controlled_substance_input' => $request->input('controlled_substance'),
                'controlled_substance_processed' => $validated['controlled_substance'],
                'prescription_required_input' => $request->input('prescription_required'),
                'prescription_required_processed' => $validated['prescription_required']
            ]);
            
            // Ensure arrays are always arrays (even if empty)
            $validated['enabled_units'] = array_unique($request->input('enabled_units', []));
            $validated['enabled_retail_units'] = array_unique($request->input('enabled_retail_units', []));
            
            // Log the arrays for debugging
            Log::debug('Array values for medicine:', [
                'enabled_units' => $validated['enabled_units'],
                'enabled_retail_units' => $validated['enabled_retail_units']
            ]);
            
            // Process pricing data
            $prices = [];
            foreach (['carton', 'box', 'strip', 'unit'] as $unit) {
                if (in_array($unit, $validated['enabled_units'])) {
                    $prices['supplier_price_' . $unit] = $request->input('supplier_price_' . $unit, 0);
                }
                if (in_array($unit, $validated['enabled_retail_units'])) {
                    $prices['retail_price_' . $unit] = $request->input('retail_price_' . $unit, 0);
                }
            }
            
            // Set unit_price from retail_price_unit if available
            if (isset($prices['retail_price_unit'])) {
                $validated['unit_price'] = $prices['retail_price_unit'];
            }
            
            // Extract supplier ID before creating medicine
            $supplierId = $validated['supplier_id'] ?? null;
            unset($validated['supplier_id']);
            
            // Create medicine with basic info and pricing
            DB::beginTransaction();
            
            try {
                // Create medicine with pricing data and array fields
                $medicine = Medicine::create(array_merge($validated, $prices));
                
                // Attach supplier if provided
                if ($supplierId) {
                    $medicine->suppliers()->attach($supplierId, [
                        'price' => $prices['supplier_price_unit'] ?? 0,
                        'is_default' => true,
                        'created_by' => Auth::id()
                    ]);
                }
                
                DB::commit();
                
                return redirect()
                    ->route('inventory.medicines.index')
                    ->with('success', 'Medicine information saved successfully without inventory data');
                    
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
            
        } catch (\Exception $e) {
            Log::error('Error creating medicine info only: ' . $e->getMessage());
            
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Error saving medicine information: ' . $e->getMessage());
        }
    }

    public function edit($id)
    {
        try {
            $medicine = Medicine::findOrFail($id);
            // Ensure supplier_id is set for the form (for single supplier selection)
            if (empty($medicine->supplier_id) && $medicine->suppliers && $medicine->suppliers->count() > 0) {
                $medicine->supplier_id = $medicine->suppliers->first()->id;
            }
            
            // Get location_id from the latest inventory record
            $latestInventory = $medicine->inventories()->latest('created_at')->first();
            $medicine->location_id = $latestInventory ? $latestInventory->location_id : null;
            
            // Fetch current active batch (inventory record with highest quantity and not expired)
            $activeBatch = $medicine->inventories()
                ->where('quantity', '>', 0)
                ->whereDate('expiry_date', '>', now())
                ->orderByDesc('quantity')
                ->orderBy('expiry_date')
                ->first();
            $active_expiry_date = $activeBatch->expiry_date ?? null;

            $categories = Category::where('is_active', 1)->get();
            $manufacturers = Manufacturer::where('is_active', 1)->get();
            $suppliers = Supplier::where('is_active', 1)->get();
            $locations = Location::where('is_active', 1)->get();
            $warehouses = Warehouse::where('is_active', 1)->get();
            $unitTypes = UnitType::where('is_active', 1)->get();

            return view('inventory.medicines.edit', compact(
                'medicine',
                'categories',
                'manufacturers',
                'suppliers',
                'locations',
                'warehouses',
                'unitTypes',
                'active_expiry_date'
            ));
        } catch (\Exception $e) {
            Log::error('Error loading medicine for edit: ' . $e->getMessage());
            return redirect()
                ->route('inventory.medicines.index')
                ->with('error', 'Medicine not found.');
        }
    }

    public function update(UpdateMedicineRequest $request, $id)
    {
        try {
            $medicine = Medicine::findOrFail($id);
            
            $validatedData = $request->validated();
            
            // Always use the medicine's retail_price_unit as the unit_price
            $validatedData['unit_price'] = $medicine->retail_price_unit;
            
            // Process the quantity with box multiplier if needed
            if ($request->has('box_multiplier')) {
                $boxMultiplier = max(1, (int) $request->input('box_multiplier'));
                
                // Check if display_quantity is provided and use that as the base
                if ($request->has('display_quantity') && is_numeric($request->input('display_quantity'))) {
                    $validatedData['quantity'] = (float) $request->input('display_quantity') * $boxMultiplier;
                } else {
                    // Check if medicine has valid box pattern values (for medicines created without inventory)
                    $stripsPerBox = $medicine->strips_per_box ?: $request->input('strips_per_box', 0);
                    $piecesPerStrip = $medicine->pieces_per_strip ?: $request->input('pieces_per_strip', 0);
                    $boxQuantity = $medicine->box_quantity ?: $request->input('box_quantity', 0);
                    
                    // Calculate base quantity, ensuring we have valid values
                    if ($stripsPerBox > 0 && $piecesPerStrip > 0 && $boxQuantity > 0) {
                        $baseQuantity = $stripsPerBox * $piecesPerStrip * $boxQuantity;
                        $validatedData['quantity'] = $baseQuantity * $boxMultiplier;
                    } else {
                        // If any box pattern value is missing or zero, use the direct quantity input
                        $validatedData['quantity'] = $request->input('quantity', 0);
                    }
                }
                
                // Update the medicine with the box pattern values and stock limits if they're missing
                $medicineUpdateData = [
                    'strips_per_box' => $stripsPerBox ?? $request->input('strips_per_box', 0),
                    'pieces_per_strip' => $piecesPerStrip ?? $request->input('pieces_per_strip', 0),
                    'box_quantity' => $boxQuantity ?? $request->input('box_quantity', 0)
                ];
                
                // Always update minimum_stock and maximum_stock if provided
                if ($request->has('minimum_stock')) {
                    $medicineUpdateData['minimum_stock'] = $request->input('minimum_stock');
                }
                
                if ($request->has('maximum_stock')) {
                    $medicineUpdateData['maximum_stock'] = $request->input('maximum_stock');
                }
                
                // Update the medicine model
                $medicine->update($medicineUpdateData);
            }
            
            // Log the quantity calculation for debugging
            Log::debug('Medicine inventory update quantity calculation', [
                'medicine_id' => $medicine->id,
                'strips_per_box' => $medicine->strips_per_box,
                'pieces_per_strip' => $medicine->pieces_per_strip,
                'box_quantity' => $medicine->box_quantity,
                'box_multiplier' => $request->input('box_multiplier', 1),
                'display_quantity' => $request->input('display_quantity'),
                'calculated_quantity' => $validatedData['quantity'],
                'direct_quantity_input' => $request->input('quantity'),
                'minimum_stock' => $request->input('minimum_stock'),
                'maximum_stock' => $request->input('maximum_stock')
            ]);
            
            // Extract inventory data
            $inventoryData = [
                'quantity' => $validatedData['quantity'],
                'expiry_date' => $validatedData['expiry_date'],
                'unit_price' => $validatedData['unit_price'],
                'batch_number' => $validatedData['batch_number']
            ];
            
            // Extract purchase-related data
            $purchaseData = [
                'purchase_number' => $request->input('purchase_number', 'PO-' . date('Ymd') . '-' . str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT)),
                'purchase_date' => $request->input('purchase_date', now()->format('Y-m-d')),
                'tax' => $request->input('tax', 0),
                'discount' => $request->input('discount', 0),
                'shipping_cost' => $request->input('shipping_cost', 0),
                'purchase_notes' => $request->input('purchase_notes')
            ];
            
            // Extract movement type
            $movementType = $validatedData['movement_type'] ?? 'addition';
            
            // Get location_id for inventory but don't update it on the medicine model
            $locationId = $validatedData['location_id'];
            
            // Only update the inventory - don't modify basic medicine data
            // This is because we're using the inventory update tab
            $medicine = $this->medicineService->updateInventory($medicine, array_merge($inventoryData, $purchaseData), $movementType, $locationId);
            
            return redirect()
                ->route('inventory.medicines.index')
                ->with('success', 'Medicine inventory updated successfully');
                
        } catch (\Exception $e) {
            // Log the full technical error for debugging
            Log::error('Error updating medicine inventory: ' . $e->getMessage());
            
            // Prepare a user-friendly error message
            $errorMessage = 'Error updating medicine inventory';
            
            // Check for known error patterns
            if (str_contains($e->getMessage(), 'batch number already exists')) {
                $errorMessage = 'This batch number already exists for this medicine at this location. Please use a different batch number.';
            } else if ($e instanceof QueryException && str_contains($e->getMessage(), 'inventories_medicine_batch_location_unique')) {
                $errorMessage = 'This batch number already exists for this medicine at this location. Please use a different batch number.';
            }
            
            return redirect()
                ->back()
                ->withInput()
                ->with('error', $errorMessage);
        }
    }

    /**
     * Update only medicine information without affecting inventory data
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function updateMedicineInfo(Request $request, $id)
    {
        try {
            $medicine = Medicine::findOrFail($id);
            
            // Transform checkbox values to boolean
            $request->merge([
                'controlled_substance' => $request->has('controlled_substance') ? true : false,
                'prescription_required' => $request->has('prescription_required') ? true : false,
            ]);
            
            // Validate only medicine information fields
            $validatedData = $request->validate([
                'name' => 'required|string|max:255',
                'generic_name' => 'required|string|max:255',
                'dosage' => 'nullable|string|max:255',
                'manufacturer_id' => 'required|exists:manufacturers,id',
                'supplier_id' => 'nullable|exists:suppliers,id',
                'category_id' => 'nullable|exists:categories,id',
                'unit_type_id' => 'required|exists:unit_types,id',
                'minimum_stock' => 'nullable|integer|min:0',
                'maximum_stock' => 'nullable|integer|min:0',
                'controlled_substance' => 'boolean',
                'prescription_required' => 'boolean',
                'strips_per_box' => 'nullable|integer|min:0',
                'pieces_per_strip' => 'nullable|integer|min:0',
                'box_quantity' => 'nullable|integer|min:0',
                'enabled_units' => 'nullable|array',
                'enabled_units.*' => 'string|in:carton,box,strip,unit',
                'enabled_retail_units' => 'nullable|array',
                'enabled_retail_units.*' => 'string|in:carton,box,strip,unit',
                'supplier_price_carton' => 'nullable|numeric|min:0',
                'supplier_price_box' => 'nullable|numeric|min:0',
                'supplier_price_strip' => 'nullable|numeric|min:0',
                'supplier_price_unit' => 'nullable|numeric|min:0',
                'retail_price_carton' => 'nullable|numeric|min:0',
                'retail_price_box' => 'nullable|numeric|min:0',
                'retail_price_strip' => 'nullable|numeric|min:0',
                'retail_price_unit' => 'nullable|numeric|min:0',
                'supplier_margin_percentage' => 'nullable|numeric|min:0|max:100',
            ]);
            
            // Ensure enabled_units and enabled_retail_units are arrays
            $validatedData['enabled_units'] = $request->input('enabled_units', []);
            $validatedData['enabled_retail_units'] = $request->input('enabled_retail_units', []);
            
            // Ensure price fields are properly formatted
            foreach (['supplier_price_carton', 'supplier_price_box', 'supplier_price_strip', 'supplier_price_unit',
                     'retail_price_carton', 'retail_price_box', 'retail_price_strip', 'retail_price_unit'] as $field) {
                $validatedData[$field] = $request->has($field) ? (float)$request->input($field) : 0;
            }
            
            // Update medicine information using the service
            $this->medicineService->updateMedicineInfo($medicine, $validatedData);
            
            return redirect()
                ->route('inventory.medicines.index')
                ->with('success', 'Medicine information updated successfully');
                
        } catch (\Exception $e) {
            // Log the full technical error for debugging
            Log::error('Error updating medicine info: ' . $e->getMessage());
            
            // Prepare a user-friendly error message
            $errorMessage = 'Error updating medicine information. Please check your input and try again.';
            
            return redirect()
                ->back()
                ->withInput()
                ->with('error', $errorMessage);
        }
    }

    public function destroy($id)
    {
        try {
            $medicine = Medicine::findOrFail($id);
            $medicine->delete();
            
            return redirect()
                ->route('inventory.medicines.index')
                ->with('success', 'Medicine archived successfully');
        } catch (\Exception $e) {
            Log::error('Error archiving medicine: ' . $e->getMessage());
            return redirect()
                ->back()
                ->with('error', 'Error archiving medicine: ' . $e->getMessage());
        }
    }

    public function archived(Request $request)
    {
        $search = $request->input('search');
        
        $medicines = Medicine::onlyTrashed()
            ->with(['category', 'manufacturer', 'inventories' => function($query) {
                $query->select('id', 'medicine_id', 'quantity', 'unit_price', 'expiry_date');
            }])
            ->when($search, function($query) use ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('generic_name', 'like', "%{$search}%");
                });
            })
            ->latest('deleted_at')
            ->paginate(15)
            ->appends(['search' => $search]);
            
        return view('inventory.medicines.archived', compact('medicines'));
    }

    public function restore($id)
    {
        try {
            $medicine = Medicine::onlyTrashed()->findOrFail($id);
            $medicine->restore();
            
            return redirect()
                ->route('inventory.medicines.archived')
                ->with('success', 'Medicine restored successfully');
        } catch (\Exception $e) {
            Log::error('Error restoring medicine: ' . $e->getMessage());
            return redirect()
                ->back()
                ->with('error', 'Error restoring medicine: ' . $e->getMessage());
        }
    }

    public function history($id)
    {
        try {
            $medicine = Medicine::with([
                'inventories.location',
                'stockMovements.creator',
                'stockMovements.sourceLocation',
                'stockMovements.destinationLocation'
            ])->findOrFail($id);
            
            // Get medicine statistics
            $stats = $this->medicineService->getMedicineStats($medicine);
            
            // Get active batches with location info
            $batches = $this->medicineService->getMedicineBatches($medicine);
            
            // Get paginated movement history with related data
            $movements = $medicine->stockMovements()
                ->with(['creator', 'sourceLocation', 'destinationLocation'])
                ->orderBy('created_at', 'desc')
                ->paginate(15);
            
            return view('inventory.medicines.history', array_merge(
                compact('medicine', 'movements', 'batches'),
                $stats
            ));
        } catch (\Exception $e) {
            Log::error('Error viewing medicine history: ' . $e->getMessage());
            return redirect()
                ->route('inventory.medicines.index')
                ->with('error', 'Error viewing medicine history: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified medicine.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $medicine = Medicine::with([
                'category', 
                'manufacturer', 
                'inventories.location',
                'suppliers'
            ])->findOrFail($id);
            
            // Get medicine statistics
            $stats = $this->medicineService->getMedicineStats($medicine);
            
            // Get active batches with location info
            $batches = $this->medicineService->getMedicineBatches($medicine);
            
            // Calculate total stock
            $total_stock = $medicine->inventories->sum('quantity');
            
            return view('inventory.medicines.show', array_merge(
                compact('medicine', 'batches', 'total_stock'),
                $stats
            ));
        } catch (\Exception $e) {
            Log::error('Error viewing medicine details: ' . $e->getMessage());
            return redirect()
                ->route('inventory.medicines.index')
                ->with('error', 'Error viewing medicine details: ' . $e->getMessage());
        }
    }

    /**
     * Search for medicines based on a search term.
     * Returns exact matches first, followed by generic matches.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchMedicines(Request $request)
    {
        $searchTerm = $request->input('query');
        
        // Only search if the term is at least 3 characters
        if (strlen($searchTerm) < 3) {
            return response()->json([]);
        }

        // Step 1: Find exact matches
        $exactMatches = Medicine::where(function($query) use ($searchTerm) {
                // Exact name match gets highest priority
                $query->where('name', 'LIKE', $searchTerm)
                    // Similar name matches get second priority
                    ->orWhere('name', 'LIKE', "%{$searchTerm}%")
                    // Generic name matches get third priority
                    ->orWhere('generic_name', 'LIKE', "%{$searchTerm}%");
            })
            ->with(['manufacturer', 'category', 'unitType', 'inventories.location'])
            ->active()
            ->orderByRaw("CASE 
                WHEN name LIKE ? THEN 1
                WHEN name LIKE ? THEN 2
                WHEN generic_name LIKE ? THEN 3
                ELSE 4
            END", [$searchTerm, "%{$searchTerm}%", "%{$searchTerm}%"])
            ->get();

        // Step 2: Find generic matches (medicines with the same generic_name as the exact matches)
        $genericNames = $exactMatches->pluck('generic_name')->unique()->filter();
        
        $genericMatches = collect();
        if ($genericNames->isNotEmpty()) {
            $genericMatches = Medicine::whereIn('generic_name', $genericNames)
                ->whereNotIn('id', $exactMatches->pluck('id')) // Exclude exact matches
                ->with(['manufacturer', 'category', 'unitType', 'inventories.location'])
                ->active()
                ->get();
        }

        // Combine results
        $results = $exactMatches->merge($genericMatches);

        // Transform the results to include only the necessary data
        $transformedResults = $results->map(function ($medicine) {
            // Get batches for this medicine
            $batches = $medicine->getActiveBatches();
            
            // Format batches for frontend
            $formattedBatches = $batches->map(function ($batch) {
                return [
                    'number' => $batch->batch_number,
                    'expiry_formatted' => $batch->expiry_date ? $batch->expiry_date->format('d M Y') : 'N/A',
                    'quantity' => $batch->quantity,
                    'location' => $batch->location ? $batch->location->name : 'Unknown',
                    'rack_number' => $batch->rack_number,
                    'bin_location' => $batch->bin_location,
                ];
            });

            // Get default batch (first active batch)
            $defaultBatch = $formattedBatches->first();
            
            // Calculate total stock quantity across all batches
            $totalStock = $formattedBatches->sum('quantity');
            
            // Extract location information - optimized for single-line display
            $locationData = [];
            $locationSummary = '';
            
            if ($batches->isNotEmpty()) {
                // Group by location to consolidate quantities
                $locationGroups = $batches->groupBy(function ($batch) {
                    return $batch->location ? $batch->location->name : 'Unknown';
                });
                
                // Create concise location data for each group
                $locationData = $locationGroups->map(function ($batchGroup, $locationName) {
                    $totalQuantity = $batchGroup->sum('quantity');
                    
                    // Get rack and bin info from the first batch in the group
                    $firstBatch = $batchGroup->first();
                    $rackInfo = $firstBatch->rack_number ? " (R:{$firstBatch->rack_number})" : '';
                    $binInfo = $firstBatch->bin_location ? " (B:{$firstBatch->bin_location})" : '';
                    
                    return [
                        'name' => $locationName . $rackInfo . $binInfo,
                        'quantity' => $totalQuantity
                    ];
                })->values()->toArray();
                
                // Create a summary string for quick display
                $locationSummary = collect($locationData)->map(function ($loc) {
                    return "{$loc['name']}: {$loc['quantity']}";
                })->join(', ');
            }

            return [
                'id' => $medicine->id,
                'name' => $medicine->name,
                'generic_name' => $medicine->generic_name,
                'dosage' => $medicine->dosage,
                'manufacturer' => $medicine->manufacturer ? $medicine->manufacturer->name : '',
                'category' => $medicine->category ? $medicine->category->name : '',
                'batches' => $formattedBatches,
                'default_batch' => $defaultBatch ? $defaultBatch['number'] : '',
                'default_price' => $medicine->unit_price,
                'selling_price' => $medicine->unit_price,
                'enabled_units' => $medicine->enabled_units ?? ['piece'],
                'retail_price_box' => $medicine->retail_price_box,
                'retail_price_strip' => $medicine->retail_price_strip,
                'retail_price_unit' => $medicine->retail_price_unit,
                'strips_per_box' => $medicine->strips_per_box,
                'pieces_per_strip' => $medicine->pieces_per_strip,
                'total_stock' => $totalStock,
                'has_location_data' => count($locationData) > 0,
                'locations' => $locationData,
                'location_summary' => $locationSummary,
                'storage_location' => count($locationData) > 0 ? $locationData[0]['name'] : 'No location data'
            ];
        });

        return response()->json($transformedResults);
    }

    /**
     * Duplicate the specified medicine.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function duplicate($id)
    {
        try {
            $originalMedicine = Medicine::with(['category', 'manufacturer', 'suppliers', 'unitType'])->findOrFail($id);
            
            // Create a copy of the medicine attributes
            $newMedicineData = $originalMedicine->toArray();
            
            // Remove attributes that should not be duplicated
            unset(
                $newMedicineData['id'], 
                $newMedicineData['created_at'], 
                $newMedicineData['updated_at'], 
                $newMedicineData['deleted_at']
            );
            
            // Modify the name to indicate it's a copy
            $newMedicineData['name'] = $originalMedicine->name . ' (Copy)';
            
            // Create the new medicine
            DB::beginTransaction();
            
            try {
                // Create new medicine with the same attributes
                $newMedicine = Medicine::create($newMedicineData);
                
                // Attach the same suppliers if any
                if ($originalMedicine->suppliers->isNotEmpty()) {
                    foreach ($originalMedicine->suppliers as $supplier) {
                        $newMedicine->suppliers()->attach($supplier->id, [
                            'price' => $supplier->pivot->price ?? 0,
                            'is_default' => $supplier->pivot->is_default ?? false,
                            'created_by' => Auth::id()
                        ]);
                    }
                }
                
                DB::commit();
                
                return redirect()
                    ->route('inventory.medicines.edit', $newMedicine->id)
                    ->with('success', 'Medicine duplicated successfully. You can now edit the copy.');
                    
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
            
        } catch (\Exception $e) {
            Log::error('Error duplicating medicine: ' . $e->getMessage());

            return redirect()
                ->route('inventory.medicines.index')
                ->with('error', 'Error duplicating medicine: ' . $e->getMessage());
        }
    }

    /**
     * Export medicines to Excel/CSV
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Request $request)
    {
        try {
            $filters = $request->only([
                'search',
                'category_id',
                'manufacturer_id',
                'status',
                'controlled_substance',
                'prescription_required',
                'sort'
            ]);

            // Get format from request, default to xlsx
            $format = $request->input('format', 'xlsx');

            return $this->importExportService->exportMedicines($filters, $format);

        } catch (\Exception $e) {
            Log::error('Medicine export error: ' . $e->getMessage());

            return redirect()
                ->route('inventory.medicines.index')
                ->with('error', 'Failed to export medicines: ' . $e->getMessage());
        }
    }

    /**
     * Import medicines from Excel/CSV
     *
     * @param ImportMedicineRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function import(ImportMedicineRequest $request)
    {
        try {
            $file = $request->file('import_file');
            $updateExisting = $request->boolean('update_existing', false);

            $result = $this->importExportService->importMedicines($file, $updateExisting);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Medicine import error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Import failed: ' . $e->getMessage(),
                'details' => [],
                'errors' => [$e->getMessage()]
            ], 500);
        }
    }

    /**
     * Download import template
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function template()
    {
        try {
            return $this->importExportService->generateTemplate();

        } catch (\Exception $e) {
            Log::error('Template generation error: ' . $e->getMessage());

            return redirect()
                ->route('inventory.medicines.index')
                ->with('error', 'Failed to generate template: ' . $e->getMessage());
        }
    }
}
