<?php

namespace App\Http\Controllers\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Inventory\Supplier;
use App\Models\Inventory\Purchase;
use App\Models\Inventory\SupplierPayment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class SupplierPaymentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $supplierId = $request->input('supplier_id');
            $supplier = null;

            if ($supplierId) {
                try {
                    $supplier = Supplier::findOrFail($supplierId);
                } catch (\Exception $e) {
                    return redirect()->route('inventory.suppliers.index')
                        ->with('error', 'Supplier not found.');
                }
                
                $payments = SupplierPayment::with(['supplier', 'purchase', 'createdBy'])
                    ->where('supplier_id', $supplierId)
                    ->orderBy('payment_date', 'desc')
                    ->paginate(15);
            } else {
                $payments = SupplierPayment::with(['supplier', 'purchase', 'createdBy'])
                    ->orderBy('payment_date', 'desc')
                    ->paginate(15);
            }
            
            $suppliers = Supplier::orderBy('name')->get();
            
            return view('inventory.suppliers.payments.index', compact('payments', 'suppliers', 'supplier'));
        } catch (\Exception $e) {
            return redirect()->route('inventory.suppliers.index')
                ->with('error', 'An error occurred while loading the payments page. Please try again.');
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $supplierId = $request->input('supplier_id');
        $purchaseId = $request->input('purchase_id');
        
        $suppliers = Supplier::orderBy('name')->get();
        $supplier = null;
        $purchase = null;
        $pendingPurchases = collect();
        
        if ($supplierId) {
            $supplier = Supplier::findOrFail($supplierId);
            $pendingPurchases = Purchase::where('supplier_id', $supplierId)
                ->whereIn('payment_status', ['pending', 'partial'])
                ->orderBy('created_at', 'desc')
                ->get();
        }
        
        if ($purchaseId) {
            $purchase = Purchase::findOrFail($purchaseId);
            if (!$supplier) {
                $supplier = $purchase->supplier;
                $pendingPurchases = Purchase::where('supplier_id', $supplier->id)
                    ->whereIn('payment_status', ['pending', 'partial'])
                    ->orderBy('created_at', 'desc')
                    ->get();
            }
        }
        
        return view('inventory.suppliers.payments.create', compact('suppliers', 'supplier', 'purchase', 'pendingPurchases'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'supplier_id' => 'required|exists:suppliers,id',
            'amount' => 'required|numeric|min:0.01',
            'payment_method' => 'required|string',
            'payment_date' => 'required|date',
            'purchase_id' => 'nullable|exists:purchases,id',
        ]);
        
        $payment = new SupplierPayment();
        $payment->supplier_id = $request->supplier_id;
        $payment->purchase_id = $request->purchase_id;
        $payment->amount = $request->amount;
        $payment->payment_method = $request->payment_method;
        $payment->reference_number = $request->reference_number;
        $payment->payment_date = $request->payment_date;
        $payment->notes = $request->notes;
        $payment->created_by = Auth::id();
        $payment->save();
        
        return redirect()->route('inventory.suppliers.payments.index', ['supplier_id' => $payment->supplier_id])
            ->with('success', 'Payment recorded successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show($payment)
    {
        $payment = SupplierPayment::findOrFail($payment);
        $payment->load(['supplier', 'purchase', 'createdBy']);
        return view('inventory.suppliers.payments.show', compact('payment'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($payment)
    {
        $payment = SupplierPayment::findOrFail($payment);
        $payment->load(['supplier', 'purchase']);
        
        $suppliers = Supplier::orderBy('name')->get();
        $pendingPurchases = Purchase::where('supplier_id', $payment->supplier_id)
            ->whereIn('payment_status', ['pending', 'partial'])
            ->orderBy('created_at', 'desc')
            ->get();
            
        if ($payment->purchase_id) {
            $pendingPurchases = $pendingPurchases->push($payment->purchase)->unique('id');
        }
        
        return view('inventory.suppliers.payments.edit', compact('payment', 'suppliers', 'pendingPurchases'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $payment)
    {
        $payment = SupplierPayment::findOrFail($payment);
        
        $request->validate([
            'supplier_id' => 'required|exists:suppliers,id',
            'amount' => 'required|numeric|min:0.01',
            'payment_method' => 'required|string',
            'payment_date' => 'required|date',
            'purchase_id' => 'nullable|exists:purchases,id',
        ]);
        
        $payment->supplier_id = $request->supplier_id;
        $payment->purchase_id = $request->purchase_id;
        $payment->amount = $request->amount;
        $payment->payment_method = $request->payment_method;
        $payment->reference_number = $request->reference_number;
        $payment->payment_date = $request->payment_date;
        $payment->notes = $request->notes;
        $payment->updated_by = Auth::id();
        $payment->save();
        
        return redirect()->route('inventory.suppliers.payments.index', ['supplier_id' => $payment->supplier_id])
            ->with('success', 'Payment updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($payment)
    {
        $payment = SupplierPayment::findOrFail($payment);
        $supplierId = $payment->supplier_id;
        $payment->delete();
        
        return redirect()->route('inventory.suppliers.payments.index', ['supplier_id' => $supplierId])
            ->with('success', 'Payment deleted successfully.');
    }
} 