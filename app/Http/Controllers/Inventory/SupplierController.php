<?php

namespace App\Http\Controllers\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Inventory\Supplier;
use App\Services\Inventory\SupplierService;
use Illuminate\Http\Request;

class SupplierController extends Controller
{
    protected $supplierService;

    public function __construct(SupplierService $supplierService)
    {
        $this->supplierService = $supplierService;
    }

    public function index(Request $request)
    {
        $suppliers = $this->supplierService->getPaginatedSuppliers($request);
        
        // Return JSON for search suggestions
        if ($request->wantsJson() || $request->has('format') && $request->format === 'json') {
            if ($request->has('search') && strlen($request->search) >= 3) {
                $searchResults = $suppliers->getCollection()->map(function ($supplier) {
                    return [
                        'id' => $supplier->id,
                        'name' => $supplier->name,
                        'contact_person' => $supplier->contact_person ?? '-',
                        'email' => $supplier->email ?? '-',
                        'phone' => $supplier->phone ?? '-',
                        'is_active' => $supplier->is_active
                    ];
                });
                
                return response()->json($searchResults);
            }
            
            return $suppliers;
        }
        
        return view('inventory.suppliers.index', compact('suppliers'));
    }

    public function create()
    {
        return view('inventory.suppliers.create');
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'contact_person' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'tax_number' => 'nullable|string|max:50',
            'status' => 'nullable|string|in:active,inactive',
        ]);

        try {
            $this->supplierService->createSupplier($validatedData);
            return redirect()->route('inventory.suppliers.index')
                ->with('success', 'Supplier created successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Error creating supplier. Please try again.');
        }
    }

    public function edit($id)
    {
        try {
            $supplier = $this->supplierService->findSupplier($id);
            return view('inventory.suppliers.edit', compact('supplier'));
        } catch (\Exception $e) {
            return redirect()->route('inventory.suppliers.index')
                ->with('error', 'Supplier not found.');
        }
    }

    public function show(Supplier $supplier)
    {
        return view('inventory.suppliers.edit', compact('supplier'));
    }

    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'contact_person' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'tax_number' => 'nullable|string|max:50',
            'status' => 'nullable|string|in:active,inactive',
        ]);

        try {
            $supplier = $this->supplierService->findSupplier($id);
            $this->supplierService->updateSupplier($supplier, $validatedData);
            return redirect()->route('inventory.suppliers.index')
                ->with('success', 'Supplier updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Error updating supplier. Please try again.');
        }
    }

    public function destroy($id)
    {
        try {
            $supplier = $this->supplierService->findSupplier($id);
            $this->supplierService->deleteSupplier($supplier);
            return redirect()->route('inventory.suppliers.index')
                ->with('success', 'Supplier deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', $e->getMessage());
        }
    }

    public function toggleStatus($id)
    {
        try {
            $supplier = $this->supplierService->findSupplier($id);
            $supplier->is_active = !$supplier->is_active;
            $supplier->save();

            $status = $supplier->is_active ? 'enabled' : 'disabled';
            return response()->json([
                'success' => true,
                'message' => "Supplier {$status} successfully",
                'is_active' => $supplier->is_active
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating supplier status'
            ], 500);
        }
    }
}
