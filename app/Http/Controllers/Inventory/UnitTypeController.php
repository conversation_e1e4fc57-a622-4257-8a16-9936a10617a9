<?php

namespace App\Http\Controllers\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Inventory\UnitType;
use App\Services\Inventory\UnitTypeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class UnitTypeController extends Controller
{
    protected $unitTypeService;

    public function __construct(UnitTypeService $unitTypeService)
    {
        $this->unitTypeService = $unitTypeService;
    }

    public function index(Request $request)
    {
        $filters = $request->only(['search', 'status', 'category', 'is_base', 'sort']);
        
        // Handle JSON format for search suggestions
        if ($request->has('format') && $request->format === 'json') {
            if ($request->has('search') && strlen($request->search) >= 3) {
                $unitTypes = $this->unitTypeService->searchUnitTypes($request->search);
                return response()->json($unitTypes);
            }
            return response()->json([]);
        }
        
        $unitTypes = $this->unitTypeService->getPaginatedUnitTypes($filters);
        
        // Get unique categories for filter dropdown
        $categories = UnitType::select('category')->distinct()->pluck('category');
        
        return view('inventory.unit-types.index', compact('unitTypes', 'categories'));
    }

    public function create()
    {
        return view('inventory.unit-types.create');
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:unit_types,name',
            'abbreviation' => 'required|string|max:10|unique:unit_types,abbreviation',
            'code' => 'required|string|max:50|unique:unit_types,code',
            'category' => 'required|string|in:weight,volume,length,unit,time,temperature,quantity',
            'description' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
            'is_base' => 'boolean'
        ]);

        try {
            $this->unitTypeService->createUnitType($validatedData);

            return redirect()
                ->route('inventory.unit-types.index')
                ->with('success', 'Unit type added successfully');
        } catch (\Exception $e) {
            Log::error('Error creating unit type: ' . $e->getMessage());
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Error creating unit type: ' . $e->getMessage());
        }
    }

    public function edit(UnitType $unitType)
    {
        return view('inventory.unit-types.edit', compact('unitType'));
    }

    public function update(Request $request, UnitType $unitType)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:unit_types,name,' . $unitType->id,
            'abbreviation' => 'required|string|max:10|unique:unit_types,abbreviation,' . $unitType->id,
            'description' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ]);

        try {
            $this->unitTypeService->updateUnitType($unitType, $validatedData);

            return redirect()
                ->route('inventory.unit-types.index')
                ->with('success', 'Unit type updated successfully');
        } catch (\Exception $e) {
            Log::error('Error updating unit type: ' . $e->getMessage());
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Error updating unit type. Please try again.');
        }
    }

    public function destroy(UnitType $unitType)
    {
        try {
            $this->unitTypeService->deleteUnitType($unitType);

            return redirect()
                ->route('inventory.unit-types.index')
                ->with('success', 'Unit type deleted successfully');
        } catch (\Exception $e) {
            Log::error('Error deleting unit type: ' . $e->getMessage());
            return redirect()
                ->back()
                ->with('error', $e->getMessage());
        }
    }
}
