<?php

namespace App\Http\Controllers\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Inventory\BatchHistory;
use App\Services\Inventory\BatchHistoryService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;

class BatchHistoryController extends Controller
{
    protected $batchHistoryService;

    public function __construct(BatchHistoryService $batchHistoryService)
    {
        $this->batchHistoryService = $batchHistoryService;
    }

    public function index(Request $request)
    {
        $medicines = $this->batchHistoryService->getAllMedicines();
        $locations = $this->batchHistoryService->getAllLocations();
        $filters = $request->only(['medicine', 'location', 'action_type', 'date_range']);

        $batchHistories = $this->batchHistoryService->getFilteredBatchHistories($filters);

        if ($request->wantsJson()) {
            return Response::json([
                'medicines' => $medicines,
                'locations' => $locations,
                'batchHistories' => $batchHistories
            ]);
        }

        return view('inventory.batch.history', compact('medicines', 'locations', 'batchHistories'));
    }

    public function show(BatchHistory $history)
    {
        $history = $this->batchHistoryService->getBatchHistoryDetails($history);
        return view('inventory.batch.show', compact('history'));
    }

    public function export(Request $request)
    {
        $filters = $request->only(['medicine', 'location', 'action_type', 'date_range']);
        $exportData = $this->batchHistoryService->exportBatchHistories($filters);

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $exportData['filename'] . '"'
        ];

        $callback = function() use ($exportData) {
            $file = fopen('php://output', 'w');
            foreach ($exportData['data'] as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }
}
