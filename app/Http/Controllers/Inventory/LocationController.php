<?php

namespace App\Http\Controllers\Inventory;

use App\Http\Controllers\Controller;
use App\Http\Resources\LocationResource;
use App\Models\Inventory\Location;
use App\Services\Inventory\LocationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class LocationController extends Controller
{
    protected $locationService;

    public function __construct(LocationService $locationService)
    {
        $this->locationService = $locationService;
    }

    public function index(Request $request)
    {
        $filters = $request->only(['section', 'zone', 'temperature', 'search']);
        $locations = $this->locationService->getPaginatedLocations($filters);

        if ($request->wantsJson() || $request->has('format') && $request->format === 'json') {
            // For search suggestions, return a simplified array of locations
            if ($request->has('search') && strlen($request->search) >= 3) {
                $searchResults = $locations->getCollection()->map(function ($location) {
                    return [
                        'id' => $location->id,
                        'name' => $location->name,
                        'code' => $location->location_code ?? '-',
                        'type' => $location->type ?? 'location',
                        'section' => $location->section ?? '-',
                        'zone' => $location->zone ?? '-',
                        'is_active' => $location->status === 'active'
                    ];
                });
                
                return response()->json($searchResults);
            }
            
            return $locations;
        }

        return view('inventory.locations.index', compact('locations'));
    }

    public function create()
    {
        $parents = $this->locationService->getParentLocations();
        return view('inventory.locations.create', compact('parents'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'type' => ['required', Rule::in(['warehouse', 'store', 'shelf'])],
            'parent_id' => ['nullable', 'exists:locations,id'],
            'section' => ['nullable', 'string', 'max:255'],
            'zone' => ['nullable', 'string', 'max:255'],
            'aisle_number' => ['nullable', 'string', 'max:50'],
            'rack_number' => ['nullable', 'string', 'max:50'],
            'bin_location' => ['nullable', 'string', 'max:50'],
            'temperature_requirement' => ['nullable', 'string', 'max:50'],
            'address' => ['nullable', 'string', 'max:255'],
            'status' => ['required', Rule::in(['active', 'inactive'])],
        ]);

        try {
            $this->locationService->createLocation($validated);

            return redirect()
                ->route('inventory.locations.index')
                ->with('success', 'Location created successfully.');
        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Error creating location: ' . $e->getMessage());
        }
    }

    public function show(Location $location)
    {
        $location->load([
            'parent',
            'creator',
            'updater',
            'batchHistories.medicine',
            'movements' => function ($query) {
                $query->with(['medicine', 'creator'])
                    ->latest()
                    ->take(50);
            }
        ]);

        return view('inventory.locations.show', compact('location'));
    }

    public function edit(Location $location)
    {
        $parents = $this->locationService->getParentLocations($location);
        return view('inventory.locations.edit', compact('location', 'parents'));
    }

    public function update(Request $request, Location $location)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'section' => ['nullable', 'string', 'max:255'],
            'zone' => ['nullable', 'string', 'max:255'],
            'aisle_number' => ['nullable', 'string', 'max:50'],
            'rack_number' => ['nullable', 'string', 'max:50'],
            'bin_location' => ['nullable', 'string', 'max:50'],
            'temperature_requirement' => ['nullable', 'string', 'max:50'],
            'address' => ['nullable', 'string', 'max:255'],
            'status' => ['required', Rule::in(['active', 'inactive'])],
        ]);

        try {
            $this->locationService->updateLocation($location, $validated);

            return redirect()
                ->route('inventory.locations.index')
                ->with('success', 'Location updated successfully.');
        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Error updating location: ' . $e->getMessage());
        }
    }

    public function destroy(Location $location)
    {
        try {
            $this->locationService->deleteLocation($location);

            return redirect()
                ->route('inventory.locations.index')
                ->with('success', 'Location deleted successfully.');
        } catch (\Exception $e) {
            return back()
                ->with('error', 'Error deleting location: ' . $e->getMessage());
        }
    }

    public function findSuitable(Request $request)
    {
        $validated = $request->validate([
            'temperature' => ['nullable', 'string'],
            'section' => ['nullable', 'string'],
            'controlled' => ['nullable', 'boolean'],
        ]);

        $locations = $this->locationService->findSuitableLocations($validated)
            ->through(fn ($location) => new LocationResource($location));

        return response()->json($locations);
    }
}
