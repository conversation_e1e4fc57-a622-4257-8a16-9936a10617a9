<?php

namespace App\Http\Controllers\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Inventory\Category;
use App\Services\Inventory\CategoryService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CategoryController extends Controller
{
    protected $categoryService;

    public function __construct(CategoryService $categoryService)
    {
        $this->categoryService = $categoryService;
    }

    public function index(Request $request)
    {
        $categories = $this->categoryService->getPaginatedCategories($request);
        
        // Return JSON for search suggestions
        if ($request->filled('format') && $request->input('format') === 'json') {
            return response()->json($categories->items());
        }
        
        // Get parent categories for filter dropdown
        $parentCategories = $this->categoryService->getParentCategories();
        
        // Append query parameters to pagination links
        $categories->appends($request->query());
        
        return view('inventory.categories.index', compact('categories', 'parentCategories'));
    }

    public function create()
    {
        $categories = $this->categoryService->getParentCategories();
        return view('inventory.categories.create', compact('categories'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'parent_id' => 'nullable|exists:categories,id',
            'is_active' => 'boolean',
        ]);

        try {
            $this->categoryService->createCategory($validatedData);

            return redirect()
                ->route('inventory.categories.index')
                ->with('success', 'Category added successfully');
        } catch (\Exception $e) {
            Log::error('Error creating category: ' . $e->getMessage());
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Error creating category. Please try again.');
        }
    }

    public function edit(Category $category)
    {
        $categories = $this->categoryService->getParentCategories($category);
        return view('inventory.categories.edit', compact('category', 'categories'));
    }

    public function update(Request $request, Category $category)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'parent_id' => 'nullable|exists:categories,id',
            'is_active' => 'boolean',
        ]);

        try {
            $this->categoryService->updateCategory($category, $validatedData);

            return redirect()
                ->route('inventory.categories.index')
                ->with('success', 'Category updated successfully');
        } catch (\Exception $e) {
            Log::error('Error updating category: ' . $e->getMessage());
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Error updating category. Please try again.');
        }
    }

    public function destroy(Category $category)
    {
        try {
            $this->categoryService->deleteCategory($category);

            return redirect()
                ->route('inventory.categories.index')
                ->with('success', 'Category deleted successfully');
        } catch (\Exception $e) {
            Log::error('Error deleting category: ' . $e->getMessage());
            return redirect()
                ->back()
                ->with('error', $e->getMessage());
        }
    }
}
