<?php

namespace App\Http\Controllers\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Inventory\Medicine;
use App\Models\Inventory\StockMovement;
use App\Models\Inventory\Location;
use App\Models\Inventory\Inventory;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Services\Inventory\StockService;
use Illuminate\Support\Facades\Log;

class StockController extends Controller
{
    protected $stockService;

    public function __construct(StockService $stockService)
    {
        $this->stockService = $stockService;
    }

    public function index()
    {
        $summary = $this->stockService->getStockSummary();
        return view('inventory.stock.index', $summary);
    }

    public function transfer()
    {
        $locations = Location::where(function($query) {
            $query->where('status', 'active')
                  ->orWhereNull('status');
        })->get();

        $medicines = Medicine::where(function($query) {
            $query->where('status', 'active')
                  ->orWhereNull('status');
        })
        ->orderBy('name')
        ->get();
            
        return view('inventory.stock.transfer', compact('locations', 'medicines'));
    }

    public function getMedicinesByLocation(Location $location)
    {
        $medicines = $this->stockService->getMedicinesByLocation($location);
        return response()->json($medicines);
    }

    public function getBatchesByLocationAndMedicine(Request $request)
    {
        $request->validate([
            'location_id' => 'required|exists:locations,id',
            'medicine_id' => 'required|exists:medicines,id'
        ]);

        $batches = $this->stockService->getBatchesByLocationAndMedicine(
            Location::findOrFail($request->location_id),
            Medicine::findOrFail($request->medicine_id)
        );

        return response()->json([
            'batches' => $batches->map(function($batch) {
                return [
                    'id' => $batch->id,
                    'batch_number' => $batch->batch_number,
                    'quantity' => $batch->quantity,
                    'expiry_date' => $batch->expiry_date ? $batch->expiry_date->format('Y-m-d') : null,
                    'unit_price' => $batch->unit_price
                ];
            })
        ]);
    }

    public function storeTransfer(Request $request)
    {
        $validatedData = $request->validate([
            'source_location_id' => 'required|exists:locations,id',
            'destination_location_id' => 'required|exists:locations,id|different:source_location_id',
            'medicine_id' => 'required|exists:medicines,id',
            'quantity' => 'required|integer|min:1',
            'batch_number' => 'required|string',
            'movement_date' => 'required|date',
            'notes' => 'nullable|string'
        ]);

        try {
            // Log the incoming request data
            Log::info('Stock transfer request received', [
                'source_location_id' => $validatedData['source_location_id'],
                'destination_location_id' => $validatedData['destination_location_id'],
                'medicine_id' => $validatedData['medicine_id'],
                'quantity' => $validatedData['quantity'],
                'batch_number' => $validatedData['batch_number'],
                'movement_date' => $validatedData['movement_date']
            ]);
            
            // Map form field names to the expected parameter names for StockService
            $transferData = [
                'source_location' => $validatedData['source_location_id'],
                'destination_location' => $validatedData['destination_location_id'],
                'medicine' => $validatedData['medicine_id'],
                'quantity' => $validatedData['quantity'],
                'batch_number' => $validatedData['batch_number'],
                'transfer_date' => $validatedData['movement_date'],
                'notes' => $validatedData['notes'] ?? null
            ];
            
            // Log the mapped data being sent to the service
            Log::info('Calling transferStock service with mapped data', $transferData);
            
            $this->stockService->transferStock($transferData);
            
            // Get medicine name for a more informative success message
            $medicine = Medicine::findOrFail($validatedData['medicine_id']);
            $sourceLocation = Location::findOrFail($validatedData['source_location_id']);
            $destinationLocation = Location::findOrFail($validatedData['destination_location_id']);
            
            Log::info('Stock transfer successful', [
                'medicine' => $medicine->name,
                'quantity' => $validatedData['quantity'],
                'source' => $sourceLocation->name,
                'destination' => $destinationLocation->name
            ]);
            
            // Create success message
            $successMessage = "Successfully transferred {$validatedData['quantity']} units of {$medicine->name} from {$sourceLocation->name} to {$destinationLocation->name}.";
            
            // Store the success message in the session
            $request->session()->flash('success', $successMessage);
            
            // Also include the message as a URL parameter as a fallback
            return redirect()->route('inventory.stock.transfer', ['flash_success' => urlencode($successMessage)]);
        } catch (\Exception $e) {
            Log::error('Stock transfer failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            
            // Create error message
            $errorMessage = 'An error occurred while processing the transfer: ' . $e->getMessage();
            
            // Store the error message in the session
            $request->session()->flash('error', $errorMessage);
            
            // Also include the message as a URL parameter as a fallback
            return redirect()->back()->withInput()->with('flash_error', urlencode($errorMessage));
        }
    }

    public function locations()
    {
        $locations = Location::where(function($query) {
            $query->where('status', 'active')
                  ->orWhereNull('status');
        })
        ->orderBy('name')
        ->paginate(10);
            
        return view('inventory.stock.locations', compact('locations'));
    }

    public function storeLocation(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:locations,name',
            'type' => 'required|in:warehouse,store,shelf',
            'address' => 'nullable|string'
        ]);

        try {
            $this->stockService->createLocation($validatedData);
            return redirect()->route('inventory.stock.locations')
                ->with('success', 'Location has been added successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'An error occurred while creating the location. Please try again.')
                ->withInput();
        }
    }

    public function editLocation(Location $location)
    {
        return view('inventory.stock.locations.edit', compact('location'));
    }

    public function updateLocation(Request $request, Location $location)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:locations,name,' . $location->id,
            'type' => 'required|in:warehouse,store,shelf',
            'address' => 'nullable|string',
            'status' => 'required|in:active,inactive'
        ]);

        try {
            $this->stockService->updateLocation($location, $validatedData);
            return redirect()->route('inventory.stock.locations')
                ->with('success', 'Location has been updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'An error occurred while updating the location. Please try again.')
                ->withInput();
        }
    }

    public function destroyLocation(Location $location)
    {
        try {
            $this->stockService->deleteLocation($location);
            return redirect()->route('inventory.stock.locations')
                ->with('success', 'Location has been deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', $e->getMessage());
        }
    }

    public function lowStock()
    {
        $lowStockItems = $this->stockService->getLowStockItems();
        return view('inventory.stock.low_stock', compact('lowStockItems'));
    }

    public function expiring()
    {
        $expiringItems = $this->stockService->getExpiringItems();
        return view('inventory.stock.expiring', [
            'expiringItems' => $expiringItems,
            'title' => 'Expiring Items'
        ]);
    }

    public function medicineHistory(Medicine $medicine)
    {
        $history = $this->stockService->getMedicineHistory($medicine);
        return view('inventory.medicines.history', array_merge(['medicine' => $medicine], $history));
    }
}
