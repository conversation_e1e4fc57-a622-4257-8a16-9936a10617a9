<?php

namespace App\Http\Controllers\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Medicine;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class MedicineSearchController extends Controller
{
    public function search(Request $request)
    {
        $query = $request->input('query');
        
        Log::debug('Medicine search query: ' . $query);

        // Get medicines matching the search query
        $results = Medicine::where('name', 'like', '%' . $query . '%')
            ->orWhere('generic_name', 'like', '%' . $query . '%')
            ->with(['manufacturer', 'category', 'inventories' => function($query) {
                $query->where('quantity', '>', 0)
                      ->whereDate('expiry_date', '>', now())
                      ->orderBy('expiry_date', 'asc');
            }])
            ->get();
            
        Log::debug('Found ' . $results->count() . ' medicines matching the query');

        // Transform the results to include only the necessary data
        $transformedResults = $results->map(function ($medicine) {
            // Get total stock quantity from all valid inventories
            $totalStock = $medicine->inventories->sum('quantity');
            
            // Get inventory IDs for direct location query
            $inventoryIds = $medicine->inventories->pluck('id')->toArray();
            
            // Directly query the locations for these inventories
            $locationData = [];
            if (!empty($inventoryIds)) {
                $locationResults = DB::table('inventories as i')
                    ->leftJoin('locations as l', 'i.location_id', '=', 'l.id')
                    ->select('i.id as inventory_id', 'i.batch_number', 'i.rack_number as inventory_rack', 
                             'i.bin_location as inventory_bin', 'l.id as location_id', 'l.name as location_name',
                             'l.rack_number as location_rack', 'l.bin_location as location_bin')
                    ->whereIn('i.id', $inventoryIds)
                    ->get();
                
                // Convert each stdClass object to an array and index by inventory_id
                foreach ($locationResults as $item) {
                    $locationData[$item->inventory_id] = $item;
                    Log::debug("Location data for inventory {$item->inventory_id}: " . json_encode($item));
                }
                
                Log::debug('Direct location query found ' . count($locationData) . ' location records');
            }
            
            // Get batches with inventory information
            $batches = $medicine->inventories->map(function ($inventory) use ($locationData) {
                $inventoryId = $inventory->id;
                
                // Get location data from direct query
                $location = isset($locationData[$inventoryId]) ? $locationData[$inventoryId] : null;
                
                if ($location) {
                    Log::debug("Found location for inventory {$inventoryId}: ID={$location->location_id}, Name={$location->location_name}");
                } else {
                    Log::debug("No location found for inventory {$inventoryId}");
                }
                
                $locationName = $location ? $location->location_name : 'Unknown';
                $locationId = $location ? $location->location_id : null;
                
                // Get rack number - prefer location rack over inventory rack
                $rackNumber = null;
                if ($location && !empty($location->location_rack) && $location->location_rack !== 'NULL') {
                    $rackNumber = $location->location_rack;
                    Log::debug("Using location rack: {$rackNumber}");
                } elseif (!empty($inventory->rack_number) && $inventory->rack_number !== 'NULL') {
                    $rackNumber = $inventory->rack_number;
                    Log::debug("Using inventory rack: {$rackNumber}");
                }
                
                // Get bin location - prefer location bin over inventory bin
                $binLocation = null;
                if ($location && !empty($location->location_bin) && $location->location_bin !== 'NULL') {
                    $binLocation = $location->location_bin;
                    Log::debug("Using location bin: {$binLocation}");
                } elseif (!empty($inventory->bin_location) && $inventory->bin_location !== 'NULL') {
                    $binLocation = $inventory->bin_location;
                    Log::debug("Using inventory bin: {$binLocation}");
                }
                
                Log::debug("Final location data for inventory {$inventoryId}: location={$locationName}, rack={$rackNumber}, bin={$binLocation}");
                
                return [
                    'number' => $inventory->batch_number,
                    'expiry' => $inventory->expiry_date,
                    'expiry_formatted' => Carbon::parse($inventory->expiry_date)->format('d M Y'),
                    'quantity' => $inventory->quantity,
                    'location' => $locationName !== 'Unknown' ? $locationName : null,
                    'location_id' => $locationId,
                    'rack_number' => $rackNumber,
                    'bin_location' => $binLocation
                ];
            });
            
            // Get enabled units
            $enabledUnits = is_string($medicine->enabled_units) 
                ? json_decode($medicine->enabled_units) 
                : ($medicine->enabled_units ?: ['piece']);
            
            // Get earliest batch for storage location
            $earliestBatch = $batches->first();
            $storageLocation = null;
            
            if ($earliestBatch) {
                $locationParts = [];
                if (!empty($earliestBatch['location'])) {
                    $locationParts[] = $earliestBatch['location'];
                    Log::debug("Adding location name to parts: {$earliestBatch['location']}");
                }
                if (!empty($earliestBatch['rack_number'])) {
                    $locationParts[] = "Rack: " . $earliestBatch['rack_number'];
                    Log::debug("Adding rack to parts: {$earliestBatch['rack_number']}");
                }
                if (!empty($earliestBatch['bin_location'])) {
                    $locationParts[] = "Bin: " . $earliestBatch['bin_location'];
                    Log::debug("Adding bin to parts: {$earliestBatch['bin_location']}");
                }
                
                $storageLocation = !empty($locationParts) ? implode(' | ', $locationParts) : 'No location data';
                Log::debug("Medicine {$medicine->name} storage location: {$storageLocation}");
                
                // Additional debug info
                Log::debug("Location parts: " . json_encode($locationParts));
                Log::debug("Earliest batch data: " . json_encode($earliestBatch));
            } else {
                $storageLocation = 'No batch data';
                Log::debug("Medicine {$medicine->name} has no batch data");
            }
            
            $hasLocationData = !empty($storageLocation) && $storageLocation !== 'No location data' && $storageLocation !== 'No batch data';
            Log::debug("Medicine {$medicine->name} has_location_data: " . ($hasLocationData ? 'true' : 'false'));
            
            return [
                'id' => $medicine->id,
                'name' => $medicine->name,
                'generic_name' => $medicine->generic_name,
                'manufacturer' => $medicine->manufacturer ? $medicine->manufacturer->name : null,
                'category' => $medicine->category ? $medicine->category->name : null,
                'selling_price' => floatval($medicine->retail_price_unit),
                'retail_price_box' => floatval($medicine->retail_price_box),
                'retail_price_strip' => floatval($medicine->retail_price_strip),
                'retail_price_unit' => floatval($medicine->retail_price_unit),
                'strips_per_box' => intval($medicine->strips_per_box),
                'pieces_per_strip' => intval($medicine->pieces_per_strip),
                'enabled_units' => $enabledUnits,
                'total_stock' => $totalStock,
                'batches' => $batches,
                'storage_location' => $storageLocation,
                'has_location_data' => $hasLocationData
            ];
        });

        return response()->json($transformedResults);
    }
} 