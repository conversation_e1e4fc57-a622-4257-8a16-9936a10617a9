<?php

namespace App\Http\Controllers\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Inventory\Manufacturer;
use App\Services\Inventory\ManufacturerService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ManufacturerController extends Controller
{
    protected $manufacturerService;

    public function __construct(ManufacturerService $manufacturerService)
    {
        $this->manufacturerService = $manufacturerService;
    }

    public function index(Request $request)
    {
        // For debugging
        if ($request->filled('search')) {
            Log::info('Search term received: ' . $request->input('search'));
        }
        
        $manufacturers = $this->manufacturerService->getPaginatedManufacturers($request);
        
        // Return JSON for search suggestions
        if ($request->filled('format') && $request->input('format') === 'json') {
            return response()->json($manufacturers->items());
        }
        
        // Append query parameters to pagination links
        $manufacturers->appends($request->query());
        
        return view('inventory.manufacturers.index', compact('manufacturers'));
    }

    public function create()
    {
        return view('inventory.manufacturers.create');
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:manufacturers,name',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'website' => 'nullable|url|max:255',
            'is_active' => 'boolean',
        ]);

        try {
            $this->manufacturerService->createManufacturer($validatedData);

            return redirect()
                ->route('inventory.manufacturers.index')
                ->with('success', 'Manufacturer added successfully');
        } catch (\Exception $e) {
            Log::error('Error creating manufacturer: ' . $e->getMessage());
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Error creating manufacturer. Please try again.');
        }
    }

    public function edit(Manufacturer $manufacturer)
    {
        return view('inventory.manufacturers.edit', compact('manufacturer'));
    }

    public function update(Request $request, Manufacturer $manufacturer)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:manufacturers,name,' . $manufacturer->id,
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'website' => 'nullable|url|max:255',
            'is_active' => 'boolean',
        ]);

        try {
            $this->manufacturerService->updateManufacturer($manufacturer, $validatedData);

            return redirect()
                ->route('inventory.manufacturers.index')
                ->with('success', 'Manufacturer updated successfully');
        } catch (\Exception $e) {
            Log::error('Error updating manufacturer: ' . $e->getMessage());
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Error updating manufacturer. Please try again.');
        }
    }

    public function destroy(Manufacturer $manufacturer)
    {
        try {
            $this->manufacturerService->deleteManufacturer($manufacturer);

            return redirect()
                ->route('inventory.manufacturers.index')
                ->with('success', 'Manufacturer deleted successfully');
        } catch (\Exception $e) {
            Log::error('Error deleting manufacturer: ' . $e->getMessage());
            return redirect()
                ->back()
                ->with('error', $e->getMessage());
        }
    }
}
