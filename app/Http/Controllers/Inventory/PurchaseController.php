<?php

namespace App\Http\Controllers\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Inventory\Purchase;
use App\Models\Inventory\PurchaseItem;
use App\Models\Inventory\Medicine;
use App\Models\Inventory\Supplier;
use App\Models\Inventory\Inventory;
use App\Models\Inventory\StockMovement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PurchaseController extends Controller
{
    public function index(Request $request)
    {
        if ($request->wantsJson()) {
            $query = Purchase::query()
                ->with('supplier')
                ->when($request->search, function ($query, $search) {
                    $query->where(function ($q) use ($search) {
                        $q->where('purchase_number', 'like', '%' . $search . '%')
                          ->orWhereHas('supplier', function ($q) use ($search) {
                              $q->where('name', 'like', '%' . $search . '%');
                          });
                    });
                })
                ->latest()
                ->take(10);

            $purchases = $query->get()->map(function ($purchase) {
                return [
                    'id' => $purchase->id,
                    'purchase_number' => $purchase->purchase_number,
                    'supplier' => [
                        'name' => $purchase->supplier->name
                    ],
                    'status' => $purchase->status,
                    'payment_status' => $purchase->payment_status,
                    'order_date' => $purchase->order_date->format('M d, Y'),
                    'total_amount' => number_format($purchase->final_amount, 2)
                ];
            });

            return response()->json(['purchases' => $purchases]);
        }

        return view('inventory.purchases.index');
    }

    public function create()
    {
        return view('inventory.purchases.create');
    }

    public function show(Purchase $purchase)
    {
        $purchase->load(['supplier', 'items.medicine', 'payments']);
        return view('inventory.purchases.show', compact('purchase'));
    }

    public function edit(Purchase $purchase)
    {
        if ($purchase->status !== 'pending') {
            session()->flash('error', 'Only pending purchase orders can be edited.');
            return redirect()->route('inventory.purchases.show', $purchase);
        }

        $purchase->load(['supplier', 'items.medicine']);
        $suppliers = Supplier::orderBy('name')->get();
        $medicines = Medicine::orderBy('name')->get();

        return view('inventory.purchases.edit', compact('purchase', 'suppliers', 'medicines'));
    }

    public function update(Request $request, Purchase $purchase)
    {
        if ($purchase->status !== 'pending') {
            session()->flash('error', 'Only pending purchase orders can be updated.');
            return redirect()->route('inventory.purchases.show', $purchase);
        }

        $request->validate([
            'supplier_id' => 'required|exists:suppliers,id',
            'order_date' => 'required|date',
            'expected_date' => 'required|date|after_or_equal:order_date',
            'notes' => 'nullable|string|max:500',
            'tax_percentage' => 'required|numeric|min:0|max:100',
            'discount_percentage' => 'required|numeric|min:0|max:100',
            'shipping_cost' => 'required|numeric|min:0',
            'items' => 'required|array|min:1',
            'items.*.medicine_id' => 'required|exists:medicines,id',
            'items.*.quantity' => 'required|numeric|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            // Update purchase order
            $purchase->update([
                'supplier_id' => $request->supplier_id,
                'order_date' => $request->order_date,
                'expected_date' => $request->expected_date,
                'notes' => $request->notes,
                'tax_percentage' => $request->tax_percentage,
                'tax_amount' => collect($request->items)->sum('tax_amount'),
                'discount_percentage' => $request->discount_percentage,
                'discount_amount' => collect($request->items)->sum('discount_amount'),
                'shipping_cost' => $request->shipping_cost,
                'total_amount' => collect($request->items)->sum(function($item) {
                    return $item['quantity'] * $item['unit_price'];
                }),
                'final_amount' => collect($request->items)->sum('total_amount') + $request->shipping_cost,
            ]);

            // Delete existing items
            $purchase->items()->delete();

            // Create new items
            foreach ($request->items as $item) {
                PurchaseItem::create([
                    'purchase_id' => $purchase->id,
                    'medicine_id' => $item['medicine_id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'tax_percentage' => $request->tax_percentage,
                    'tax_amount' => $item['tax_amount'] ?? 0,
                    'discount_percentage' => $request->discount_percentage,
                    'discount_amount' => $item['discount_amount'] ?? 0,
                    'total_amount' => $item['total_amount'],
                    // Expiry date and batch number will be set during receiving
                    'expiry_date' => null,
                    'batch_number' => null,
                ]);
            }

            DB::commit();

            session()->flash('success', 'Purchase order updated successfully.');
            return redirect()->route('inventory.purchases.show', $purchase);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating purchase order: ' . $e->getMessage());
            session()->flash('error', 'Error updating purchase order. Please try again.');
            return back()->withInput();
        }
    }

    public function destroy(Purchase $purchase)
    {
        if ($purchase->status !== 'pending') {
            session()->flash('error', 'Only pending purchase orders can be deleted.');
            return redirect()->route('inventory.purchases.show', $purchase);
        }

        try {
            DB::beginTransaction();

            // Delete purchase items
            $purchase->items()->delete();

            // Delete purchase payments
            $purchase->payments()->delete();

            // Delete purchase
            $purchase->delete();

            DB::commit();

            session()->flash('success', 'Purchase order deleted successfully.');
            return redirect()->route('inventory.purchases.index');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting purchase order: ' . $e->getMessage());
            session()->flash('error', 'Error deleting purchase order. Please try again.');
            return back();
        }
    }

    public function showReceive(Purchase $purchase)
    {
        if (!in_array($purchase->status, ['ordered', 'partially_received'])) {
            session()->flash('error', 'Only ordered or partially received purchase orders can be received.');
            return redirect()->route('inventory.purchases.show', $purchase);
        }

        return view('inventory.purchases.receive', compact('purchase'));
    }



    public function order(Purchase $purchase)
    {
        if ($purchase->status !== 'pending') {
            session()->flash('error', 'Only pending purchase orders can be ordered.');
            return redirect()->route('inventory.purchases.show', $purchase);
        }

        try {
            $purchase->update([
                'status' => 'ordered',
                'updated_by' => auth()->id()
            ]);

            session()->flash('success', 'Purchase order has been placed successfully.');
            return redirect()->route('inventory.purchases.show', $purchase);

        } catch (\Exception $e) {
            Log::error('Error ordering purchase order: ' . $e->getMessage());
            session()->flash('error', 'Error placing purchase order. Please try again.');
            return back();
        }
    }

    public function cancel(Purchase $purchase)
    {
        if (!in_array($purchase->status, ['pending', 'ordered'])) {
            session()->flash('error', 'Only pending or ordered purchase orders can be cancelled.');
            return redirect()->route('inventory.purchases.show', $purchase);
        }

        try {
            $purchase->update([
                'status' => 'cancelled',
                'updated_by' => auth()->id()
            ]);

            session()->flash('success', 'Purchase order cancelled successfully.');
            return redirect()->route('inventory.purchases.show', $purchase);

        } catch (\Exception $e) {
            Log::error('Error cancelling purchase order: ' . $e->getMessage());
            session()->flash('error', 'Error cancelling purchase order. Please try again.');
            return back();
        }
    }
} 