<?php

namespace App\Http\Controllers\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Inventory\UnitConversion;
use App\Services\Inventory\UnitConversionService;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class UnitConversionController extends Controller
{
    protected $unitConversionService;

    public function __construct(UnitConversionService $unitConversionService)
    {
        $this->unitConversionService = $unitConversionService;
    }

    public function index()
    {
        $baseUnits = $this->unitConversionService->getBaseUnits();
        $allUnits = $this->unitConversionService->getAllUnits();
        $conversionRules = $this->unitConversionService->getPaginatedConversionRules();

        return view('inventory.units.index', compact('baseUnits', 'allUnits', 'conversionRules'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'from_unit_id' => 'required|exists:unit_types,id',
            'to_unit_id' => 'required|exists:unit_types,id|different:from_unit_id',
            'conversion_factor' => 'required|numeric|gt:0',
        ]);

        try {
            $this->unitConversionService->createConversionRule($validated, auth()->id());

            return redirect()
                ->route('inventory.units.index')
                ->with('success', 'Unit conversion created successfully.');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Error creating unit conversion. Please try again.');
        }
    }

    public function edit(UnitConversion $unitConversion)
    {
        if (request()->ajax()) {
            return response()->json([
                'id' => $unitConversion->id,
                'from_unit_id' => $unitConversion->from_unit_id,
                'to_unit_id' => $unitConversion->to_unit_id,
                'from_unit_name' => $unitConversion->fromUnit->name,
                'from_unit_code' => $unitConversion->fromUnit->code,
                'to_unit_name' => $unitConversion->toUnit->name,
                'to_unit_code' => $unitConversion->toUnit->code,
                'conversion_factor' => $unitConversion->conversion_factor,
                'status' => $unitConversion->status,
            ]);
        }
        
        return redirect()->route('inventory.units.index');
    }

    public function update(Request $request, UnitConversion $unitConversion)
    {
        $validated = $request->validate([
            'conversion_factor' => 'required|numeric|gt:0',
            'status' => 'required|in:active,inactive',
        ]);

        try {
            $this->unitConversionService->updateConversionRule(
                $unitConversion,
                $validated,
                auth()->id()
            );

            return redirect()
                ->route('inventory.units.index')
                ->with('success', 'Unit conversion updated successfully.');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Error updating unit conversion. Please try again.');
        }
    }

    public function destroy(UnitConversion $unitConversion)
    {
        try {
            $this->unitConversionService->deleteConversionRule($unitConversion);

            return redirect()
                ->route('inventory.units.index')
                ->with('success', 'Unit conversion deleted successfully.');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', 'Error deleting unit conversion. Please try again.');
        }
    }

    public function convert(Request $request)
    {
        $validated = $request->validate([
            'value' => 'required|numeric',
            'from_unit_id' => 'required|exists:unit_types,id',
            'to_unit_id' => 'required|exists:unit_types,id',
        ]);

        try {
            $result = $this->unitConversionService->convertUnits($validated);
            return response()->json($result);
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw ValidationException::withMessages([
                'conversion' => ['Error converting units.'],
            ]);
        }
    }
}
