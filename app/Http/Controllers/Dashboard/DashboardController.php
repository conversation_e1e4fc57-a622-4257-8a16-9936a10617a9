<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Sales\Sale;
use App\Models\Sales\SaleItem;
use App\Models\Inventory\Medicine;
use App\Models\Customers\Customer;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\View\View;

class DashboardController extends Controller
{
    public function index()
    {
        try {
            $today = now()->startOfDay();
            $yesterday = now()->subDay()->startOfDay();
            $startOfWeek = now()->startOfWeek();
            $startOfMonth = now()->startOfMonth();
            $thirtyDaysAgo = now()->subDays(30)->startOfDay();

            // Add debug logging
            Log::info('Fetching dashboard data with dates:', [
                'today' => $today->toDateTimeString(),
                'yesterday' => $yesterday->toDateTimeString(),
                'startOfWeek' => $startOfWeek->toDateTimeString(),
                'startOfMonth' => $startOfMonth->toDateTimeString(),
                'thirtyDaysAgo' => $thirtyDaysAgo->toDateTimeString(),
            ]);

            $data = [
                'sales_stats' => $this->getSalesStats($today, $yesterday, $startOfWeek, $startOfMonth),
                'inventory_stats' => $this->getInventoryStats(),
                'customer_stats' => $this->getCustomerStats($startOfMonth),
                'sales_trend' => $this->getSalesTrend($thirtyDaysAgo),
                'payment_methods' => $this->getPaymentMethodsDistribution($startOfMonth),
                'recent_orders' => $this->getRecentOrders(),
                'top_products' => $this->getTopProducts($thirtyDaysAgo),
            ];

            // Debug logging for data
            Log::info('Dashboard data:', array_map(function($item) {
                if (is_object($item)) {
                    return 'Object: ' . get_class($item) . ' (Count: ' . ($item instanceof \Illuminate\Support\Collection ? $item->count() : 'N/A') . ')';
                } elseif (is_array($item)) {
                    return 'Array with keys: ' . implode(', ', array_keys($item));
                }
                return gettype($item);
            }, $data));

            return view('dashboard.admin', $data);
        } catch (\Exception $e) {
            Log::error('Error loading dashboard: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            
            // Return a view with empty data in the expected format
            return view('dashboard.admin', [
                'sales_stats' => [
                    'today' => 0,
                    'yesterday' => 0,
                    'week' => 0,
                    'month' => 0,
                    'total_orders' => 0,
                    'pending_orders' => 0
                ],
                'inventory_stats' => [
                    'total_medicines' => 0,
                    'low_stock' => 0,
                    'out_of_stock' => 0,
                    'expiring_soon' => 0
                ],
                'customer_stats' => [
                    'total_customers' => 0,
                    'active_customers' => 0,
                    'loyalty_members' => 0,
                    'new_customers_this_month' => 0
                ],
                'sales_trend' => collect([
                    ['date' => now()->format('M d'), 'total_sales' => 0, 'total_orders' => 0]
                ]),
                'payment_methods' => collect([
                    ['payment_method' => 'No Data', 'count' => 0]
                ]),
                'recent_orders' => collect(),
                'top_products' => collect(),
                'error_message' => $e->getMessage()
            ]);
        }
    }

    private function getSalesStats(Carbon $today, Carbon $yesterday, Carbon $startOfWeek, Carbon $startOfMonth): array
    {
        try {
            Log::info('Getting sales stats');
            return [
                'today' => Sale::whereDate('created_at', $today)->sum('total_amount') ?? 0,
                'yesterday' => Sale::whereDate('created_at', $yesterday)->sum('total_amount') ?? 0,
                'week' => Sale::whereBetween('created_at', [$startOfWeek, now()])->sum('total_amount') ?? 0,
                'month' => Sale::whereBetween('created_at', [$startOfMonth, now()])->sum('total_amount') ?? 0,
                'total_orders' => Sale::count(),
                'pending_orders' => Sale::where('payment_status', 'pending')->count(),
            ];
        } catch (\Exception $e) {
            Log::error('Error getting sales stats: ' . $e->getMessage());
            throw $e;
        }
    }

    private function getInventoryStats(): array
    {
        try {
            Log::info('Getting inventory stats');
            return [
                'total_medicines' => Medicine::count(),
                'low_stock' => DB::table('medicines')
                    ->join('inventories', 'medicines.id', '=', 'inventories.medicine_id')
                    ->where('inventories.quantity', '<=', DB::raw('medicines.minimum_stock'))
                    ->distinct()
                    ->count('medicines.id'),
                'out_of_stock' => DB::table('medicines')
                    ->leftJoin('inventories', 'medicines.id', '=', 'inventories.medicine_id')
                    ->where(function($query) {
                        $query->whereNull('inventories.id')
                            ->orWhere('inventories.quantity', '=', 0);
                    })
                    ->distinct()
                    ->count('medicines.id'),
                'expiring_soon' => DB::table('medicines')
                    ->join('inventories', 'medicines.id', '=', 'inventories.medicine_id')
                    ->where('inventories.quantity', '>', 0)
                    ->whereDate('inventories.expiry_date', '<=', now()->addMonths(3))
                    ->whereDate('inventories.expiry_date', '>', now())
                    ->distinct()
                    ->count('medicines.id'),
            ];
        } catch (\Exception $e) {
            Log::error('Error getting inventory stats: ' . $e->getMessage());
            throw $e;
        }
    }

    private function getCustomerStats(Carbon $startOfMonth): array
    {
        try {
            Log::info('Getting customer stats');
            return [
                'total_customers' => Customer::count(),
                'active_customers' => Customer::where('status', 'active')->count(),
                'loyalty_members' => Customer::where('loyalty_points', '>', 0)->count(),
                'new_customers_this_month' => Customer::whereBetween('created_at', [$startOfMonth, now()])->count(),
            ];
        } catch (\Exception $e) {
            Log::error('Error getting customer stats: ' . $e->getMessage());
            throw $e;
        }
    }

    private function getTopProducts(Carbon $thirtyDaysAgo)
    {
        try {
            Log::info('Getting top products');
            return DB::table('sale_items')
                ->join('medicines', 'sale_items.medicine_id', '=', 'medicines.id')
                ->leftJoin('categories', 'medicines.category_id', '=', 'categories.id')
                ->leftJoin('sales', 'sale_items.sale_id', '=', 'sales.id')
                ->whereNull('sales.deleted_at')
                ->select(
                    'medicines.id as medicine_id',
                    'medicines.name as medicine_name',
                    'categories.name as category_name',
                    DB::raw('SUM(sale_items.quantity) as total_quantity'),
                    DB::raw('SUM(sale_items.quantity * sale_items.unit_price) as total_amount')
                )
                ->whereBetween('sale_items.created_at', [$thirtyDaysAgo, now()])
                ->groupBy('medicines.id', 'medicines.name', 'categories.name')
                ->orderByDesc('total_quantity')
                ->limit(5)
                ->get();
        } catch (\Exception $e) {
            Log::error('Error getting top products: ' . $e->getMessage());
            throw $e;
        }
    }

    private function getSalesTrend(Carbon $thirtyDaysAgo)
    {
        try {
            Log::info('Getting sales trend');
            $sales = DB::table('sales')
                ->select(
                    DB::raw('DATE(created_at) as date'),
                    DB::raw('COUNT(*) as total_orders'),
                    DB::raw('COALESCE(SUM(total_amount), 0) as total_sales')
                )
                ->whereBetween('created_at', [$thirtyDaysAgo, now()])
                ->groupBy('date')
                ->orderBy('date')
                ->get()
                ->keyBy('date');

            // Fill in missing dates with zero values
            $dates = collect();
            for ($date = clone $thirtyDaysAgo; $date <= now(); $date->addDay()) {
                $dateStr = $date->format('Y-m-d');
                if (!isset($sales[$dateStr])) {
                    $dates->push((object)[
                        'date' => $date->format('M d'),
                        'total_orders' => 0,
                        'total_sales' => 0
                    ]);
                } else {
                    $sale = $sales[$dateStr];
                    $sale->date = Carbon::parse($sale->date)->format('M d');
                    $dates->push($sale);
                }
            }

            return $dates;
        } catch (\Exception $e) {
            Log::error('Error getting sales trend: ' . $e->getMessage());
            throw $e;
        }
    }

    private function getPaymentMethodsDistribution(Carbon $startOfMonth)
    {
        try {
            Log::info('Getting payment methods distribution');
            $methods = DB::table('sales')
                ->select('payment_method', DB::raw('COUNT(*) as count'))
                ->whereNotNull('payment_method')
                ->where('payment_method', '!=', '')
                ->whereBetween('created_at', [$startOfMonth, now()])
                ->groupBy('payment_method')
                ->get()
                ->map(function ($method) {
                    $method->payment_method = ucfirst(str_replace('_', ' ', $method->payment_method));
                    return $method;
                });

            // If no payment methods found, return a default "No Data" entry
            if ($methods->isEmpty()) {
                return collect([
                    (object)[
                        'payment_method' => 'No Data',
                        'count' => 0
                    ]
                ]);
            }

            return $methods;
        } catch (\Exception $e) {
            Log::error('Error getting payment methods: ' . $e->getMessage());
            throw $e;
        }
    }

    private function getRecentOrders()
    {
        try {
            Log::info('Getting recent orders');
            return DB::table('sales')
                ->leftJoin('customers', 'sales.customer_id', '=', 'customers.id')
                ->select(
                    'sales.id',
                    'sales.invoice_number',
                    'sales.total_amount',
                    'sales.payment_status',
                    'sales.payment_method',
                    'sales.created_at',
                    'customers.name as customer_name'
                )
                ->whereNull('sales.deleted_at')
                ->orderByDesc('sales.created_at')
                ->limit(5)
                ->get()
                ->map(function($order) {
                    $order->created_at = Carbon::parse($order->created_at);
                    return $order;
                });
        } catch (\Exception $e) {
            Log::error('Error getting recent orders: ' . $e->getMessage());
            throw $e;
        }
    }
}
