<?php

namespace App\Http\Controllers\Reports;

use App\Http\Controllers\Controller;
use App\Models\Sales\Sale;
use App\Models\Sales\SaleItem;
use App\Models\Inventory\Medicine;
use App\Models\Inventory\Supplier;
use App\Models\Inventory\Purchase;
use App\Models\Inventory\SupplierPayment;
use App\Services\Reporting\ReportService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\Inventory\ProfitLoss;

class ReportsController extends Controller
{
    protected $reportService;

    public function __construct(ReportService $reportService)
    {
        $this->reportService = $reportService;
    }

    public function salesReport(Request $request)
    {
        // Default to a wider date range to catch more data (similar to profit loss report)
        $startDate = $request->input('start_date')
            ? Carbon::parse($request->input('start_date'))->startOfDay()
            : Carbon::now()->subMonths(3)->startOfMonth();
        $endDate = $request->input('end_date')
            ? Carbon::parse($request->input('end_date'))->endOfDay()
            : Carbon::now()->endOfDay();

        // Include ALL sales (both registered customers and walk-in customers)
        $sales = Sale::with(['customer', 'items.medicine'])
            ->whereBetween('created_at', [$startDate, $endDate])
            ->orderBy('created_at', 'desc')
            ->get();

        $totalSales = $sales->sum('total_amount');
        $totalItems = $sales->sum(function($sale) {
            return $sale->items->sum('quantity');
        });

        $topProducts = SaleItem::with('medicine')
            ->select('medicine_id',
                DB::raw('SUM(quantity) as total_quantity'),
                DB::raw('SUM(quantity * unit_price) as total_amount'))
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('medicine_id')
            ->orderByDesc('total_quantity')
            ->limit(10)
            ->get();

        return view('reports.sales', compact('sales', 'totalSales', 'totalItems', 'topProducts', 'startDate', 'endDate'));
    }

    public function dueReport(Request $request)
    {
        // Default to a wider date range to catch more data
        $startDate = $request->input('start_date')
            ? Carbon::parse($request->input('start_date'))->startOfDay()
            : Carbon::now()->subMonths(3)->startOfMonth();
        $endDate = $request->input('end_date')
            ? Carbon::parse($request->input('end_date'))->endOfDay()
            : Carbon::now()->endOfDay();

        // Include both pending and partial payments as due sales
        $dueSales = Sale::with(['customer'])
            ->whereNotNull('customer_id')
            ->whereIn('payment_status', ['pending', 'partial'])
            ->whereBetween('created_at', [$startDate, $endDate])
            ->orderBy('created_at', 'desc')
            ->get();

        // Calculate total due amount more robustly
        $totalDue = $dueSales->sum(function($sale) {
            return max(0, $sale->total_amount - ($sale->paid_amount ?? 0));
        });

        return view('reports.due', compact('dueSales', 'totalDue', 'startDate', 'endDate'));
    }

    public function inventoryReport(Request $request)
    {
        // Get filter parameters
        $statusFilter = $request->input('status', 'all');
        $categoryFilter = $request->input('category_id');
        $manufacturerFilter = $request->input('manufacturer_id');
        $searchQuery = $request->input('search');

        // Build base query with proper relationship loading
        $query = Medicine::with(['category', 'manufacturer', 'inventories.location'])
            ->where('status', 'active')
            ->whereNotNull('name'); // Ensure medicine has a name

        // Apply category filter
        if ($categoryFilter) {
            $query->where('category_id', $categoryFilter);
        }

        // Apply manufacturer filter
        if ($manufacturerFilter) {
            $query->where('manufacturer_id', $manufacturerFilter);
        }

        // Apply search filter
        if ($searchQuery) {
            $query->where(function($q) use ($searchQuery) {
                $q->where('name', 'like', "%{$searchQuery}%")
                  ->orWhere('generic_name', 'like', "%{$searchQuery}%")
                  ->orWhereHas('category', function($categoryQuery) use ($searchQuery) {
                      $categoryQuery->where('name', 'like', "%{$searchQuery}%");
                  })
                  ->orWhereHas('manufacturer', function($manufacturerQuery) use ($searchQuery) {
                      $manufacturerQuery->where('name', 'like', "%{$searchQuery}%");
                  });
            });
        }

        // Get all medicines with calculated data
        $allMedicines = $query->get()->filter(function($medicine) {
            // Filter out any medicines that might have null relationships
            return $medicine && $medicine->name;
        })->map(function($medicine) {
            $totalStock = $medicine->inventories->sum('quantity');
            $totalValue = $totalStock * ($medicine->selling_price ?? $medicine->unit_price ?? 0);

            // Get expiry information
            $expiringBatches = $medicine->inventories
                ->where('quantity', '>', 0)
                ->where('expiry_date', '<=', now()->addMonths(3))
                ->where('expiry_date', '>', now());

            $expiredBatches = $medicine->inventories
                ->where('quantity', '>', 0)
                ->where('expiry_date', '<=', now());

            $nearestExpiryDate = $medicine->inventories
                ->where('quantity', '>', 0)
                ->where('expiry_date', '>', now())
                ->min('expiry_date');

            // Determine stock status
            $stockStatus = $this->determineStockStatus($medicine, $totalStock, $expiringBatches->count(), $expiredBatches->count());

            return [
                'medicine' => $medicine,
                'total_stock' => $totalStock,
                'total_value' => $totalValue,
                'stock_status' => $stockStatus,
                'expiring_batches_count' => $expiringBatches->count(),
                'expired_batches_count' => $expiredBatches->count(),
                'nearest_expiry_date' => $nearestExpiryDate,
                'has_expiring_stock' => $expiringBatches->sum('quantity') > 0,
                'has_expired_stock' => $expiredBatches->sum('quantity') > 0,
            ];
        });

        // Apply status filter
        $medicines = $this->applyStatusFilter($allMedicines, $statusFilter);

        // Calculate statistics
        $statistics = $this->calculateInventoryStatistics($allMedicines);

        // Get filter options
        $categories = \App\Models\Inventory\Category::orderBy('name')->get();
        $manufacturers = \App\Models\Inventory\Manufacturer::orderBy('name')->get();

        return view('reports.inventory', compact(
            'medicines',
            'statistics',
            'categories',
            'manufacturers',
            'statusFilter',
            'categoryFilter',
            'manufacturerFilter',
            'searchQuery'
        ));
    }

    public function financialReport(Request $request)
    {
        // Default to a wider date range to catch more data
        $startDate = $request->input('start_date')
            ? Carbon::parse($request->input('start_date'))->startOfDay()
            : Carbon::now()->subMonths(3)->startOfMonth();
        $endDate = $request->input('end_date')
            ? Carbon::parse($request->input('end_date'))->endOfDay()
            : Carbon::now()->endOfDay();

        $salesData = Sale::whereBetween('created_at', [$startDate, $endDate])
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(total_amount) as total_sales'),
                DB::raw('SUM(paid_amount) as total_received'),
                DB::raw('COUNT(*) as number_of_sales')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $totalRevenue = $salesData->sum('total_sales');
        $totalReceived = $salesData->sum('total_received');
        $totalPending = $totalRevenue - $totalReceived;

        return view('reports.financial', compact(
            'salesData',
            'totalRevenue',
            'totalReceived',
            'totalPending',
            'startDate',
            'endDate'
        ));
    }

    public function profitLossReport(Request $request)
    {
        // Default to a wider date range to catch more data
        $startDate = $request->input('start_date', Carbon::now()->subMonths(3)->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->format('Y-m-d'));

        $transactions = ProfitLoss::with(['medicine', 'sale', 'purchase'])
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->orderBy('transaction_date', 'desc')
            ->paginate(20);

        // Get summary statistics using the model method
        $summary = ProfitLoss::getSummary($startDate, $endDate);

        return view('reports.profit_loss', [
            'transactions' => $transactions,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'totalRevenue' => $summary['total_revenue'],
            'totalCost' => $summary['total_cost'],
            'grossProfit' => $summary['gross_profit'],
            'netProfit' => $summary['net_profit']
        ]);
    }

    public function supplierPaymentsReport(Request $request)
    {
        $startDate = $request->input('start_date')
            ? Carbon::parse($request->input('start_date'))->startOfDay()
            : Carbon::now()->startOfMonth();
        $endDate = $request->input('end_date')
            ? Carbon::parse($request->input('end_date'))->endOfDay()
            : Carbon::now()->endOfDay();
        $supplierId = $request->input('supplier_id');

        $query = Supplier::with(['purchases' => function($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }, 'payments' => function($query) use ($startDate, $endDate) {
            $query->whereBetween('payment_date', [$startDate, $endDate]);
        }]);

        if ($supplierId) {
            $suppliers = $query->where('id', $supplierId)->get();
        } else {
            $suppliers = $query->get();
        }

        // Calculate totals
        $totalPurchases = $suppliers->sum(function($supplier) {
            return $supplier->purchases->sum('final_amount');
        });

        $totalPaid = $suppliers->sum(function($supplier) {
            return $supplier->payments->sum('amount');
        });

        $totalDue = $totalPurchases - $totalPaid;

        // Get all suppliers for the dropdown filter
        $allSuppliers = Supplier::orderBy('name')->get();

        return view('reports.supplier_payments', compact(
            'suppliers',
            'allSuppliers',
            'totalPurchases',
            'totalPaid',
            'totalDue',
            'startDate',
            'endDate',
            'supplierId'
        ));
    }

    /**
     * Determine stock status based on various criteria
     */
    private function determineStockStatus($medicine, $totalStock, $expiringBatchesCount, $expiredBatchesCount)
    {
        // Check if expired stock exists
        if ($expiredBatchesCount > 0) {
            return 'expired';
        }

        // Check if out of stock
        if ($totalStock <= 0) {
            return 'out_of_stock';
        }

        // Check if expiring soon
        if ($expiringBatchesCount > 0) {
            return 'expiring_soon';
        }

        // Check if low stock (use minimum_stock if available, otherwise default to 10)
        $minimumStock = $medicine->minimum_stock ?? 10;
        if ($totalStock <= $minimumStock) {
            return 'low_stock';
        }

        return 'in_stock';
    }

    /**
     * Apply status filter to medicines collection
     */
    private function applyStatusFilter($medicines, $statusFilter)
    {
        if ($statusFilter === 'all') {
            return $medicines;
        }

        return $medicines->filter(function($item) use ($statusFilter) {
            switch ($statusFilter) {
                case 'in_stock':
                    return $item['stock_status'] === 'in_stock';
                case 'low_stock':
                    return $item['stock_status'] === 'low_stock';
                case 'out_of_stock':
                    return $item['stock_status'] === 'out_of_stock';
                case 'expiring_soon':
                    return $item['stock_status'] === 'expiring_soon';
                case 'expired':
                    return $item['stock_status'] === 'expired';
                default:
                    return true;
            }
        });
    }

    /**
     * Calculate inventory statistics
     */
    private function calculateInventoryStatistics($medicines)
    {
        $totalInventoryValue = $medicines->sum('total_value');
        $totalProducts = $medicines->count();
        $inStockItems = $medicines->where('stock_status', 'in_stock');
        $lowStockItems = $medicines->where('stock_status', 'low_stock');
        $outOfStockItems = $medicines->where('stock_status', 'out_of_stock');
        $expiringSoonItems = $medicines->where('stock_status', 'expiring_soon');
        $expiredItems = $medicines->where('stock_status', 'expired');

        return [
            'total_inventory_value' => $totalInventoryValue,
            'total_products' => $totalProducts,
            'in_stock_count' => $inStockItems->count(),
            'low_stock_count' => $lowStockItems->count(),
            'out_of_stock_count' => $outOfStockItems->count(),
            'expiring_soon_count' => $expiringSoonItems->count(),
            'expired_count' => $expiredItems->count(),
        ];
    }
}
