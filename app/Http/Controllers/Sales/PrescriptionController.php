<?php

namespace App\Http\Controllers\Sales;

use App\Http\Controllers\Controller;
use App\Models\Sales\Prescription;
use Illuminate\Http\Request;

class PrescriptionController extends Controller
{
    /**
     * Display a listing of the prescriptions.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $prescriptions = Prescription::with(['sale.customer'])
            ->latest()
            ->paginate(10);

        return view('prescriptions.index', compact('prescriptions'));
    }

    /**
     * Display the specified prescription.
     *
     * @param  \App\Models\Sales\Prescription  $prescription
     * @return \Illuminate\View\View
     */
    public function show(Prescription $prescription)
    {
        $prescription->load(['sale.customer', 'sale.items.medicine']);
        
        return view('prescriptions.show', compact('prescription'));
    }
}
