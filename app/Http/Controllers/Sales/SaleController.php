<?php

namespace App\Http\Controllers\Sales;

use App\Http\Controllers\Controller;
use App\Models\Sales\Sale;
use App\Models\Customers\Customer;
use App\Models\Inventory\Medicine;
use App\Services\LoyaltyService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class SaleController extends Controller
{
    protected function generateInvoiceNumber()
    {
        // Get the latest invoice number from the database
        $latestInvoice = Sale::orderBy('id', 'desc')->first();
        
        if (!$latestInvoice) {
            // If no invoices exist, start with INV-000001
            return 'INV-000001';
        }
        
        // Extract the numeric part of the latest invoice number
        $latestNumber = intval(substr($latestInvoice->invoice_number, 4));
        
        // Generate new number with padding
        $newNumber = $latestNumber + 1;
        $paddedNumber = str_pad($newNumber, 6, '0', STR_PAD_LEFT);
        
        // Verify uniqueness and increment if necessary
        while (Sale::where('invoice_number', 'INV-' . $paddedNumber)->exists()) {
            $newNumber++;
            $paddedNumber = str_pad($newNumber, 6, '0', STR_PAD_LEFT);
        }
        
        return 'INV-' . $paddedNumber;
    }

    public function index()
    {
        $sales = Sale::with(['customer', 'items', 'createdBy'])
            ->latest()
            ->paginate(10);

        return view('sales.index', compact('sales'));
    }

    public function create()
    {
        $customers = Customer::orderBy('name')->get();
        
        // Fetch unique medicines with their inventory information
        $medicines = Medicine::select('medicines.*')
            ->with(['inventories' => function($query) {
                $query->where('quantity', '>', 0)
                    ->whereDate('expiry_date', '>', now())
                    ->orderBy('expiry_date', 'asc')
                    ->with('location');
            }])
            ->where('status', 'active')
            ->where('is_active', true)
            ->orderBy('name')
            ->distinct()
            ->get()
            ->map(function ($medicine) {
                // Group all batches for this medicine
                $batches = $medicine->inventories->map(function ($inventory) use ($medicine) {
                    return [
                        'number' => $inventory->batch_number,
                        'expiry' => $inventory->expiry_date,
                        'expiry_formatted' => Carbon::parse($inventory->expiry_date)->format('d M Y'),
                        'quantity' => $inventory->quantity,
                        'price' => $medicine->retail_price_unit,
                        'location' => $inventory->location ? $inventory->location->name : null,
                        'rack_number' => $inventory->rack_number,
                        'bin_location' => $inventory->bin_location
                    ];
                })->sortBy('expiry')->values();
                
                // Get the earliest expiry batch
                $earliestBatch = $batches->first();
                
                // Get enabled units
                $enabledUnits = is_string($medicine->enabled_units) 
                    ? json_decode($medicine->enabled_units) 
                    : ($medicine->enabled_units ?: ['piece']);
                
                // Format prices to ensure they are numeric
                $retailPriceBox = floatval($medicine->retail_price_box);
                $retailPriceStrip = floatval($medicine->retail_price_strip);
                $retailPriceUnit = floatval($medicine->retail_price_unit);
                
                // Get total stock
                $totalStock = $medicine->inventories->sum('quantity');
                
                // Get storage location info
                $storageLocation = null;
                if ($earliestBatch) {
                    $locationParts = [];
                    if (isset($earliestBatch['location'])) {
                        $locationParts[] = $earliestBatch['location'];
                    }
                    if (isset($earliestBatch['rack_number']) && $earliestBatch['rack_number']) {
                        $locationParts[] = "Rack: " . $earliestBatch['rack_number'];
                    }
                    if (isset($earliestBatch['bin_location']) && $earliestBatch['bin_location']) {
                        $locationParts[] = "Bin: " . $earliestBatch['bin_location'];
                    }
                    if (!empty($locationParts)) {
                        $storageLocation = implode(' | ', $locationParts);
                    }
                }
                
                return [
                    'id' => $medicine->id,
                    'name' => $medicine->name,
                    'generic_name' => $medicine->generic_name,
                    'dosage' => $medicine->dosage,
                    'selling_price' => $retailPriceUnit,
                    'retail_price_box' => $retailPriceBox,
                    'retail_price_strip' => $retailPriceStrip,
                    'retail_price_unit' => $retailPriceUnit,
                    'strips_per_box' => intval($medicine->strips_per_box),
                    'pieces_per_strip' => intval($medicine->pieces_per_strip),
                    'enabled_units' => $enabledUnits,
                    'batches' => $batches,
                    'default_batch' => $earliestBatch ? $earliestBatch['number'] : null,
                    'default_price' => $earliestBatch ? $retailPriceUnit : 0,
                    'total_stock' => $totalStock,
                    'storage_location' => $storageLocation
                ];
            })
            ->filter(function ($medicine) {
                return count($medicine['batches']) > 0;
            })
            ->values();

        return view('sales.create', compact('customers', 'medicines'));
    }

    public function store(Request $request)
    {
        DB::beginTransaction();
        try {
            // Generate unique invoice number
            $invoiceNumber = $this->generateInvoiceNumber();
            
            // Verify one more time before using
            if (Sale::where('invoice_number', $invoiceNumber)->exists()) {
                throw new \Exception('Failed to generate unique invoice number. Please try again.');
            }

            $request->validate([
                'customer_id' => 'nullable|exists:customers,id',
                'items' => 'required|array|min:1',
                'items.*.medicine_id' => 'required|exists:medicines,id',
                'items.*.batch_number' => 'required|string',
                'items.*.quantity' => 'required|integer|min:1',
                'items.*.unit' => 'required|string|in:box,strip,piece,unit',
                'payment_method' => 'required|string',
                'paid_amount' => 'required|numeric|min:0',
                'due_date' => 'required_if:paid_amount,0|nullable|date|after_or_equal:today',
                'loyalty_points_to_redeem' => 'nullable|integer|min:0',
                'prescription.doctor_name' => 'nullable|string|max:255',
                'prescription.hospital_name' => 'nullable|string|max:255',
                'prescription.date' => 'nullable|date',
                'prescription.image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:10240'
            ]);

            try {
                // Calculate totals
                $totalAmount = 0;
                $totalTax = 0;
                $totalDiscount = 0;

                foreach ($request->items as $item) {
                    $medicine = Medicine::findOrFail($item['medicine_id']);
                    $quantity = $item['quantity'];
                    $unit = $item['unit'];
                    
                    // Convert quantity to pieces based on unit
                    $quantityInPieces = $this->convertToPieces($quantity, $unit, $medicine);
                    
                    // Check stock availability
                    $inventory = $medicine->inventories()
                        ->where('batch_number', $item['batch_number'])
                        ->where('quantity', '>=', $quantityInPieces)
                        ->firstOrFail();

                    // Get unit price based on selected unit
                    $unitPrice = $this->getUnitPrice($unit, $medicine);
                    
                    $itemTotal = $unitPrice * $quantity;
                    $itemTax = $itemTotal * ($medicine->tax_rate ?? 0) / 100;
                    $itemDiscount = $itemTotal * ($item['discount'] ?? 0) / 100;

                    $totalAmount += $itemTotal;
                    $totalTax += $itemTax;
                    $totalDiscount += $itemDiscount;
                }

                // Apply invoice discount if any
                if ($request->filled('invoice_discount_value')) {
                    $invoiceDiscount = $request->invoice_discount_type === 'percentage'
                        ? $totalAmount * ($request->invoice_discount_value / 100)
                        : min($request->invoice_discount_value, $totalAmount);
                    $totalDiscount += $invoiceDiscount;
                }

                // Process loyalty points redemption
                $loyaltyDiscount = 0;
                $loyaltyTransaction = null;
                if ($request->filled('loyalty_points_to_redeem') && $request->customer_id) {
                    $pointsToRedeem = (int) $request->loyalty_points_to_redeem;
                    if ($pointsToRedeem > 0) {
                        $customer = Customer::findOrFail($request->customer_id);

                        // Validate customer has enough points
                        if ($customer->loyalty_points >= $pointsToRedeem) {
                            // Calculate discount (100 points = 1 unit of currency)
                            $loyaltyDiscount = $pointsToRedeem / 100;

                            // Ensure loyalty discount doesn't exceed remaining total
                            $subtotalAfterInvoiceDiscount = $totalAmount + $totalTax - $totalDiscount;
                            $loyaltyDiscount = min($loyaltyDiscount, $subtotalAfterInvoiceDiscount);

                            // Add to total discount
                            $totalDiscount += $loyaltyDiscount;
                        }
                    }
                }

                // Calculate final amounts
                $finalTotal = $totalAmount + $totalTax - $totalDiscount;
                $paidAmount = min($request->paid_amount, $finalTotal);
                $dueAmount = max(0, $finalTotal - $paidAmount);

                // Create sale
                $sale = Sale::create([
                    'invoice_number' => $invoiceNumber,
                    'customer_id' => $request->customer_id,
                    'total_amount' => $finalTotal,
                    'paid_amount' => $paidAmount,
                    'due_amount' => $dueAmount,
                    'discount_amount' => $totalDiscount,
                    'tax_amount' => $totalTax,
                    'payment_method' => $request->payment_method,
                    'payment_status' => $paidAmount >= $finalTotal ? 'completed' : ($paidAmount > 0 ? 'partial' : 'pending'),
                    'due_date' => $request->due_date,
                    'created_by' => Auth::id()
                ]);

                // Process loyalty points redemption if applicable
                if ($loyaltyDiscount > 0 && $request->customer_id) {
                    $pointsToRedeem = (int) $request->loyalty_points_to_redeem;
                    $customer = Customer::findOrFail($request->customer_id);

                    $loyaltyService = new LoyaltyService();
                    $redemptionResult = $loyaltyService->redeemPoints($customer, $pointsToRedeem, $sale);

                    if (!$redemptionResult['success']) {
                        throw new \Exception('Failed to redeem loyalty points: ' . $redemptionResult['message']);
                    }

                    Log::info('Loyalty points redeemed during sale', [
                        'sale_id' => $sale->id,
                        'customer_id' => $customer->id,
                        'points_redeemed' => $pointsToRedeem,
                        'discount_applied' => $loyaltyDiscount
                    ]);
                }

                // Create initial payment record if paid amount > 0
                if ($paidAmount > 0) {
                    $sale->payments()->create([
                        'amount' => $paidAmount,
                        'payment_method' => $request->payment_method,
                        'recorded_by' => Auth::id(),
                        'notes' => 'Initial payment at sale'
                    ]);
                }

                // Create sale items and update inventory
                foreach ($request->items as $item) {
                    $medicine = Medicine::findOrFail($item['medicine_id']);
                    $quantity = $item['quantity'];
                    $unit = $item['unit'];
                    
                    // Convert quantity to pieces
                    $quantityInPieces = $this->convertToPieces($quantity, $unit, $medicine);
                    
                    // Get unit price based on selected unit
                    $unitPrice = $this->getUnitPrice($unit, $medicine);
                    
                    $inventory = $medicine->inventories()
                        ->where('batch_number', $item['batch_number'])
                        ->firstOrFail();

                    $sale->items()->create([
                        'medicine_id' => $item['medicine_id'],
                        'batch_number' => $item['batch_number'],
                        'quantity' => $quantity,
                        'unit' => $unit,
                        'unit_price' => $unitPrice,
                        'discount' => $item['discount'] ?? 0,
                        'tax_rate' => $medicine->tax_rate ?? 0,
                    ]);

                    // Decrease inventory by pieces
                    $inventory->decrement('quantity', $quantityInPieces);
                }

                // Create prescription if provided
                if ($request->has('prescription')) {
                    try {
                        $prescriptionData = [
                            'doctor_name' => $request->input('prescription.doctor_name'),
                            'hospital_name' => $request->input('prescription.hospital_name'),
                            'prescription_date' => $request->input('prescription.date'),
                        ];

                        $prescription = $sale->prescription()->create($prescriptionData);

                        if ($request->hasFile('prescription.image')) {
                            try {
                                $path = $request->file('prescription.image')
                                    ->store('prescriptions', 'public');
                                
                                if (!$path) {
                                    throw new \Exception('Failed to store prescription image');
                                }
                                
                                $prescription->update(['prescription_image' => $path]);
                                
                                Log::info('Prescription image stored successfully', [
                                    'sale_id' => $sale->id,
                                    'prescription_id' => $prescription->id,
                                    'path' => $path
                                ]);
                            } catch (\Exception $e) {
                                Log::error('Failed to store prescription image', [
                                    'sale_id' => $sale->id,
                                    'prescription_id' => $prescription->id,
                                    'error' => $e->getMessage()
                                ]);
                                throw new \Exception('Failed to store prescription image: ' . $e->getMessage());
                            }
                        }
                    } catch (\Exception $e) {
                        Log::error('Failed to create prescription', [
                            'sale_id' => $sale->id,
                            'error' => $e->getMessage()
                        ]);
                        throw new \Exception('Failed to create prescription: ' . $e->getMessage());
                    }
                }

                // Manually trigger profit loss creation after all items are created
                // This ensures the sale items exist when the observer logic runs
                try {
                    $saleObserver = new \App\Observers\SaleObserver();
                    $sale->load('items.medicine'); // Ensure items are loaded
                    $saleObserver->created($sale);
                    Log::info('Manually triggered profit loss creation for sale', [
                        'sale_id' => $sale->id,
                        'invoice_number' => $sale->invoice_number
                    ]);
                } catch (\Exception $e) {
                    Log::error('Failed to create profit loss records manually', [
                        'sale_id' => $sale->id,
                        'invoice_number' => $sale->invoice_number,
                        'error' => $e->getMessage()
                    ]);
                    // Don't fail the sale creation if profit loss creation fails
                }

                DB::commit();

                if ($request->wantsJson()) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Sale completed successfully.',
                        'redirect' => route('sales.show', $sale)
                    ]);
                }

                return redirect()->route('sales.show', $sale)
                    ->with('success', 'Sale completed successfully.');

            } catch (\Exception $e) {
                DB::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to process sale. ' . $e->getMessage()
                ], 422);
            }
            
            return back()->with('error', 'Failed to process sale. ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Convert quantity to pieces based on unit
     */
    private function convertToPieces(int $quantity, string $unit, Medicine $medicine): int
    {
        switch ($unit) {
            case 'box':
                return $quantity * $medicine->strips_per_box * $medicine->pieces_per_strip;
            case 'strip':
                return $quantity * $medicine->pieces_per_strip;
            case 'piece':
            case 'unit':
            default:
                return $quantity;
        }
    }

    /**
     * Get unit price based on selected unit
     */
    private function getUnitPrice(string $unit, Medicine $medicine): float
    {
        switch ($unit) {
            case 'box':
                return $medicine->retail_price_box;
            case 'strip':
                return $medicine->retail_price_strip;
            case 'piece':
            case 'unit':
            default:
                return $medicine->retail_price_unit;
        }
    }

    public function show(Sale $sale)
    {
        $sale->load(['customer', 'items.medicine', 'prescription', 'createdBy']);
        return view('sales.show', compact('sale'));
    }

    public function printPreview(Sale $sale)
    {
        return view('sales.print', compact('sale'));
    }
}
