<?php

namespace App\Http\Controllers\Prescriptions;

use App\Http\Controllers\Controller;
use App\Models\Sales\Prescription;
use Illuminate\Http\Request;

class PrescriptionController extends Controller
{
    /**
     * Display a listing of the prescriptions.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View|\Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Prescription::with(['sale.customer']);

        // Apply search filter
        if ($request->filled('search')) {
            $searchTerm = $request->input('search');
            $query->where(function($q) use ($searchTerm) {
                $q->where('doctor_name', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('hospital_name', 'LIKE', "%{$searchTerm}%")
                  ->orWhereHas('sale.customer', function($query) use ($searchTerm) {
                      $query->where('name', 'LIKE', "%{$searchTerm}%")
                            ->orWhere('phone', 'LIKE', "%{$searchTerm}%");
                  });
            });
        }

        // Apply date filter
        if ($request->filled('date')) {
            $query->whereDate('prescription_date', $request->input('date'));
        }

        // Return JSON response for search suggestions
        if ($request->filled('format') && $request->input('format') === 'json') {
            $prescriptions = $query->take(10)->get()->map(function ($prescription) {
                return [
                    'id' => $prescription->id,
                    'doctor_name' => $prescription->doctor_name,
                    'hospital_name' => $prescription->hospital_name,
                    'customer_name' => $prescription->sale?->customer?->name ?? 'Walk-in Customer',
                    'customer_phone' => $prescription->sale?->customer?->phone ?? null,
                    'date' => $prescription->prescription_date->format('d M Y')
                ];
            });
            return response()->json($prescriptions);
        }

        $prescriptions = $query->latest()->paginate(10);

        // Append query parameters to pagination links
        $prescriptions->appends($request->query());

        return view('prescriptions.index', compact('prescriptions'));
    }

    /**
     * Display the specified prescription.
     *
     * @param  \App\Models\Sales\Prescription  $prescription
     * @return \Illuminate\View\View
     */
    public function show(Prescription $prescription)
    {
        $prescription->load(['sale.customer', 'sale.items.medicine']);
        
        return view('prescriptions.show', compact('prescription'));
    }

    /**
     * Show the form for creating a new prescription.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function create(Request $request)
    {
        $customerId = $request->query('customer_id');
        
        return view('prescriptions.create', compact('customerId'));
    }

    /**
     * Print the specified prescription.
     *
     * @param  \App\Models\Sales\Prescription  $prescription
     * @return \Illuminate\View\View
     */
    public function print(Prescription $prescription)
    {
        $prescription->load(['sale.customer', 'sale.items.medicine']);
        
        return view('prescriptions.print', compact('prescription'));
    }
}
