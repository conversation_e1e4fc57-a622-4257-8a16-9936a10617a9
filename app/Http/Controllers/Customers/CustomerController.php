<?php

namespace App\Http\Controllers\Customers;

use App\Http\Controllers\Controller;
use App\Models\Customers\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CustomerController extends Controller
{
    public function index(Request $request)
    {
        $query = Customer::withCount('sales');
        
        // Apply search if provided
        if ($request->filled('search')) {
            $searchTerm = $request->input('search');
            $query->where(function($q) use ($searchTerm) {
                $q->where('name', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('email', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('phone', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('insurance_provider', 'LIKE', "%{$searchTerm}%");
            });
        }
        
        // Apply status filter
        if ($request->filled('status')) {
            if ($request->input('status') === 'active') {
                $query->whereHas('sales');
            } elseif ($request->input('status') === 'inactive') {
                $query->whereDoesntHave('sales');
            }
        }
        
        // Apply sorting
        switch ($request->input('sort')) {
            case 'recent':
                $query->orderBy('created_at', 'desc');
                break;
            case 'sales':
                $query->orderByDesc('sales_count');
                break;
            case 'name':
            default:
                $query->orderBy('name');
                break;
        }
        
        // Return JSON for search suggestions
        if ($request->filled('format') && $request->input('format') === 'json') {
            $customers = $query->limit(10)->get();
            return response()->json($customers);
        }
        
        $customers = $query->paginate(10);
        
        return view('customers.index', compact('customers'));
    }

    public function create()
    {
        return view('customers.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:customers,email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'insurance_provider' => 'nullable|string|max:255',
            'insurance_number' => 'nullable|string|max:255',
        ]);

        $customer = Customer::create($validated);

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Customer created successfully',
                'customer' => $customer
            ]);
        }

        return redirect()->route('customers.show', $customer)
            ->with('success', 'Customer created successfully.');
    }

    public function show(Customer $customer)
    {
        $customer->load(['sales' => function($query) {
            $query->latest()->take(10);
        }, 'prescriptions']);

        return view('customers.show', compact('customer'));
    }

    public function edit(Customer $customer)
    {
        return view('customers.edit', compact('customer'));
    }

    public function update(Request $request, Customer $customer)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:customers,email,' . $customer->id,
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'insurance_provider' => 'nullable|string|max:255',
            'insurance_number' => 'nullable|string|max:255',
        ]);

        $customer->update($validated);

        return redirect()->route('customers.show', $customer)
            ->with('success', 'Customer updated successfully.');
    }

    public function destroy(Customer $customer)
    {
        if ($customer->sales()->exists()) {
            return back()->with('error', 'Cannot delete customer with existing sales.');
        }

        $customer->delete();

        return redirect()->route('customers.index')
            ->with('success', 'Customer deleted successfully.');
    }

    public function loyalty()
    {
        // Get customers with their loyalty points and last purchase date
        $customers = Customer::select([
            'customers.id',
            'customers.name',
            'customers.email',
            'customers.phone',
            'customers.address',
            'customers.loyalty_points',
            'customers.insurance_provider',
            'customers.insurance_number',
            'customers.last_purchase_date',
            'customers.created_at',
            'customers.updated_at'
        ])
        ->with(['loyaltyTransactions' => function($query) {
            $query->latest()->limit(5);
        }])
        ->orderByDesc('customers.loyalty_points')
        ->paginate(10);

        // Calculate total points in the system (currently held by customers)
        $totalPoints = Customer::sum('loyalty_points');

        // Count active members (customers with points or recent purchases)
        $activeMembers = Customer::where(function($query) {
            $query->where('loyalty_points', '>', 0)
                  ->orWhere('last_purchase_date', '>=', now()->subMonths(6));
        })->count();

        // Calculate total points redeemed from loyalty transactions
        $pointsRedeemed = \App\Models\Customers\LoyaltyTransaction::where('type', 'redeemed')
            ->sum(\DB::raw('ABS(points)'));

        // Additional statistics
        $totalPointsEarned = \App\Models\Customers\LoyaltyTransaction::where('type', 'earned')
            ->sum('points');

        $totalDiscountGiven = \App\Models\Customers\LoyaltyTransaction::where('type', 'redeemed')
            ->sum('discount_applied');

        $goldMembers = Customer::where('loyalty_points', '>=', 1000)->count();

        return view('customers.loyalty', compact(
            'customers',
            'totalPoints',
            'activeMembers',
            'pointsRedeemed',
            'totalPointsEarned',
            'totalDiscountGiven',
            'goldMembers'
        ));
    }

    public function loyaltyHistory(Customer $customer)
    {
        $transactions = $customer->loyaltyTransactions()
            ->with(['sale', 'createdBy'])
            ->latest()
            ->paginate(10);

        $html = view('customers.partials.loyalty-history', compact('transactions'))->render();

        return response()->json([
            'customer' => $customer,
            'html' => $html
        ]);
    }
}
