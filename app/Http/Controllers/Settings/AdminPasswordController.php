<?php

namespace App\Http\Controllers\Settings;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;

class AdminPasswordController extends Controller
{
    /**
     * Verify admin password for settings access
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verify(Request $request)
    {
        // Rate limiting to prevent brute force attacks
        $key = 'admin-password-verify:' . $request->ip();
        
        if (RateLimiter::tooManyAttempts($key, 5)) {
            $seconds = RateLimiter::availableIn($key);
            return response()->json([
                'success' => false,
                'message' => "Too many attempts. Please try again in {$seconds} seconds."
            ], 429);
        }

        $request->validate([
            'password' => 'required|string'
        ]);

        $user = Auth::user();
        
        // Check if user has admin role or manage settings permission
        if (!$user->hasRole('admin') && !$user->can('manage settings')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access'
            ], 403);
        }

        // Verify password
        if (!Hash::check($request->password, $user->password)) {
            RateLimiter::hit($key, 300); // 5 minute lockout
            
            return response()->json([
                'success' => false,
                'message' => 'Invalid password'
            ], 401);
        }

        // Clear rate limiter on successful verification
        RateLimiter::clear($key);

        // Store verification in session with timestamp
        session([
            'settings_password_verified_at' => now(),
            'settings_password_verified_user' => $user->id
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Password verified successfully'
        ]);
    }

    /**
     * Check if current session has valid password verification
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkVerification(Request $request)
    {
        $verifiedAt = session('settings_password_verified_at');
        $verifiedUser = session('settings_password_verified_user');
        
        if (!$verifiedAt || !$verifiedUser || $verifiedUser !== Auth::id()) {
            return response()->json([
                'verified' => false
            ]);
        }

        // Check if verification is still valid (30 minutes)
        $expiryTime = $verifiedAt->addMinutes(30);
        
        if (now()->isAfter($expiryTime)) {
            // Clear expired verification
            session()->forget(['settings_password_verified_at', 'settings_password_verified_user']);
            
            return response()->json([
                'verified' => false
            ]);
        }

        return response()->json([
            'verified' => true,
            'expires_at' => $expiryTime->timestamp * 1000 // Convert to milliseconds for JavaScript
        ]);
    }

    /**
     * Clear password verification from session
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function clearVerification(Request $request)
    {
        session()->forget(['settings_password_verified_at', 'settings_password_verified_user']);
        
        return response()->json([
            'success' => true,
            'message' => 'Verification cleared'
        ]);
    }
}
