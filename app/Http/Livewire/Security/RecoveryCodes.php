<?php

namespace App\Livewire\Security;

use Livewire\Component;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use App\Models\Users\User;

class RecoveryCodes extends Component
{
    public $showingRecoveryCodes = false;
    public $confirmingRegeneration = false;
    public $recoveryCodes;

    public function mount()
    {
        $this->recoveryCodes = json_decode(
            decrypt(Auth::user()->two_factor_recovery_codes), true
        );
    }

    public function getRecoveryCodesProperty()
    {
        return json_decode(decrypt(Auth::user()->two_factor_recovery_codes), true) ?? [];
    }

    public function showRecoveryCodes()
    {
        $this->showingRecoveryCodes = true;
    }

    public function confirmRegenerateRecoveryCodes()
    {
        $this->confirmingRegeneration = true;
    }

    /**
     * Regenerate two-factor recovery codes
     *
     * @throws \RuntimeException When user is not authenticated or update fails
     * @return void
     */
    public function regenerateCodes()
    {
        /** @var User $user */
        $user = Auth::user();
        
        if (!$user) {
            throw new \RuntimeException('User not authenticated');
        }

        $this->recoveryCodes = collect(range(1, 8))->map(function () {
            return Str::random(10).'-'.Str::random(10);
        })->all();

        try {
            $user->forceFill([
                'two_factor_recovery_codes' => encrypt(json_encode($this->recoveryCodes)),
            ])->save();

            $this->confirmingRegeneration = false;
            $this->showingRecoveryCodes = true;

            $this->emit('codesRegenerated');
        } catch (\Exception $e) {
            throw new \RuntimeException('Failed to update recovery codes: ' . $e->getMessage());
        }
    }

    public function downloadRecoveryCodes()
    {
        $codes = $this->recoveryCodesProperty;
        $content = implode("\n", $codes);
        
        return response()->streamDownload(function () use ($content) {
            echo $content;
        }, 'recovery-codes.txt');
    }

    public function render()
    {
        return view('livewire.security.recovery-codes');
    }
}
