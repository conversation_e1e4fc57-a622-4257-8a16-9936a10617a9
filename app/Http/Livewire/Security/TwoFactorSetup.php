<?php

namespace App\Http\Livewire\Security;

use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Str;
use PragmaRX\Google2FA\Google2FA;
use BaconQrCode\Renderer\Color\Rgb;
use BaconQrCode\Renderer\ImageRenderer;
use BaconQrCode\Renderer\RendererStyle\Fill;
use BaconQrCode\Writer;
use BaconQrCode\Renderer\Image\SvgImageBackEnd;
use BaconQrCode\Renderer\RendererStyle\RendererStyle;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class TwoFactorSetup extends Component
{
    public $showingQrCode = false;
    public $showingRecoveryCodes = false;
    public $showingConfirmation = false;
    public $code = '';
    public $secret;
    public $recoveryCodes;
    protected $google2fa;

    protected $rules = [
        'code' => ['required', 'string', 'min:6', 'max:6'],
    ];

    public function mount()
    {
        $this->initGoogle2FA();
        
        if (! auth()->user()->two_factor_enabled) {
            $this->generateNewSecret();
            $this->showingQrCode = true;
        }
    }

    protected function initGoogle2FA()
    {
        if (!$this->google2fa) {
            $this->google2fa = new Google2FA();
        }
    }

    public function generateNewSecret()
    {
        $this->initGoogle2FA();
        $this->secret = $this->google2fa->generateSecretKey();
        $this->showingQrCode = true;
        $this->showingConfirmation = false;
        $this->code = '';
    }

    public function getQrCodeSvgProperty()
    {
        $this->initGoogle2FA();
        $renderer = new ImageRenderer(
            new RendererStyle(192, 1, null, null, Fill::uniformColor(new Rgb(255, 255, 255), new Rgb(45, 55, 72))),
            new SvgImageBackEnd()
        );

        $writer = new Writer($renderer);

        $companyName = config('app.name');
        $userEmail = Auth::user()->email;
        
        return $writer->writeString(
            $this->google2fa->getQRCodeUrl(
                $companyName,
                $userEmail,
                $this->secret
            )
        );
    }

    public function enableTwoFactorAuth()
    {
        $this->initGoogle2FA();
        $this->resetErrorBag();

        try {
            $valid = $this->google2fa->verifyKey(
                $this->secret, $this->code
            );

            if (! $valid) {
                throw ValidationException::withMessages([
                    'code' => [__('The provided two-factor authentication code was invalid.')],
                ]);
            }
        } catch (\Exception $e) {
            throw ValidationException::withMessages([
                'code' => [__('The provided two-factor authentication code was invalid.')],
            ]);
        }

        // Generate recovery codes
        $this->recoveryCodes = collect(range(1, 8))->map(function () {
            return Str::random(10).'-'.Str::random(10);
        })->all();

        $user = Auth::user();
        
        $user->forceFill([
            'two_factor_secret' => encrypt($this->secret),
            'two_factor_recovery_codes' => encrypt(json_encode($this->recoveryCodes)),
            'two_factor_confirmed_at' => now(),
            'two_factor_enabled' => true,
        ])->save();

        $this->showingQrCode = false;
        $this->showingRecoveryCodes = true;
    }

    public function disableTwoFactorAuth()
    {
        $user = Auth::user();
        
        $user->forceFill([
            'two_factor_secret' => null,
            'two_factor_recovery_codes' => null,
            'two_factor_confirmed_at' => null,
            'two_factor_enabled' => false,
        ])->save();

        $this->showingQrCode = false;
        $this->showingRecoveryCodes = false;
        $this->showingConfirmation = false;
        $this->code = '';
        $this->generateNewSecret();
    }

    public function confirmDisable()
    {
        $this->showingConfirmation = true;
    }

    public function render()
    {
        return view('livewire.security.two-factor-setup');
    }
}
