<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class FlashSessionDebugMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Log session data before processing the request
        if ($request->session()->has('success') || $request->session()->has('error')) {
            Log::info('Flash session data (before)', [
                'success' => $request->session()->get('success'),
                'error' => $request->session()->get('error'),
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'has_success' => $request->session()->has('success'),
                'has_error' => $request->session()->has('error')
            ]);
        }

        // Process the request
        $response = $next($request);

        // Log session data after processing the request
        if ($request->session()->has('success') || $request->session()->has('error')) {
            Log::info('Flash session data (after)', [
                'success' => $request->session()->get('success'),
                'error' => $request->session()->get('error'),
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'has_success' => $request->session()->has('success'),
                'has_error' => $request->session()->has('error'),
                'response_status' => $response->getStatusCode()
            ]);
        }

        return $response;
    }
}
