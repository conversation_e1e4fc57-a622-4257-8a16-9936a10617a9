<?php

namespace App\Http\Resources\Api\V1;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MedicineResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'generic_name' => $this->generic_name,
            'manufacturer' => $this->manufacturer,
            'manufacturer_id' => $this->manufacturer_id,
            'category_id' => $this->category_id,
            'category' => new CategoryResource($this->whenLoaded('category')),
            'unit_type' => $this->unit_type,
            'enabled_units' => $this->enabled_units,
            'minimum_stock' => $this->minimum_stock,
            'maximum_stock' => $this->maximum_stock,
            'controlled_substance' => $this->controlled_substance,
            'prescription_required' => $this->prescription_required,
            'inventories' => InventoryResource::collection($this->whenLoaded('inventories')),
            'is_active' => $this->is_active,
            'total_quantity' => $this->total_quantity,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
