<?php

namespace App\Http\Resources\Api\V1;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class InventoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'batch_number' => $this->batch_number,
            'quantity' => $this->quantity,
            'expiry_date' => $this->expiry_date,
            'unit_price' => $this->unit_price,
            'location' => $this->whenLoaded('location', function() {
                return new LocationResource($this->location);
            }),
            'medicine' => $this->whenLoaded('medicine', function() {
                return new MedicineResource($this->medicine);
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
