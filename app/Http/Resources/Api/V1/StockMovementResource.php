<?php

namespace App\Http\Resources\Api\V1;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StockMovementResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'medicine' => new MedicineResource($this->whenLoaded('medicine')),
            'source_location' => new LocationResource($this->whenLoaded('sourceLocation')),
            'destination_location' => new LocationResource($this->whenLoaded('destinationLocation')),
            'quantity' => $this->quantity,
            'batch_number' => $this->batch_number,
            'movement_date' => $this->movement_date,
            'notes' => $this->notes,
            'created_by' => $this->whenLoaded('createdBy', function() {
                return [
                    'id' => $this->createdBy->id,
                    'name' => $this->createdBy->name
                ];
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
