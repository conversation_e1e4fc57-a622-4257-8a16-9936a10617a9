<?php

namespace App\Http\Resources\Api\V1;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UnitConversionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'from_unit' => [
                'id' => $this->fromUnit->id,
                'name' => $this->fromUnit->name,
                'code' => $this->fromUnit->code,
                'is_base' => $this->fromUnit->is_base,
            ],
            'to_unit' => [
                'id' => $this->toUnit->id,
                'name' => $this->toUnit->name,
                'code' => $this->toUnit->code,
                'is_base' => $this->toUnit->is_base,
            ],
            'conversion_factor' => $this->conversion_factor,
            'status' => $this->status,
            'created_by' => $this->created_by,
            'updated_by' => $this->updated_by,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}
