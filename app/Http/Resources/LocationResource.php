<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LocationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $storageConditions = $this->getStorageConditions();
        $hierarchy = $this->getLocationHierarchy();

        return [
            'id' => $this->id,
            'name' => $this->name,
            'type' => $this->type,
            'status' => $this->status,
            'hierarchy' => [
                'store' => $hierarchy['store'],
                'section' => $hierarchy['section'],
                'zone' => $hierarchy['zone'],
                'aisle' => $hierarchy['aisle'],
                'rack' => $hierarchy['rack'],
                'bin' => $hierarchy['bin'],
            ],
            'storage_conditions' => [
                'temperature' => $storageConditions['temperature'],
                'requires_refrigeration' => $storageConditions['requires_refrigeration'],
                'is_controlled' => $storageConditions['is_controlled'],
            ],
            'location_code' => $this->location_code,
            'full_location' => sprintf(
                '%s > %s > %s > %s > %s > %s',
                $hierarchy['store'] ?? 'Main',
                $hierarchy['section'] ?? '-',
                $hierarchy['zone'] ?? '-',
                $hierarchy['aisle'] ?? '-',
                $hierarchy['rack'] ?? '-',
                $hierarchy['bin'] ?? '-'
            ),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }
}
