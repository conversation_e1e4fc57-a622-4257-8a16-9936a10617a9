<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessInventoryUpdate implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $medicineId;
    protected $quantity;
    protected $type;

    public function __construct($medicineId, $quantity, $type)
    {
        $this->medicineId = $medicineId;
        $this->quantity = $quantity;
        $this->type = $type;
    }

    public function handle()
    {
        // Implementation will be added
    }
}
