<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class GenerateReports implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $reportType;
    protected $parameters;

    public function __construct($reportType, $parameters = [])
    {
        $this->reportType = $reportType;
        $this->parameters = $parameters;
    }

    public function handle()
    {
        // Implementation will be added
    }
}
