<?php

namespace App\Imports;

use App\Models\Inventory\Medicine;
use App\Models\Inventory\Category;
use App\Models\Inventory\Manufacturer;
use App\Models\Inventory\UnitType;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class MedicineImport implements 
    ToModel, 
    WithHeadingRow, 
    WithValidation, 
    SkipsOnError, 
    SkipsOnFailure,
    WithBatchInserts,
    WithChunkReading
{
    use Importable, SkipsErrors, SkipsFailures;

    protected $updateExisting;
    protected $importedCount = 0;
    protected $updatedCount = 0;
    protected $skippedCount = 0;

    public function __construct(bool $updateExisting = false)
    {
        $this->updateExisting = $updateExisting;
    }

    /**
     * @param array $row
     * @return Medicine|null
     */
    public function model(array $row)
    {
        try {
            // Clean and prepare data
            $data = $this->prepareData($row);
            
            if (empty($data['name'])) {
                $this->skippedCount++;
                return null;
            }

            // Check if medicine exists (by name and generic_name)
            $existingMedicine = Medicine::where('name', $data['name'])
                ->where('generic_name', $data['generic_name'])
                ->first();

            if ($existingMedicine && $this->updateExisting) {
                // Update existing medicine
                $existingMedicine->update($data);
                $this->updatedCount++;
                return null; // Don't create new model
            } elseif ($existingMedicine && !$this->updateExisting) {
                // Skip if exists and not updating
                $this->skippedCount++;
                return null;
            }

            // Create new medicine
            $this->importedCount++;
            return new Medicine($data);

        } catch (\Exception $e) {
            Log::error('Medicine import error: ' . $e->getMessage(), ['row' => $row]);
            $this->skippedCount++;
            return null;
        }
    }

    /**
     * Prepare data from row
     */
    protected function prepareData(array $row): array
    {
        // Get or create category
        $categoryId = null;
        if (!empty($row['category'])) {
            $category = Category::firstOrCreate(
                ['name' => trim($row['category'])],
                [
                    'slug' => \Str::slug(trim($row['category'])),
                    'is_active' => true,
                    'created_by' => Auth::id()
                ]
            );
            $categoryId = $category->id;
        }

        // Get or create manufacturer
        $manufacturerId = null;
        if (!empty($row['manufacturer'])) {
            $manufacturer = Manufacturer::firstOrCreate(
                ['name' => trim($row['manufacturer'])],
                [
                    'slug' => \Str::slug(trim($row['manufacturer'])),
                    'is_active' => true,
                    'created_by' => Auth::id()
                ]
            );
            $manufacturerId = $manufacturer->id;
        }

        // Get or create unit type
        $unitTypeId = null;
        if (!empty($row['unit_type'])) {
            $unitType = UnitType::firstOrCreate(
                ['name' => trim($row['unit_type'])],
                [
                    'slug' => \Str::slug(trim($row['unit_type'])),
                    'is_active' => true,
                    'created_by' => Auth::id()
                ]
            );
            $unitTypeId = $unitType->id;
        }

        // Parse enabled units
        $enabledUnits = ['box', 'strip', 'unit']; // default
        if (!empty($row['enabled_units'])) {
            $enabledUnits = array_map('trim', explode(',', $row['enabled_units']));
        }

        // Parse enabled retail units
        $enabledRetailUnits = ['box', 'strip', 'unit']; // default
        if (!empty($row['enabled_retail_units'])) {
            $enabledRetailUnits = array_map('trim', explode(',', $row['enabled_retail_units']));
        }

        return [
            'name' => trim($row['name']),
            'generic_name' => trim($row['generic_name'] ?? ''),
            'dosage' => trim($row['dosage'] ?? ''),
            'category_id' => $categoryId,
            'manufacturer_id' => $manufacturerId,
            'unit_type_id' => $unitTypeId,
            'minimum_stock' => (int) ($row['minimum_stock'] ?? 0),
            'maximum_stock' => (int) ($row['maximum_stock'] ?? 0),
            'unit_price' => (float) ($row['unit_price'] ?? 0),
            'supplier_price_carton' => (float) ($row['supplier_price_carton'] ?? 0),
            'supplier_price_box' => (float) ($row['supplier_price_box'] ?? 0),
            'supplier_price_strip' => (float) ($row['supplier_price_strip'] ?? 0),
            'supplier_price_unit' => (float) ($row['supplier_price_unit'] ?? 0),
            'retail_price_carton' => (float) ($row['retail_price_carton'] ?? 0),
            'retail_price_box' => (float) ($row['retail_price_box'] ?? 0),
            'retail_price_strip' => (float) ($row['retail_price_strip'] ?? 0),
            'retail_price_unit' => (float) ($row['retail_price_unit'] ?? 0),
            'strips_per_box' => (int) ($row['strips_per_box'] ?? 1),
            'pieces_per_strip' => (int) ($row['pieces_per_strip'] ?? 1),
            'box_quantity' => (int) ($row['box_quantity'] ?? 1),
            'controlled_substance' => $this->parseBoolean($row['controlled_substance'] ?? false),
            'prescription_required' => $this->parseBoolean($row['prescription_required'] ?? false),
            'enabled_units' => $enabledUnits,
            'enabled_retail_units' => $enabledRetailUnits,
            'status' => trim($row['status'] ?? 'active'),
            'created_by' => Auth::id(),
            'updated_by' => Auth::id(),
        ];
    }

    /**
     * Parse boolean values from various formats
     */
    protected function parseBoolean($value): bool
    {
        if (is_bool($value)) {
            return $value;
        }
        
        $value = strtolower(trim($value));
        return in_array($value, ['1', 'true', 'yes', 'y', 'on']);
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'generic_name' => 'nullable|string|max:255',
            'dosage' => 'nullable|string|max:100',
            'category' => 'nullable|string|max:255',
            'manufacturer' => 'nullable|string|max:255',
            'unit_type' => 'nullable|string|max:255',
            'minimum_stock' => 'nullable|integer|min:0',
            'maximum_stock' => 'nullable|integer|min:0',
            'unit_price' => 'nullable|numeric|min:0',
            'supplier_price_carton' => 'nullable|numeric|min:0',
            'supplier_price_box' => 'nullable|numeric|min:0',
            'supplier_price_strip' => 'nullable|numeric|min:0',
            'supplier_price_unit' => 'nullable|numeric|min:0',
            'retail_price_carton' => 'nullable|numeric|min:0',
            'retail_price_box' => 'nullable|numeric|min:0',
            'retail_price_strip' => 'nullable|numeric|min:0',
            'retail_price_unit' => 'nullable|numeric|min:0',
            'strips_per_box' => 'nullable|integer|min:1',
            'pieces_per_strip' => 'nullable|integer|min:1',
            'box_quantity' => 'nullable|integer|min:1',
            'controlled_substance' => 'nullable',
            'prescription_required' => 'nullable',
            'enabled_units' => 'nullable|string',
            'enabled_retail_units' => 'nullable|string',
            'status' => 'nullable|string|in:active,inactive',
        ];
    }

    /**
     * @return int
     */
    public function batchSize(): int
    {
        return 100;
    }

    /**
     * @return int
     */
    public function chunkSize(): int
    {
        return 100;
    }

    /**
     * Get import statistics
     */
    public function getImportStats(): array
    {
        return [
            'imported' => $this->importedCount,
            'updated' => $this->updatedCount,
            'skipped' => $this->skippedCount,
            'errors' => count($this->errors()),
            'failures' => count($this->failures())
        ];
    }
}
