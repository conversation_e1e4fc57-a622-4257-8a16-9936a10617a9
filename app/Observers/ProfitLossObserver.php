<?php

namespace App\Observers;

use App\Models\Inventory\ProfitLoss;

class ProfitLossObserver
{
    /**
     * Handle the ProfitLoss "created" event.
     */
    public function created(ProfitLoss $profitLoss): void
    {
        //
    }

    /**
     * Handle the ProfitLoss "updated" event.
     */
    public function updated(ProfitLoss $profitLoss): void
    {
        //
    }

    /**
     * Handle the ProfitLoss "deleted" event.
     */
    public function deleted(ProfitLoss $profitLoss): void
    {
        //
    }

    /**
     * Handle the ProfitLoss "restored" event.
     */
    public function restored(ProfitLoss $profitLoss): void
    {
        //
    }

    /**
     * Handle the ProfitLoss "force deleted" event.
     */
    public function forceDeleted(ProfitLoss $profitLoss): void
    {
        //
    }
}
