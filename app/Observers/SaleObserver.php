<?php

namespace App\Observers;

use App\Models\Sales\Sale;
use App\Models\Inventory\ProfitLoss;
use App\Services\LoyaltyService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class SaleObserver
{
    public function __construct()
    {
        Log::debug('SaleObserver instantiated');
    }

    /**
     * Handle the Sale "created" event.
     */
    public function created(Sale $sale): void
    {
        // Ensure items are loaded before processing
        if (!$sale->relationLoaded('items')) {
            $sale->load('items.medicine');
        }

        Log::debug('Sale observer created event triggered', [
            'sale_id' => $sale->id,
            'invoice_number' => $sale->invoice_number,
            'total_amount' => $sale->total_amount,
            'items_count' => $sale->items->count()
        ]);

        // Skip if no items (they might be created after the sale)
        if ($sale->items->count() === 0) {
            Log::debug('Sale has no items yet, skipping profit loss creation', [
                'sale_id' => $sale->id,
                'invoice_number' => $sale->invoice_number
            ]);
            return;
        }

        try {
            $this->recordProfitLoss($sale);
            Log::info('Profit loss records created successfully for sale', [
                'sale_id' => $sale->id,
                'invoice_number' => $sale->invoice_number
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to create profit loss records for sale', [
                'sale_id' => $sale->id,
                'invoice_number' => $sale->invoice_number,
                'error' => $e->getMessage()
            ]);
            // Don't re-throw to prevent sale creation from failing
        }

        // Award loyalty points if customer is present
        if ($sale->customer_id) {
            try {
                $loyaltyService = new LoyaltyService();
                $loyaltyService->awardPoints($sale);
                Log::info('Loyalty points awarded for sale', [
                    'sale_id' => $sale->id,
                    'customer_id' => $sale->customer_id,
                    'invoice_number' => $sale->invoice_number
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to award loyalty points for sale', [
                    'sale_id' => $sale->id,
                    'customer_id' => $sale->customer_id,
                    'invoice_number' => $sale->invoice_number,
                    'error' => $e->getMessage()
                ]);
                // Don't re-throw to prevent sale creation from failing
            }
        }
    }

    /**
     * Handle the Sale "updated" event.
     */
    public function updated(Sale $sale): void
    {
        Log::debug('Sale observer updated event triggered', [
            'sale_id' => $sale->id,
            'invoice_number' => $sale->invoice_number,
            'total_amount' => $sale->total_amount,
            'dirty_fields' => $sale->getDirty()
        ]);
        // If the sale amount or items have changed, update the profit loss record
        if ($sale->isDirty(['total_amount', 'discount_amount', 'tax_amount'])) {
            $this->recordProfitLoss($sale);
        }
    }

    /**
     * Handle the Sale "deleted" event.
     */
    public function deleted(Sale $sale): void
    {
        Log::debug('Sale observer deleted event triggered', [
            'sale_id' => $sale->id,
            'invoice_number' => $sale->invoice_number
        ]);
        // Delete associated profit loss records
        ProfitLoss::where('sale_id', $sale->id)->delete();
    }

    /**
     * Handle the Sale "restored" event.
     */
    public function restored(Sale $sale): void
    {
        //
    }

    /**
     * Handle the Sale "force deleted" event.
     */
    public function forceDeleted(Sale $sale): void
    {
        //
    }

    /**
     * Record profit and loss for each item in the sale
     */
    protected function recordProfitLoss(Sale $sale): void
    {
        // Ensure items and medicine relationships are loaded
        if (!$sale->relationLoaded('items')) {
            $sale->load('items.medicine');
        }

        Log::debug('Recording profit loss for sale', [
            'sale_id' => $sale->id,
            'invoice_number' => $sale->invoice_number,
            'items_count' => $sale->items->count(),
            'total_amount' => $sale->total_amount,
            'discount_amount' => $sale->discount_amount,
            'tax_amount' => $sale->tax_amount
        ]);

        if ($sale->items->count() === 0) {
            Log::warning('Sale has no items to process for profit loss', [
                'sale_id' => $sale->id,
                'invoice_number' => $sale->invoice_number
            ]);
            return;
        }

        foreach ($sale->items as $item) {
            $medicine = $item->medicine;

            // Calculate base item total before any adjustments
            $itemTotal = round($item->quantity * $item->unit_price, 2);

            // Calculate proportional discount and tax for this item
            // Use the sum of all item totals as the base for proportion calculation
            $allItemsTotal = $sale->items->sum(function($saleItem) {
                return $saleItem->quantity * $saleItem->unit_price;
            });

            $discountRatio = $allItemsTotal > 0 ? $sale->discount_amount / $allItemsTotal : 0;
            $taxRatio = $allItemsTotal > 0 ? $sale->tax_amount / $allItemsTotal : 0;

            $itemDiscount = round($itemTotal * $discountRatio, 2);
            $itemTax = round($itemTotal * $taxRatio, 2);

            // Calculate net revenue (after discount, before tax)
            $netRevenue = round($itemTotal - $itemDiscount, 2);

            // Validate supplier price exists
            $supplierPrice = $medicine->supplier_price_unit ?? 0;
            if ($supplierPrice <= 0) {
                Log::warning('Medicine has no supplier price set', [
                    'medicine_id' => $medicine->id,
                    'medicine_name' => $medicine->name,
                    'sale_id' => $sale->id
                ]);
            }

            Log::debug('Creating profit loss record for sale item', [
                'sale_id' => $sale->id,
                'medicine_id' => $item->medicine_id,
                'quantity' => $item->quantity,
                'unit_price' => $item->unit_price,
                'item_total' => $itemTotal,
                'net_revenue' => $netRevenue,
                'discount' => $itemDiscount,
                'tax' => $itemTax,
                'supplier_price' => $supplierPrice
            ]);

            // Create or update profit loss record
            ProfitLoss::updateOrCreate(
                [
                    'sale_id' => $sale->id,
                    'medicine_id' => $item->medicine_id,
                    'reference_number' => $sale->invoice_number,
                    'transaction_type' => 'sale'
                ],
                [
                    'quantity' => $item->quantity,
                    'unit_cost' => $supplierPrice,
                    'unit_price' => $item->unit_price,
                    'total_cost' => round($item->quantity * $supplierPrice, 2),
                    'total_revenue' => $netRevenue, // Store net revenue (after discount)
                    'discount_amount' => $itemDiscount,
                    'tax_amount' => $itemTax,
                    'transaction_date' => $sale->created_at->toDateString(),
                    'created_by' => Auth::id() ?? $sale->created_by,
                    'updated_by' => Auth::id() ?? $sale->updated_by
                ]
            )->calculateProfits();
        }
    }
}
