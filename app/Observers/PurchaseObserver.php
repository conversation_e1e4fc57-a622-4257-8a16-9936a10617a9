<?php

namespace App\Observers;

use App\Models\Inventory\Purchase;
use App\Models\Inventory\ProfitLoss;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class PurchaseObserver
{
    public function __construct()
    {
        Log::debug('PurchaseObserver instantiated');
    }

    /**
     * Handle the Purchase "created" event.
     */
    public function created(Purchase $purchase): void
    {
        Log::debug('Purchase observer created event triggered', [
            'purchase_id' => $purchase->id,
            'purchase_number' => $purchase->purchase_number,
            'total_amount' => $purchase->total_amount,
            'supplier_id' => $purchase->supplier_id
        ]);
        $this->recordPurchaseCost($purchase);
    }

    /**
     * Handle the Purchase "updated" event.
     */
    public function updated(Purchase $purchase): void
    {
        Log::debug('Purchase observer updated event triggered', [
            'purchase_id' => $purchase->id,
            'purchase_number' => $purchase->purchase_number,
            'total_amount' => $purchase->total_amount,
            'dirty_fields' => $purchase->getDirty()
        ]);
        // If the purchase amount or items have changed, update the cost record
        if ($purchase->isDirty(['total_amount', 'discount_amount', 'tax_amount', 'shipping_cost'])) {
            $this->recordPurchaseCost($purchase);
        }
    }

    /**
     * Handle the Purchase "deleted" event.
     */
    public function deleted(Purchase $purchase): void
    {
        Log::debug('Purchase observer deleted event triggered', [
            'purchase_id' => $purchase->id,
            'purchase_number' => $purchase->purchase_number
        ]);
        // Delete associated profit loss records
        ProfitLoss::where('purchase_id', $purchase->id)->delete();
    }

    /**
     * Handle the Purchase "restored" event.
     */
    public function restored(Purchase $purchase): void
    {
        //
    }

    /**
     * Handle the Purchase "force deleted" event.
     */
    public function forceDeleted(Purchase $purchase): void
    {
        //
    }

    /**
     * Record purchase costs for each item
     */
    protected function recordPurchaseCost(Purchase $purchase): void
    {
        Log::debug('Recording purchase costs', [
            'purchase_id' => $purchase->id,
            'purchase_number' => $purchase->purchase_number,
            'items_count' => $purchase->items->count()
        ]);

        foreach ($purchase->items as $item) {
            // Calculate proportional shipping cost for this item
            $itemTotal = $item->quantity * $item->unit_price;
            $purchaseTotal = $purchase->total_amount;
            $shippingRatio = $purchaseTotal > 0 ? $purchase->shipping_cost / $purchaseTotal : 0;
            
            $itemShippingCost = round($itemTotal * $shippingRatio, 2);

            Log::debug('Creating profit loss record for purchase item', [
                'purchase_id' => $purchase->id,
                'medicine_id' => $item->medicine_id,
                'quantity' => $item->quantity,
                'unit_price' => $item->unit_price,
                'item_total' => $itemTotal,
                'shipping_cost' => $itemShippingCost
            ]);

            // Create or update profit loss record
            $profitLoss = ProfitLoss::updateOrCreate(
                [
                    'purchase_id' => $purchase->id,
                    'medicine_id' => $item->medicine_id,
                    'reference_number' => $purchase->purchase_number,
                    'transaction_type' => 'purchase'
                ],
                [
                    'quantity' => $item->quantity,
                    'unit_cost' => $item->unit_price,
                    'unit_price' => 0,
                    'total_revenue' => 0,
                    'total_cost' => round($itemTotal, 2),
                    'shipping_cost' => $itemShippingCost,
                    'transaction_date' => $purchase->order_date,
                    'created_by' => Auth::id() ?? $purchase->created_by,
                    'updated_by' => Auth::id() ?? $purchase->updated_by
                ]
            );

            // Calculate purchase cost
            $profitLoss->calculatePurchaseCost();

            // Update medicine's supplier price
            $item->medicine->update([
                'supplier_price_unit' => $item->unit_price
            ]);
        }
    }
}
