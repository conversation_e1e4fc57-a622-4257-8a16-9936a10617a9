<?php

namespace App\Traits;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;

trait HasSettings
{
    /**
     * Get a setting value
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    protected function getSetting(string $key, $default = null)
    {
        $cacheKey = "settings.{$key}";

        return Cache::remember($cacheKey, now()->addDay(), function () use ($key, $default) {
            $setting = Setting::where('key', $key)->first();
            return $setting ? $setting->value : $default;
        });
    }

    /**
     * Save a setting value
     *
     * @param string $key
     * @param mixed $value
     * @param string $group
     * @param bool $isEncrypted
     * @return void
     */
    protected function saveSetting(string $key, $value, string $group = 'general', bool $isEncrypted = false)
    {
        $setting = Setting::updateOrCreate(
            ['key' => $key],
            [
                'value' => $isEncrypted ? encrypt($value) : $value,
                'group' => $group,
                'is_encrypted' => $isEncrypted,
            ]
        );

        Cache::forget("settings.{$key}");
    }
} 