{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "asantibanez/livewire-charts": "^4.1", "bacon/bacon-qr-code": "^3.0", "doctrine/dbal": "^4.2", "jenssegers/agent": "^2.6", "laravel/breeze": "^2.3", "laravel/fortify": "^1.25", "laravel/framework": "^11.31", "laravel/sanctum": "^4.0", "laravel/scout": "^10.12", "laravel/tinker": "^2.9", "livewire/livewire": "^3.5", "livewire/volt": "^1.0", "maatwebsite/excel": "^3.1", "pragmarx/google2fa": "^8.0", "pragmarx/google2fa-laravel": "^2.2", "spatie/laravel-permission": "^6.15", "twilio/sdk": "^8.3", "vonage/client": "^4.1"}, "require-dev": {"fakerphp/faker": "^1.24", "friendsofphp/php-cs-fixer": "^3.68", "laravel/dusk": "^8.3", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^11.0.1", "squizlabs/php_codesniffer": "^3.11"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}