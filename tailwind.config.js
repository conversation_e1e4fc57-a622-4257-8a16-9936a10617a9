import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
    ],

    theme: {
        extend: {
            colors: {
                primary: {
                    50: '#f0f0ff',
                    100: '#e5e5ff',
                    200: '#d1d1ff',
                    300: '#b3b3ff',
                    400: '#9090ff',
                    500: '#5647e5',
                    600: '#4a3cd6',
                    700: '#3e32c7',
                    800: '#3228b8',
                    900: '#261ea9',
                },
            },
            fontFamily: {
                sans: ['Figtree', ...defaultTheme.fontFamily.sans],
            },
            spacing: {
                '72': '18rem',
                '84': '21rem',
                '96': '24rem',
                'full': '100%'
            },
            maxHeight: {
                '0': '0',
                '1/4': '25%',
                '1/2': '50%',
                '3/4': '75%',
                'full': '100%',
            },
            fontWeight: {
                'normal': 400,
                'medium': 500,
                'semibold': 600,
                'bold': 700,
            },
            opacity: {
                '0': 0,
                '25': 0.25,
                '50': 0.5,
                '75': 0.75,
                '100': 1,
            },
        },
    },

    plugins: [
        forms,
        function({ addBase, addComponents }) {
            addBase({
                ':root': {
                    '--tw-font-normal': '400',
                    '--tw-font-medium': '500',
                    '--tw-font-semibold': '600',
                    '--tw-font-bold': '700',
                    '--tw-opacity-0': '0',
                    '--tw-opacity-25': '0.25',
                    '--tw-opacity-50': '0.5',
                    '--tw-opacity-75': '0.75',
                    '--tw-opacity-100': '1',
                },
            });
            addComponents({
                '.font-normal': {
                    'font-weight': 'var(--tw-font-normal)',
                },
                '.font-medium': {
                    'font-weight': 'var(--tw-font-medium)',
                },
                '.font-semibold': {
                    'font-weight': 'var(--tw-font-semibold)',
                },
                '.font-bold': {
                    'font-weight': 'var(--tw-font-bold)',
                },
                '.opacity-0': {
                    'opacity': 'var(--tw-opacity-0)',
                },
                '.opacity-25': {
                    'opacity': 'var(--tw-opacity-25)',
                },
                '.opacity-50': {
                    'opacity': 'var(--tw-opacity-50)',
                },
                '.opacity-75': {
                    'opacity': 'var(--tw-opacity-75)',
                },
                '.opacity-100': {
                    'opacity': 'var(--tw-opacity-100)',
                },
            });
        },
    ],

    future: {
        hoverOnlyWhenSupported: true,
    },

    experimental: {
        optimizeUniversalDefaults: true
    }
};
