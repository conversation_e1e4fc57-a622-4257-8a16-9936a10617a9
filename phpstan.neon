parameters:
    level: 5
    paths:
        - app
        - tests
    excludePaths:
        - app/Console/Kernel.php
        - tests/CreatesApplication.php
    checkMissingIterableValueType: false
    checkGenericClassInNonGenericObjectType: false
    ignoreErrors:
        - '#Call to an undefined method Illuminate\\Database\\Eloquent\\Model::[a-zA-Z0-9_]+\(\)#'
        - '#Call to an undefined method Illuminate\\Database\\Eloquent\\Builder::[a-zA-Z0-9_]+\(\)#'
        - '#Access to an undefined property [a-zA-Z0-9\\_]+::\$[a-zA-Z0-9_]+#'
        - '#Call to an undefined method Illuminate\\Support\\Facades\\[a-zA-Z0-9_]+::[a-zA-Z0-9_]+\(\)#'
