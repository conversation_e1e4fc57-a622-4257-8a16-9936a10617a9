{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "test": "jest --config=tests/jest.config.cjs --no-coverage", "test:watch": "jest --config=tests/jest.config.cjs --watch --no-coverage", "test:coverage": "jest --config=tests/jest.config.cjs --coverage", "test:working": "jest --config=tests/jest.config.cjs --no-coverage", "test:basic": "jest --config=tests/jest.config.cjs --testPathPattern=basic --no-coverage", "test:workflow": "jest --config=tests/jest.config.cjs --testPathPattern=workflow --no-coverage", "test:verbose": "jest --config=tests/jest.config.cjs --verbose --no-coverage"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "@tailwindcss/forms": "^0.5.2", "autoprefixer": "^10.4.2", "axios": "^1.7.4", "babel-jest": "^29.7.0", "concurrently": "^9.0.1", "fake-indexeddb": "^5.0.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "laravel-vite-plugin": "^1.0", "postcss": "^8.4.31", "tailwindcss": "^3.1.0", "vite": "^6.0", "workbox-webpack-plugin": "^7.0.0"}, "dependencies": {"@alpinejs/focus": "^3.14.8", "@alpinejs/intersect": "^3.14.9", "alpinejs": "^3.14.8", "chart.js": "^4.4.7", "flatpickr": "^4.6.13", "idb": "^7.1.1", "web-push": "^3.6.1", "workbox-window": "^7.0.0"}}