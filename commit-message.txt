feat: implement comprehensive offline-first architecture for sales operations

- Add enhanced IndexedDB schema with 15 stores for complete offline data management
- Implement robust sync service with conflict resolution and retry logic
- Add comprehensive business logic validation for offline sales processing
- Create priority-based sync queue with exponential backoff retry mechanism
- Implement loyalty points calculation, inventory validation, and profit/loss tracking
- Add real-time UI status indicators for offline/sync status and data freshness
- Create comprehensive test suite with 95+ test cases and coverage reporting
- Support complete sales workflows including multi-item sales, prescriptions, and customer management
- Ensure data integrity with validation checks and consistency verification
- Enable seamless offline-to-online synchronization with automatic conflict resolution

BREAKING CHANGE: Database schema updated from 7 to 15 stores, requires data migration