<?php

namespace Database\Factories\Customers;

use App\Models\Customers\Customer;
use App\Models\Users\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class CustomerFactory extends Factory
{
    protected $model = Customer::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name,
            'email' => $this->faker->unique()->safeEmail,
            'phone' => $this->faker->numerify('##########'),
            'address' => $this->faker->address,
            'loyalty_points' => $this->faker->numberBetween(0, 1000),
            'status' => 'active',
            'created_by' => User::factory(),
            'updated_by' => User::factory(),
        ];
    }
} 