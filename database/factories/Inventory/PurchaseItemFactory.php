<?php

namespace Database\Factories\Inventory;

use App\Models\Inventory\PurchaseItem;
use App\Models\Inventory\Purchase;
use App\Models\Inventory\Medicine;
use Illuminate\Database\Eloquent\Factories\Factory;

class PurchaseItemFactory extends Factory
{
    protected $model = PurchaseItem::class;

    public function definition(): array
    {
        $quantity = $this->faker->numberBetween(1, 100);
        $unitPrice = $this->faker->randomFloat(2, 10, 1000);
        $subtotal = $quantity * $unitPrice;
        $taxPercentage = $this->faker->randomFloat(2, 0, 20);
        $taxAmount = $subtotal * ($taxPercentage / 100);
        $discountPercentage = $this->faker->randomFloat(2, 0, 15);
        $discountAmount = $subtotal * ($discountPercentage / 100);
        $totalAmount = $subtotal + $taxAmount - $discountAmount;

        return [
            'purchase_id' => Purchase::factory(),
            'medicine_id' => Medicine::factory(),
            'quantity' => $quantity,
            'unit_price' => $unitPrice,
            'tax_percentage' => $taxPercentage,
            'tax_amount' => $taxAmount,
            'discount_percentage' => $discountPercentage,
            'discount_amount' => $discountAmount,
            'total_amount' => $totalAmount,
            'received_quantity' => 0,
            'expiry_date' => $this->faker->dateTimeBetween('+6 months', '+2 years'),
            'batch_number' => 'BATCH-' . $this->faker->unique()->randomNumber(6),
            'notes' => $this->faker->optional()->sentence(),
        ];
    }

    public function received(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'received_quantity' => $attributes['quantity'],
            ];
        });
    }
} 