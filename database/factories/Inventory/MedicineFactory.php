<?php

namespace Database\Factories\Inventory;

use App\Models\Inventory\Medicine;
use Illuminate\Database\Eloquent\Factories\Factory;

class MedicineFactory extends Factory
{
    protected $model = Medicine::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->words(3, true),
            'generic_name' => $this->faker->words(2, true),
            'description' => $this->faker->paragraph(),
            'category_id' => 1,
            'manufacturer_id' => 1,
            'unit_type_id' => 1,
            'minimum_stock' => $this->faker->numberBetween(10, 50),
            'maximum_stock' => $this->faker->numberBetween(100, 200),
            'controlled_substance' => false,
            'prescription_required' => false,
            'supplier_price_carton' => $this->faker->randomFloat(2, 1000, 5000),
            'supplier_price_box' => $this->faker->randomFloat(2, 100, 500),
            'supplier_price_strip' => $this->faker->randomFloat(2, 10, 50),
            'supplier_price_unit' => $this->faker->randomFloat(2, 1, 5),
            'retail_price_carton' => $this->faker->randomFloat(2, 2000, 10000),
            'retail_price_box' => $this->faker->randomFloat(2, 200, 1000),
            'retail_price_strip' => $this->faker->randomFloat(2, 20, 100),
            'retail_price_unit' => $this->faker->randomFloat(2, 2, 10),
            'unit_price' => $this->faker->randomFloat(2, 1, 5),
            'enabled_units' => json_encode(['carton', 'box', 'strip', 'unit']),
            'status' => 'active',
            'strips_per_box' => 10,
            'pieces_per_strip' => 10,
            'is_active' => true,
            'created_by' => 1,
            'updated_by' => 1,
        ];
    }
} 