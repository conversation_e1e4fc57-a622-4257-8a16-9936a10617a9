<?php

namespace Database\Factories\Inventory;

use App\Models\Inventory\Purchase;
use App\Models\Inventory\Supplier;
use App\Models\Users\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

class PurchaseFactory extends Factory
{
    protected $model = Purchase::class;

    public function definition(): array
    {
        $totalAmount = $this->faker->randomFloat(2, 100, 10000);
        $taxPercentage = $this->faker->randomFloat(2, 0, 20);
        $taxAmount = $totalAmount * ($taxPercentage / 100);
        $discountPercentage = $this->faker->randomFloat(2, 0, 15);
        $discountAmount = $totalAmount * ($discountPercentage / 100);
        $shippingCost = $this->faker->randomFloat(2, 0, 100);
        $finalAmount = $totalAmount + $taxAmount - $discountAmount + $shippingCost;

        return [
            'purchase_number' => 'PO-' . $this->faker->unique()->numberBetween(1000, 9999),
            'supplier_id' => Supplier::factory(),
            'total_amount' => $totalAmount,
            'tax_percentage' => $taxPercentage,
            'tax_amount' => $taxAmount,
            'discount_percentage' => $discountPercentage,
            'discount_amount' => $discountAmount,
            'shipping_cost' => $shippingCost,
            'final_amount' => $finalAmount,
            'status' => $this->faker->randomElement(['pending', 'ordered', 'received', 'cancelled']),
            'payment_status' => $this->faker->randomElement(['pending', 'partial', 'paid']),
            'order_date' => Carbon::now()->subDays($this->faker->numberBetween(1, 30)),
            'expected_date' => Carbon::now()->addDays($this->faker->numberBetween(1, 14)),
            'delivery_date' => null,
            'quality_rating' => $this->faker->optional()->randomFloat(1, 1, 5),
            'notes' => $this->faker->optional()->sentence(),
            'created_by' => User::factory(),
            'updated_by' => User::factory(),
        ];
    }

    public function received(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'received',
                'delivery_date' => Carbon::now(),
                'quality_rating' => $this->faker->randomFloat(1, 1, 5),
            ];
        });
    }

    public function paid(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'payment_status' => 'paid',
            ];
        });
    }
} 