<?php

namespace Database\Factories\Inventory;

use App\Models\Inventory\UnitType;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class UnitTypeFactory extends Factory
{
    protected $model = UnitType::class;

    public function definition(): array
    {
        $name = $this->faker->unique()->word();
        $categories = ['weight', 'volume', 'length', 'unit', 'time', 'temperature'];
        
        return [
            'name' => $name,
            'slug' => Str::slug($name),
            'abbreviation' => strtoupper(substr($name, 0, 3)),
            'code' => strtoupper(Str::random(3)),
            'category' => $this->faker->randomElement($categories),
            'description' => $this->faker->sentence(),
            'is_active' => true,
            'is_base' => false,
            'created_by' => 1, // Will be set in tests
            'updated_by' => 1, // Will be set in tests
        ];
    }

    public function base()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_base' => true,
            ];
        });
    }
} 