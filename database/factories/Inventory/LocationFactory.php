<?php

namespace Database\Factories\Inventory;

use App\Models\Inventory\Location;
use App\Models\Users\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class LocationFactory extends Factory
{
    protected $model = Location::class;

    public function definition(): array
    {
        $name = $this->faker->words(2, true);
        $address = sprintf(
            "%s\n%s, %s %s\n%s",
            $this->faker->streetAddress,
            $this->faker->city,
            $this->faker->stateAbbr,
            $this->faker->postcode,
            $this->faker->country
        );

        return [
            'name' => $name,
            'type' => $this->faker->randomElement(['store', 'warehouse', 'shelf']),
            'address' => $address,
            'status' => 'active',
            'is_active' => true,
            'created_by' => User::factory(),
            'updated_by' => User::factory(),
            'level' => 0,
            'path' => null,
            'location_code' => Str::upper(Str::slug($name)),
            'section' => null,
            'zone' => null
        ];
    }

    public function inactive(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false
            ];
        });
    }
} 