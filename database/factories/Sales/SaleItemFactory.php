<?php

namespace Database\Factories\Sales;

use App\Models\Sales\Sale;
use App\Models\Sales\SaleItem;
use App\Models\Inventory\Medicine;
use Illuminate\Database\Eloquent\Factories\Factory;

class SaleItemFactory extends Factory
{
    protected $model = SaleItem::class;

    public function definition(): array
    {
        return [
            'sale_id' => Sale::factory(),
            'medicine_id' => Medicine::factory(),
            'batch_number' => 'BATCH-' . $this->faker->unique()->randomNumber(6),
            'quantity' => $this->faker->numberBetween(1, 10),
            'unit_price' => $this->faker->randomFloat(2, 10, 100),
            'discount' => $this->faker->randomFloat(2, 0, 10),
            'tax_rate' => $this->faker->randomFloat(2, 0, 10),
        ];
    }
} 