<?php

namespace Database\Factories\Sales;

use App\Models\Sales\Sale;
use App\Models\Users\User;
use App\Models\Customers\Customer;
use Illuminate\Database\Eloquent\Factories\Factory;

class SaleFactory extends Factory
{
    protected $model = Sale::class;

    public function definition(): array
    {
        $totalAmount = $this->faker->randomFloat(2, 100, 1000);
        $paidAmount = $this->faker->randomFloat(2, 0, $totalAmount);
        $dueAmount = $totalAmount - $paidAmount;
        $discountAmount = $this->faker->randomFloat(2, 0, 50);
        $taxAmount = $this->faker->randomFloat(2, 0, 50);

        return [
            'invoice_number' => 'INV-' . $this->faker->unique()->randomNumber(6),
            'customer_id' => Customer::factory(),
            'total_amount' => $totalAmount,
            'paid_amount' => $paidAmount,
            'due_amount' => $dueAmount,
            'discount_amount' => $discountAmount,
            'tax_amount' => $taxAmount,
            'payment_method' => $this->faker->randomElement(['cash', 'card', 'bank_transfer']),
            'payment_status' => $this->faker->randomElement(['pending', 'completed', 'partial']),
            'due_date' => $this->faker->dateTimeBetween('now', '+30 days'),
            'created_by' => User::factory(),
        ];
    }
} 