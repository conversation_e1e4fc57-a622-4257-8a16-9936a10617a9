<?php

namespace Database\Seeders;

use App\Models\Inventory\Manufacturer;
use App\Models\Users\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class ManufacturerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first admin user
        $user = User::first();
        if (!$user) {
            throw new \Exception('No user found. Please run UserSeeder first.');
        }

        // Path to the CSV file
        $csvFile = base_path('csv-data/manufacturers.csv');
        
        if (!File::exists($csvFile)) {
            throw new \Exception('Manufacturers CSV file not found at: ' . $csvFile);
        }
        
        // Disable foreign key checks to allow clearing the table
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        
        // Clear existing manufacturers
        Manufacturer::query()->delete();
        
        // Enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        
        // Manually add specific manufacturers from the CSV
        // This is more reliable for complex data with multi-line addresses
        
        $this->createManufacturer($user, [
            'id' => 1,
            'name' => 'Square Pharmaceuticals PLC',
            'slug' => 'square-pharmaceuticals-plc',
            'email' => '<EMAIL>',
            'phone' => '+8802222283604',
            'address' => "SQUARE Centre\n48, Mohakhali C/A\nDhaka 1212,\nBangladesh.",
                'website' => 'https://www.squarepharma.com.bd',
            'is_active' => true,
        ]);
        
        // Process the rest of the manufacturers (starting from line 3)
        $fileHandle = fopen($csvFile, 'r');
        
        // Skip header and first data row (already handled)
        fgetcsv($fileHandle);
        fgetcsv($fileHandle);
        
        $imported = 1; // Start at 1 since we already added Square Pharmaceuticals
        
        while (($data = fgetcsv($fileHandle)) !== false) {
            // Skip if no id or name
            if (empty($data[0]) || empty($data[1])) {
                continue;
            }
            
            $manufacturerData = [
                'id' => $data[0],
                'name' => $data[1],
                'slug' => $data[2],
                'email' => $data[3],
                'phone' => $data[4],
                'address' => $data[5],
                'website' => $data[6],
                'is_active' => $data[7] ?? true
            ];
            
            $this->createManufacturer($user, $manufacturerData);
            $imported++;
        }
        
        fclose($fileHandle);
        
        $this->command->info("Successfully imported {$imported} manufacturers from CSV file");
    }
    
    /**
     * Create a manufacturer record
     */
    private function createManufacturer($user, $data)
    {
        try {
            Manufacturer::create([
                'id' => $data['id'],
                'name' => $data['name'],
                'slug' => $data['slug'] ?? Str::slug($data['name']),
                'email' => !empty($data['email']) ? $data['email'] : null,
                'phone' => !empty($data['phone']) ? $data['phone'] : null,
                'address' => !empty($data['address']) ? $data['address'] : null,
                'website' => !empty($data['website']) ? $data['website'] : null,
                'is_active' => $data['is_active'] ?? true,
                    'created_by' => $user->id,
                'updated_by' => $user->id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            
            return true;
        } catch (\Exception $e) {
            $this->command->error("Error importing manufacturer: {$data['name']} - {$e->getMessage()}");
            return false;
        }
    }
}
