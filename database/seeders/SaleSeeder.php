<?php

namespace Database\Seeders;

use App\Models\Sales\Sale;
use App\Models\Sales\SaleItem;
use App\Models\Sales\SalePayment;
use App\Models\Customers\Customer;
use App\Models\Inventory\Medicine;
use App\Models\Users\User;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class SaleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first admin user
        $user = User::first();
        if (!$user) {
            throw new \Exception('No user found. Please run UserSeeder first.');
        }

        // Get customers
        $customers = Customer::all();
        if ($customers->isEmpty()) {
            throw new \Exception('No customers found. Please run CustomerSeeder first.');
        }

        // Get medicines
        $medicines = Medicine::all();
        if ($medicines->isEmpty()) {
            throw new \Exception('No medicines found. Please run MedicineSeeder first.');
        }

        // Define payment methods and their probabilities
        $paymentMethods = [
            'cash' => 40,
            'card' => 30,
            'bank_transfer' => 15,
            'mobile_payment' => 15
        ];

        // Define payment statuses and their probabilities
        $paymentStatuses = [
            'pending' => 15,
            'partial' => 25,
            'completed' => 60
        ];

        // Create 50 sales with items spread over last 90 days
        for ($i = 1; $i <= 50; $i++) {
            $customer = $customers->random();
            $saleDate = Carbon::now()->subDays(rand(1, 90));
            
            // Randomly select payment method and status based on probabilities
            $paymentMethod = $this->getRandomWeighted($paymentMethods);
            $paymentStatus = $this->getRandomWeighted($paymentStatuses);
            
            // Create sale
            $sale = Sale::create([
                'invoice_number' => 'INV-' . date('Ym', $saleDate->timestamp) . '-' . str_pad($i, 4, '0', STR_PAD_LEFT),
                'customer_id' => $customer->id,
                'total_amount' => 0,
                'paid_amount' => 0,
                'due_amount' => 0,
                'discount_amount' => 0,
                'tax_amount' => 0,
                'payment_method' => $paymentMethod,
                'payment_status' => $paymentStatus,
                'due_date' => $paymentStatus !== 'completed' ? $saleDate->copy()->addDays(30) : null,
                'created_by' => $user->id,
                'updated_by' => $user->id,
                'created_at' => $saleDate,
                'updated_at' => $saleDate,
            ]);

            // Add 1-8 items to each sale
            $itemCount = rand(1, 8);
            $total = 0;
            $usedMedicines = collect();

            for ($j = 0; $j < $itemCount; $j++) {
                // Get a random medicine not already in this sale
                $medicine = $medicines->reject(function($med) use ($usedMedicines) {
                    return $usedMedicines->contains($med->id);
                })->random();
                $usedMedicines->push($medicine->id);

                // Get available batch
                $batch = $medicine->inventories()
                    ->where('quantity', '>', 0)
                    ->first();

                if (!$batch) continue;

                $quantity = rand(1, min(5, $batch->quantity));
                $unitPrice = $medicine->retail_price_unit;
                
                // Apply random discount (0-20%) based on quantity
                $discountPercentage = $quantity >= 3 ? rand(5, 20) : rand(0, 10);
                $discount = ($quantity * $unitPrice) * ($discountPercentage / 100);
                
                // Apply tax
                $taxRate = 15; // 15% tax
                $subtotal = ($quantity * $unitPrice) - $discount;
                $tax = $subtotal * ($taxRate / 100);
                
                $total += $subtotal + $tax;

                SaleItem::create([
                    'sale_id' => $sale->id,
                    'medicine_id' => $medicine->id,
                    'batch_number' => $batch->batch_number,
                    'quantity' => $quantity,
                    'unit_price' => $unitPrice,
                    'discount' => $discount,
                    'tax_rate' => $taxRate,
                    'created_at' => $saleDate,
                    'updated_at' => $saleDate,
                ]);

                // Update inventory quantity
                $batch->decrement('quantity', $quantity);
            }

            // Calculate final amounts
            $totalAmount = $total;
            $paidAmount = match($paymentStatus) {
                'completed' => $totalAmount,
                'partial' => $totalAmount * (rand(30, 70) / 100), // 30-70% paid
                default => 0
            };
            $dueAmount = $totalAmount - $paidAmount;

            // Update sale totals
            $sale->update([
                'total_amount' => $totalAmount,
                'paid_amount' => $paidAmount,
                'due_amount' => $dueAmount,
            ]);

            // Create payment records if any payment was made
            if ($paidAmount > 0) {
                // For partial payments, create 1-3 payment records
                $remainingAmount = $paidAmount;
                $paymentCount = $paymentStatus === 'partial' ? rand(1, 3) : 1;
                
                for ($k = 0; $k < $paymentCount && $remainingAmount > 0; $k++) {
                    $paymentAmount = $k === $paymentCount - 1 ? 
                        $remainingAmount : 
                        min($remainingAmount, $paidAmount * (rand(20, 50) / 100));
                    
                    SalePayment::create([
                        'sale_id' => $sale->id,
                        'amount' => $paymentAmount,
                        'payment_method' => $paymentMethod,
                        'reference_number' => $this->generatePaymentReference($paymentMethod),
                        'notes' => $this->generatePaymentNote($k + 1, $paymentCount),
                        'recorded_by' => $user->id,
                        'created_at' => $k === 0 ? $saleDate : $saleDate->copy()->addDays(rand(1, 5)),
                    ]);

                    $remainingAmount -= $paymentAmount;
                }
            }
        }
    }

    /**
     * Get a random key based on weights
     */
    private function getRandomWeighted(array $weights): string
    {
        $total = array_sum($weights);
        $rand = rand(1, $total);
        
        foreach ($weights as $key => $weight) {
            $rand -= $weight;
            if ($rand <= 0) {
                return $key;
            }
        }
        
        return array_key_first($weights);
    }

    /**
     * Generate a realistic payment reference number
     */
    private function generatePaymentReference(string $method): string
    {
        return match($method) {
            'card' => 'CARD-' . strtoupper(substr(md5(rand()), 0, 8)),
            'bank_transfer' => 'TRF-' . date('Ymd') . substr(md5(rand()), 0, 6),
            'mobile_payment' => 'MOB-' . strtoupper(substr(md5(rand()), 0, 8)),
            default => 'CASH-' . date('Ymd') . rand(1000, 9999)
        };
    }

    /**
     * Generate a realistic payment note
     */
    private function generatePaymentNote(int $current, int $total): string
    {
        if ($total === 1) {
            return 'Full payment received';
        }
        
        return match($current) {
            1 => 'Initial payment',
            $total => 'Final installment',
            default => 'Installment ' . $current . ' of ' . $total
        };
    }
}
