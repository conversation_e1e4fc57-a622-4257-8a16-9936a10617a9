<?php

namespace Database\Seeders;

use App\Models\Inventory\Medicine;
use App\Models\Inventory\Location;
use App\Models\Inventory\Inventory;
use App\Models\Users\User;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class InventorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get required relationships
        $location = Location::where('type', 'store')->first();
        $user = User::first();
        
        // Get all medicines
        $medicines = Medicine::all();
        
        foreach ($medicines as $medicine) {
            // Generate random expiry date between 1-3 years from now
            $expiryDate = Carbon::now()->addYears(rand(1, 3))->addMonths(rand(0, 11));
            
            // Generate random quantity based on min and max stock
            $quantity = rand(
                $medicine->minimum_stock,
                $medicine->maximum_stock
            );
            
            // Create inventory record
            Inventory::create([
                'medicine_id' => $medicine->id,
                'location_id' => $location->id,
                'batch_number' => 'BATCH-' . strtoupper(substr($medicine->name, 0, 3)) . '-' . rand(1000, 9999),
                'expiry_date' => $expiryDate,
                'quantity' => $quantity,
                'unit_price' => $medicine->supplier_price_unit,
                'rack_number' => 'R' . rand(1, 20),
                'bin_location' => 'B' . rand(1, 50),
                'temperature_requirement' => rand(15, 25) . '°C',
                'notes' => 'Initial stock entry',
                'created_by' => $user->id,
                'updated_by' => $user->id
            ]);
        }
    }
} 