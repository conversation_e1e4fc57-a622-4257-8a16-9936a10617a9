<?php

namespace Database\Seeders;

use App\Models\Customers\Customer;
use App\Models\Users\User;
use Illuminate\Database\Seeder;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first admin user
        $user = User::first();
        if (!$user) {
            throw new \Exception('No user found. Please run UserSeeder first.');
        }

        $customers = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+880-1711-123456',
                'address' => 'House 123, Road 4, Dhanmondi, Dhaka',
                'loyalty_points' => 100,
                'status' => 'active',
                'insurance_provider' => 'MetLife',
                'insurance_number' => 'ML123456',
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+880-1722-234567',
                'address' => 'Apartment 45B, Block C, Bashundhara R/A, Dhaka',
                'loyalty_points' => 250,
                'status' => 'active',
                'insurance_provider' => 'AIA',
                'insurance_number' => 'AIA789012',
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+880-1733-345678',
                'address' => '789 Gulshan Avenue, Dhaka',
                'loyalty_points' => 500,
                'status' => 'active',
                'insurance_provider' => 'Pragati',
                'insurance_number' => 'PL345678',
            ],
            [
                'name' => 'Sarah Khan',
                'email' => '<EMAIL>',
                'phone' => '+880-1744-456789',
                'address' => 'House 56, Road 11, Banani, Dhaka',
                'loyalty_points' => 150,
                'status' => 'active',
                'insurance_provider' => null,
                'insurance_number' => null,
            ],
            [
                'name' => 'Abdul Karim',
                'email' => '<EMAIL>',
                'phone' => '+880-1755-567890',
                'address' => '234 Mirpur DOHS, Dhaka',
                'loyalty_points' => 300,
                'status' => 'active',
                'insurance_provider' => 'Green Delta',
                'insurance_number' => 'GD901234',
            ],
        ];

        foreach ($customers as $customer) {
            Customer::create(array_merge($customer, [
                'created_by' => $user->id,
                'updated_by' => $user->id,
            ]));
        }
    }
}
