<?php

namespace Database\Seeders;

use App\Models\Inventory\Purchase;
use App\Models\Inventory\PurchaseItem;
use App\Models\Inventory\Supplier;
use App\Models\Inventory\Medicine;
use App\Models\Users\User;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class PurchaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first admin user
        $user = User::first();
        if (!$user) {
            throw new \Exception('No user found. Please run UserSeeder first.');
        }

        // Get suppliers
        $suppliers = Supplier::all();
        if ($suppliers->isEmpty()) {
            throw new \Exception('No suppliers found. Please run SupplierSeeder first.');
        }

        // Get medicines
        $medicines = Medicine::all();
        if ($medicines->isEmpty()) {
            throw new \Exception('No medicines found. Please run MedicineSeeder first.');
        }

        // Define possible statuses and their probabilities
        $statuses = [
            'pending' => 10,
            'ordered' => 15,
            'partially_received' => 15,
            'received' => 50,
            'cancelled' => 10
        ];

        // Define payment statuses and their probabilities
        $paymentStatuses = [
            'pending' => 20,
            'partial' => 30,
            'paid' => 50
        ];

        // Create 20 purchases with items
        for ($i = 1; $i <= 20; $i++) {
            $supplier = $suppliers->random();
            
            // Generate realistic dates
            $purchaseDate = Carbon::now()->subDays(rand(1, 90));
            $expectedDate = $purchaseDate->copy()->addDays(rand(5, 14));
            $deliveryDate = null;
            
            // Randomly select status based on probabilities
            $status = $this->getRandomWeighted($statuses);
            
            // Set delivery date based on status
            if (in_array($status, ['partially_received', 'received'])) {
                $deliveryDate = $expectedDate->copy()->subDays(rand(-2, 2)); // Delivery around expected date
            }
            
            // Randomly select payment status based on probabilities
            $paymentStatus = $this->getRandomWeighted($paymentStatuses);
            
            // Create purchase
            $purchase = Purchase::create([
                'purchase_number' => 'PO-' . date('Ym') . '-' . str_pad($i, 4, '0', STR_PAD_LEFT),
                'supplier_id' => $supplier->id,
                'order_date' => $purchaseDate,
                'expected_date' => $expectedDate,
                'delivery_date' => $deliveryDate,
                'payment_status' => $paymentStatus,
                'notes' => $this->generatePurchaseNote($status),
                'created_by' => $user->id,
                'updated_by' => $user->id,
                'total_amount' => 0,
                'tax_amount' => 0,
                'discount_amount' => 0,
                'shipping_cost' => 0,
                'final_amount' => 0,
                'status' => $status,
                'quality_rating' => $status === 'received' ? rand(35, 50) / 10 : null,
            ]);

            // Add 3-10 items to each purchase, but not more than available medicines
            $maxItems = min(10, $medicines->count());
            $itemCount = rand(min(3, $maxItems), $maxItems);
            $total = 0;

            // Keep track of used medicines to avoid duplicates in same purchase
            $usedMedicines = collect();

            for ($j = 0; $j < $itemCount; $j++) {
                // Get a random medicine not already in this purchase
                $availableMedicines = $medicines->reject(function($med) use ($usedMedicines) {
                    return $usedMedicines->contains($med->id);
                });

                // If no more medicines available, break
                if ($availableMedicines->isEmpty()) {
                    break;
                }

                $medicine = $availableMedicines->random();
                $usedMedicines->push($medicine->id);

                $quantity = rand(20, 200);
                $unitPrice = $medicine->supplier_price_unit;
                $subtotal = $quantity * $unitPrice;
                
                // Variable tax and discount rates
                $taxPercentage = rand(10, 20); // 10-20% tax
                $taxAmount = $subtotal * ($taxPercentage / 100);
                $discountPercentage = rand(0, 15); // 0-15% discount
                $discountAmount = $subtotal * ($discountPercentage / 100);
                $totalAmount = $subtotal + $taxAmount - $discountAmount;
                $total += $totalAmount;

                // Calculate received quantity based on status
                $receivedQuantity = match($status) {
                    'received' => $quantity,
                    'partially_received' => rand(1, $quantity - 1),
                    default => 0
                };

                PurchaseItem::create([
                    'purchase_id' => $purchase->id,
                    'medicine_id' => $medicine->id,
                    'batch_number' => 'B' . date('Ym') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT),
                    'quantity' => $quantity,
                    'unit_price' => $unitPrice,
                    'tax_percentage' => $taxPercentage,
                    'tax_amount' => $taxAmount,
                    'discount_percentage' => $discountPercentage,
                    'discount_amount' => $discountAmount,
                    'total_amount' => $totalAmount,
                    'received_quantity' => $receivedQuantity,
                    'expiry_date' => Carbon::now()->addMonths(rand(12, 36)),
                    'notes' => $this->generateItemNote($medicine, $status),
                ]);
            }

            // Calculate shipping cost based on total (0.5% to 5%)
            $shippingCost = $total * (rand(5, 50) / 1000);

            // Update purchase total
            $finalAmount = $total + $shippingCost;
            
            // For partial payments, calculate a random paid amount
            $paidAmount = match($paymentStatus) {
                'paid' => $finalAmount,
                'partial' => $finalAmount * (rand(30, 70) / 100), // 30-70% paid
                default => 0
            };

            $purchase->update([
                'total_amount' => $total,
                'shipping_cost' => $shippingCost,
                'final_amount' => $finalAmount,
                'paid_amount' => $paidAmount,
                'due_amount' => $finalAmount - $paidAmount
            ]);
        }
    }

    /**
     * Get a random key based on weights
     */
    private function getRandomWeighted(array $weights): string
    {
        $total = array_sum($weights);
        $rand = rand(1, $total);
        
        foreach ($weights as $key => $weight) {
            $rand -= $weight;
            if ($rand <= 0) {
                return $key;
            }
        }
        
        return array_key_first($weights);
    }

    /**
     * Generate a realistic purchase note
     */
    private function generatePurchaseNote(string $status): string
    {
        return match($status) {
            'pending' => 'Purchase order created and pending approval',
            'ordered' => 'Order confirmed with supplier, awaiting delivery',
            'partially_received' => 'Partial delivery received, remaining items expected soon',
            'received' => 'All items received and verified',
            'cancelled' => 'Order cancelled due to ' . ['supplier stock unavailability', 'pricing discrepancy', 'delivery delay', 'budget constraints'][rand(0, 3)],
            default => ''
        };
    }

    /**
     * Generate a realistic item note
     */
    private function generateItemNote(Medicine $medicine, string $status): string
    {
        if ($status === 'received') {
            return 'Received in good condition';
        } elseif ($status === 'partially_received') {
            return 'Partial quantity received, remainder expected in next shipment';
        } elseif ($status === 'cancelled') {
            return 'Cancelled - ' . ['Out of stock', 'Price changed', 'Alternative product selected'][rand(0, 2)];
        }
        
        return '';
    }
} 