<?php

namespace Database\Seeders;

use App\Models\Inventory\Location;
use App\Models\Users\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class LocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first admin user
        $user = User::first();
        if (!$user) {
            throw new \Exception('No user found. Please run UserSeeder first.');
        }

        // Path to the CSV file
        $csvFile = base_path('csv-data/locations.csv');
        
        if (!File::exists($csvFile)) {
            throw new \Exception('Locations CSV file not found at: ' . $csvFile);
        }
        
        // Process locations from CSV
        $fileHandle = fopen($csvFile, 'r');
        
        // Skip header row
        fgetcsv($fileHandle);
        
        $imported = 0;
        $locations = [];
        
        // First pass: Create all locations without parent relationships
        while (($data = fgetcsv($fileHandle)) !== false) {
            // Skip if no name
            if (empty($data[5])) {
                continue;
            }
            
            try {
                // Check if location already exists
                $location = Location::find($data[0]);
                
                if (!$location) {
                    // Create a new location
                    $location = new Location();
                    $location->id = $data[0];
                }
                
                // Update location data
                $location->name = $data[5];
                $location->type = !empty($data[6]) ? $data[6] : null;
                $location->level = $data[2];
                $location->is_active = $data[3];
                $location->path = !empty($data[4]) ? $data[4] : null;
                $location->section = !empty($data[7]) ? $data[7] : null;
                $location->zone = !empty($data[8]) ? $data[8] : null;
                $location->aisle_number = !empty($data[9]) ? $data[9] : null;
                $location->address = !empty($data[10]) ? $data[10] : null;
                $location->rack_number = !empty($data[11]) ? $data[11] : null;
                $location->bin_location = !empty($data[12]) ? $data[12] : null;
                $location->location_code = !empty($data[13]) ? $data[13] : null;
                $location->temperature_requirement = !empty($data[14]) ? $data[14] : null;
                $location->status = !empty($data[15]) ? $data[15] : 'active';
                $location->is_default = $data[16] ?? false;
                $location->created_by = $user->id;
                $location->updated_by = $user->id;
                
                // Don't set parent_id here, we'll do it in the second pass
                
                $location->save();
                $locations[$data[0]] = $location;
                $imported++;
            } catch (\Exception $e) {
                $this->command->error("Error importing location: {$data[5]} - {$e->getMessage()}");
                Log::error("Error importing location: {$data[5]} - {$e->getMessage()}");
            }
        }
        
        // Reset file pointer
        rewind($fileHandle);
        
        // Skip header row again
        fgetcsv($fileHandle);
        
        // Second pass: Set parent relationships
        while (($data = fgetcsv($fileHandle)) !== false) {
            // Skip if no name or no parent
            if (empty($data[5]) || empty($data[1])) {
                continue;
            }
            
            try {
                $location = $locations[$data[0]] ?? null;
                $parentLocation = $locations[$data[1]] ?? null;
                
                if ($location && $parentLocation) {
                    $location->parent_id = $parentLocation->id;
                    $location->save();
                }
            } catch (\Exception $e) {
                $this->command->error("Error setting parent for location: {$data[5]} - {$e->getMessage()}");
                Log::error("Error setting parent for location: {$data[5]} - {$e->getMessage()}");
            }
        }
        
        fclose($fileHandle);
        
        $this->command->info("Successfully imported {$imported} locations from CSV file");
    }
} 