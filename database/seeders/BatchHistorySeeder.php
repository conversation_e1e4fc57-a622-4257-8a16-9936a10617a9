<?php

namespace Database\Seeders;

use App\Models\Inventory\BatchHistory;
use App\Models\Inventory\Inventory;
use App\Models\Inventory\Location;
use App\Models\Inventory\Medicine;
use App\Models\Users\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class BatchHistorySeeder extends Seeder
{
    public function run(): void
    {
        $admin = User::first();
        $inventories = Inventory::all();
        $locations = Location::where('type', 'bin')->get();

        foreach ($inventories as $inventory) {
            // Create initial stock entry history
            BatchHistory::create([
                'medicine_id' => $inventory->medicine_id,
                'batch_number' => $inventory->batch_number,
                'location_id' => $inventory->location_id,
                'quantity' => $inventory->quantity,
                'action_type' => 'created',
                'notes' => 'Initial stock entry',
                'created_by' => $admin->id,
                'created_at' => Carbon::now()->subDays(rand(1, 30)),
                'expiry_date' => $inventory->expiry_date,
            ]);

            // For some inventories, create movement histories
            if (rand(0, 1)) {
                $newLocation = $locations->where('id', '!=', $inventory->location_id)->random();
                
                BatchHistory::create([
                    'medicine_id' => $inventory->medicine_id,
                    'batch_number' => $inventory->batch_number,
                    'location_id' => $newLocation->id,
                    'quantity' => rand(10, $inventory->quantity),
                    'action_type' => 'moved',
                    'notes' => 'Stock moved to ' . $newLocation->name,
                    'created_by' => $admin->id,
                    'created_at' => Carbon::now()->subDays(rand(1, 15)),
                    'expiry_date' => $inventory->expiry_date,
                ]);
            }

            // For some inventories, create adjustment histories
            if (rand(0, 1)) {
                $adjustmentQuantity = rand(-20, 20);
                
                BatchHistory::create([
                    'medicine_id' => $inventory->medicine_id,
                    'batch_number' => $inventory->batch_number,
                    'location_id' => $inventory->location_id,
                    'quantity' => abs($adjustmentQuantity),
                    'action_type' => 'adjusted',
                    'notes' => $adjustmentQuantity > 0 ? 'Stock count adjusted (increase)' : 'Stock count adjusted (decrease)',
                    'created_by' => $admin->id,
                    'created_at' => Carbon::now()->subDays(rand(1, 7)),
                    'expiry_date' => $inventory->expiry_date,
                ]);
            }

            // For some old batches, create expiry histories
            if (Carbon::parse($inventory->expiry_date)->isPast()) {
                BatchHistory::create([
                    'medicine_id' => $inventory->medicine_id,
                    'batch_number' => $inventory->batch_number,
                    'location_id' => $inventory->location_id,
                    'quantity' => $inventory->quantity,
                    'action_type' => 'expired',
                    'notes' => 'Batch expired',
                    'created_by' => $admin->id,
                    'created_at' => Carbon::parse($inventory->expiry_date)->addDays(1),
                    'expiry_date' => $inventory->expiry_date,
                ]);
            }
        }
    }
}
