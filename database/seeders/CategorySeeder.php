<?php

namespace Database\Seeders;

use App\Models\Inventory\Category;
use App\Models\Users\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    public function run(): void
    {
        // Get the first admin user
        $user = User::first();
        if (!$user) {
            throw new \Exception('No user found. Please run UserSeeder first.');
        }

        // Path to the CSV file
        $csvFile = base_path('csv-data/categories.csv');
        
        if (!File::exists($csvFile)) {
            throw new \Exception('Categories CSV file not found at: ' . $csvFile);
        }
        
        // Disable foreign key checks to allow clearing the table
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        
        // Clear existing categories
        Category::query()->delete();
        
        // Enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        
        // Process categories from CSV
        $fileHandle = fopen($csvFile, 'r');
        
        // Skip header row
        fgetcsv($fileHandle);
        
        $imported = 0;
        
        while (($data = fgetcsv($fileHandle)) !== false) {
            // Skip if no name
            if (empty($data[1])) {
                continue;
            }
            
            try {
                // Create the category
                $categoryData = [
                    'id' => $data[0],
                    'name' => $data[1],
                    'slug' => $data[2],
                    'description' => !empty($data[3]) ? $data[3] : null,
                    'is_active' => $data[5] ?? true,
                    'created_at' => now(),
                    'updated_at' => now(),
                    'created_by' => $user->id,
                    'updated_by' => $user->id,
                ];
                
                // Handle parent_id separately to ensure NULL is correctly processed
                if (!empty($data[4]) && strtolower($data[4]) !== 'null') {
                    $categoryData['parent_id'] = $data[4];
                }
                
                Category::create($categoryData);
                
                $imported++;
            } catch (\Exception $e) {
                $this->command->error("Error importing category: {$data[1]} - {$e->getMessage()}");
            }
        }
        
        fclose($fileHandle);

        $this->command->info("Successfully imported {$imported} categories from CSV file");
    }
}
