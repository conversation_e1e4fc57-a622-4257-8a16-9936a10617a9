<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    public function run()
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // User management
            'view users',
            'create users',
            'edit users',
            'delete users',
            
            // Inventory management
            'view inventory',
            'create inventory',
            'edit inventory',
            'delete inventory',
            'view archived inventory',
            'restore archived inventory',
            
            // Sales management
            'view sales',
            'create sales',
            'edit sales',
            'delete sales',
            
            // Customer management
            'view customers',
            'create customers',
            'edit customers',
            'delete customers',

            // Prescription management
            'view prescriptions',
            'create prescriptions',
            'edit prescriptions',
            'delete prescriptions',
            
            // Reports
            'view reports',
            'create reports',
            
            // Settings
            'manage settings'
        ];

        // Create or update permissions
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create or update roles and assign permissions
        
        // Admin role
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $adminRole->syncPermissions(Permission::all());

        // Manager role
        $managerRole = Role::firstOrCreate(['name' => 'manager']);
        $managerRole->syncPermissions([
            'view users',
            'view inventory',
            'create inventory',
            'edit inventory',
            'view sales',
            'create sales',
            'edit sales',
            'view customers',
            'create customers',
            'edit customers',
            'view prescriptions',
            'create prescriptions',
            'edit prescriptions',
            'view reports',
            'create reports',
            'view archived inventory',
            'restore archived inventory'
        ]);

        // Pharmacist role
        $pharmacistRole = Role::firstOrCreate(['name' => 'pharmacist']);
        $pharmacistRole->syncPermissions([
            'view inventory',
            'edit inventory',
            'view sales',
            'create sales',
            'view customers',
            'create customers',
            'edit customers',
            'view prescriptions',
            'create prescriptions',
            'edit prescriptions',
            'view archived inventory',
            'restore archived inventory'
        ]);
        
        // Sales Person role
        $salesPersonRole = Role::firstOrCreate(['name' => 'sales_person']);
        $salesPersonRole->syncPermissions([
            'view inventory',
            'view sales',
            'create sales',
            'edit sales',
            'view customers',
            'create customers',
            'edit customers',
            'view prescriptions',
            'view reports',
            'view archived inventory'
        ]);

        // Cashier role
        $cashierRole = Role::firstOrCreate(['name' => 'cashier']);
        $cashierRole->syncPermissions([
            'view sales',
            'create sales',
            'view customers',
            'create customers',
            'view prescriptions',
            'view archived inventory'
        ]);
    }
}
