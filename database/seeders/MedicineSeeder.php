<?php

namespace Database\Seeders;

use App\Models\Inventory\Medicine;
use App\Models\Inventory\Location;
use App\Models\Inventory\Category;
use App\Models\Inventory\Manufacturer;
use App\Models\Inventory\UnitType;
use App\Models\Users\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class MedicineSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first admin user
        $user = User::first();
        if (!$user) {
            throw new \Exception('No user found. Please run UserSeeder first.');
        }
        
        // Find a default unit type to use for NULL values
        $defaultUnitType = UnitType::where('code', 'PCS')->first();
        if (!$defaultUnitType) {
            $defaultUnitType = UnitType::first();
        }
        
        if (!$defaultUnitType) {
            throw new \Exception('No unit type found. Please run UnitTypeSeeder first.');
        }

        // Path to the CSV file
        $csvFile = base_path('csv-data/medicines.csv');
        
        if (!File::exists($csvFile)) {
            throw new \Exception('Medicines CSV file not found at: ' . $csvFile);
        }
        
        // Disable foreign key checks to allow clearing the table
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        
        // Clear existing medicines
        Medicine::query()->delete();
        
        // Enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        
        // Process medicines from CSV
        $fileHandle = fopen($csvFile, 'r');
        
        // Skip header row
        fgetcsv($fileHandle);
        
        $imported = 0;
        
        while (($data = fgetcsv($fileHandle)) !== false) {
            // Skip if no name
            if (empty($data[1])) {
                continue;
            }
            
            try {
                // Convert enabled_units and enabled_retail_units to arrays
                $enabledUnits = json_decode($data[20], true) ?? ['box', 'strip', 'unit'];
                $enabledRetailUnits = json_decode($data[21], true) ?? ['box', 'strip', 'unit'];
            
                // Handle NULL values properly
                $medicineData = [
                    'name' => $data[1],
                    'generic_name' => $data[2],
                    'description' => !empty($data[3]) && strtolower($data[3]) !== 'null' ? $data[3] : null,
                    'manufacturer_id' => $data[4],
                    'category_id' => $data[5],
                    'unit_type_id' => !empty($data[6]) && strtolower($data[6]) !== 'null' ? $data[6] : $defaultUnitType->id,
                    'minimum_stock' => $data[7],
                    'maximum_stock' => $data[8],
                    'controlled_substance' => $data[9],
                    'prescription_required' => $data[10],
                    'supplier_price_carton' => $data[11],
                    'supplier_price_box' => $data[12],
                    'supplier_price_strip' => $data[13],
                    'supplier_price_unit' => $data[14],
                    'retail_price_carton' => $data[15],
                    'retail_price_box' => $data[16],
                    'retail_price_strip' => $data[17],
                    'retail_price_unit' => $data[18],
                    'unit_price' => $data[19],
                    'enabled_units' => $enabledUnits,
                    'enabled_retail_units' => $enabledRetailUnits,
                    'status' => $data[22],
                    'strips_per_box' => $data[23],
                    'pieces_per_strip' => $data[24],
                    'box_quantity' => $data[25],
                    'is_active' => $data[26],
                'created_by' => $user->id,
                    'updated_by' => $user->id,
                    'created_at' => !empty($data[30]) && strtolower($data[30]) !== 'null' ? $data[30] : now(),
                    'updated_at' => !empty($data[31]) && strtolower($data[31]) !== 'null' ? $data[31] : now(),
                ];
                
                // Handle deleted_at specially
                if (!empty($data[29]) && strtolower($data[29]) !== 'null') {
                    $medicineData['deleted_at'] = $data[29];
        }
                
                // Create or update the medicine record
                Medicine::updateOrCreate(
                    ['id' => $data[0]],
                    $medicineData
                );
                
                $imported++;
            } catch (\Exception $e) {
                $this->command->error("Error importing medicine: {$data[1]} - {$e->getMessage()}");
            }
        }
        
        fclose($fileHandle);
        
        $this->command->info("Successfully imported {$imported} medicines from CSV file");
    }
} 