<?php

namespace Database\Seeders;

use App\Models\Inventory\Warehouse;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class WarehouseSeeder extends Seeder
{
    private $warehouses = [
        [
            'name' => 'Main Warehouse',
            'code' => 'WH-MAIN',
            'address' => 'Main Street, Dhaka',
            'description' => 'Main storage facility for all medicines',
            'capacity' => 10000,
            'is_active' => true,
        ],
        [
            'name' => 'Cold Storage Facility',
            'code' => 'WH-COLD',
            'address' => 'Cold Storage Road, Dhaka',
            'description' => 'Temperature controlled storage for sensitive medicines',
            'capacity' => 5000,
            'is_active' => true,
        ],
        [
            'name' => 'Secure Storage',
            'code' => 'WH-SECURE',
            'address' => 'Secure Zone, Dhaka',
            'description' => 'High security storage for controlled substances',
            'capacity' => 2000,
            'is_active' => true,
        ],
    ];

    public function run(): void
    {
        foreach ($this->warehouses as $warehouse) {
            Warehouse::firstOrCreate(
                ['code' => $warehouse['code']],
                [
                    'name' => $warehouse['name'],
                    'address' => $warehouse['address'],
                    'description' => $warehouse['description'],
                    'capacity' => $warehouse['capacity'],
                    'is_active' => $warehouse['is_active'],
                    'slug' => Str::slug($warehouse['name']),
                ]
            );
        }
    }
}
