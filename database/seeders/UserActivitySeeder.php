<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Users\User;
use App\Models\Users\UserActivity;
use Carbon\Carbon;

class UserActivitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();

        foreach ($users as $user) {
            // Create sample activities for each user
            $this->createSampleActivities($user);
        }
    }

    private function createSampleActivities(User $user): void
    {
        $activities = [
            [
                'action' => UserActivity::ACTION_LOGIN,
                'description' => 'User logged in',
                'created_at' => now()->subHours(2),
            ],
            [
                'action' => UserActivity::ACTION_PROFILE_UPDATED,
                'description' => 'Profile updated: name, phone',
                'created_at' => now()->subHours(4),
                'properties' => [
                    'changed_fields' => ['name', 'phone'],
                    'changes' => [
                        'name' => ['old' => 'Old Name', 'new' => $user->name],
                        'phone' => ['old' => null, 'new' => $user->phone],
                    ],
                ],
            ],
            [
                'action' => UserActivity::ACTION_PASSWORD_CHANGED,
                'description' => 'Password changed',
                'created_at' => now()->subDay(),
            ],
            [
                'action' => UserActivity::ACTION_SALE_CREATED,
                'description' => 'Sale created: #INV-001',
                'created_at' => now()->subDays(2),
                'properties' => [
                    'invoice_number' => 'INV-001',
                    'total_amount' => 150.00,
                ],
            ],
            [
                'action' => UserActivity::ACTION_LOGIN,
                'description' => 'User logged in',
                'created_at' => now()->subDays(3),
            ],
            [
                'action' => UserActivity::ACTION_MEDICINE_CREATED,
                'description' => 'Medicine created: Paracetamol 500mg',
                'created_at' => now()->subDays(4),
                'properties' => [
                    'medicine_name' => 'Paracetamol 500mg',
                ],
            ],
            [
                'action' => UserActivity::ACTION_2FA_ENABLED,
                'description' => 'Two-factor authentication enabled',
                'created_at' => now()->subDays(5),
            ],
            [
                'action' => UserActivity::ACTION_AVATAR_UPDATED,
                'description' => 'Profile avatar updated',
                'created_at' => now()->subDays(6),
            ],
            [
                'action' => UserActivity::ACTION_CUSTOMER_CREATED,
                'description' => 'Customer created: John Doe',
                'created_at' => now()->subDays(7),
                'properties' => [
                    'customer_name' => 'John Doe',
                ],
            ],
            [
                'action' => UserActivity::ACTION_SETTINGS_UPDATED,
                'description' => 'Settings updated',
                'created_at' => now()->subDays(8),
                'properties' => [
                    'settings' => [
                        'theme' => 'dark',
                        'language' => 'en',
                        'timezone' => 'UTC',
                    ],
                ],
            ],
        ];

        foreach ($activities as $activity) {
            UserActivity::create([
                'user_id' => $user->id,
                'action' => $activity['action'],
                'description' => $activity['description'],
                'ip_address' => '192.168.1.' . rand(1, 255),
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'properties' => $activity['properties'] ?? null,
                'created_at' => $activity['created_at'],
                'updated_at' => $activity['created_at'],
            ]);
        }
    }
}
