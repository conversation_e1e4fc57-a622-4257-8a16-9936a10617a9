<?php

namespace Database\Seeders;

use App\Models\Sales\Prescription;
use App\Models\Sales\PrescriptionItem;
use App\Models\Sales\Sale;
use App\Models\Customers\Customer;
use App\Models\Inventory\Medicine;
use App\Models\Users\User;
use Illuminate\Database\Seeder;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class PrescriptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first admin user
        $user = User::first();
        if (!$user) {
            throw new \Exception('No user found. Please run UserSeeder first.');
        }

        // Get sales
        $sales = Sale::all();
        if ($sales->isEmpty()) {
            throw new \Exception('No sales found. Please run SaleSeeder first.');
        }

        // Get medicines
        $medicines = Medicine::where('prescription_required', true)->get();
        if ($medicines->isEmpty()) {
            throw new \Exception('No prescription medicines found. Please run MedicineSeeder first.');
        }

        // Create 5 prescriptions
        for ($i = 1; $i <= 5; $i++) {
            $sale = $sales->random();
            $prescriptionDate = Carbon::parse($sale->created_at);
            
            // Create prescription
            $prescription = Prescription::create([
                'sale_id' => $sale->id,
                'doctor_name' => 'Dr. ' . $this->generateRandomName(),
                'hospital_name' => $this->generateRandomHospital(),
                'prescription_date' => $prescriptionDate,
            ]);
        }
    }
    
    /**
     * Generate a random person name
     */
    private function generateRandomName(): string
    {
        $firstNames = ['John', 'Jane', 'Robert', 'Sarah', 'Michael', 'Emily', 'David', 'Emma', 'Richard', 'Olivia'];
        $lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez'];
        
        return $firstNames[array_rand($firstNames)] . ' ' . $lastNames[array_rand($lastNames)];
    }
    
    /**
     * Generate a random hospital name
     */
    private function generateRandomHospital(): string
    {
        $prefixes = ['Central', 'Community', 'Regional', 'General', 'University', 'Memorial', 'Saint', 'Metro', 'National', 'City'];
        $suffixes = ['Hospital', 'Medical Center', 'Health Center', 'Clinic', 'Healthcare', 'Medical Facility'];
        
        return $prefixes[array_rand($prefixes)] . ' ' . $suffixes[array_rand($suffixes)];
    }
} 