<?php

namespace Database\Seeders;

use App\Models\Inventory\UnitType;
use App\Models\Inventory\UnitConversion;
use App\Models\Users\User;
use Illuminate\Support\Str;
use Illuminate\Database\Seeder;

class UnitTypeSeeder extends Seeder
{
    public function run(): void
    {
        // Get the first admin user
        $user = User::first();
        if (!$user) {
            throw new \Exception('No user found. Please run UserSeeder first.');
        }

        // Create base units
        $units = [
            // Pharmaceutical forms
            ['name' => 'Tablet', 'code' => 'TAB', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'tab'],
            ['name' => 'Capsule', 'code' => 'CAP', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'cap'],
            ['name' => 'Injection', 'code' => 'INJ', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'inj'],
            ['name' => 'Syrup', 'code' => 'SYP', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'syp'],
            ['name' => 'Ampoule', 'code' => 'AMP', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'amp'],
            ['name' => 'Bottle', 'code' => 'BTL', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'btl'],
            ['name' => 'Tube', 'code' => 'TUBE', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'tube'],
            ['name' => 'Suppository', 'code' => 'SUPP', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'supp'],
            ['name' => 'Drops', 'code' => 'DROPS', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'drops'],
            ['name' => 'Pediatric Drops', 'code' => 'PED_DROPS', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'ped_drops'],
            ['name' => 'Eye Drops', 'code' => 'EYE_DROPS', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'eye_drops'],
            ['name' => 'Ear Drops', 'code' => 'EAR_DROPS', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'ear_drops'],
            ['name' => 'Cream', 'code' => 'CRM', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'crm'],
            ['name' => 'Ointment', 'code' => 'OINT', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'oint'],
            ['name' => 'Gel', 'code' => 'GEL', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'gel'],
            ['name' => 'Lotion', 'code' => 'LOT', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'lot'],
            ['name' => 'Powder', 'code' => 'PWD', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'pwd'],
            ['name' => 'Solution', 'code' => 'SOL', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'sol'],
            ['name' => 'Suspension', 'code' => 'SUSP', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'susp'],
            ['name' => 'Emulsion', 'code' => 'EMUL', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'emul'],
            ['name' => 'Spray', 'code' => 'SPR', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'spr'],
            ['name' => 'Inhaler', 'code' => 'INH', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'inh'],
            ['name' => 'Patch', 'code' => 'PATCH', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'patch'],
            ['name' => 'Sachet', 'code' => 'SACH', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'sach'],
            ['name' => 'Vial', 'code' => 'VIAL', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'vial'],
            ['name' => 'Syringe', 'code' => 'SYR', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'syr'],
            ['name' => 'Pessary', 'code' => 'PESS', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'pess'],
            ['name' => 'Lozenge', 'code' => 'LOZ', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'loz'],
            ['name' => 'Granules', 'code' => 'GRAN', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'gran'],
            ['name' => 'Elixir', 'code' => 'ELIX', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'elix'],
            ['name' => 'Tincture', 'code' => 'TINCT', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'tinct'],
            ['name' => 'Liniment', 'code' => 'LIN', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'lin'],
            ['name' => 'Mouthwash', 'code' => 'MW', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'mw'],
            ['name' => 'Gargle', 'code' => 'GARG', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'garg'],
            
            // Unit (Quantity) units
            ['name' => 'Piece', 'code' => 'PCS', 'category' => 'quantity', 'is_base' => true, 'abbreviation' => 'pc'],
            ['name' => 'Strip', 'code' => 'STRIP', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'strip'],
            ['name' => 'Box', 'code' => 'BOX', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'box'],
            ['name' => 'Pack', 'code' => 'PACK', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'pack'],
            ['name' => 'Carton', 'code' => 'CTN', 'category' => 'quantity', 'is_base' => false, 'abbreviation' => 'ctn'],
                        
            // Volume units
            ['name' => 'Milliliter', 'code' => 'ML', 'category' => 'volume', 'is_base' => true, 'abbreviation' => 'ml'],
            ['name' => 'Liter', 'code' => 'L', 'category' => 'volume', 'is_base' => false, 'abbreviation' => 'l'],
            
            // Weight units
            ['name' => 'Milligram', 'code' => 'MG', 'category' => 'weight', 'is_base' => true, 'abbreviation' => 'mg'],
            ['name' => 'Gram', 'code' => 'G', 'category' => 'weight', 'is_base' => false, 'abbreviation' => 'g'],
            ['name' => 'Kilogram', 'code' => 'KG', 'category' => 'weight', 'is_base' => false, 'abbreviation' => 'kg'],
            
            // Time units
            ['name' => 'Second', 'code' => 'SEC', 'category' => 'time', 'is_base' => true, 'abbreviation' => 's'],
            ['name' => 'Minute', 'code' => 'MIN', 'category' => 'time', 'is_base' => false, 'abbreviation' => 'min'],
            ['name' => 'Hour', 'code' => 'HR', 'category' => 'time', 'is_base' => false, 'abbreviation' => 'hr'],
            
            // Temperature units
            ['name' => 'Celsius', 'code' => 'C', 'category' => 'temperature', 'is_base' => true, 'abbreviation' => '°C'],
            ['name' => 'Fahrenheit', 'code' => 'F', 'category' => 'temperature', 'is_base' => false, 'abbreviation' => '°F'],
        ];

        foreach ($units as $unit) {
            UnitType::firstOrCreate(
                ['code' => $unit['code']],
                array_merge($unit, [
                    'slug' => Str::slug($unit['name']),
                    'description' => "Standard {$unit['name']} unit for {$unit['category']}",
                    'is_active' => true,
                    'created_by' => $user->id,
                    'updated_by' => $user->id
                ])
            );
        }

        // Create common conversions
        $conversions = [
            // Weight conversions
            ['from' => 'MG', 'to' => 'G', 'factor' => 0.001],
            ['from' => 'G', 'to' => 'KG', 'factor' => 0.001],

            // Volume conversions
            ['from' => 'ML', 'to' => 'L', 'factor' => 0.001],
            ['from' => 'DROPS', 'to' => 'ML', 'factor' => 0.05], // 20 drops = 1 ml (standard pharmaceutical conversion)
            ['from' => 'PED_DROPS', 'to' => 'ML', 'factor' => 0.05], // 20 pediatric drops = 1 ml (standard pharmaceutical conversion)
            ['from' => 'EYE_DROPS', 'to' => 'ML', 'factor' => 0.05], // 20 eye drops = 1 ml (standard pharmaceutical conversion)
            ['from' => 'EAR_DROPS', 'to' => 'ML', 'factor' => 0.05], // 20 ear drops = 1 ml (standard pharmaceutical conversion)

            // Time conversions
            ['from' => 'SEC', 'to' => 'MIN', 'factor' => 1/60],
            ['from' => 'MIN', 'to' => 'HR', 'factor' => 1/60],

            // Common medicine packaging
            ['from' => 'PCS', 'to' => 'STRIP', 'factor' => 0.1], // 10 pieces = 1 strip
            ['from' => 'STRIP', 'to' => 'BOX', 'factor' => 0.1], // 10 strips = 1 box
            ['from' => 'BOX', 'to' => 'PACK', 'factor' => 0.2], // 5 boxes = 1 pack
            ['from' => 'PACK', 'to' => 'CTN', 'factor' => 0.1], // 10 packs = 1 carton

            // Pharmaceutical form conversions
            ['from' => 'TAB', 'to' => 'STRIP', 'factor' => 0.1], // 10 tablets = 1 strip
            ['from' => 'CAP', 'to' => 'STRIP', 'factor' => 0.1], // 10 capsules = 1 strip
            ['from' => 'SACH', 'to' => 'BOX', 'factor' => 0.1], // 10 sachets = 1 box
            ['from' => 'LOZ', 'to' => 'STRIP', 'factor' => 0.1], // 10 lozenges = 1 strip
            ['from' => 'SUPP', 'to' => 'STRIP', 'factor' => 0.1], // 10 suppositories = 1 strip
            ['from' => 'PESS', 'to' => 'STRIP', 'factor' => 0.1], // 10 pessaries = 1 strip

            // Liquid form conversions (approximate)
            ['from' => 'SYP', 'to' => 'BTL', 'factor' => 1.0], // 1 syrup = 1 bottle (container relationship)
            ['from' => 'SOL', 'to' => 'BTL', 'factor' => 1.0], // 1 solution = 1 bottle (container relationship)
            ['from' => 'SUSP', 'to' => 'BTL', 'factor' => 1.0], // 1 suspension = 1 bottle (container relationship)
        ];

        foreach ($conversions as $conversion) {
            $fromUnit = UnitType::where('code', $conversion['from'])->first();
            $toUnit = UnitType::where('code', $conversion['to'])->first();

            if ($fromUnit && $toUnit) {
                UnitConversion::firstOrCreate(
                    [
                        'from_unit_id' => $fromUnit->id,
                        'to_unit_id' => $toUnit->id
                    ],
                    [
                        'conversion_factor' => $conversion['factor'],
                        'created_by' => $user->id,
                        'updated_by' => $user->id
                    ]
                );
            }
        }
    }
}
