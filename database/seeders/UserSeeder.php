<?php

namespace Database\Seeders;

use App\Models\Users\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create roles if they don't exist
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $managerRole = Role::firstOrCreate(['name' => 'manager']);
        $pharmacistRole = Role::firstOrCreate(['name' => 'pharmacist']);
        $salesPersonRole = Role::firstOrCreate(['name' => 'sales_person']);
        $cashierRole = Role::firstOrCreate(['name' => 'cashier']);

        // Create permissions
        $permissions = [
            'view dashboard',
            'view inventory',
            'manage inventory',
            'view sales',
            'manage sales',
            'view reports',
            'manage users',
            'manage settings',
            'view customers',
            'manage customers',
            'view prescriptions',
            'manage prescriptions'
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign all permissions to admin role
        $adminRole->syncPermissions(Permission::all());

        // Create Juwel Ahamed admin user (as first user)
        $juwelAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Juwel Ahamed',
                'password' => Hash::make('Pharmadesk123'),
                'email_verified_at' => now(),
            ]
        );

        // Create admin user
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Firoz Anam',
                'password' => Hash::make('Interstellar@@#1940'),
                'email_verified_at' => now(),
            ]
        );

        // Create manager user
        $manager = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Manager',
                'password' => Hash::make('Manager123'),
                'email_verified_at' => now(),
            ]
        );
        
        // Create pharmacist user
        $pharmacist = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Pharmacist',
                'password' => Hash::make('Pharmacist123'),
                'email_verified_at' => now(),
            ]
        );
        
        // Create sales person user
        $salesPerson = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Sales Person',
                'password' => Hash::make('Sales123'),
                'email_verified_at' => now(),
            ]
        );
        
        // Create cashier user
        $cashier = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Cashier',
                'password' => Hash::make('Cashier123'),
                'email_verified_at' => now(),
            ]
        );

        // Assign roles to users
        $juwelAdmin->assignRole($adminRole);
        $admin->assignRole($adminRole);
        $manager->assignRole($managerRole);
        $pharmacist->assignRole($pharmacistRole);
        $salesPerson->assignRole($salesPersonRole);
        $cashier->assignRole($cashierRole);
    }
}
