<?php

namespace Database\Seeders;

use App\Models\Users\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class AdminUserSeeder extends Seeder
{
    public function run(): void
    {
        // Create permissions if they don't exist
        $permissions = [
            'view inventory',
            'create inventory',
            'edit inventory',
            'delete inventory',
            'view sales',
            'create sales',
            'edit sales',
            'delete sales',
            'view customers',
            'create customers',
            'edit customers',
            'delete customers',
            'view reports',
            'create reports',
            'manage settings',
            'manage users',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create admin role and assign all permissions
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        
        // Get all existing permissions
        $existingPermissions = Permission::whereIn('name', $permissions)->get();
        
        // Sync permissions to admin role
        $adminRole->syncPermissions($existingPermissions);

        // Create admin user if it doesn't exist
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Firoz Anam',
                'password' => Hash::make('Interstellar@@#1940'),
                'email_verified_at' => now(),
            ]
        );

        // Assign admin role to user
        $admin->syncRoles(['admin']);
    }
}
