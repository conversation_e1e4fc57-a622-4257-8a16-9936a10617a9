<?php

namespace Database\Seeders;

use App\Models\Inventory\Supplier;
use App\Models\Users\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class SupplierSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first admin user
        $user = User::first();
        if (!$user) {
            throw new \Exception('No user found. Please run UserSeeder first.');
        }

        // Path to the CSV file
        $csvFile = base_path('csv-data/suppliers.csv');
        
        if (!File::exists($csvFile)) {
            throw new \Exception('Suppliers CSV file not found at: ' . $csvFile);
        }
        
        // Disable foreign key checks to allow clearing the table
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        
        // Clear existing suppliers
        Supplier::query()->delete();
        
        // Enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        
        // Process suppliers from CSV
        $fileHandle = fopen($csvFile, 'r');
        
        // Skip header row
        fgetcsv($fileHandle);
        
        $imported = 0;
        
        while (($data = fgetcsv($fileHandle)) !== false) {
            // Skip if no name
            if (empty($data[1])) {
                continue;
            }
            
            try {
                // Create the supplier
                Supplier::create([
                    'id' => $data[0],
                    'name' => $data[1],
                    'contact_person' => $data[2],
                    'email' => !empty($data[3]) ? $data[3] : null,
                    'phone' => $data[4],
                    'address' => !empty($data[5]) ? $data[5] : null,
                    'tax_number' => !empty($data[6]) ? $data[6] : null,
                    'is_active' => $data[7] ?? true,
                    'created_by' => $user->id,
                    'updated_by' => $user->id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                
                $imported++;
            } catch (\Exception $e) {
                $this->command->error("Error importing supplier: {$data[1]} - {$e->getMessage()}");
            }
        }
        
        fclose($fileHandle);
        
        $this->command->info("Successfully imported {$imported} suppliers from CSV file");
    }
}
