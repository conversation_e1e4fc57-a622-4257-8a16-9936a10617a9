<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;

class PermissionSeeder extends Seeder
{
    public function run(): void
    {
        // Report permissions
        Permission::firstOrCreate(['name' => 'view reports']);
        Permission::firstOrCreate(['name' => 'manage reports']);

        // Settings permissions
        Permission::firstOrCreate(['name' => 'manage settings']);

        // Inventory permissions
        Permission::firstOrCreate(['name' => 'view inventory']);
        Permission::firstOrCreate(['name' => 'manage inventory']);
        Permission::firstOrCreate(['name' => 'view archived inventory']);
        Permission::firstOrCreate(['name' => 'restore archived inventory']);

        // Sales permissions
        Permission::firstOrCreate(['name' => 'view sales']);
        Permission::firstOrCreate(['name' => 'manage sales']);

        // Customer permissions
        Permission::firstOrCreate(['name' => 'view customers']);
        Permission::firstOrCreate(['name' => 'manage customers']);

        // Prescription permissions
        Permission::firstOrCreate(['name' => 'view prescriptions']);
        Permission::firstOrCreate(['name' => 'manage prescriptions']);
    }
} 