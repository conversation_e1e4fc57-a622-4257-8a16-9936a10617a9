<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loyalty_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->foreignId('sale_id')->nullable()->constrained()->onDelete('cascade');
            $table->enum('type', ['earned', 'redeemed', 'expired', 'adjusted']);
            $table->integer('points');
            $table->decimal('amount_spent', 10, 2)->nullable(); // For earned points
            $table->decimal('discount_applied', 10, 2)->nullable(); // For redeemed points
            $table->string('description');
            $table->json('metadata')->nullable(); // Additional data like earning rate, etc.
            $table->timestamp('expires_at')->nullable(); // For point expiration
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();

            // Indexes for better query performance
            $table->index(['customer_id', 'type', 'created_at']);
            $table->index(['customer_id', 'expires_at']);
            $table->index('sale_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loyalty_transactions');
    }
};
