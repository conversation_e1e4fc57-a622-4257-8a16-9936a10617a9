<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * This consolidated migration creates the stock_movements table with all columns
     * that were added across 8 separate migration files:
     * - 2025_01_22_000003_create_stock_movements_table.php
     * - 2025_01_23_132339_add_movement_type_to_stock_movements.php
     * - 2025_01_23_132546_add_reference_number_to_stock_movements.php
     * - 2025_01_23_132719_add_location_fields_to_stock_movements.php
     * - 2025_01_23_133704_add_batch_number_to_stock_movements.php (duplicate column)
     * - 2025_01_23_133948_add_movement_date_to_stock_movements.php (duplicate column)
     * - 2025_06_15_154208_add_missing_columns_to_stock_movements_table.php
     * - 2025_06_15_154947_make_location_fields_nullable_in_stock_movements.php
     * - 2025_06_15_155241_make_reference_number_nullable_in_stock_movements.php
     * - 2025_06_15_155420_make_movement_date_nullable_in_stock_movements.php
     */
    public function up(): void
    {
        // Skip if table already exists (for existing installations)
        if (Schema::hasTable('stock_movements')) {
            return;
        }

        Schema::create('stock_movements', function (Blueprint $table) {
            // Primary key
            $table->id();
            
            // Medicine and inventory references
            $table->foreignId('medicine_id')->constrained()->onDelete('restrict');
            $table->foreignId('inventory_id')->nullable()->constrained()->onDelete('restrict');
            
            // Location references (nullable based on later migrations)
            $table->foreignId('source_location_id')->nullable()
                  ->constrained('locations')->onDelete('restrict');
            $table->foreignId('destination_location_id')->nullable()
                  ->constrained('locations')->onDelete('restrict');
            
            // Movement details
            $table->integer('quantity');
            $table->string('movement_type');
            $table->datetime('movement_date')->nullable();
            
            // Reference information
            $table->string('reference_number')->nullable();
            $table->string('reference_type')->nullable();
            $table->unsignedBigInteger('reference_id')->nullable();
            
            // Batch and product details
            $table->string('batch_number')->nullable();
            $table->date('expiry_date')->nullable();
            
            // Pricing information
            $table->decimal('unit_price', 10, 2)->nullable();
            $table->decimal('total_cost', 10, 2)->nullable();
            
            // Additional information
            $table->text('notes')->nullable();
            
            // User tracking
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('updated_by')->nullable()->constrained('users');
            
            // Timestamps and soft deletes
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_movements');
    }
};
