<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('purchase_items', function (Blueprint $table) {
            $table->foreignId('created_by')->nullable()->constrained('users');
            $table->foreignId('updated_by')->nullable()->constrained('users');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Check if foreign keys exist using raw SQL
        $foreignKeys = DB::select("
            SELECT CONSTRAINT_NAME
            FROM information_schema.TABLE_CONSTRAINTS
            WHERE CONSTRAINT_TYPE = 'FOREIGN KEY'
            AND TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'purchase_items'
        ");
        
        $foreignKeyNames = array_map(function($key) {
            return $key->CONSTRAINT_NAME;
        }, $foreignKeys);
        
        Schema::table('purchase_items', function (Blueprint $table) use ($foreignKeyNames) {
            if (in_array('purchase_items_created_by_foreign', $foreignKeyNames)) {
                $table->dropForeign(['created_by']);
            }
            
            if (in_array('purchase_items_updated_by_foreign', $foreignKeyNames)) {
                $table->dropForeign(['updated_by']);
            }
            
            $table->dropColumn(['created_by', 'updated_by']);
        });
    }
}; 