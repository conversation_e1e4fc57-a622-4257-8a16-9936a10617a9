<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * This consolidated migration creates the medicines table with all columns
     * that were added across multiple migration files:
     * - 2024_01_22_000006_create_medicines_table.php (original CREATE)
     * - 2025_01_25_102500_create_medicines_table.php (duplicate CREATE - ignored)
     * - 2025_01_29_000007_update_medicines_unit_type.php (unit_type -> unit_type_id)
     * - 2025_01_29_124234_add_box_pattern_to_medicines_table.php (strips_per_box, pieces_per_strip)
     * - 2025_02_18_170424_add_box_quantity_and_retail_units_to_medicines_table.php
     * - 2025_06_03_045409_add_dosage_to_medicines_table.php
     * 
     * Note: The second CREATE migration (2025_01_25_102500) had different columns
     * (location_id, warehouse_id) but since the first one ran, we use the original structure.
     */
    public function up(): void
    {
        // Skip if table already exists (for existing installations)
        if (Schema::hasTable('medicines')) {
            return;
        }

        Schema::create('medicines', function (Blueprint $table) {
            // Primary key
            $table->id();
            
            // Basic medicine information
            $table->string('name');
            $table->string('generic_name');
            $table->text('description')->nullable();
            
            // Relationships
            $table->foreignId('manufacturer_id')->nullable()->constrained('manufacturers');
            $table->foreignId('category_id')->nullable()->constrained();
            $table->foreignId('unit_type_id')->nullable()->constrained('unit_types');
            
            // Stock management
            $table->integer('minimum_stock')->default(0);
            $table->integer('maximum_stock')->default(0);
            
            // Medicine properties
            $table->boolean('controlled_substance')->default(false);
            $table->boolean('prescription_required')->default(false);
            
            // Packaging structure (from add_box_pattern_to_medicines_table)
            $table->integer('strips_per_box')->default(0);
            $table->integer('pieces_per_strip')->default(0);
            $table->integer('box_quantity')->default(0);
            
            // Dosage information (from add_dosage_to_medicines_table)
            $table->string('dosage')->nullable();
            
            // Supplier pricing
            $table->decimal('supplier_price_carton', 10, 2)->default(0);
            $table->decimal('supplier_price_box', 10, 2)->default(0);
            $table->decimal('supplier_price_strip', 10, 2)->default(0);
            $table->decimal('supplier_price_unit', 10, 2)->default(0);
            
            // Retail pricing
            $table->decimal('retail_price_carton', 10, 2)->default(0);
            $table->decimal('retail_price_box', 10, 2)->default(0);
            $table->decimal('retail_price_strip', 10, 2)->default(0);
            $table->decimal('retail_price_unit', 10, 2)->default(0);
            $table->decimal('unit_price', 10, 2)->default(0);
            
            // Unit configuration
            $table->json('enabled_units')->nullable();
            $table->json('enabled_retail_units')->nullable();
            
            // Status
            $table->string('status')->default('active');
            $table->boolean('is_active')->default(true);
            
            // User tracking
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('updated_by')->nullable()->constrained('users');
            
            // Timestamps and soft deletes
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('medicines');
    }
};
