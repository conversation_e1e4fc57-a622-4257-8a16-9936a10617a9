<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        // Create unit types table if it doesn't exist
        if (!Schema::hasTable('unit_types')) {
            Schema::create('unit_types', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('code', 10)->unique();
                $table->text('description')->nullable();
                $table->enum('category', ['weight', 'volume', 'quantity', 'length'])->default('quantity');
                $table->boolean('is_base_unit')->default(false);
                $table->string('status')->default('active');
                $table->foreignId('created_by')->constrained('users');
                $table->foreignId('updated_by')->nullable()->constrained('users');
                $table->timestamps();
                $table->softDeletes();
            });
        }

        // Create unit conversions table
        Schema::create('unit_conversions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('from_unit_id')->constrained('unit_types');
            $table->foreignId('to_unit_id')->constrained('unit_types');
            $table->decimal('conversion_factor', 15, 6);
            $table->text('notes')->nullable();
            $table->string('status')->default('active');
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('updated_by')->nullable()->constrained('users');
            $table->timestamps();
            $table->softDeletes();

            // Ensure unique conversion pairs
            $table->unique(['from_unit_id', 'to_unit_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('unit_conversions');
        Schema::dropIfExists('unit_types');
    }
};
