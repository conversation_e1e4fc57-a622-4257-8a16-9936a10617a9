<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * This migration removes inventory-related fields from the medicines table
     * to create a clean separation between medicine master data and inventory management.
     * Inventory will now be managed exclusively through purchase orders.
     */
    public function up(): void
    {
        Schema::table('medicines', function (Blueprint $table) {
            // Remove inventory-related fields that should not be in medicine master data
            if (Schema::hasColumn('medicines', 'unit_price')) {
                $table->dropColumn('unit_price');
            }
            
            // Remove unit_type string field if it exists (we use unit_type_id instead)
            if (Schema::hasColumn('medicines', 'unit_type')) {
                $table->dropColumn('unit_type');
            }
            
            // Remove is_active field if it exists (we use status instead)
            if (Schema::hasColumn('medicines', 'is_active')) {
                $table->dropColumn('is_active');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('medicines', function (Blueprint $table) {
            // Add back the removed fields
            $table->decimal('unit_price', 10, 2)->default(0)->after('retail_price_unit');
            $table->string('unit_type')->nullable()->after('unit_type_id');
            $table->boolean('is_active')->default(true)->after('status');
        });
    }
};
