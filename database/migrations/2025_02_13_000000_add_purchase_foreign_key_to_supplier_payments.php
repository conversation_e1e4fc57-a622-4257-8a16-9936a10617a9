<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Add foreign key constraint to supplier_payments.purchase_id
     * after purchases table has been created.
     */
    public function up(): void
    {
        // Check if the foreign key constraint already exists
        $constraintExists = \DB::select("
            SELECT COUNT(*) as count
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'supplier_payments'
            AND COLUMN_NAME = 'purchase_id'
            AND REFERENCED_TABLE_NAME = 'purchases'
        ")[0]->count > 0;

        // Only add the foreign key if it doesn't already exist
        if (!$constraintExists) {
            Schema::table('supplier_payments', function (Blueprint $table) {
                $table->foreign('purchase_id')->references('id')->on('purchases')->onDelete('set null');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('supplier_payments', function (Blueprint $table) {
            $table->dropForeign(['purchase_id']);
        });
    }
};
