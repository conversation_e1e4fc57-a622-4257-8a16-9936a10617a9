<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('medicines')) {
            Schema::create('medicines', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('generic_name');
                $table->text('description')->nullable();
                $table->foreignId('manufacturer_id')->nullable()->constrained('manufacturers');
                $table->foreignId('category_id')->nullable()->constrained();
                $table->string('unit_type');
                $table->integer('minimum_stock')->default(0);
                $table->integer('maximum_stock')->default(0);
                $table->boolean('controlled_substance')->default(false);
                $table->boolean('prescription_required')->default(false);
                $table->decimal('supplier_price_carton', 10, 2)->default(0);
                $table->decimal('supplier_price_box', 10, 2)->default(0);
                $table->decimal('supplier_price_strip', 10, 2)->default(0);
                $table->decimal('supplier_price_unit', 10, 2)->default(0);
                $table->decimal('retail_price_carton', 10, 2)->default(0);
                $table->decimal('retail_price_box', 10, 2)->default(0);
                $table->decimal('retail_price_strip', 10, 2)->default(0);
                $table->decimal('retail_price_unit', 10, 2)->default(0);
                $table->decimal('unit_price', 10, 2)->default(0);
                $table->json('enabled_units')->nullable();
                $table->string('status')->default('active');
                $table->boolean('is_active')->default(true);
                $table->foreignId('created_by')->constrained('users');
                $table->foreignId('updated_by')->nullable()->constrained('users');
                $table->softDeletes();
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('medicines');
    }
};
