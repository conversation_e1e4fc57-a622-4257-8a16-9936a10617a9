<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * This consolidated migration creates the locations table with all columns
     * that were added across 8 separate migration files:
     * - 2025_01_22_000002_create_locations_table.php
     * - 2025_01_22_000008_update_locations_table.php  
     * - 2025_01_22_000009_add_rack_and_bin_to_locations.php
     * - 2025_01_22_000013_add_parent_id_to_locations.php
     * - 2025_01_23_132720_add_is_default_to_locations.php
     * - 2025_01_23_200751_add_storage_organization_to_locations_table.php
     * - 2025_01_25_083956_add_is_active_to_locations_table.php
     * - 2025_01_25_100628_add_section_and_zone_to_locations_table.php (duplicate)
     */
    public function up(): void
    {
        // Skip if table already exists (for existing installations)
        if (Schema::hasTable('locations')) {
            return;
        }

        Schema::create('locations', function (Blueprint $table) {
            // Primary key
            $table->id();
            
            // Hierarchical structure (from add_parent_id_to_locations)
            $table->foreignId('parent_id')->nullable()
                  ->constrained('locations')->onDelete('restrict');
            $table->integer('level')->default(0);
            $table->string('path')->nullable();
            
            // Basic location information
            $table->string('name');
            $table->enum('type', ['warehouse', 'store', 'rack', 'zone', 'shelf', 'bin'])
                  ->default('store');
            $table->text('address')->nullable();
            
            // Storage organization (from add_storage_organization_to_locations_table)
            $table->string('section')->nullable()
                  ->comment('Storage section (e.g., OTC, Prescription, Controlled)');
            $table->string('zone')->nullable()
                  ->comment('Storage zone (e.g., Normal, Cold Storage, Controlled Access)');
            $table->string('aisle_number')->nullable()
                  ->comment('Aisle identifier (e.g., A1, B2)');
            
            // Physical location details (from add_rack_and_bin_to_locations)
            $table->string('rack_number')->nullable()
                  ->comment('Rack identifier within aisle');
            $table->string('bin_location')->nullable()
                  ->comment('Specific location within rack');
            $table->string('temperature_requirement')->nullable()
                  ->comment('Required temperature range (e.g., 2-8°C, 15-25°C)');
            
            // Location identification (from add_storage_organization_to_locations_table)
            $table->string('location_code')->nullable()
                  ->comment('Unique location identifier for barcode/QR');
            
            // Status and flags
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->boolean('is_default')->default(false);
            $table->boolean('is_active')->default(true);
            
            // User tracking
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            
            // Timestamps and soft deletes
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes for performance (from add_parent_id_to_locations)
            $table->index(['parent_id', 'type']);
            $table->index('path');
            $table->index('level');
            
            // Index for storage organization (from add_storage_organization_to_locations_table)
            $table->index(['section', 'zone', 'aisle_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('locations');
    }
};
