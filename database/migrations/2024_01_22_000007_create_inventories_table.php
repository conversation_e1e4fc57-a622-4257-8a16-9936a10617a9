<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('inventories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('medicine_id')->constrained()->onDelete('restrict');
            $table->string('batch_number');
            $table->date('expiry_date');
            $table->integer('quantity')->default(0);
            $table->decimal('unit_price', 10, 2);
            $table->string('rack_number')->nullable();
            $table->string('bin_location')->nullable();
            $table->foreignId('warehouse_id')->nullable()->constrained()->onDelete('restrict');
            $table->string('temperature_requirement')->nullable();
            $table->timestamps();

            $table->index(['medicine_id', 'batch_number']);
            $table->index('expiry_date');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('inventories');
    }
};
