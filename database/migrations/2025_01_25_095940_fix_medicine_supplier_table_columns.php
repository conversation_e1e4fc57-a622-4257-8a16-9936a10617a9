<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the existing table
        Schema::dropIfExists('medicine_supplier');
            
        // Recreate the table with correct columns
        Schema::create('medicine_supplier', function (Blueprint $table) {
            $table->id();
            $table->foreignId('medicine_id')
                ->constrained('medicines')
                ->onDelete('cascade');
            $table->foreignId('supplier_id')
                ->constrained('suppliers')
                ->onDelete('cascade');
            $table->timestamps();
            
            $table->unique(['medicine_id', 'supplier_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('medicine_supplier');
    }
};
