<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        if (!Schema::hasTable('stock_movements')) {
            Schema::create('stock_movements', function (Blueprint $table) {
                $table->id();
                $table->foreignId('medicine_id')->constrained()->onDelete('restrict');
                $table->foreignId('source_location_id')->constrained('locations')->onDelete('restrict');
                $table->foreignId('destination_location_id')->constrained('locations')->onDelete('restrict');
                $table->integer('quantity');
                $table->string('batch_number');
                $table->dateTime('movement_date');
                $table->text('notes')->nullable();
                $table->foreignId('created_by')->constrained('users');
                $table->foreignId('updated_by')->nullable()->constrained('users');
                $table->timestamps();
                $table->softDeletes();
            });
        }
    }

    public function down()
    {
        Schema::dropIfExists('stock_movements');
    }
};
