<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * This consolidated migration creates the inventories table with all columns
     * that were added across 7+ separate migration files:
     * - 2024_01_22_000007_create_inventories_table.php (original CREATE)
     * - 2025_01_25_104500_add_location_id_to_inventories_table.php
     * - 2025_01_28_081107_update_inventories_table_structure.php
     * - 2025_01_28_184809_fix_inventory_location_column.php
     * - 2025_01_28_215029_add_user_tracking_to_inventories_table.php
     * - 2025_01_29_025100_add_notes_to_inventories_table.php
     * - 2025_02_18_144516_update_inventories_unique_constraint.php
     * - 2025_02_18_152156_update_inventories_table_unique_constraint.php
     * - 2025_02_18_152712_fix_inventory_unique_constraints.php
     * - 2025_02_18_173400_update_inventory_locations.php
     * - 2025_02_19_000001_fix_inventory_cost_prices.php
     * - 2025_02_19_000002_verify_inventory_purchase_data.php
     * - 2025_02_19_000003_add_unique_constraint_to_batch_numbers.php
     * - 2025_02_20_000001_add_purchase_price_to_inventories_table.php
     * - 2025_05_29_182217_modify_expiry_date_in_inventories_table.php (make nullable)
     */
    public function up(): void
    {
        // Skip if table already exists (for existing installations)
        if (Schema::hasTable('inventories')) {
            return;
        }

        Schema::create('inventories', function (Blueprint $table) {
            // Primary key
            $table->id();
            
            // Medicine reference
            $table->foreignId('medicine_id')->constrained()->onDelete('restrict');
            
            // Batch information
            $table->string('batch_number');
            $table->date('expiry_date')->nullable(); // Made nullable in later migration
            
            // Quantity and pricing
            $table->integer('quantity')->default(0);
            $table->decimal('unit_price', 10, 2);
            $table->decimal('purchase_price', 10, 2)->nullable();
            
            // Location information
            $table->string('rack_number')->nullable();
            $table->string('bin_location')->nullable();
            $table->foreignId('warehouse_id')->nullable()->constrained()->onDelete('restrict');
            $table->foreignId('location_id')->nullable()->constrained('locations');
            
            // Storage requirements
            $table->string('temperature_requirement')->nullable();
            
            // Additional information
            $table->text('notes')->nullable();
            
            // User tracking (from add_user_tracking_to_inventories_table)
            $table->foreignId('created_by')->nullable()->constrained('users');
            $table->foreignId('updated_by')->nullable()->constrained('users');
            
            // Timestamps
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['medicine_id', 'batch_number']);
            $table->index('expiry_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventories');
    }
};
