<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * This migration enhances the purchase_items table to better support
     * the new purchase order workflow and inventory creation process.
     */
    public function up(): void
    {
        Schema::table('purchase_items', function (Blueprint $table) {
            // Rename quantity to quantity_ordered for clarity
            if (Schema::hasColumn('purchase_items', 'quantity') && 
                !Schema::hasColumn('purchase_items', 'quantity_ordered')) {
                $table->renameColumn('quantity', 'quantity_ordered');
            }
            
            // Add new columns if they don't exist
            if (!Schema::hasColumn('purchase_items', 'manufacture_date')) {
                $table->date('manufacture_date')->nullable()->after('expiry_date');
            }
            
            if (!Schema::hasColumn('purchase_items', 'location_id')) {
                $table->foreignId('location_id')->nullable()->constrained('locations')->after('batch_number');
            }
            
            if (!Schema::hasColumn('purchase_items', 'received_at')) {
                $table->timestamp('received_at')->nullable()->after('received_quantity');
            }
            
            if (!Schema::hasColumn('purchase_items', 'created_by')) {
                $table->foreignId('created_by')->nullable()->constrained('users')->after('notes');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('purchase_items', function (Blueprint $table) {
            // Remove added columns
            if (Schema::hasColumn('purchase_items', 'created_by')) {
                $table->dropForeign(['created_by']);
                $table->dropColumn('created_by');
            }
            
            if (Schema::hasColumn('purchase_items', 'received_at')) {
                $table->dropColumn('received_at');
            }
            
            if (Schema::hasColumn('purchase_items', 'location_id')) {
                $table->dropForeign(['location_id']);
                $table->dropColumn('location_id');
            }
            
            if (Schema::hasColumn('purchase_items', 'manufacture_date')) {
                $table->dropColumn('manufacture_date');
            }
            
            // Rename back to quantity
            if (Schema::hasColumn('purchase_items', 'quantity_ordered') && 
                !Schema::hasColumn('purchase_items', 'quantity')) {
                $table->renameColumn('quantity_ordered', 'quantity');
            }
        });
    }
};
