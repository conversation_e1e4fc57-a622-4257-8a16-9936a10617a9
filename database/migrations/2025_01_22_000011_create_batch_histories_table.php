<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('batch_histories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('medicine_id')->constrained()->onDelete('restrict');
            $table->string('batch_number');
            $table->date('expiry_date');
            $table->enum('action_type', ['created', 'moved', 'adjusted', 'expired']);
            $table->integer('quantity');
            $table->foreignId('location_id')->constrained()->onDelete('restrict');
            $table->decimal('unit_price', 10, 2)->nullable();
            $table->text('notes')->nullable();
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();
            $table->softDeletes();

            // Add indexes for common queries
            $table->index(['medicine_id', 'batch_number']);
            $table->index(['location_id', 'batch_number']);
            $table->index('action_type');
            $table->index('expiry_date');
        });
    }

    public function down()
    {
        Schema::dropIfExists('batch_histories');
    }
};
