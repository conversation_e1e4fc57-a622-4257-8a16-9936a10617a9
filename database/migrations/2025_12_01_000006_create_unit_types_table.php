<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * This consolidated migration creates the unit_types and unit_conversions tables
     * with all columns that were added across 5 separate migration files:
     * - 2024_01_22_000000_create_unit_types_and_conversions_tables.php (original CREATE)
     * - 2025_01_24_000002_update_unit_types_structure.php (add slug, abbreviation, is_active, is_base)
     * - 2025_01_29_000000_update_unit_types_table_structure.php (drop is_base_unit, status; modify code length)
     * - 2025_01_29_000001_update_unit_types_category_data.php (data migration - not structural)
     * - 2025_06_16_074535_add_missing_columns_to_unit_types_table.php (re-add slug, abbreviation)
     * - 2025_06_16_074639_update_unit_types_category_enum.php (expand category enum)
     */
    public function up(): void
    {
        // Skip if table already exists (for existing installations)
        if (Schema::hasTable('unit_types')) {
            return;
        }

        // Create unit_types table with all consolidated columns
        Schema::create('unit_types', function (Blueprint $table) {
            // Primary key
            $table->id();
            
            // Basic information
            $table->string('name');
            $table->string('slug')->nullable(); // Made nullable in later migration
            $table->string('abbreviation', 20)->nullable(); // Made nullable in later migration
            $table->string('code', 50)->unique(); // Extended length in later migration
            $table->text('description')->nullable();
            
            // Category with expanded enum (from update_unit_types_category_enum)
            $table->enum('category', ['weight', 'volume', 'quantity', 'length', 'time', 'temperature'])
                  ->default('quantity');
            
            // Status flags (consolidated from multiple migrations)
            $table->boolean('is_base')->default(false);
            $table->boolean('is_active')->default(true);
            
            // User tracking
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('updated_by')->nullable()->constrained('users');
            
            // Timestamps and soft deletes
            $table->timestamps();
            $table->softDeletes();
        });

        // Create unit_conversions table (from original create_unit_types_and_conversions_tables)
        Schema::create('unit_conversions', function (Blueprint $table) {
            // Primary key
            $table->id();
            
            // Unit relationships
            $table->foreignId('from_unit_id')->constrained('unit_types');
            $table->foreignId('to_unit_id')->constrained('unit_types');
            
            // Conversion details
            $table->decimal('conversion_factor', 15, 6);
            $table->text('notes')->nullable();
            $table->string('status')->default('active');
            
            // User tracking
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('updated_by')->nullable()->constrained('users');
            
            // Timestamps and soft deletes
            $table->timestamps();
            $table->softDeletes();

            // Ensure unique conversion pairs
            $table->unique(['from_unit_id', 'to_unit_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('unit_conversions');
        Schema::dropIfExists('unit_types');
    }
};
