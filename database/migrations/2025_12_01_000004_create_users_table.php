<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * This consolidated migration creates the users table with all columns
     * that were added across 5 separate migration files:
     * - 0001_01_01_000000_create_users_table.php (original CREATE + password_reset_tokens + sessions)
     * - 2024_01_26_223350_add_profile_fields_to_users_table.php
     * - 2025_01_30_000001_add_status_to_users_table.php
     * - 2025_02_13_121125_add_two_factor_columns_to_users_table.php
     * - 2025_06_16_000002_add_preferences_to_users_table.php
     */
    public function up(): void
    {
        // Skip if table already exists (for existing installations)
        if (Schema::hasTable('users')) {
            return;
        }

        // Create users table with all consolidated columns
        Schema::create('users', function (Blueprint $table) {
            // Primary key
            $table->id();
            
            // Basic authentication
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->string('status')->default('active');
            
            // Two-factor authentication (from add_two_factor_columns_to_users_table)
            $table->text('two_factor_secret')->nullable();
            $table->text('two_factor_recovery_codes')->nullable();
            $table->timestamp('two_factor_confirmed_at')->nullable();
            
            // Profile fields (from add_profile_fields_to_users_table)
            $table->string('phone')->nullable();
            $table->string('position')->nullable();
            $table->text('bio')->nullable();
            $table->string('avatar')->nullable();
            $table->string('timezone')->default(config('app.timezone'));
            
            // User preferences (from add_preferences_to_users_table)
            $table->string('theme')->default('light');
            $table->string('language')->default('en');
            $table->boolean('email_notifications')->default(true);
            $table->boolean('push_notifications')->default(true);
            
            // Laravel defaults
            $table->rememberToken();
            $table->timestamps();
        });

        // Create password reset tokens table (from original create_users_table)
        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        // Create sessions table (from original create_users_table)
        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
