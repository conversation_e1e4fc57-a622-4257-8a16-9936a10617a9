<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('medicine_suppliers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('medicine_id')->constrained()->onDelete('cascade');
            $table->foreignId('supplier_id')->constrained()->onDelete('cascade');
            $table->boolean('is_primary')->default(false);
            $table->decimal('unit_price', 10, 2)->nullable();
            $table->string('supplier_medicine_code')->nullable();
            $table->integer('minimum_order_quantity')->nullable();
            $table->integer('lead_time_days')->nullable();
            $table->text('notes')->nullable();
            $table->string('status')->default('active');
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('updated_by')->nullable()->constrained('users');
            $table->timestamps();
            $table->softDeletes();

            // Ensure unique medicine-supplier combination
            $table->unique(['medicine_id', 'supplier_id']);
            
            // Add index for common queries
            $table->index(['medicine_id', 'is_primary']);
            $table->index('status');
        });
    }

    public function down()
    {
        Schema::dropIfExists('medicine_suppliers');
    }
};
