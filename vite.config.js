import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import { resolve } from 'path';

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/css/app.css', 'resources/js/app.js'],
            refresh: true,
        }),
    ],
    resolve: {
        alias: {
            // Ensure idb is properly resolved
            'idb': resolve(__dirname, 'node_modules/idb')
        }
    },
    optimizeDeps: {
        include: ['idb']
    }
});
