<?php

/**
 * <PERSON><PERSON> - A PHP Framework For Web Artisans
 *
 * @package  <PERSON><PERSON>
 * <AUTHOR> <<EMAIL>>
 */

$uri = urldecode(
    parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH) ?? ''
);

// This file allows us to emulate Apache's "mod_rewrite" functionality from the
// built-in PHP web server. This provides a convenient way to test a Laravel
// application without having installed a "real" web server software here.
if ($uri !== '/' && file_exists(__DIR__.'/public'.$uri)) {
    return false;
}

// For static files that don't exist in public, we'll try to serve them from the root
if ($uri !== '/' && !file_exists(__DIR__.'/public'.$uri) && file_exists(__DIR__.$uri)) {
    $contents = file_get_contents(__DIR__.$uri);
    
    // Use error suppression operator to prevent "broken pipe" errors
    @file_put_contents(__DIR__.'/public'.$uri, $contents);
    
    return false;
}

require_once __DIR__.'/public/index.php'; 