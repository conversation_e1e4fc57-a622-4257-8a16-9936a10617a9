# Customer Sync System Documentation

## Overview

The Customer Sync System provides comprehensive offline functionality for customer data in the PharmaDesk application. It automatically synchronizes customer information, loyalty transactions, and sales history to IndexedDB for offline access during sales operations.

## Architecture

### Components

1. **CustomerOfflineDB** (`resources/js/customer-offline-db.js`)
   - IndexedDB management for customer data storage
   - CRUD operations for offline customer access
   - Data schema management and migrations

2. **CustomerSyncService** (`resources/js/customer-sync-service.js`)
   - Handles data synchronization between server and IndexedDB
   - Conflict resolution and retry logic
   - Online/offline state management

3. **CustomerSyncScheduler** (`resources/js/customer-sync-scheduler.js`)
   - Automatic background synchronization
   - Configurable sync intervals and quiet hours
   - Connection and visibility change handling

4. **CustomerSyncProgressComponent** (`resources/views/components/customer-sync-progress.blade.php`)
   - UI component for sync status and progress
   - Manual sync triggers and configuration
   - Real-time sync feedback

5. **CustomerSyncManager** (`resources/js/customer-sync-init.js`)
   - Orchestrates all sync components
   - Global configuration and initialization
   - Event coordination and notifications

## API Endpoints

### Customer Data Endpoints

- `GET /api/offline/customers` - Paginated customer list with search and filtering
- `GET /api/offline/customers/{id}` - Individual customer details with related data
- `POST /api/offline/customers` - Create new customer (offline-capable)

### Sync Management Endpoints

- `GET /api/offline/customers/sync-status` - Current sync status and statistics
- `POST /api/offline/customers/sync-trigger` - Trigger manual sync operation
- `GET /api/offline/customers/sync-progress/{syncId}` - Track sync progress
- `DELETE /api/offline/customers/sync-cancel/{syncId}` - Cancel running sync

## Features

### Offline-First Architecture

- **Local Storage**: All customer data stored in IndexedDB for offline access
- **Automatic Sync**: Background synchronization when online
- **Conflict Resolution**: Handles data conflicts between local and server
- **Graceful Degradation**: Full functionality when offline

### Customer Data Synchronization

- **Comprehensive Data**: Name, contact info, loyalty points, transaction history
- **Incremental Sync**: Only syncs changed data to minimize bandwidth
- **Batch Processing**: Efficient handling of large customer datasets
- **Real-time Updates**: Immediate sync of new customer data

### Loyalty Program Integration

- **Points Tracking**: Automatic loyalty points calculation and storage
- **Transaction History**: Complete loyalty transaction records
- **Tier Management**: Gold/Regular tier calculations
- **Offline Redemption**: Points redemption works offline

### Advanced Sync Features

- **Scheduled Sync**: Configurable automatic sync intervals
- **Manual Triggers**: On-demand sync initiation
- **Progress Tracking**: Real-time sync progress monitoring
- **Error Handling**: Comprehensive error recovery and retry logic

## Usage

### Basic Initialization

```javascript
// The system initializes automatically on page load
// Access via global instance
const syncManager = window.CustomerSyncManager;

// Check initialization status
const status = await syncManager.getStatus();
console.log('Sync system status:', status);
```

### Customer Operations

```javascript
// Get customers (offline-capable)
const customers = await syncManager.getCustomers({
    limit: 20,
    offset: 0,
    search: 'john',
    status: 'active'
});

// Get specific customer
const customer = await syncManager.getCustomerById(123);

// Search customers
const results = await syncManager.searchCustomers('john doe', 10);

// Save new customer
const newCustomer = await syncManager.saveCustomer({
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '+1234567890',
    status: 'active'
});
```

### Sync Management

```javascript
// Trigger manual sync
await syncManager.triggerSync({
    forceFullSync: false
});

// Update sync configuration
await syncManager.updateConfig({
    syncInterval: 10 * 60 * 1000, // 10 minutes
    enableNotifications: true
});

// Show/hide progress component
syncManager.toggleProgress(true);
```

### Event Handling

```javascript
// Listen for sync events
window.addEventListener('customer_sync_completed', (event) => {
    console.log('Sync completed:', event.detail);
});

window.addEventListener('customer_sync_error', (event) => {
    console.error('Sync failed:', event.detail.error);
});

window.addEventListener('customer_sync_progress', (event) => {
    console.log('Sync progress:', event.detail.progress_percentage + '%');
});
```

## Configuration

### Sync Scheduler Configuration

```javascript
const config = {
    syncInterval: 5 * 60 * 1000,    // 5 minutes
    autoSyncEnabled: true,           // Enable automatic sync
    syncOnStartup: true,             // Sync on application startup
    syncOnVisibilityChange: true,    // Sync when page becomes visible
    syncOnConnectionChange: true,    // Sync when connection restored
    quietHours: {
        enabled: false,              // Enable quiet hours
        start: '22:00',             // Quiet hours start time
        end: '06:00'                // Quiet hours end time
    }
};

await syncManager.updateConfig(config);
```

### Database Configuration

The IndexedDB stores are automatically created with the following structure:

- **customers**: Main customer data with computed loyalty attributes
- **loyalty_transactions**: Customer loyalty transaction history
- **customer_sales**: Recent sales history for customers
- **sync_metadata**: Sync timestamps and configuration

## Testing

### Running Tests

```bash
# Run customer sync API tests
php artisan test tests/Feature/CustomerSyncApiTest.php

# Run customer sync integration tests
php artisan test tests/Feature/CustomerSyncIntegrationTest.php

# Run all customer sync tests
php artisan test tests/Feature/CustomerSync*
```

### Test Coverage

- **API Endpoints**: 21 tests covering all sync endpoints
- **Integration**: 8 tests covering end-to-end scenarios
- **Total**: 29 tests with 242 assertions

## Performance Considerations

### Optimization Strategies

1. **Batch Processing**: Sync customers in batches of 50 to balance performance and memory usage
2. **Incremental Sync**: Use timestamp-based incremental sync to minimize data transfer
3. **Indexed Queries**: IndexedDB indexes on name, email, phone, and status for fast searches
4. **Connection Awareness**: Automatic sync pause when offline, resume when online

### Memory Management

- **Lazy Loading**: Customer details loaded on-demand
- **Data Cleanup**: Automatic cleanup of old sync metadata
- **Efficient Storage**: Optimized data structures for IndexedDB storage

## Troubleshooting

### Common Issues

1. **Sync Failures**: Check network connectivity and server availability
2. **Data Conflicts**: Review conflict resolution logs in browser console
3. **Performance Issues**: Adjust batch size and sync interval
4. **Storage Limits**: Monitor IndexedDB storage usage

### Debug Mode

```javascript
// Enable debug logging
localStorage.setItem('customer_sync_debug', 'true');

// View sync statistics
const stats = await syncManager.getStatus();
console.log('Sync statistics:', stats);

// Clear all data (use with caution)
await syncManager.clearAllData();
```

## Security Considerations

- **Data Encryption**: Customer data stored in IndexedDB is not encrypted by default
- **Access Control**: API endpoints should implement proper authentication
- **Data Validation**: All customer data validated on both client and server
- **Audit Trail**: Sync operations logged for security monitoring

## Future Enhancements

- **Real-time Sync**: WebSocket-based real-time synchronization
- **Conflict Resolution UI**: User interface for resolving data conflicts
- **Advanced Filtering**: More sophisticated customer filtering options
- **Export/Import**: Customer data export and import functionality
- **Analytics**: Sync performance analytics and reporting
