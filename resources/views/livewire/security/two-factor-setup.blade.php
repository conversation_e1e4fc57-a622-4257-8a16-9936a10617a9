<div>
    <div class="space-y-6">
        <div>
            <h3 class="text-lg font-medium text-gray-900">
                {{ __('Two Factor Authentication') }}
            </h3>

            <p class="mt-1 text-sm text-gray-600">
                {{ __('Add additional security to your account using two-factor authentication.') }}
            </p>
        </div>

        @if(! auth()->user()->two_factor_enabled)
            <div class="mt-5">
                @if($showingQrCode)
                    <div>
                        <p class="text-sm text-gray-600">
                            {{ __('To enable two-factor authentication, scan the following QR code using your phone\'s authenticator application or enter the setup key.') }}
                        </p>

                        <div class="mt-4">
                            <div class="p-2 inline-block bg-white border rounded-lg shadow-sm">
                                {!! $this->getQrCodeSvgProperty() !!}
                            </div>
                        </div>

                        <div class="mt-4 max-w-xl text-sm text-gray-600">
                            <p class="font-semibold">
                                {{ __('Setup Key') }}: {{ $secret }}
                            </p>
                        </div>

                        <div class="mt-4">
                            <x-label for="code" value="{{ __('Code') }}" />
                            <x-input wire:model="code"
                                     id="code"
                                     type="text"
                                     name="code"
                                     class="block mt-1 w-1/4"
                                     inputmode="numeric"
                                     autofocus
                                     autocomplete="one-time-code"
                                     placeholder="{{ __('Enter 6-digit code') }}" />
                            <x-input-error :messages="$errors->get('code')" class="mt-2" />
                        </div>

                        <div class="mt-5 flex items-center gap-4">
                            <x-button wire:click="enableTwoFactorAuth" wire:loading.attr="disabled" class="bg-green-600 hover:bg-green-700">
                                {{ __('Enable Two-Factor Authentication') }}
                            </x-button>

                            <x-secondary-button wire:click="generateNewSecret">
                                {{ __('Generate New QR Code') }}
                            </x-secondary-button>
                        </div>
                    </div>
                @endif
            </div>
        @else
            @if($showingRecoveryCodes)
                <div>
                    <p class="text-sm text-gray-600">
                        {{ __('Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two-factor authentication device is lost.') }}
                    </p>

                    <div class="grid gap-1 max-w-xl mt-4 px-4 py-4 font-mono text-sm bg-gray-100 rounded-lg">
                        @foreach ($recoveryCodes as $code)
                            <div class="select-all">{{ $code }}</div>
                        @endforeach
                    </div>

                    <div class="mt-5">
                        <x-button wire:click="$set('showingRecoveryCodes', false)" wire:loading.attr="disabled" class="bg-blue-600 hover:bg-blue-700">
                            {{ __('Done') }}
                        </x-button>
                    </div>
                </div>
            @else
                @if($showingConfirmation)
                    <div class="bg-white px-4 py-5 sm:p-6 shadow sm:rounded-lg border border-red-200">
                        <h3 class="text-lg font-medium text-red-900">
                            {{ __('Disable Two-Factor Authentication') }}
                        </h3>

                        <div class="mt-3 max-w-xl text-sm text-gray-600">
                            <p>
                                {{ __('Are you sure you want to disable two-factor authentication? Your account security will be reduced.') }}
                            </p>
                        </div>

                        <div class="mt-5 flex items-center gap-4">
                            <x-button wire:click="disableTwoFactorAuth" wire:loading.attr="disabled" class="bg-red-600 hover:bg-red-700">
                                {{ __('Yes, Disable It') }}
                            </x-button>

                            <x-secondary-button wire:click="$set('showingConfirmation', false)">
                                {{ __('Cancel') }}
                            </x-secondary-button>
                        </div>
                    </div>
                @else
                    <div class="flex items-center gap-4">
                        <x-button wire:click="confirmDisable" wire:loading.attr="disabled" class="bg-red-600 hover:bg-red-700">
                            {{ __('Disable Two-Factor Authentication') }}
                        </x-button>

                        @if(auth()->user()->two_factor_recovery_codes)
                            <x-secondary-button wire:click="$set('showingRecoveryCodes', true)">
                                {{ __('Show Recovery Codes') }}
                            </x-secondary-button>
                        @endif
                    </div>
                @endif
            @endif
        @endif
    </div>
</div>
