<div>
    {{-- The best athlete wants his opponent at his best. --}}
    <div class="mt-4 max-w-xl text-sm text-gray-600">
        <p>
            {{ __('Recovery codes allow you to access your account if your two-factor authentication device is lost or unavailable.') }}
        </p>
    </div>

    @if($showingRecoveryCodes)
        <div class="mt-4">
            <div class="bg-gray-100 rounded-lg p-4">
                <div class="text-sm text-gray-600 mb-4">
                    <p class="font-semibold">
                        {{ __('Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two-factor authentication device is lost.') }}
                    </p>
                </div>

                <div class="grid gap-1 max-w-xl font-mono text-sm">
                    @foreach ($this->recoveryCodes as $code)
                        <div class="p-2">{{ $code }}</div>
                    @endforeach
                </div>
            </div>
        </div>

        <div class="mt-4 flex space-x-4">
            <x-button wire:click="downloadRecoveryCodes" wire:loading.attr="disabled">
                {{ __('Download Codes') }}
            </x-button>

            <x-secondary-button wire:click="$toggle('showingRecoveryCodes')" wire:loading.attr="disabled">
                {{ __('Hide Codes') }}
            </x-secondary-button>
        </div>
    @else
        <div class="mt-4">
            @if($confirmingRegeneration)
                <div class="bg-white px-4 py-5 sm:p-6">
                    <h3 class="text-lg font-medium text-gray-900">
                        {{ __('Regenerate Recovery Codes') }}
                    </h3>

                    <div class="mt-2 max-w-xl text-sm text-gray-600">
                        <p>
                            {{ __('Regenerating recovery codes will invalidate your existing codes. Make sure you have access to your authenticator app before proceeding.') }}
                        </p>
                    </div>

                    <div class="mt-5 flex items-center space-x-4">
                        <x-button wire:click="regenerateRecoveryCodes" wire:loading.attr="disabled">
                            {{ __('Yes, Regenerate Codes') }}
                        </x-button>

                        <x-secondary-button wire:click="$toggle('confirmingRegeneration')" wire:loading.attr="disabled">
                            {{ __('Cancel') }}
                        </x-secondary-button>
                    </div>
                </div>
            @else
                <div class="flex items-center space-x-4">
                    <x-button wire:click="showRecoveryCodes" wire:loading.attr="disabled">
                        {{ __('Show Recovery Codes') }}
                    </x-button>

                    <x-secondary-button wire:click="confirmRegenerateRecoveryCodes" wire:loading.attr="disabled">
                        {{ __('Regenerate Recovery Codes') }}
                    </x-secondary-button>
                </div>
            @endif
        </div>
    @endif
</div>
