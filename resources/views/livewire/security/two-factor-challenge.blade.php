<div>
    {{-- In work, do what you enjoy. --}}
    <div class="mb-4 text-sm text-gray-600">
        @if($recovery)
            {{ __('Please confirm access to your account by entering one of your emergency recovery codes.') }}
        @else
            {{ __('Please confirm access to your account by entering the authentication code provided by your authenticator application.') }}
        @endif
    </div>

    <form wire:submit.prevent="confirmTwoFactorAuthentication">
        <div class="mt-4" x-data="{}">
            @if(! $recovery)
                <x-label for="code" value="{{ __('Code') }}" />
                <x-input wire:model="code"
                         id="code"
                         type="text"
                         inputmode="numeric"
                         class="block mt-1 w-full"
                         autofocus
                         autocomplete="one-time-code"
                         placeholder="{{ __('Enter 6-digit code') }}" />
            @else
                <x-label for="recovery_code" value="{{ __('Recovery Code') }}" />
                <x-input wire:model="code"
                         id="recovery_code"
                         type="text"
                         class="block mt-1 w-full"
                         autocomplete="one-time-code"
                         placeholder="{{ __('Enter recovery code') }}" />
            @endif

            <x-input-error for="code" class="mt-2" />
        </div>

        <div class="flex items-center justify-between mt-4">
            <label for="remember" class="inline-flex items-center">
                <input wire:model="rememberDevice"
                       id="remember"
                       type="checkbox"
                       class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500">
                <span class="ml-2 text-sm text-gray-600">{{ __('Remember this device') }}</span>
            </label>

            <button type="button"
                    class="text-sm text-gray-600 hover:text-gray-900 underline cursor-pointer"
                    wire:click="toggleRecovery">
                {{ $recovery ? __('Use an authentication code') : __('Use a recovery code') }}
            </button>
        </div>

        <div class="flex justify-end mt-4">
            <x-button class="ml-4" wire:loading.attr="disabled">
                {{ __('Confirm') }}
            </x-button>
        </div>
    </form>
</div>
