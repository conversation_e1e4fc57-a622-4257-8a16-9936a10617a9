<div>
    <form wire:submit="updateProfile" class="space-y-8">
        <!-- Profile Photo -->
        <div>
            <h3 class="text-base font-medium text-gray-900">Profile Photo</h3>
            <p class="text-sm text-gray-500 mt-1">Click or tap image to upload a new profile photo</p>
            
            <div class="mt-4 flex justify-center">
                <div class="relative w-32 h-32 bg-gray-100 rounded-full overflow-hidden cursor-pointer">
                    @if ($newAvatar)
                        <img class="w-full h-full object-cover" src="{{ $newAvatar->temporaryUrl() }}" alt="Profile photo">
                    @else
                        <img class="w-full h-full object-cover" src="{{ auth()->user()->avatar ? Storage::url(auth()->user()->avatar) : asset('images/default-avatar.png') }}" alt="Profile photo">
                    @endif
                    <label class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 cursor-pointer">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <input type="file" wire:model="newAvatar" class="hidden">
                    </label>
                </div>
            </div>
            @error('newAvatar') <span class="mt-1 text-sm text-red-600">{{ $message }}</span> @enderror
        </div>

        <!-- Basic Information -->
        <div>
            <h3 class="text-base font-medium text-gray-900">Basic Information</h3>
            <p class="text-sm text-gray-500 mt-1">Update your personal information here</p>

            <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                    <input type="text" wire:model="name" id="name" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    @error('name') <span class="mt-1 text-sm text-red-600">{{ $message }}</span> @enderror
                </div>

                <!-- Email -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                    <input type="email" wire:model="email" id="email" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    @error('email') <span class="mt-1 text-sm text-red-600">{{ $message }}</span> @enderror
                </div>

                <!-- Phone -->
                <div>
                    <label for="phone" class="block text-sm font-medium text-gray-700">Phone</label>
                    <input type="tel" wire:model="phone" id="phone" placeholder="Enter your phone number" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    @error('phone') <span class="mt-1 text-sm text-red-600">{{ $message }}</span> @enderror
                </div>

                <!-- Position -->
                <div>
                    <label for="position" class="block text-sm font-medium text-gray-700">Position</label>
                    <input type="text" wire:model="position" id="position" placeholder="Enter your position" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    @error('position') <span class="mt-1 text-sm text-red-600">{{ $message }}</span> @enderror
                </div>

                <!-- Timezone -->
                <div class="col-span-2">
                    <label for="timezone" class="block text-sm font-medium text-gray-700">Timezone</label>
                    <select wire:model="timezone" id="timezone" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        @foreach(timezone_identifiers_list() as $tz)
                            <option value="{{ $tz }}">{{ $tz }}</option>
                        @endforeach
                    </select>
                    @error('timezone') <span class="mt-1 text-sm text-red-600">{{ $message }}</span> @enderror
                </div>

                <!-- Bio -->
                <div class="col-span-2">
                    <label for="bio" class="block text-sm font-medium text-gray-700">Bio</label>
                    <textarea wire:model="bio" id="bio" rows="4" placeholder="Write a short bio about yourself" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
                    @error('bio') <span class="mt-1 text-sm text-red-600">{{ $message }}</span> @enderror
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-end gap-4">
            <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50">
                Cancel
            </button>
            <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-gray-900 border border-transparent rounded-md shadow-sm hover:bg-gray-800">
                Save Changes
            </button>
        </div>
    </form>
</div>