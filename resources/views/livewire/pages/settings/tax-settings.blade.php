<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
            <div class="p-6 lg:p-8">
                <h2 class="text-2xl font-medium text-gray-900">
                    Tax Settings
                </h2>

                <div class="mt-6">
                    @if (session('success'))
                        <div class="mb-4 px-4 py-2 bg-green-100 border border-green-200 text-green-700 rounded">
                            {{ session('success') }}
                        </div>
                    @endif

                    <form wire:submit="save" class="space-y-6">
                        <!-- Enable Tax -->
                        <div class="flex items-center">
                            <input wire:model="enableTax" type="checkbox" id="enableTax" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500">
                            <label for="enableTax" class="ml-2 block text-sm text-gray-900">Enable Tax Calculations</label>
                        </div>

                        @if($enableTax)
                            <!-- Default Tax Rate -->
                            <div>
                                <x-input-label for="defaultTaxRate" value="Default Tax Rate (%)" />
                                <x-text-input wire:model="defaultTaxRate" id="defaultTaxRate" class="block mt-1 w-full" type="number" step="0.01" min="0" max="100" required />
                                <x-input-error :messages="$errors->get('defaultTaxRate')" class="mt-2" />
                            </div>

                            <!-- Tax Number -->
                            <div>
                                <x-input-label for="taxNumber" value="Tax Registration Number" />
                                <x-text-input wire:model="taxNumber" id="taxNumber" class="block mt-1 w-full" type="text" />
                                <x-input-error :messages="$errors->get('taxNumber')" class="mt-2" />
                            </div>

                            <!-- Tax Rules -->
                            <div class="space-y-4">
                                <h3 class="text-lg font-medium text-gray-900">Tax Rules</h3>

                                <!-- Existing Rules -->
                                @foreach($taxRules as $index => $rule)
                                    <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded">
                                        <div class="flex-1">
                                            <p class="font-medium">{{ $rule['name'] }}</p>
                                            <p class="text-sm text-gray-600">Rate: {{ $rule['rate'] }}% | Applies to: {{ $rule['applies_to'] }}</p>
                                            @if($rule['applies_to'] === 'category')
                                                <p class="text-sm text-gray-600">Category ID: {{ $rule['category_id'] }}</p>
                                            @endif
                                        </div>
                                        <button type="button" wire:click="removeRule({{ $index }})" class="text-red-600 hover:text-red-800">
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>
                                @endforeach

                                <!-- Add New Rule -->
                                <div class="mt-4 p-4 bg-gray-50 rounded">
                                    <h4 class="text-md font-medium text-gray-900 mb-4">Add New Rule</h4>
                                    
                                    <div class="space-y-4">
                                        <div>
                                            <x-input-label for="newRule.name" value="Rule Name" />
                                            <x-text-input wire:model="newRule.name" id="newRule.name" class="block mt-1 w-full" type="text" />
                                            <x-input-error :messages="$errors->get('newRule.name')" class="mt-2" />
                                        </div>

                                        <div>
                                            <x-input-label for="newRule.rate" value="Tax Rate (%)" />
                                            <x-text-input wire:model="newRule.rate" id="newRule.rate" class="block mt-1 w-full" type="number" step="0.01" min="0" max="100" />
                                            <x-input-error :messages="$errors->get('newRule.rate')" class="mt-2" />
                                        </div>

                                        <div>
                                            <x-input-label for="newRule.applies_to" value="Applies To" />
                                            <x-select-input wire:model="newRule.applies_to" id="newRule.applies_to" class="block mt-1 w-full">
                                                <option value="all">All Products</option>
                                                <option value="category">Specific Category</option>
                                            </x-select-input>
                                            <x-input-error :messages="$errors->get('newRule.applies_to')" class="mt-2" />
                                        </div>

                                        @if($newRule['applies_to'] === 'category')
                                            <div>
                                                <x-input-label for="newRule.category_id" value="Category" />
                                                <x-select-input wire:model="newRule.category_id" id="newRule.category_id" class="block mt-1 w-full">
                                                    <option value="">Select Category</option>
                                                    @foreach(\App\Models\Inventory\Category::all() as $category)
                                                        <option value="{{ $category->id }}">{{ $category->name }}</option>
                                                    @endforeach
                                                </x-select-input>
                                                <x-input-error :messages="$errors->get('newRule.category_id')" class="mt-2" />
                                            </div>
                                        @endif

                                        <div>
                                            <x-primary-button type="button" wire:click="addRule" class="w-full justify-center">
                                                Add Rule
                                            </x-primary-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <div class="flex items-center justify-end mt-4">
                            <x-primary-button>
                                Save Settings
                            </x-primary-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
