<div>
    <x-admin-layout>
        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <div>
                            <x-section-title>
                                <x-slot name="title">{{ __('Security Settings') }}</x-slot>
                                <x-slot name="description">{{ __('Configure security settings for your application.') }}</x-slot>
                            </x-section-title>

                            <x-section-content>
                                <div class="space-y-6">
                                    <!-- Password Settings -->
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900">{{ __('Password Settings') }}</h3>
                                        <div class="mt-4 space-y-4">
                                            <div>
                                                <x-label for="minimumPasswordLength" value="{{ __('Minimum Password Length') }}" />
                                                <x-input id="minimumPasswordLength" type="number" class="mt-1 block w-full" wire:model="minimumPasswordLength" />
                                                <x-input-error for="minimumPasswordLength" class="mt-2" />
                                            </div>

                                            <div class="flex items-start">
                                                <div class="flex items-center h-5">
                                                    <x-checkbox id="requireUppercase" wire:model="requireUppercase" />
                                                </div>
                                                <div class="ml-3 text-sm">
                                                    <label for="requireUppercase" class="font-medium text-gray-700">{{ __('Require Uppercase Letters') }}</label>
                                                </div>
                                            </div>

                                            <div class="flex items-start">
                                                <div class="flex items-center h-5">
                                                    <x-checkbox id="requireNumbers" wire:model="requireNumbers" />
                                                </div>
                                                <div class="ml-3 text-sm">
                                                    <label for="requireNumbers" class="font-medium text-gray-700">{{ __('Require Numbers') }}</label>
                                                </div>
                                            </div>

                                            <div class="flex items-start">
                                                <div class="flex items-center h-5">
                                                    <x-checkbox id="requireSpecialCharacters" wire:model="requireSpecialCharacters" />
                                                </div>
                                                <div class="ml-3 text-sm">
                                                    <label for="requireSpecialCharacters" class="font-medium text-gray-700">{{ __('Require Special Characters') }}</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Session Settings -->
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900">{{ __('Session Settings') }}</h3>
                                        <div class="mt-4 space-y-4">
                                            <div>
                                                <x-label for="sessionTimeout" value="{{ __('Session Timeout (minutes)') }}" />
                                                <x-input id="sessionTimeout" type="number" class="mt-1 block w-full" wire:model="sessionTimeout" />
                                                <x-input-error for="sessionTimeout" class="mt-2" />
                                            </div>

                                            <div class="flex items-start">
                                                <div class="flex items-center h-5">
                                                    <x-checkbox id="forceLogout" wire:model="forceLogout" />
                                                </div>
                                                <div class="ml-3 text-sm">
                                                    <label for="forceLogout" class="font-medium text-gray-700">{{ __('Force Logout on Browser Close') }}</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Two-Factor Authentication -->
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900">{{ __('Two-Factor Authentication') }}</h3>
                                        <div class="mt-4 space-y-4">
                                            <div class="flex items-start">
                                                <div class="flex items-center h-5">
                                                    <x-checkbox id="require2FA" wire:model="require2FA" />
                                                </div>
                                                <div class="ml-3 text-sm">
                                                    <label for="require2FA" class="font-medium text-gray-700">{{ __('Require Two-Factor Authentication') }}</label>
                                                </div>
                                            </div>

                                            <div>
                                                <x-label for="twoFactorMethod" value="{{ __('Default 2FA Method') }}" />
                                                <x-select id="twoFactorMethod" class="mt-1 block w-full" wire:model="twoFactorMethod">
                                                    <option value="app">{{ __('Authenticator App') }}</option>
                                                    <option value="sms">{{ __('SMS') }}</option>
                                                    <option value="email">{{ __('Email') }}</option>
                                                </x-select>
                                                <x-input-error for="twoFactorMethod" class="mt-2" />
                                            </div>
                                        </div>
                                    </div>

                                    <!-- IP Security -->
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900">{{ __('IP Security') }}</h3>
                                        <div class="mt-4 space-y-4">
                                            <div class="flex items-start">
                                                <div class="flex items-center h-5">
                                                    <x-checkbox id="enableIpWhitelist" wire:model="enableIpWhitelist" />
                                                </div>
                                                <div class="ml-3 text-sm">
                                                    <label for="enableIpWhitelist" class="font-medium text-gray-700">{{ __('Enable IP Whitelist') }}</label>
                                                </div>
                                            </div>

                                            <div>
                                                <x-label for="whitelistedIps" value="{{ __('Whitelisted IP Addresses') }}" />
                                                <x-textarea id="whitelistedIps" class="mt-1 block w-full" wire:model="whitelistedIps" placeholder="Enter one IP address per line" />
                                                <x-input-error for="whitelistedIps" class="mt-2" />
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-6">
                                        <x-button wire:click="save" wire:loading.attr="disabled">
                                            {{ __('Save Changes') }}
                                        </x-button>

                                        <x-action-message on="saved">
                                            {{ __('Saved.') }}
                                        </x-action-message>
                                    </div>
                                </div>
                            </x-section-content>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </x-admin-layout>
</div>
