<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900 dark:text-gray-100">
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h2 class="text-2xl font-semibold">{{ __('Notification Settings') }}</h2>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">{{ __('Configure how and when you receive notifications.') }}</p>
                    </div>
                </div>

                <form wire:submit.prevent="save" class="space-y-8">
                    <!-- Email Settings Card -->
                    <div class="bg-white dark:bg-gray-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-6">
                                <div>
                                    <h3 class="text-lg font-medium">{{ __('Email Settings') }}</h3>
                                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">{{ __('Configure your email notification preferences.') }}</p>
                                </div>
                                <x-toggle-input 
                                    wire:model.live="emailEnabled"
                                    label="{{ __('Enable Email Notifications') }}"
                                />
                            </div>

                            @if($emailEnabled)
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                                    <div class="space-y-4">
                                        <div>
                                            <x-input-label for="smtpHost" :value="__('SMTP Host')" />
                                            <x-text-input wire:model="smtpHost" id="smtpHost" class="block mt-1 w-full" type="text" placeholder="smtp.example.com" />
                                            <x-input-error :messages="$errors->get('smtpHost')" class="mt-2" />
                                        </div>

                                        <div>
                                            <x-input-label for="smtpPort" :value="__('SMTP Port')" />
                                            <x-text-input wire:model="smtpPort" id="smtpPort" class="block mt-1 w-full" type="text" placeholder="587" />
                                            <x-input-error :messages="$errors->get('smtpPort')" class="mt-2" />
                                        </div>

                                        <div>
                                            <x-input-label for="smtpUsername" :value="__('SMTP Username')" />
                                            <x-text-input wire:model="smtpUsername" id="smtpUsername" class="block mt-1 w-full" type="text" placeholder="<EMAIL>" />
                                            <x-input-error :messages="$errors->get('smtpUsername')" class="mt-2" />
                                        </div>
                                    </div>

                                    <div class="space-y-4">
                                        <div>
                                            <x-input-label for="smtpPassword" :value="__('SMTP Password')" />
                                            <x-text-input wire:model="smtpPassword" id="smtpPassword" class="block mt-1 w-full" type="password" />
                                            <x-input-error :messages="$errors->get('smtpPassword')" class="mt-2" />
                                        </div>

                                        <div>
                                            <x-input-label for="smtpEncryption" :value="__('Encryption')" />
                                            <x-select-input wire:model="smtpEncryption" id="smtpEncryption" class="block mt-1 w-full">
                                                <option value="tls">TLS</option>
                                                <option value="ssl">SSL</option>
                                            </x-select-input>
                                            <x-input-error :messages="$errors->get('smtpEncryption')" class="mt-2" />
                                        </div>

                                        <div class="space-y-4">
                                            <div>
                                                <x-input-label for="fromAddress" :value="__('From Email Address')" />
                                                <x-text-input wire:model="fromAddress" id="fromAddress" class="block mt-1 w-full" type="email" placeholder="<EMAIL>" />
                                                <x-input-error :messages="$errors->get('fromAddress')" class="mt-2" />
                                            </div>

                                            <div>
                                                <x-input-label for="fromName" :value="__('From Name')" />
                                                <x-text-input wire:model="fromName" id="fromName" class="block mt-1 w-full" type="text" placeholder="Your Pharmacy Name" />
                                                <x-input-error :messages="$errors->get('fromName')" class="mt-2" />
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Email Test Configuration -->
                                <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ __('Test Email Configuration') }}</h4>
                                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ __('Send a test email to verify your configuration.') }}</p>
                                        </div>
                                        <div class="flex items-center space-x-4">
                                            <div class="flex items-center">
                                                <x-text-input wire:model="testEmailAddress" type="email" class="block w-64" placeholder="Enter test email address" />
                                            </div>
                                            <button wire:click="sendTestEmail" wire:loading.attr="disabled" type="button" class="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md font-semibold text-xs text-gray-700 dark:text-gray-300 uppercase tracking-widest shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 transition ease-in-out duration-150">
                                                <span wire:loading.remove wire:target="sendTestEmail">{{ __('Send Test Email') }}</span>
                                                <span wire:loading wire:target="sendTestEmail">{{ __('Sending...') }}</span>
                                            </button>
                                        </div>
                                    </div>
                                    @if(session('emailTestSuccess'))
                                        <div class="mt-2 text-sm text-green-600 dark:text-green-400">
                                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            {{ session('emailTestSuccess') }}
                                        </div>
                                    @endif
                                    @if(session('emailTestError'))
                                        <div class="mt-2 text-sm text-red-600 dark:text-red-400">
                                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            {{ session('emailTestError') }}
                                        </div>
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- SMS Settings Card -->
                    <div class="bg-white dark:bg-gray-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-6">
                                <div>
                                    <h3 class="text-lg font-medium">{{ __('SMS Settings') }}</h3>
                                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">{{ __('Configure your SMS notification preferences.') }}</p>
                                </div>
                                <x-toggle-input 
                                    wire:model.live="smsEnabled"
                                    label="{{ __('Enable SMS Notifications') }}"
                                />
                            </div>

                            @if($smsEnabled)
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                                    <div>
                                        <x-input-label for="smsProvider" :value="__('SMS Provider')" />
                                        <x-select-input wire:model="smsProvider" id="smsProvider" class="block mt-1 w-full">
                                            <option value="twilio">Twilio</option>
                                            <option value="nexmo">Nexmo</option>
                                        </x-select-input>
                                        <x-input-error :messages="$errors->get('smsProvider')" class="mt-2" />
                                    </div>

                                    <div>
                                        <x-input-label for="smsAccountSid" :value="__('Account SID')" />
                                        <x-text-input wire:model="smsAccountSid" id="smsAccountSid" class="block mt-1 w-full" type="text" />
                                        <x-input-error :messages="$errors->get('smsAccountSid')" class="mt-2" />
                                    </div>

                                    <div>
                                        <x-input-label for="smsAuthToken" :value="__('Auth Token')" />
                                        <x-text-input wire:model="smsAuthToken" id="smsAuthToken" class="block mt-1 w-full" type="password" />
                                        <x-input-error :messages="$errors->get('smsAuthToken')" class="mt-2" />
                                    </div>

                                    <div>
                                        <x-input-label for="smsFromNumber" :value="__('From Number')" />
                                        <x-text-input wire:model="smsFromNumber" id="smsFromNumber" class="block mt-1 w-full" type="text" placeholder="+**********" />
                                        <x-input-error :messages="$errors->get('smsFromNumber')" class="mt-2" />
                                        <p class="mt-1 text-sm text-gray-500">{{ __('Enter number in international format (e.g., +**********)') }}</p>
                                    </div>
                                </div>

                                <!-- SMS Test Configuration -->
                                <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ __('Test SMS Configuration') }}</h4>
                                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ __('Send a test SMS to verify your configuration.') }}</p>
                                        </div>
                                        <div class="flex items-center space-x-4">
                                            <div class="flex items-center">
                                                <x-text-input wire:model="testPhoneNumber" type="tel" class="block w-64" placeholder="Enter test phone number" />
                                            </div>
                                            <button wire:click="sendTestSMS" wire:loading.attr="disabled" type="button" class="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md font-semibold text-xs text-gray-700 dark:text-gray-300 uppercase tracking-widest shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 transition ease-in-out duration-150">
                                                <span wire:loading.remove wire:target="sendTestSMS">{{ __('Send Test SMS') }}</span>
                                                <span wire:loading wire:target="sendTestSMS">{{ __('Sending...') }}</span>
                                            </button>
                                        </div>
                                    </div>
                                    @if(session('smsTestSuccess'))
                                        <div class="mt-2 text-sm text-green-600 dark:text-green-400">
                                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            {{ session('smsTestSuccess') }}
                                        </div>
                                    @endif
                                    @if(session('smsTestError'))
                                        <div class="mt-2 text-sm text-red-600 dark:text-red-400">
                                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            {{ session('smsTestError') }}
                                        </div>
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Save Button Section -->
                    <div class="flex items-center justify-between px-6 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 rounded-lg">
                        <div class="flex items-center gap-4">
                            <x-primary-button>
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                {{ __('Save Settings') }}
                            </x-primary-button>

                            @if (session('success'))
                                <span class="text-sm text-green-600 dark:text-green-400">
                                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    {{ session('success') }}
                                </span>
                            @endif
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
