<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
            <div class="p-6 lg:p-8">
                <!-- Header Section -->
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900">General Settings</h2>
                            <p class="text-sm text-gray-600">Configure your pharmacy's basic information and preferences</p>
                        </div>
                    </div>
                </div>

                <!-- Success Message -->
                @if (session('success'))
                    <div class="mb-6 rounded-md bg-green-50 p-4 border border-green-200">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Livewire Success Message -->
                @if ($message)
                    <div class="mb-6 rounded-md bg-green-50 p-4 border border-green-200" x-data="{ show: true }" x-show="show" x-transition>
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-green-800">{{ $message }}</p>
                            </div>
                            <div class="ml-auto pl-3">
                                <div class="-mx-1.5 -my-1.5">
                                    <button @click="show = false" class="inline-flex rounded-md bg-green-50 p-1.5 text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-600 focus:ring-offset-2 focus:ring-offset-green-50">
                                        <svg class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <form wire:submit="save" class="space-y-8">
                    <!-- Basic Information Section -->
                    <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                                    <p class="text-sm text-gray-600">Essential details about your pharmacy</p>
                                </div>
                            </div>
                        </div>
                        <div class="px-6 py-6 space-y-6">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- Pharmacy Name -->
                                <div class="lg:col-span-2">
                                    <x-input-label for="pharmacyName" value="Pharmacy Name" />
                                    <div class="mt-1 relative">
                                        <x-text-input wire:model="pharmacyName" id="pharmacyName" class="block w-full pl-10" type="text" required placeholder="Enter your pharmacy name" />
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                            </svg>
                                        </div>
                                    </div>
                                    <x-input-error :messages="$errors->get('pharmacyName')" class="mt-2" />
                                </div>

                                <!-- Address -->
                                <div class="lg:col-span-2">
                                    <x-input-label for="address" value="Address" />
                                    <div class="mt-1 relative">
                                        <x-textarea-input wire:model="address" id="address" class="block w-full pl-10" rows="3" required placeholder="Enter your pharmacy's complete address" />
                                        <div class="absolute top-3 left-0 pl-3 flex items-start pointer-events-none">
                                            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <x-input-error :messages="$errors->get('address')" class="mt-2" />
                                </div>

                                <!-- Phone -->
                                <div>
                                    <x-input-label for="phone" value="Phone Number" />
                                    <div class="mt-1 relative">
                                        <x-text-input wire:model="phone" id="phone" class="block w-full pl-10" type="tel" required placeholder="Enter phone number" />
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <x-input-error :messages="$errors->get('phone')" class="mt-2" />
                                </div>

                                <!-- Email -->
                                <div>
                                    <x-input-label for="email" value="Email Address" />
                                    <div class="mt-1 relative">
                                        <x-text-input wire:model="email" id="email" class="block w-full pl-10" type="email" required placeholder="Enter email address" />
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <x-input-error :messages="$errors->get('email')" class="mt-2" />
                                </div>

                                <!-- Website -->
                                <div class="lg:col-span-2">
                                    <x-input-label for="website" value="Website URL" />
                                    <div class="mt-1 relative">
                                        <x-text-input wire:model="website" id="website" class="block w-full pl-10" type="url" placeholder="https://www.yourpharmacy.com" />
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9" />
                                            </svg>
                                        </div>
                                    </div>
                                    <p class="mt-1 text-sm text-gray-500">Optional: Your pharmacy's website URL</p>
                                    <x-input-error :messages="$errors->get('website')" class="mt-2" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Regional Settings Section -->
                    <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900">Regional Settings</h3>
                                    <p class="text-sm text-gray-600">Configure currency, timezone, and localization preferences</p>
                                </div>
                            </div>
                        </div>
                        <div class="px-6 py-6 space-y-6">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- Currency -->
                                <div>
                                    <x-input-label for="currency" value="Currency" />
                                    <div class="mt-1 relative">
                                        <x-select-input wire:model="currency" id="currency" class="block w-full pl-10" required>
                                            <option value="">Select Currency</option>
                                            <option value="USD">USD - US Dollar ($)</option>
                                            <option value="EUR">EUR - Euro (€)</option>
                                            <option value="GBP">GBP - British Pound (£)</option>
                                            <option value="BDT">BDT - Bangladeshi Taka (৳)</option>
                                            <option value="INR">INR - Indian Rupee (₹)</option>
                                            <option value="CAD">CAD - Canadian Dollar (C$)</option>
                                            <option value="AUD">AUD - Australian Dollar (A$)</option>
                                            <option value="JPY">JPY - Japanese Yen (¥)</option>
                                            <option value="CNY">CNY - Chinese Yuan (¥)</option>
                                            <option value="SGD">SGD - Singapore Dollar (S$)</option>
                                            <option value="MYR">MYR - Malaysian Ringgit (RM)</option>
                                            <option value="THB">THB - Thai Baht (฿)</option>
                                            <option value="PKR">PKR - Pakistani Rupee (₨)</option>
                                            <option value="LKR">LKR - Sri Lankan Rupee (Rs)</option>
                                            <option value="NPR">NPR - Nepalese Rupee (Rs)</option>
                                        </x-select-input>
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                            </svg>
                                        </div>
                                    </div>
                                    <p class="mt-1 text-sm text-gray-500">Currency used for pricing and transactions</p>
                                    <x-input-error :messages="$errors->get('currency')" class="mt-2" />
                                </div>

                                <!-- Timezone -->
                                <div>
                                    <x-input-label for="timezone" value="Timezone" />
                                    <div class="mt-1 relative">
                                        <x-select-input wire:model="timezone" id="timezone" class="block w-full pl-10" required>
                                            <option value="">Select Timezone</option>
                                            <optgroup label="UTC">
                                                <option value="UTC">UTC - Coordinated Universal Time</option>
                                            </optgroup>
                                            <optgroup label="Asia">
                                                <option value="Asia/Dhaka">Asia/Dhaka (GMT+6)</option>
                                                <option value="Asia/Kolkata">Asia/Kolkata (GMT+5:30)</option>
                                                <option value="Asia/Karachi">Asia/Karachi (GMT+5)</option>
                                                <option value="Asia/Colombo">Asia/Colombo (GMT+5:30)</option>
                                                <option value="Asia/Kathmandu">Asia/Kathmandu (GMT+5:45)</option>
                                                <option value="Asia/Singapore">Asia/Singapore (GMT+8)</option>
                                                <option value="Asia/Kuala_Lumpur">Asia/Kuala_Lumpur (GMT+8)</option>
                                                <option value="Asia/Bangkok">Asia/Bangkok (GMT+7)</option>
                                                <option value="Asia/Tokyo">Asia/Tokyo (GMT+9)</option>
                                                <option value="Asia/Shanghai">Asia/Shanghai (GMT+8)</option>
                                                <option value="Asia/Dubai">Asia/Dubai (GMT+4)</option>
                                            </optgroup>
                                            <optgroup label="Europe">
                                                <option value="Europe/London">Europe/London (GMT+0/+1)</option>
                                                <option value="Europe/Paris">Europe/Paris (GMT+1/+2)</option>
                                                <option value="Europe/Berlin">Europe/Berlin (GMT+1/+2)</option>
                                                <option value="Europe/Rome">Europe/Rome (GMT+1/+2)</option>
                                                <option value="Europe/Madrid">Europe/Madrid (GMT+1/+2)</option>
                                            </optgroup>
                                            <optgroup label="America">
                                                <option value="America/New_York">America/New_York (GMT-5/-4)</option>
                                                <option value="America/Chicago">America/Chicago (GMT-6/-5)</option>
                                                <option value="America/Denver">America/Denver (GMT-7/-6)</option>
                                                <option value="America/Los_Angeles">America/Los_Angeles (GMT-8/-7)</option>
                                                <option value="America/Toronto">America/Toronto (GMT-5/-4)</option>
                                            </optgroup>
                                            <optgroup label="Australia">
                                                <option value="Australia/Sydney">Australia/Sydney (GMT+10/+11)</option>
                                                <option value="Australia/Melbourne">Australia/Melbourne (GMT+10/+11)</option>
                                                <option value="Australia/Perth">Australia/Perth (GMT+8)</option>
                                            </optgroup>
                                        </x-select-input>
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <p class="mt-1 text-sm text-gray-500">Timezone for dates and timestamps</p>
                                    <x-input-error :messages="$errors->get('timezone')" class="mt-2" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Branding & Legal Section -->
                    <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900">Branding & Legal Information</h3>
                                    <p class="text-sm text-gray-600">Upload your logo and configure legal details</p>
                                </div>
                            </div>
                        </div>
                        <div class="px-6 py-6 space-y-6">
                            <!-- Logo Upload -->
                            <div>
                                <x-input-label for="logo" value="Pharmacy Logo" />
                                <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors">
                                    <div class="space-y-1 text-center">
                                        @if($logo)
                                            <div class="mb-4">
                                                <img src="{{ $logo->temporaryUrl() }}" alt="Logo Preview" class="mx-auto h-32 w-auto rounded-lg shadow-md">
                                                <p class="mt-2 text-sm text-gray-600">New logo preview</p>
                                            </div>
                                        @elseif($this->getSetting('logo'))
                                            <div class="mb-4">
                                                <img src="{{ Storage::url($this->getSetting('logo')) }}" alt="Current Logo" class="mx-auto h-32 w-auto rounded-lg shadow-md">
                                                <p class="mt-2 text-sm text-gray-600">Current logo</p>
                                            </div>
                                        @else
                                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                            </svg>
                                        @endif
                                        <div class="flex text-sm text-gray-600">
                                            <label for="logo" class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500">
                                                <span>{{ $logo || $this->getSetting('logo') ? 'Change logo' : 'Upload a logo' }}</span>
                                                <input wire:model="logo" id="logo" name="logo" type="file" class="sr-only" accept="image/*">
                                            </label>
                                            <p class="pl-1">or drag and drop</p>
                                        </div>
                                        <p class="text-xs text-gray-500">PNG, JPG, GIF up to 1MB</p>
                                    </div>
                                </div>
                                <x-input-error :messages="$errors->get('logo')" class="mt-2" />
                            </div>

                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- VAT Number -->
                                <div>
                                    <x-input-label for="vatNumber" value="VAT/Tax Number" />
                                    <div class="mt-1 relative">
                                        <x-text-input wire:model="vatNumber" id="vatNumber" class="block w-full pl-10" type="text" placeholder="Enter VAT/Tax number" />
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <p class="mt-1 text-sm text-gray-500">Optional: VAT or tax registration number</p>
                                    <x-input-error :messages="$errors->get('vatNumber')" class="mt-2" />
                                </div>

                                <!-- Registration Number -->
                                <div>
                                    <x-input-label for="registrationNumber" value="Business Registration Number" />
                                    <div class="mt-1 relative">
                                        <x-text-input wire:model="registrationNumber" id="registrationNumber" class="block w-full pl-10" type="text" placeholder="Enter registration number" />
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <p class="mt-1 text-sm text-gray-500">Optional: Business or pharmacy license number</p>
                                    <x-input-error :messages="$errors->get('registrationNumber')" class="mt-2" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="bg-gray-50 px-6 py-4 border-t border-gray-200 rounded-b-lg">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-sm text-gray-500">
                                <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                Changes will be applied immediately after saving
                            </div>
                            <div class="flex items-center space-x-3">
                                <button type="button"
                                        onclick="window.location.reload()"
                                        class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 shadow-sm text-sm font-semibold rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 ease-in-out transform hover:scale-105 hover:shadow-md">
                                    <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                    </svg>
                                    Reset
                                </button>
                                <button type="submit"
                                        wire:loading.attr="disabled"
                                        class="relative inline-flex items-center justify-center px-6 py-3 bg-indigo-600 hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-800 border border-transparent rounded-lg font-semibold text-sm text-white tracking-wide focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all duration-200 ease-in-out transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none shadow-lg hover:shadow-xl"
                                        style="background-color: #5647e5; border-color: #5647e5;">
                                    <span wire:loading.remove class="flex items-center">
                                        <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                        Save Settings
                                    </span>
                                    <span wire:loading class="flex items-center">
                                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Saving...
                                    </span>
                                    <!-- Hover effect overlay -->
                                    <div class="absolute inset-0 bg-white opacity-0 hover:opacity-10 transition-opacity duration-200 rounded-lg pointer-events-none"></div>
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
