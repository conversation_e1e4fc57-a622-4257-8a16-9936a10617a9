<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900 dark:text-gray-100">
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h2 class="text-2xl font-semibold">{{ __('Payment Settings') }}</h2>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">{{ __('Configure your payment methods and preferences.') }}</p>
                    </div>
                </div>

                <form wire:submit.prevent="save" class="space-y-8">
                    <!-- Default Payment Method Card -->
                    <div class="bg-white dark:bg-gray-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <h3 class="text-lg font-medium">{{ __('Default Payment Method') }}</h3>
                                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">{{ __('Select the default payment method for transactions.') }}</p>
                                </div>
                            </div>
                            <div class="mt-4">
                                <x-select-input wire:model="defaultMethod" id="defaultMethod" class="block w-full max-w-md">
                                    <option value="cash">{{ __('Cash Payment') }}</option>
                                    <option value="stripe">{{ __('Credit Card (Stripe)') }}</option>
                                    <option value="paypal">{{ __('PayPal') }}</option>
                                    <option value="bank_transfer">{{ __('Bank Transfer') }}</option>
                                </x-select-input>
                                <x-input-error :messages="$errors->get('defaultMethod')" class="mt-2" />
                            </div>
                        </div>
                    </div>

                    <!-- Payment Methods Configuration -->
                    <div class="bg-white dark:bg-gray-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-6">
                                <div>
                                    <h3 class="text-lg font-medium">{{ __('Payment Methods') }}</h3>
                                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">{{ __('Enable and configure available payment methods.') }}</p>
                                </div>
                            </div>

                            <div class="space-y-6">
                                <!-- Cash -->
                                <div class="p-4 rounded-lg bg-gray-50 dark:bg-gray-800 border border-gray-100 dark:border-gray-700">
                                    <x-toggle-input 
                                        wire:model.live="cashEnabled"
                                        label="{{ __('Cash Payment') }}"
                                    />
                                    <p class="mt-2 ml-12 text-sm text-gray-600 dark:text-gray-400">{{ __('Accept cash payments at your physical location.') }}</p>
                                </div>

                                <!-- Stripe -->
                                <div class="p-4 rounded-lg bg-gray-50 dark:bg-gray-800 border border-gray-100 dark:border-gray-700">
                                    <x-toggle-input 
                                        wire:model.live="stripeEnabled"
                                        label="{{ __('Credit Card (Stripe)') }}"
                                    />
                                    <p class="mt-2 ml-12 text-sm text-gray-600 dark:text-gray-400">{{ __('Accept credit card payments via Stripe.') }}</p>
                                    @if($stripeEnabled)
                                        <div class="mt-4 ml-12 grid gap-4">
                                            <div class="max-w-md">
                                                <x-input-label for="stripeKey" :value="__('Stripe Public Key')" />
                                                <x-text-input wire:model="stripeKey" id="stripeKey" class="block mt-1 w-full" type="text" placeholder="pk_test_..." />
                                                <x-input-error :messages="$errors->get('stripeKey')" class="mt-2" />
                                            </div>
                                            <div class="max-w-md">
                                                <x-input-label for="stripeSecret" :value="__('Stripe Secret Key')" />
                                                <x-text-input wire:model="stripeSecret" id="stripeSecret" class="block mt-1 w-full" type="password" placeholder="sk_test_..." />
                                                <x-input-error :messages="$errors->get('stripeSecret')" class="mt-2" />
                                            </div>
                                        </div>
                                    @endif
                                </div>

                                <!-- PayPal -->
                                <div class="p-4 rounded-lg bg-gray-50 dark:bg-gray-800 border border-gray-100 dark:border-gray-700">
                                    <x-toggle-input 
                                        wire:model.live="paypalEnabled"
                                        label="{{ __('PayPal') }}"
                                    />
                                    <p class="mt-2 ml-12 text-sm text-gray-600 dark:text-gray-400">{{ __('Accept payments through PayPal.') }}</p>
                                    @if($paypalEnabled)
                                        <div class="mt-4 ml-12 grid gap-4">
                                            <div class="max-w-md">
                                                <x-input-label for="paypalClientId" :value="__('PayPal Client ID')" />
                                                <x-text-input wire:model="paypalClientId" id="paypalClientId" class="block mt-1 w-full" type="text" />
                                                <x-input-error :messages="$errors->get('paypalClientId')" class="mt-2" />
                                            </div>
                                            <div class="max-w-md">
                                                <x-input-label for="paypalSecret" :value="__('PayPal Secret')" />
                                                <x-text-input wire:model="paypalSecret" id="paypalSecret" class="block mt-1 w-full" type="password" />
                                                <x-input-error :messages="$errors->get('paypalSecret')" class="mt-2" />
                                            </div>
                                        </div>
                                    @endif
                                </div>

                                <!-- Bank Transfer -->
                                <div class="p-4 rounded-lg bg-gray-50 dark:bg-gray-800 border border-gray-100 dark:border-gray-700">
                                    <x-toggle-input 
                                        wire:model.live="bankTransferEnabled"
                                        label="{{ __('Bank Transfer') }}"
                                    />
                                    <p class="mt-2 ml-12 text-sm text-gray-600 dark:text-gray-400">{{ __('Accept payments via bank transfer.') }}</p>
                                    @if($bankTransferEnabled)
                                        <div class="mt-4 ml-12 max-w-md">
                                            <x-input-label for="bankDetails" :value="__('Bank Account Details')" />
                                            <x-textarea-input 
                                                wire:model="bankDetails" 
                                                id="bankDetails" 
                                                class="block mt-1 w-full" 
                                                rows="4"
                                                placeholder="{{ __('Enter your bank account details (Account number, Bank name, SWIFT code, etc.)') }}" 
                                            />
                                            <x-input-error :messages="$errors->get('bankDetails')" class="mt-2" />
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Save Button Section -->
                    <div class="flex items-center justify-between px-6 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 rounded-lg">
                        <div class="flex items-center gap-4">
                            <x-primary-button>
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                {{ __('Save Settings') }}
                            </x-primary-button>

                            @if($message)
                                <span class="text-sm text-green-600 dark:text-green-400">
                                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    {{ $message }}
                                </span>
                            @endif
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
