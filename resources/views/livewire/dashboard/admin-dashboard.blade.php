<div>
    {{-- Because she competes with no one, no one can compete with her. --}}

    <!-- Error Message -->
    @if(session()->has('error'))
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6">
            <p class="font-bold">Error:</p>
            <p>{{ session('error') }}</p>
            <p class="text-sm mt-2">Please try refreshing the page. If the problem persists, contact support.</p>
        </div>
    @endif

    <!-- Sales Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-2">Today's Sales</h3>
            <p class="text-3xl font-bold text-blue-600">৳{{ number_format($salesStats['today'] ?? 0, 2) }}</p>
            @if(isset($salesStats['yesterday']) && $salesStats['yesterday'] > 0)
                <p class="text-sm text-gray-500 mt-1">
                    @if($salesStats['today'] > $salesStats['yesterday'])
                        <span class="text-green-500">↑ {{ number_format((($salesStats['today'] - $salesStats['yesterday']) / $salesStats['yesterday']) * 100, 1) }}%</span>
                    @elseif($salesStats['today'] < $salesStats['yesterday'])
                        <span class="text-red-500">↓ {{ number_format((($salesStats['yesterday'] - $salesStats['today']) / $salesStats['yesterday']) * 100, 1) }}%</span>
                    @else
                        <span class="text-gray-500">→ 0%</span>
                    @endif
                    vs Yesterday
                </p>
            @endif
        </div>
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-2">Weekly Sales</h3>
            <p class="text-3xl font-bold text-green-600">৳{{ number_format($salesStats['week'] ?? 0, 2) }}</p>
            <p class="text-sm text-gray-500 mt-1">{{ number_format($salesStats['total_orders'] ?? 0) }} orders</p>
        </div>
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-2">Monthly Sales</h3>
            <p class="text-3xl font-bold text-purple-600">৳{{ number_format($salesStats['month'] ?? 0, 2) }}</p>
            <p class="text-sm text-gray-500 mt-1">Avg. ৳{{ number_format(($salesStats['month'] ?? 0) / max(1, now()->day), 2) }}/day</p>
        </div>
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-2">Pending Orders</h3>
            <p class="text-3xl font-bold text-orange-600">{{ number_format($salesStats['pending_orders'] ?? 0) }}</p>
            <p class="text-sm text-gray-500 mt-1">of {{ number_format($salesStats['total_orders'] ?? 0) }} total</p>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Sales Trend Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-4">Sales Trend (Last 30 Days)</h3>
            <div class="relative h-80 w-full" wire:ignore>
                <div class="absolute inset-0">
                    <div id="sales-trend-chart" class="sales-trend-chart" style="width: 100%; height: 100%; max-width: 100%;"></div>
                </div>
            </div>
        </div>

        <!-- Payment Methods Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-4">Payment Methods Distribution</h3>
            <div class="relative h-80 w-full" wire:ignore>
                <div class="absolute inset-0">
                    <div id="payment-methods-chart" style="width: 100%; height: 100%; max-width: 100%; overflow-x: hidden;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Inventory Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-2">Total Medicines</h3>
            <p class="text-3xl font-bold text-blue-600">{{ number_format($inventoryStats['total_medicines'] ?? 0) }}</p>
        </div>
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-2">Low Stock</h3>
            <p class="text-3xl font-bold text-yellow-600">{{ number_format($inventoryStats['low_stock'] ?? 0) }}</p>
        </div>
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-2">Out of Stock</h3>
            <p class="text-3xl font-bold text-red-600">{{ number_format($inventoryStats['out_of_stock'] ?? 0) }}</p>
        </div>
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-2">Expiring Soon</h3>
            <p class="text-3xl font-bold text-orange-600">{{ number_format($inventoryStats['expiring_soon'] ?? 0) }}</p>
        </div>
    </div>

    <!-- Recent Orders and Top Products -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Recent Orders -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-700">Recent Orders</h3>
            </div>
            <div class="p-6">
                @foreach($recentOrders as $order)
                    <div class="mb-4 last:mb-0">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="font-semibold text-gray-800">{{ $order->customer_name ?? 'Walk-in Customer' }}</p>
                                <p class="text-sm text-gray-500">{{ $order->invoice_number }}</p>
                                <p class="text-sm text-gray-600">{{ $order->created_at->format('M d, Y H:i') }}</p>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-blue-600">৳{{ number_format($order->total_amount, 2) }}</p>
                                <p class="text-sm text-gray-600">{{ ucfirst($order->payment_status) }}</p>
                                <p class="text-xs text-gray-500">{{ ucfirst(str_replace('_', ' ', $order->payment_method)) }}</p>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Top Products -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-700">Top Selling Products</h3>
            </div>
            <div class="p-6">
                @foreach($topProducts as $product)
                    <div class="mb-4 last:mb-0">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="font-semibold text-gray-800">{{ $product->medicine_name ?? 'N/A' }}</p>
                                <p class="text-sm text-gray-600">{{ $product->category_name ?? 'Uncategorized' }}</p>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-blue-600">{{ number_format($product->total_quantity) }} units</p>
                                <p class="text-sm text-gray-600">৳{{ number_format($product->total_amount, 2) }}</p>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Financial Insights -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-4">Financial Overview</h3>
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <p class="text-sm text-gray-600">Gross Profit</p>
                    <p class="text-xl font-bold text-green-600">৳{{ number_format($financialStats['gross_profit'] ?? 0, 2) }}</p>
                    <p class="text-xs text-gray-500">+{{ number_format($financialStats['profit_margin'] ?? 0, 1) }}% margin</p>
                </div>
                <div>
                    <p class="text-sm text-gray-600">Tax Collected</p>
                    <p class="text-xl font-bold text-blue-600">৳{{ number_format($financialStats['tax_collected'] ?? 0, 2) }}</p>
                    <p class="text-xs text-gray-500">This month</p>
                </div>
                <div>
                    <p class="text-sm text-gray-600">Avg. Order Value</p>
                    <p class="text-xl font-bold text-purple-600">৳{{ number_format($financialStats['avg_order_value'] ?? 0, 2) }}</p>
                    <p class="text-xs text-gray-500">Last 30 days</p>
                </div>
                <div>
                    <p class="text-sm text-gray-600">Returns Value</p>
                    <p class="text-xl font-bold text-red-600">৳{{ number_format($financialStats['returns_value'] ?? 0, 2) }}</p>
                    <p class="text-xs text-gray-500">This month</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-4">Category Performance</h3>
            <div class="space-y-4">
                @foreach($categoryStats as $category)
                <div class="flex items-center justify-between">
                    <div>
                        <p class="font-semibold text-gray-700">{{ $category->name }}</p>
                        <p class="text-sm text-gray-500">{{ $category->total_items }} items</p>
                    </div>
                    <div class="text-right">
                        <p class="font-bold text-blue-600">৳{{ number_format($category->revenue, 2) }}</p>
                        <p class="text-sm {{ $category->growth >= 0 ? 'text-green-500' : 'text-red-500' }}">
                            {{ $category->growth >= 0 ? '↑' : '↓' }} {{ abs($category->growth) }}%
                        </p>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Inventory Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-4">Stock Insights</h3>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="text-sm text-gray-600">Stock Turnover Rate</p>
                        <p class="text-xl font-bold text-blue-600">{{ number_format($inventoryStats['turnover_rate'], 2) }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">Avg. Storage Time</p>
                        <p class="text-xl font-bold text-purple-600">{{ $inventoryStats['avg_storage_days'] }} days</p>
                    </div>
                </div>
                <div class="mt-4">
                    <h4 class="text-sm font-semibold text-gray-700 mb-2">Slow Moving Items</h4>
                    <div class="space-y-2">
                        @foreach($inventoryStats['slow_moving'] as $item)
                        <div class="flex justify-between items-center">
                            <p class="text-sm text-gray-600">{{ $item->name }}</p>
                            <p class="text-sm text-gray-700">{{ $item->days_in_stock }} days</p>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-4">Supplier Performance</h3>
            <div class="space-y-4">
                @foreach($supplierStats as $supplier)
                <div class="flex items-center justify-between">
                    <div>
                        <p class="font-semibold text-gray-700">{{ $supplier->name }}</p>
                        <p class="text-sm text-gray-500">{{ $supplier->total_orders }} orders</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm {{ $supplier->on_time_delivery >= 90 ? 'text-green-500' : 'text-yellow-500' }}">
                            {{ $supplier->on_time_delivery }}% on-time
                        </p>
                        <p class="text-sm {{ $supplier->quality_rating >= 4 ? 'text-green-500' : 'text-yellow-500' }}">
                            {{ $supplier->quality_rating }}/5 rating
                        </p>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Customer Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-4">Customer Insights</h3>
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <p class="text-sm text-gray-600">Retention Rate</p>
                    <p class="text-xl font-bold text-green-600">{{ number_format($customerStats['retention_rate'], 1) }}%</p>
                    <p class="text-xs text-gray-500">Last 30 days</p>
                </div>
                <div>
                    <p class="text-sm text-gray-600">New Customers</p>
                    <p class="text-xl font-bold text-blue-600">{{ $customerStats['new_customers'] }}</p>
                    <p class="text-xs text-gray-500">This month</p>
                </div>
                <div>
                    <p class="text-sm text-gray-600">Avg. Customer Value</p>
                    <p class="text-xl font-bold text-purple-600">৳{{ number_format($customerStats['avg_customer_value'], 2) }}</p>
                    <p class="text-xs text-gray-500">Lifetime</p>
                </div>
                <div>
                    <p class="text-sm text-gray-600">Active Loyalty Members</p>
                    <p class="text-xl font-bold text-orange-600">{{ $customerStats['active_loyalty_members'] }}</p>
                    <p class="text-xs text-gray-500">{{ number_format($customerStats['loyalty_percentage'], 1) }}% of total</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-4">Prescription Analytics</h3>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="text-sm text-gray-600">Total Prescriptions</p>
                        <p class="text-xl font-bold text-blue-600">{{ $prescriptionStats['total'] }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">Avg. Items/Prescription</p>
                        <p class="text-xl font-bold text-purple-600">{{ number_format($prescriptionStats['avg_items'], 1) }}</p>
                    </div>
                </div>
                <div class="mt-4">
                    <h4 class="text-sm font-semibold text-gray-700 mb-2">Top Prescribers</h4>
                    <div class="space-y-2">
                        @foreach($prescriptionStats['top_prescribers'] as $prescriber)
                        <div class="flex justify-between items-center">
                            <p class="text-sm text-gray-600">Dr. {{ $prescriber->name }}</p>
                            <p class="text-sm text-gray-700">{{ $prescriber->prescription_count }} prescriptions</p>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Operational Metrics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-4">Operational Performance</h3>
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <p class="text-sm text-gray-600">Avg. Processing Time</p>
                    <p class="text-xl font-bold text-blue-600">{{ $operationalStats['avg_processing_time'] }} min</p>
                    <p class="text-xs text-gray-500">Per order</p>
                </div>
                <div>
                    <p class="text-sm text-gray-600">Return Rate</p>
                    <p class="text-xl font-bold {{ $operationalStats['return_rate'] <= 5 ? 'text-green-600' : 'text-red-600' }}">
                        {{ number_format($operationalStats['return_rate'], 1) }}%
                    </p>
                    <p class="text-xs text-gray-500">Last 30 days</p>
                </div>
                <div>
                    <p class="text-sm text-gray-600">Order Accuracy</p>
                    <p class="text-xl font-bold text-green-600">{{ number_format($operationalStats['order_accuracy'], 1) }}%</p>
                    <p class="text-xs text-gray-500">This month</p>
                </div>
                <div>
                    <p class="text-sm text-gray-600">Staff Efficiency</p>
                    <p class="text-xl font-bold text-purple-600">{{ number_format($operationalStats['staff_efficiency'], 1) }}%</p>
                    <p class="text-xs text-gray-500">Based on targets</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-4">Insurance Claims Status</h3>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="text-sm text-gray-600">Pending Claims</p>
                        <p class="text-xl font-bold text-yellow-600">{{ $insuranceStats['pending_claims'] }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">Approved Claims</p>
                        <p class="text-xl font-bold text-green-600">{{ $insuranceStats['approved_claims'] }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">Rejected Claims</p>
                        <p class="text-xl font-bold text-red-600">{{ $insuranceStats['rejected_claims'] }}</p>
                    </div>
                </div>
                <div class="mt-4">
                    <h4 class="text-sm font-semibold text-gray-700 mb-2">Recent Claims</h4>
                    <div class="space-y-2">
                        @foreach($insuranceStats['recent_claims'] as $claim)
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-sm font-medium text-gray-700">{{ $claim->policy_number }}</p>
                                <p class="text-xs text-gray-500">{{ $claim->created_at->format('M d, Y') }}</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-700">৳{{ number_format($claim->amount, 2) }}</p>
                                <p class="text-xs {{ 
                                    $claim->status === 'approved' ? 'text-green-500' : 
                                    ($claim->status === 'rejected' ? 'text-red-500' : 'text-yellow-500') 
                                }}">
                                    {{ ucfirst($claim->status) }}
                                </p>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>


</div>
