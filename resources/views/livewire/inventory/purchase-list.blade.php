<div>
    <div class="bg-white shadow-sm rounded-lg overflow-hidden relative z-10">
        <!-- Header and Filters -->
        <div class="p-4 border-b border-gray-200">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-medium text-gray-900 truncate">Purchase Orders</h2>
                <a href="{{ route('inventory.purchases.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    New Purchase Order
                </a>
            </div>

            <!-- Search and Filters in Single Line -->
            <div class="flex flex-wrap items-center gap-2">
                <!-- Search with Suggestions -->
                <div class="flex-1 min-w-[200px]" x-data="{ 
                    searchTerm: @entangle('search'),
                    searchResults: [],
                    loading: false,
                    open: false,
                    noResults: false,
                    
                    init() {
                        this.$watch('searchTerm', (value) => {
                            if (value.length >= 3) {
                                this.fetchResults(value);
                            } else {
                                this.searchResults = [];
                                this.open = false;
                                this.noResults = false;
                            }
                        });
                    },
                    
                    async fetchResults(query) {
                        this.loading = true;
                        this.noResults = false;
                        try {
                            const response = await fetch(`{{ route('inventory.purchases.index') }}?search=${encodeURIComponent(query)}&format=json`, {
                                headers: {
                                    'Accept': 'application/json',
                                    'X-Requested-With': 'XMLHttpRequest'
                                }
                            });
                            if (!response.ok) throw new Error('Search failed');
                            const data = await response.json();
                            this.searchResults = data.purchases || [];
                            this.open = this.searchResults.length > 0;
                            this.noResults = this.searchResults.length === 0;
                        } catch (error) {
                            console.error('Search error:', error);
                            this.searchResults = [];
                            this.noResults = true;
                        } finally {
                            this.loading = false;
                        }
                    }
                }">
                    <div class="relative">
                        <input type="text" 
                               wire:model.debounce.300ms="search" 
                               x-model="searchTerm"
                               class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                               placeholder="Search by PO number, supplier name...">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <div x-show="loading" class="animate-spin">
                                <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                            <div x-show="!loading">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                            </div>
                        </div>

                        <!-- Search Results Dropdown -->
                        <div x-show="open" 
                             x-transition
                             @click.away="open = false"
                             class="absolute z-[60] mt-1 w-full bg-white shadow-lg rounded-md py-1 text-sm"
                             style="display: none; max-height: 300px; overflow-y: auto;">
                            <template x-for="result in searchResults" :key="result.id">
                                <a :href="`/inventory/purchases/${result.id}`" class="block px-4 py-2 hover:bg-gray-100">
                                    <div class="flex justify-between items-center">
                                        <div>
                                            <div class="font-medium" x-text="result.purchase_number"></div>
                                            <div class="text-xs text-gray-500" x-text="result.supplier.name"></div>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span class="px-2 py-1 text-xs rounded-full"
                                                  :class="{
                                                      'bg-blue-100 text-blue-800': result.status === 'ordered',
                                                      'bg-green-100 text-green-800': result.status === 'received',
                                                      'bg-orange-100 text-orange-800': result.status === 'partially_received',
                                                      'bg-yellow-100 text-yellow-800': result.status === 'pending',
                                                      'bg-red-100 text-red-800': result.status === 'cancelled'
                                                  }"
                                                  x-text="result.status === 'partially_received' ? 'Partially Received' : result.status"></span>
                                        </div>
                                    </div>
                                </a>
                            </template>
                        </div>

                        <!-- No Results Message -->
                        <div x-show="noResults && searchTerm.length >= 3" 
                             class="absolute z-[60] mt-1 w-full bg-white shadow-lg rounded-md py-2 px-4 text-sm text-gray-500"
                             style="display: none;">
                            No purchase orders found
                        </div>
                    </div>
                </div>

                <!-- Status Filter -->
                <div class="w-40">
                    <select wire:model.live="filters.status" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md @error('filters.status') border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500 @enderror">
                        <option value="">All Status</option>
                        <option value="pending">Pending</option>
                        <option value="ordered">Ordered</option>
                        <option value="received">Received</option>
                        <option value="partially_received">Partially Received</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                    @error('filters.status') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                </div>

                <!-- Payment Status Filter -->
                <div class="w-40">
                    <select wire:model.live="filters.payment_status" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md @error('filters.payment_status') border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500 @enderror">
                        <option value="">All Payment Status</option>
                        <option value="pending">Pending</option>
                        <option value="partial">Partial</option>
                        <option value="paid">Paid</option>
                        <option value="overdue">Overdue</option>
                    </select>
                    @error('filters.payment_status') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                </div>

                <!-- Supplier Filter -->
                <div class="w-48">
                    <select wire:model.live="filters.supplier_id" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md @error('filters.supplier_id') border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500 @enderror">
                        <option value="">All Suppliers</option>
                        @foreach($suppliers as $supplier)
                            <option value="{{ $supplier->id }}">{{ $supplier->name }}</option>
                        @endforeach
                    </select>
                    @error('filters.supplier_id') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                </div>

                <!-- Date Range -->
                <div class="flex items-center gap-2">
                    <div>
                        <input type="date" 
                               wire:model.live="filters.start_date" 
                               class="block w-40 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md @error('filters.start_date') border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500 @enderror"
                               max="{{ $filters['end_date'] ?? date('Y-m-d') }}">
                        @error('filters.start_date') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>
                    <span class="text-gray-500">to</span>
                    <div>
                        <input type="date" 
                               wire:model.live="filters.end_date" 
                               class="block w-40 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md @error('filters.end_date') border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500 @enderror"
                               min="{{ $filters['start_date'] }}"
                               max="{{ date('Y-m-d') }}">
                        @error('filters.end_date') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>
                </div>

                <!-- Clear Filters -->
                @if($search || $filters['status'] || $filters['payment_status'] || $filters['supplier_id'] || $filters['start_date'] || $filters['end_date'])
                    <button wire:click="resetFilters" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                        Clear
                    </button>
                @endif
            </div>
        </div>

        <!-- Table -->
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead>
                    <tr class="bg-gray-50">
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" wire:click="sortBy('purchase_number')">
                            <div class="flex items-center space-x-1">
                                <span>PO Number</span>
                                @if($sortField === 'purchase_number')
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        @if($sortDirection === 'asc')
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
                                        @else
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                        @endif
                                    </svg>
                                @endif
                            </div>
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" wire:click="sortBy('order_date')">
                            <div class="flex items-center space-x-1">
                                <span>Order Date</span>
                                @if($sortField === 'order_date')
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        @if($sortDirection === 'asc')
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
                                        @else
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                        @endif
                                    </svg>
                                @endif
                            </div>
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Status</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($purchases as $purchase)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ $purchase->purchase_number }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $purchase->supplier->name }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $purchase->order_date->format('M d, Y') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{
                                    $purchase->status === 'received' ? 'bg-green-100 text-green-800' :
                                    ($purchase->status === 'partially_received' ? 'bg-orange-100 text-orange-800' :
                                    ($purchase->status === 'ordered' ? 'bg-blue-100 text-blue-800' :
                                    ($purchase->status === 'cancelled' ? 'bg-red-100 text-red-800' :
                                    'bg-yellow-100 text-yellow-800')))
                                }}">
                                    {{ $purchase->status === 'partially_received' ? 'Partially Received' : ucfirst($purchase->status) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ 
                                    $purchase->payment_status === 'paid' ? 'bg-green-100 text-green-800' :
                                    ($purchase->payment_status === 'partial' ? 'bg-yellow-100 text-yellow-800' :
                                    ($purchase->payment_status === 'overdue' ? 'bg-red-100 text-red-800' :
                                    'bg-gray-100 text-gray-800'))
                                }}">
                                    {{ ucfirst($purchase->payment_status) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                                {{ number_format($purchase->final_amount, 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                                <a href="{{ route('inventory.purchases.show', $purchase) }}" class="text-indigo-600 hover:text-indigo-900">
                                    View
                                </a>
                                @if($purchase->status === 'pending')
                                    <a href="{{ route('inventory.purchases.edit', $purchase) }}" class="text-blue-600 hover:text-blue-900">
                                        Edit
                                    </a>
                                @endif
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                No purchase orders found.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($purchases->hasPages())
            <div class="px-4 py-3 border-t border-gray-200">
                {{ $purchases->links() }}
            </div>
        @endif
    </div>
</div> 