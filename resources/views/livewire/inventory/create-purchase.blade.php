<!-- Main Container -->
<div class="space-y-6">
    <form wire:submit.prevent="save">
        <div class="bg-white shadow-sm rounded-lg overflow-hidden">
            <!-- Header -->
            <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Supplier Information</h3>
            </div>

            <!-- Form Content -->
            <div class="px-4 py-5 sm:p-6 space-y-6">
                <!-- Basic Information -->
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <!-- Supplier -->
                    <div>
                        <label for="supplier_id" class="block text-sm font-medium text-gray-700">Supplier</label>
                        <select wire:model.live="supplier_id" id="supplier_id" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                            <option value="">Select Supplier</option>
                            @foreach($suppliers as $supplier)
                                <option value="{{ $supplier->id }}">{{ $supplier->name }}</option>
                            @endforeach
                        </select>
                        @error('supplier_id') <span class="mt-2 text-sm text-red-600">{{ $message }}</span> @enderror
                    </div>

                    <!-- Order Date -->
                    <div>
                        <label for="order_date" class="block text-sm font-medium text-gray-700">Order Date</label>
                        <input type="date" wire:model="order_date" id="order_date" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        @error('order_date') <span class="mt-2 text-sm text-red-600">{{ $message }}</span> @enderror
                    </div>

                    <!-- Expected Date -->
                    <div>
                        <label for="expected_date" class="block text-sm font-medium text-gray-700">Expected Delivery Date</label>
                        <input type="date" wire:model="expected_date" id="expected_date" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        @error('expected_date') <span class="mt-2 text-sm text-red-600">{{ $message }}</span> @enderror
                    </div>

                    <!-- Notes -->
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                        <textarea wire:model="notes" id="notes" rows="2" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"></textarea>
                        @error('notes') <span class="mt-2 text-sm text-red-600">{{ $message }}</span> @enderror
                    </div>
                </div>

                <!-- Items -->
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <h4 class="text-lg font-medium text-gray-900">Order Items</h4>
                        <button type="button" wire:click="addItem" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white {{ empty($supplier_id) ? 'bg-indigo-400 cursor-not-allowed' : 'bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500' }}" {{ empty($supplier_id) ? 'disabled' : '' }}>
                            <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                            </svg>
                            Add Item
                        </button>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead>
                                <tr>
                                    <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Medicine</th>
                                    <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                    <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Unit Price</th>
                                    <th class="px-4 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                    <th class="px-4 py-3 bg-gray-50"></th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($items as $index => $item)
                                    <tr>
                                        <td class="px-4 py-3">
                                            <div class="relative">
                                                <select wire:model.live="items.{{ $index }}.medicine_id" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" {{ empty($supplier_id) ? 'disabled' : '' }}>
                                                    <option value="">{{ empty($supplier_id) ? 'Select a supplier first' : 'Select Medicine' }}</option>
                                                    @forelse($medicines as $medicine)
                                                        <option value="{{ $medicine->id }}">{{ $medicine->name }}{{ $medicine->dosage ? ' ' . $medicine->dosage : '' }}</option>
                                                    @empty
                                                        <option value="" disabled>No medicines available for this supplier</option>
                                                    @endforelse
                                                </select>
                                                @if($loadingMedicines)
                                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                                    <svg class="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                    </svg>
                                                </div>
                                                @endif
                                            </div>
                                            @error("items.{$index}.medicine_id") <span class="mt-2 text-sm text-red-600">{{ $message }}</span> @enderror
                                        </td>
                                        <td class="px-4 py-3">
                                            <input type="number" wire:model="items.{{ $index }}.quantity_ordered" min="1" class="block w-24 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                            @error("items.{$index}.quantity_ordered") <span class="mt-2 text-sm text-red-600">{{ $message }}</span> @enderror
                                        </td>
                                        <td class="px-4 py-3">
                                            <div class="relative">
                                                <div class="flex items-center">
                                                    <input type="number" wire:model="items.{{ $index }}.unit_price" min="0" step="0.01" class="block w-32 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" {{ !empty($item['medicine_id']) && empty($item['price_editable']) ? 'readonly' : '' }}>
                                                    @if(!empty($item['medicine_id']))
                                                    <button type="button" wire:click="togglePriceEdit({{ $index }})" class="ml-2 text-indigo-600 hover:text-indigo-900">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ !empty($item['price_editable']) ? 'M5 13l4 4L19 7' : 'M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z' }}"/>
                                                        </svg>
                                                    </button>
                                                    @endif
                                                </div>
                                                @if($loadingMedicines)
                                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                                    <svg class="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                    </svg>
                                                </div>
                                                @endif
                                                @if(!empty($item['medicine_id']))
                                                <p class="mt-1 text-xs text-gray-500">
                                                    {{ !empty($item['price_editable']) ? 'Editing price manually' : 'Auto-filled from supplier price' }}
                                                </p>
                                                @endif
                                            </div>
                                            @error("items.{$index}.unit_price") <span class="mt-2 text-sm text-red-600">{{ $message }}</span> @enderror
                                        </td>
                                        <td class="px-4 py-3 text-right">
                                            {{ number_format($item['total_amount'], 2) }}
                                        </td>
                                        <td class="px-4 py-3 text-right">
                                            <button type="button" wire:click="removeItem({{ $index }})" class="text-red-600 hover:text-red-900">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                                </svg>
                                            </button>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div class="space-y-4">
                        <!-- Tax -->
                        <div>
                            <label for="tax_percentage" class="block text-sm font-medium text-gray-700">Tax Percentage (%)</label>
                            <input type="number" wire:model="tax_percentage" id="tax_percentage" min="0" max="100" step="0.01" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            @error('tax_percentage') <span class="mt-2 text-sm text-red-600">{{ $message }}</span> @enderror
                        </div>

                        <!-- Discount -->
                        <div>
                            <label for="discount_percentage" class="block text-sm font-medium text-gray-700">Discount Percentage (%)</label>
                            <input type="number" wire:model="discount_percentage" id="discount_percentage" min="0" max="100" step="0.01" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            @error('discount_percentage') <span class="mt-2 text-sm text-red-600">{{ $message }}</span> @enderror
                        </div>

                        <!-- Shipping Cost -->
                        <div>
                            <label for="shipping_cost" class="block text-sm font-medium text-gray-700">Shipping Cost</label>
                            <input type="number" wire:model="shipping_cost" id="shipping_cost" min="0" step="0.01" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            @error('shipping_cost') <span class="mt-2 text-sm text-red-600">{{ $message }}</span> @enderror
                        </div>
                    </div>

                    <div class="bg-gray-50 px-4 py-5 rounded-lg">
                        <dl class="space-y-4">
                            <div class="flex items-center justify-between">
                                <dt class="text-sm font-medium text-gray-500">Subtotal</dt>
                                <dd class="text-sm font-medium text-gray-900">{{ number_format(collect($items)->sum('total_amount'), 2) }}</dd>
                            </div>
                            <div class="flex items-center justify-between">
                                <dt class="text-sm font-medium text-gray-500">Tax</dt>
                                <dd class="text-sm font-medium text-gray-900">{{ number_format(collect($items)->sum('tax_amount'), 2) }}</dd>
                            </div>
                            <div class="flex items-center justify-between">
                                <dt class="text-sm font-medium text-gray-500">Discount</dt>
                                <dd class="text-sm font-medium text-gray-900">{{ number_format(collect($items)->sum('discount_amount'), 2) }}</dd>
                            </div>
                            <div class="flex items-center justify-between">
                                <dt class="text-sm font-medium text-gray-500">Shipping</dt>
                                <dd class="text-sm font-medium text-gray-900">{{ number_format($shipping_cost, 2) }}</dd>
                            </div>
                            <div class="border-t border-gray-200 pt-4 flex items-center justify-between">
                                <dt class="text-base font-medium text-gray-900">Order Total</dt>
                                <dd class="text-base font-medium text-gray-900">{{ number_format($orderTotal, 2) }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="px-4 py-3 bg-gray-50 text-right sm:px-6 space-x-2">
                <a href="{{ route('inventory.purchases.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Cancel
                </a>
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Create Purchase Order
                </button>
            </div>
        </div>
    </form>
</div>
