<div 
    x-data="{
        // Status states
        isOnline: navigator.onLine,
        isSyncing: false,
        pendingItems: 0,
        lastSync: localStorage.getItem('lastSync') || 'Never',
        syncError: null,
        dataFreshness: 'fresh',
        showDetails: false,
        dropdownPosition: { top: '4rem', right: '1rem' },
        
        // Computed properties
        get primaryStatus() {
            if (!this.isOnline) return 'offline';
            if (this.syncError) return 'error';
            if (this.isSyncing) return 'syncing';
            if (this.pendingItems > 0) return 'pending';
            return 'synced';
        },
        
        get statusConfig() {
            const configs = {
                offline: {
                    icon: '⚠',
                    text: 'Offline',
                    bgClass: 'bg-red-100 text-red-700 border-red-200',
                    dotClass: 'bg-red-500 animate-pulse'
                },
                error: {
                    icon: '✗',
                    text: 'Sync failed',
                    bgClass: 'bg-red-100 text-red-700 border-red-200',
                    dotClass: 'bg-red-500'
                },
                syncing: {
                    icon: '↻',
                    text: 'Syncing...',
                    bgClass: 'bg-blue-100 text-blue-700 border-blue-200',
                    dotClass: 'bg-blue-500 animate-pulse'
                },
                pending: {
                    icon: '⚠',
                    text: `${this.pendingItems} pending`,
                    bgClass: 'bg-yellow-100 text-yellow-700 border-yellow-200',
                    dotClass: 'bg-yellow-500'
                },
                synced: {
                    icon: '✓',
                    text: 'Synced',
                    bgClass: 'bg-green-100 text-green-700 border-green-200',
                    dotClass: 'bg-green-500'
                }
            };
            return configs[this.primaryStatus] || configs.synced;
        },

        updateDropdownPosition() {
            const button = this.$refs.statusButton;
            if (!button) return;

            const rect = button.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            const dropdownWidth = 320; // 20rem = 320px
            const dropdownHeight = 400; // Estimated height

            let top = rect.bottom + 8; // 8px gap below button
            let right = viewportWidth - rect.right;
            let left = 'auto';

            // Adjust for mobile
            if (viewportWidth < 768) {
                left = '1rem';
                right = '1rem';
            } else {
                // Ensure dropdown doesn't go off-screen horizontally
                if (rect.right - dropdownWidth < 0) {
                    // Not enough space on the right, align to left edge
                    left = rect.left + 'px';
                    right = 'auto';
                } else if (rect.right > viewportWidth) {
                    // Button is off-screen right, align to viewport right
                    right = '1rem';
                    left = 'auto';
                }
            }

            // Adjust for vertical overflow
            if (top + dropdownHeight > viewportHeight) {
                // Not enough space below, position above
                top = rect.top - dropdownHeight - 8;
                if (top < 0) {
                    // Not enough space above either, position at top of viewport
                    top = 8;
                }
            }

            this.dropdownPosition = {
                top: top + 'px',
                right: right === 'auto' ? 'auto' : right + 'px',
                left: left === 'auto' ? 'auto' : left
            };
        },

        toggleDetails() {
            if (!this.showDetails) {
                this.updateDropdownPosition();
            }
            this.showDetails = !this.showDetails;
        },

        async updateSyncInfo() {
            try {
                if (typeof window.getSyncStatus === 'function') {
                    const status = await window.getSyncStatus();
                    this.pendingItems = status.pendingItemsCount || 0;
                    this.lastSync = localStorage.getItem('lastSync') || 'Never';
                }
                
                // Update data freshness
                await this.updateDataFreshness();
            } catch (error) {
                console.error('Error getting sync status:', error);
            }
        },
        
        async updateDataFreshness() {
            try {
                const lastSync = localStorage.getItem('lastSync');
                if (!lastSync || lastSync === 'Never') {
                    this.dataFreshness = 'never';
                    return;
                }
                
                const lastSyncDate = new Date(lastSync);
                const now = new Date();
                const timeDiff = now - lastSyncDate;
                const minutes = Math.floor(timeDiff / (1000 * 60));
                
                if (minutes < 5) this.dataFreshness = 'fresh';
                else if (minutes < 30) this.dataFreshness = 'recent';
                else if (minutes < 1440) this.dataFreshness = 'stale'; // 24 hours
                else this.dataFreshness = 'old';
            } catch (error) {
                console.warn('Error updating data freshness:', error);
                this.dataFreshness = 'unknown';
            }
        },
        
        async triggerSync() {
            if (this.isSyncing) return;
            
            if (!this.isOnline) {
                if (typeof Livewire !== 'undefined') {
                    Livewire.dispatch('notify', { 
                        type: 'warning',
                        message: 'Cannot sync while offline'
                    });
                }
                return;
            }
            
            try {
                this.isSyncing = true;
                this.syncError = null;
                
                if (typeof window.forceSync === 'function') {
                    await window.forceSync();
                    if (typeof Livewire !== 'undefined') {
                        Livewire.dispatch('notify', { 
                            type: 'success',
                            message: 'Sync completed successfully'
                        });
                    }
                }
            } catch (error) {
                this.syncError = error.message || 'Unknown error';
                if (typeof Livewire !== 'undefined') {
                    Livewire.dispatch('notify', { 
                        type: 'error',
                        message: `Sync failed: ${error.message}`
                    });
                }
                
                // Clear error after 5 seconds
                setTimeout(() => {
                    this.syncError = null;
                }, 5000);
            } finally {
                this.isSyncing = false;
                this.updateSyncInfo();
            }
        },
        
        init() {
            // Initial update
            this.updateSyncInfo();
            
            // Listen for online/offline events
            window.addEventListener('online', () => {
                this.isOnline = true;
                this.updateSyncInfo();
            });
            
            window.addEventListener('offline', () => {
                this.isOnline = false;
            });
            
            // Listen for sync status changes
            window.addEventListener('sync-status-changed', (event) => {
                if (!event.detail) return;
                
                this.isSyncing = event.detail.syncing || false;
                
                if (event.detail.error) {
                    this.syncError = event.detail.errorMessage || 'Sync failed';
                    setTimeout(() => {
                        this.syncError = null;
                    }, 5000);
                } else if (!event.detail.syncing) {
                    this.syncError = null;
                }
                
                this.updateSyncInfo();
            });
            
            // Listen for service worker messages
            if (navigator.serviceWorker) {
                navigator.serviceWorker.addEventListener('message', (event) => {
                    if (event.data.type === 'sync-completed') {
                        this.isSyncing = false;
                        this.updateSyncInfo();
                        
                        if (typeof Livewire !== 'undefined') {
                            if (event.data.data.success) {
                                Livewire.dispatch('notify', { 
                                    type: 'success',
                                    message: `Sync completed: ${event.data.data.successCount} items synchronized`
                                });
                            } else {
                                Livewire.dispatch('notify', { 
                                    type: 'warning',
                                    message: `Sync completed with issues: ${event.data.data.failureCount} failures`
                                });
                            }
                        }
                    }
                });
            }
            
            // Periodic updates
            setInterval(() => {
                this.updateSyncInfo();
            }, 30000); // Update every 30 seconds

            // Handle window resize
            window.addEventListener('resize', () => {
                if (this.showDetails) {
                    this.updateDropdownPosition();
                }
            });

            // Handle scroll events to reposition dropdown
            window.addEventListener('scroll', () => {
                if (this.showDetails) {
                    this.updateDropdownPosition();
                }
            });
        }
    }"
    class="relative unified-status-wrapper"
    @click.away="showDetails = false"
    @keydown.escape="showDetails = false"
>
    <!-- Mobile Backdrop for Dropdown -->
    <div
        x-show="showDetails && window.innerWidth < 768"
        @click="showDetails = false"
        class="fixed inset-0 bg-black bg-opacity-25"
        style="z-index: 1050;"
        x-transition:enter="transition-opacity ease-out duration-200"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="transition-opacity ease-in duration-150"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
        x-cloak
    ></div>

    <!-- Main Status Button -->
    <button
        x-ref="statusButton"
        @click="primaryStatus === 'pending' ? triggerSync() : toggleDetails()"
        class="relative flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md border transition-all duration-200"
        :class="statusConfig.bgClass"
        :disabled="isSyncing"
        :title="statusConfig.text"
        :aria-expanded="showDetails"
        aria-haspopup="true"
        aria-label="System status and sync controls"
    >
        <!-- Status Icon -->
        <div class="flex items-center">
            <div 
                class="w-2 h-2 rounded-full mr-2"
                :class="statusConfig.dotClass"
            ></div>
            
            <!-- Sync Icon (spinning when syncing) -->
            <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 transition-transform duration-700"
                :class="{ 'animate-spin': isSyncing }"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                x-show="primaryStatus === 'syncing' || primaryStatus === 'pending'"
            >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            
            <!-- Offline Icon -->
            <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                x-show="primaryStatus === 'offline'"
            >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636a9 9 0 010 12.728m0 0l-2.829-2.829m2.829 2.829L21 21M15.536 8.464a5 5 0 010 7.072m0 0l-2.829-2.829m-4.243 2.829a4.978 4.978 0 01-1.414-2.83m-1.414 5.658a9 9 0 01-2.167-9.238m7.824 2.167a1 1 0 111.414 1.414m-1.414-1.414L3 3m8.293 8.293l1.414 1.414" />
            </svg>
            
            <!-- Success Icon -->
            <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                x-show="primaryStatus === 'synced'"
            >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            
            <!-- Error Icon -->
            <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                x-show="primaryStatus === 'error'"
            >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </div>

        <!-- Status Text - Hidden on mobile, visible on desktop -->
        <span class="hidden md:inline" x-text="statusConfig.text"></span>

        <!-- Pending indicator dot for mobile -->
        <span
            x-show="pendingItems > 0 && !isSyncing && primaryStatus === 'pending'"
            class="md:hidden absolute -top-1 -right-1 h-3 w-3 bg-yellow-400 border-2 border-white rounded-full flex items-center justify-center"
        >
            <span class="text-xs font-bold text-yellow-800" x-text="pendingItems > 9 ? '9+' : pendingItems"></span>
        </span>
    </button>

    <!-- Status Details Popover -->
    <div
        x-show="showDetails"
        x-transition:enter="transition ease-out duration-200"
        x-transition:enter-start="opacity-0 scale-95"
        x-transition:enter-end="opacity-100 scale-100"
        x-transition:leave="transition ease-in duration-150"
        x-transition:leave-start="opacity-100 scale-100"
        x-transition:leave-end="opacity-0 scale-95"
        class="fixed bg-white rounded-lg shadow-xl border border-gray-200 unified-status-dropdown"
        :class="{
            'w-80': window.innerWidth >= 768,
            'left-4 right-4 w-auto': window.innerWidth < 768
        }"
        :style="{
            'z-index': '1100',
            'top': dropdownPosition.top,
            'right': dropdownPosition.right !== 'auto' ? dropdownPosition.right : undefined,
            'left': dropdownPosition.left !== 'auto' ? dropdownPosition.left : undefined
        }"
        role="dialog"
        aria-labelledby="status-dialog-title"
        x-cloak
    >
        <div class="p-4">
            <div class="flex items-center justify-between mb-3">
                <h3 id="status-dialog-title" class="text-sm font-semibold text-gray-900">System Status</h3>
                <!-- Close button for mobile -->
                <button
                    @click="showDetails = false"
                    class="md:hidden p-1 text-gray-400 hover:text-gray-600 rounded-md"
                    aria-label="Close status details"
                >
                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <!-- Connection Status -->
            <div class="flex items-center justify-between py-2 border-b border-gray-100">
                <span class="text-sm text-gray-600">Connection</span>
                <div class="flex items-center">
                    <div
                        class="w-2 h-2 rounded-full mr-2"
                        :class="isOnline ? 'bg-green-500' : 'bg-red-500 animate-pulse'"
                    ></div>
                    <span class="text-sm font-medium" :class="isOnline ? 'text-green-700' : 'text-red-700'" x-text="isOnline ? 'Online' : 'Offline'"></span>
                </div>
            </div>

            <!-- Sync Status -->
            <div class="flex items-center justify-between py-2 border-b border-gray-100">
                <span class="text-sm text-gray-600">Sync Status</span>
                <div class="flex items-center">
                    <div
                        class="w-2 h-2 rounded-full mr-2"
                        :class="{
                            'bg-blue-500 animate-pulse': isSyncing,
                            'bg-red-500': syncError,
                            'bg-yellow-500': pendingItems > 0 && !isSyncing,
                            'bg-green-500': pendingItems === 0 && !isSyncing && !syncError
                        }"
                    ></div>
                    <span class="text-sm font-medium" x-text="
                        isSyncing ? 'Syncing...' :
                        syncError ? 'Failed' :
                        pendingItems > 0 ? 'Pending' : 'Up to date'
                    "></span>
                </div>
            </div>

            <!-- Pending Items -->
            <div class="flex items-center justify-between py-2 border-b border-gray-100" x-show="pendingItems > 0 || isSyncing">
                <span class="text-sm text-gray-600">Pending Items</span>
                <span class="text-sm font-medium text-yellow-700" x-text="pendingItems"></span>
            </div>

            <!-- Last Sync -->
            <div class="flex items-center justify-between py-2 border-b border-gray-100">
                <span class="text-sm text-gray-600">Last Sync</span>
                <span class="text-sm text-gray-500" x-text="lastSync"></span>
            </div>

            <!-- Data Freshness -->
            <div class="flex items-center justify-between py-2">
                <span class="text-sm text-gray-600">Data Status</span>
                <div class="flex items-center">
                    <div
                        class="w-2 h-2 rounded-full mr-2"
                        :class="{
                            'bg-green-500': dataFreshness === 'fresh',
                            'bg-yellow-500': dataFreshness === 'recent',
                            'bg-orange-500': dataFreshness === 'stale',
                            'bg-red-500': dataFreshness === 'old',
                            'bg-gray-400': dataFreshness === 'never' || dataFreshness === 'unknown'
                        }"
                    ></div>
                    <span class="text-sm font-medium" x-text="
                        dataFreshness === 'fresh' ? 'Fresh' :
                        dataFreshness === 'recent' ? 'Recent' :
                        dataFreshness === 'stale' ? 'Stale' :
                        dataFreshness === 'old' ? 'Old' :
                        dataFreshness === 'never' ? 'No data' : 'Unknown'
                    "></span>
                </div>
            </div>

            <!-- Error Message -->
            <div x-show="syncError" class="mt-3 p-2 bg-red-50 border border-red-200 rounded-md">
                <p class="text-sm text-red-700">
                    <span class="font-medium">Error:</span>
                    <span x-text="syncError"></span>
                </p>
            </div>

            <!-- Action Buttons -->
            <div class="mt-4 flex gap-2">
                <button
                    @click="triggerSync()"
                    :disabled="isSyncing || !isOnline"
                    class="flex-1 px-3 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    x-show="pendingItems > 0 || syncError"
                >
                    <span x-show="!isSyncing">Sync Now</span>
                    <span x-show="isSyncing">Syncing...</span>
                </button>

                <button
                    @click="showDetails = false"
                    class="px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                    Close
                </button>
            </div>
        </div>
    </div>
</div>
