{{-- Customer Sync Progress Component --}}
<div id="customer-sync-progress" class="customer-sync-progress" style="display: none;">
    <div class="sync-progress-container">
        <div class="sync-progress-header">
            <div class="sync-progress-title">
                <i class="fas fa-users sync-icon"></i>
                <span class="sync-title-text">Customer Sync</span>
            </div>
            <div class="sync-progress-actions">
                <button id="customer-sync-toggle" class="btn btn-sm btn-outline-primary" title="Toggle Auto Sync">
                    <i class="fas fa-play"></i>
                </button>
                <button id="customer-sync-manual" class="btn btn-sm btn-primary" title="Manual Sync">
                    <i class="fas fa-sync-alt"></i>
                </button>
                <button id="customer-sync-settings" class="btn btn-sm btn-outline-secondary" title="Sync Settings">
                    <i class="fas fa-cog"></i>
                </button>
                <button id="customer-sync-close" class="btn btn-sm btn-outline-secondary" title="Close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <div class="sync-progress-content">
            <div class="sync-status-info">
                <div class="sync-status-item">
                    <span class="sync-status-label">Status:</span>
                    <span id="customer-sync-status" class="sync-status-value">Idle</span>
                    <span id="customer-sync-indicator" class="sync-indicator idle"></span>
                </div>
                <div class="sync-status-item">
                    <span class="sync-status-label">Last Sync:</span>
                    <span id="customer-last-sync" class="sync-status-value">Never</span>
                </div>
                <div class="sync-status-item">
                    <span class="sync-status-label">Next Sync:</span>
                    <span id="customer-next-sync" class="sync-status-value">-</span>
                </div>
                <div class="sync-status-item">
                    <span class="sync-status-label">Customers:</span>
                    <span id="customer-count" class="sync-status-value">0</span>
                </div>
            </div>

            <div id="customer-sync-progress-bar" class="sync-progress-bar" style="display: none;">
                <div class="progress">
                    <div id="customer-progress-fill" class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                        <span id="customer-progress-text">0%</span>
                    </div>
                </div>
                <div class="sync-progress-details">
                    <small id="customer-progress-details" class="text-muted">Preparing sync...</small>
                </div>
            </div>

            <div id="customer-sync-error" class="sync-error-message" style="display: none;">
                <div class="alert alert-danger alert-sm">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span id="customer-error-text">Sync failed</span>
                    <button id="customer-retry-sync" class="btn btn-sm btn-outline-danger ml-2">
                        <i class="fas fa-redo"></i> Retry
                    </button>
                </div>
            </div>

            <div id="customer-sync-success" class="sync-success-message" style="display: none;">
                <div class="alert alert-success alert-sm">
                    <i class="fas fa-check-circle"></i>
                    <span id="customer-success-text">Sync completed successfully</span>
                </div>
            </div>
        </div>
    </div>

    {{-- Settings Modal --}}
    <div id="customer-sync-settings-modal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Customer Sync Settings</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="customer-sync-settings-form">
                        <div class="form-group">
                            <label for="customer-auto-sync">
                                <input type="checkbox" id="customer-auto-sync" checked>
                                Enable Auto Sync
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="customer-sync-interval">Sync Interval (minutes)</label>
                            <select id="customer-sync-interval" class="form-control form-control-sm">
                                <option value="1">1 minute</option>
                                <option value="5" selected>5 minutes</option>
                                <option value="10">10 minutes</option>
                                <option value="15">15 minutes</option>
                                <option value="30">30 minutes</option>
                                <option value="60">1 hour</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="customer-sync-on-startup">
                                <input type="checkbox" id="customer-sync-on-startup" checked>
                                Sync on Startup
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="customer-sync-on-visibility">
                                <input type="checkbox" id="customer-sync-on-visibility" checked>
                                Sync on Page Focus
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="customer-quiet-hours">
                                <input type="checkbox" id="customer-quiet-hours">
                                Enable Quiet Hours
                            </label>
                        </div>
                        <div id="customer-quiet-hours-config" class="form-group" style="display: none;">
                            <div class="row">
                                <div class="col-6">
                                    <label for="customer-quiet-start">Start</label>
                                    <input type="time" id="customer-quiet-start" class="form-control form-control-sm" value="22:00">
                                </div>
                                <div class="col-6">
                                    <label for="customer-quiet-end">End</label>
                                    <input type="time" id="customer-quiet-end" class="form-control form-control-sm" value="06:00">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">Cancel</button>
                    <button type="button" id="customer-save-settings" class="btn btn-primary btn-sm">Save Settings</button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.customer-sync-progress {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 350px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1050;
    font-size: 0.875rem;
}

.sync-progress-container {
    padding: 0;
}

.sync-progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
}

.sync-progress-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #495057;
}

.sync-icon {
    margin-right: 8px;
    color: #6c757d;
}

.sync-progress-actions {
    display: flex;
    gap: 4px;
}

.sync-progress-content {
    padding: 16px;
}

.sync-status-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 16px;
}

.sync-status-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sync-status-label {
    font-weight: 500;
    color: #6c757d;
}

.sync-status-value {
    color: #495057;
    font-weight: 500;
}

.sync-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: 8px;
}

.sync-indicator.idle {
    background-color: #6c757d;
}

.sync-indicator.syncing {
    background-color: #007bff;
    animation: pulse 1.5s infinite;
}

.sync-indicator.success {
    background-color: #28a745;
}

.sync-indicator.error {
    background-color: #dc3545;
}

.sync-indicator.offline {
    background-color: #ffc107;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.sync-progress-bar {
    margin-bottom: 16px;
}

.sync-progress-details {
    margin-top: 8px;
    text-align: center;
}

.sync-error-message,
.sync-success-message {
    margin-bottom: 12px;
}

.alert-sm {
    padding: 8px 12px;
    font-size: 0.8rem;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 0.8rem;
}

#customer-quiet-hours-config {
    margin-left: 20px;
    padding-left: 12px;
    border-left: 2px solid #dee2e6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .customer-sync-progress {
        width: calc(100vw - 40px);
        right: 20px;
        left: 20px;
    }
    
    .sync-status-info {
        grid-template-columns: 1fr;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .customer-sync-progress {
        background: #343a40;
        border-color: #495057;
        color: #f8f9fa;
    }
    
    .sync-progress-header {
        background: #495057;
        border-color: #6c757d;
    }
    
    .sync-status-label {
        color: #adb5bd;
    }
    
    .sync-status-value {
        color: #f8f9fa;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize customer sync progress component
    if (typeof CustomerSyncProgressComponent === 'undefined') {
        window.CustomerSyncProgressComponent = new (function() {
            let scheduler = null;
            let syncService = null;
            let isVisible = false;

            // Initialize the component
            this.init = async function(customerSyncService, customerSyncScheduler) {
                syncService = customerSyncService;
                scheduler = customerSyncScheduler;
                
                setupEventListeners();
                await loadSettings();
                updateDisplay();
                
                console.log('Customer sync progress component initialized');
            };

            // Setup event listeners
            function setupEventListeners() {
                // Button event listeners
                document.getElementById('customer-sync-toggle').addEventListener('click', toggleAutoSync);
                document.getElementById('customer-sync-manual').addEventListener('click', triggerManualSync);
                document.getElementById('customer-sync-settings').addEventListener('click', showSettings);
                document.getElementById('customer-sync-close').addEventListener('click', hideProgress);
                document.getElementById('customer-retry-sync').addEventListener('click', triggerManualSync);
                document.getElementById('customer-save-settings').addEventListener('click', saveSettings);
                
                // Settings form listeners
                document.getElementById('customer-quiet-hours').addEventListener('change', function() {
                    const config = document.getElementById('customer-quiet-hours-config');
                    config.style.display = this.checked ? 'block' : 'none';
                });

                // Sync event listeners
                window.addEventListener('customer_scheduler_sync_started', handleSyncStarted);
                window.addEventListener('customer_scheduler_sync_completed', handleSyncCompleted);
                window.addEventListener('customer_scheduler_sync_failed', handleSyncFailed);
                window.addEventListener('customer_scheduler_scheduler_started', updateDisplay);
                window.addEventListener('customer_scheduler_scheduler_stopped', updateDisplay);
                window.addEventListener('customer_sync_progress', handleSyncProgress);
            }

            // Show/hide progress component
            this.show = function() {
                document.getElementById('customer-sync-progress').style.display = 'block';
                isVisible = true;
                updateDisplay();
            };

            this.hide = function() {
                document.getElementById('customer-sync-progress').style.display = 'none';
                isVisible = false;
            };

            function hideProgress() {
                window.CustomerSyncProgressComponent.hide();
            }

            // Update display with current status
            async function updateDisplay() {
                if (!isVisible || !scheduler) return;

                const status = scheduler.getStatus();
                const stats = syncService ? await syncService.getSyncStats() : {};

                // Update status
                document.getElementById('customer-sync-status').textContent = 
                    status.isRunning ? 'Running' : 'Stopped';
                
                // Update indicator
                const indicator = document.getElementById('customer-sync-indicator');
                indicator.className = 'sync-indicator ' + 
                    (status.isRunning ? (status.isOnline ? 'syncing' : 'offline') : 'idle');

                // Update last sync
                document.getElementById('customer-last-sync').textContent = 
                    status.lastSyncAttempt ? new Date(status.lastSyncAttempt).toLocaleTimeString() : 'Never';

                // Update next sync
                document.getElementById('customer-next-sync').textContent = 
                    status.nextSync ? new Date(status.nextSync).toLocaleTimeString() : '-';

                // Update customer count
                document.getElementById('customer-count').textContent = stats.customers || 0;

                // Update toggle button
                const toggleBtn = document.getElementById('customer-sync-toggle');
                toggleBtn.innerHTML = status.isRunning ? '<i class="fas fa-pause"></i>' : '<i class="fas fa-play"></i>';
                toggleBtn.title = status.isRunning ? 'Stop Auto Sync' : 'Start Auto Sync';
            }

            // Event handlers
            function handleSyncStarted(event) {
                document.getElementById('customer-sync-status').textContent = 'Syncing...';
                document.getElementById('customer-sync-indicator').className = 'sync-indicator syncing';
                document.getElementById('customer-sync-progress-bar').style.display = 'block';
                document.getElementById('customer-sync-error').style.display = 'none';
                document.getElementById('customer-sync-success').style.display = 'none';
            }

            function handleSyncCompleted(event) {
                document.getElementById('customer-sync-status').textContent = 'Completed';
                document.getElementById('customer-sync-indicator').className = 'sync-indicator success';
                document.getElementById('customer-sync-progress-bar').style.display = 'none';
                document.getElementById('customer-sync-success').style.display = 'block';
                document.getElementById('customer-success-text').textContent = 
                    `Synced ${event.detail.result?.totalSynced || 0} customers`;
                
                setTimeout(() => {
                    document.getElementById('customer-sync-success').style.display = 'none';
                    updateDisplay();
                }, 3000);
            }

            function handleSyncFailed(event) {
                document.getElementById('customer-sync-status').textContent = 'Error';
                document.getElementById('customer-sync-indicator').className = 'sync-indicator error';
                document.getElementById('customer-sync-progress-bar').style.display = 'none';
                document.getElementById('customer-sync-error').style.display = 'block';
                document.getElementById('customer-error-text').textContent = event.detail.error || 'Sync failed';
            }

            function handleSyncProgress(event) {
                const progress = event.detail.processed || 0;
                const total = event.detail.total || 100;
                const percentage = Math.round((progress / total) * 100);
                
                document.getElementById('customer-progress-fill').style.width = percentage + '%';
                document.getElementById('customer-progress-text').textContent = percentage + '%';
                document.getElementById('customer-progress-details').textContent = 
                    `Processed ${progress} of ${total} customers`;
            }

            // Action handlers
            async function toggleAutoSync() {
                if (!scheduler) return;
                
                const status = scheduler.getStatus();
                if (status.isRunning) {
                    await scheduler.stop();
                } else {
                    await scheduler.start();
                }
                updateDisplay();
            }

            async function triggerManualSync() {
                if (!scheduler) return;
                
                try {
                    await scheduler.triggerManualSync();
                } catch (error) {
                    console.error('Manual sync failed:', error);
                }
            }

            function showSettings() {
                $('#customer-sync-settings-modal').modal('show');
                loadSettingsForm();
            }

            async function loadSettings() {
                if (!scheduler) return;
                const status = scheduler.getStatus();
                // Settings are loaded automatically by the scheduler
            }

            function loadSettingsForm() {
                if (!scheduler) return;
                
                const config = scheduler.getStatus().config;
                document.getElementById('customer-auto-sync').checked = config.autoSyncEnabled;
                document.getElementById('customer-sync-interval').value = config.syncInterval / (60 * 1000);
                document.getElementById('customer-sync-on-startup').checked = config.syncOnStartup;
                document.getElementById('customer-sync-on-visibility').checked = config.syncOnVisibilityChange;
                document.getElementById('customer-quiet-hours').checked = config.quietHours.enabled;
                document.getElementById('customer-quiet-start').value = config.quietHours.start;
                document.getElementById('customer-quiet-end').value = config.quietHours.end;
                
                document.getElementById('customer-quiet-hours-config').style.display = 
                    config.quietHours.enabled ? 'block' : 'none';
            }

            async function saveSettings() {
                if (!scheduler) return;
                
                const newConfig = {
                    autoSyncEnabled: document.getElementById('customer-auto-sync').checked,
                    syncInterval: parseInt(document.getElementById('customer-sync-interval').value) * 60 * 1000,
                    syncOnStartup: document.getElementById('customer-sync-on-startup').checked,
                    syncOnVisibilityChange: document.getElementById('customer-sync-on-visibility').checked,
                    quietHours: {
                        enabled: document.getElementById('customer-quiet-hours').checked,
                        start: document.getElementById('customer-quiet-start').value,
                        end: document.getElementById('customer-quiet-end').value
                    }
                };
                
                await scheduler.updateConfig(newConfig);
                $('#customer-sync-settings-modal').modal('hide');
                updateDisplay();
            }

            return this;
        })();
    }
});
</script>
