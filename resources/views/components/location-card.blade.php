@props(['location'])

<div class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-100">
    {{-- Location Header --}}
    <div class="px-4 py-3 border-b border-gray-100">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                {{-- Location Type Icon --}}
                <div class="flex-shrink-0">
                    @if($location->isWarehouse())
                    <div class="w-9 h-9 rounded-lg bg-blue-50 flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    @elseif($location->isStore())
                    <div class="w-9 h-9 rounded-lg bg-indigo-50 flex items-center justify-center">
                        <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h18v2a2 2 0 01-2 2H5a2 2 0 01-2-2V3zm0 8h18m-18 0v10a2 2 0 002 2h14a2 2 0 002-2v-10M9 21v-8m6 8v-8"></path>
                        </svg>
                    </div>
                    @else
                    <div class="w-9 h-9 rounded-lg bg-gray-50 flex items-center justify-center">
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    @endif
                </div>
                <div>
                    <h3 class="text-base font-medium text-gray-900 leading-5">{{ $location->name }}</h3>
                    <div class="flex items-center space-x-2 mt-0.5">
                        <span class="text-xs text-gray-500">{{ ucfirst($location->getLocationType()) }}</span>
                        <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium {{ $location->status === 'active' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700' }}">
                            {{ ucfirst($location->status) }}
                        </span>
                    </div>
                </div>
            </div>
            <div class="text-right">
                <div class="text-sm font-medium text-gray-900">{{ $location->location_code ?? 'N/A' }}</div>
            </div>
        </div>
    </div>

    {{-- Location Details --}}
    <div class="px-4 py-3 bg-gray-50 border-b border-gray-100">
        <div class="grid grid-cols-2 gap-x-6 gap-y-2.5">
            <div class="flex flex-col">
                <span class="text-xs font-medium text-gray-500">Store</span>
                <span class="text-sm text-gray-900 truncate">{{ $location->isStore() ? $location->name : ($location->parent?->name ?? 'Main') }}</span>
            </div>
            <div class="flex flex-col">
                <span class="text-xs font-medium text-gray-500">Section</span>
                <span class="text-sm text-gray-900 truncate">{{ $location->section ?? '-' }}</span>
            </div>
            <div class="flex flex-col">
                <span class="text-xs font-medium text-gray-500">Zone</span>
                <span class="text-sm text-gray-900 truncate">{{ $location->zone ?? '-' }}</span>
            </div>
            <div class="flex flex-col">
                <span class="text-xs font-medium text-gray-500">Aisle</span>
                <span class="text-sm text-gray-900 truncate">{{ $location->aisle_number ?? '-' }}</span>
            </div>
            <div class="flex flex-col">
                <span class="text-xs font-medium text-gray-500">Rack</span>
                <span class="text-sm text-gray-900 truncate">{{ $location->rack_number ?? '-' }}</span>
            </div>
            <div class="flex flex-col">
                <span class="text-xs font-medium text-gray-500">Bin</span>
                <span class="text-sm text-gray-900 truncate">{{ $location->bin_location ?? '-' }}</span>
            </div>
        </div>
    </div>

    {{-- Footer: Storage Conditions & Actions --}}
    <div class="px-4 py-2.5 flex justify-between items-center bg-white">
        <div class="flex space-x-3">
            {{-- Temperature --}}
            <div class="flex items-center space-x-1.5">
                <div class="w-6 h-6 rounded bg-blue-50 flex items-center justify-center">
                    <svg class="w-3.5 h-3.5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                    </svg>
                </div>
                <span class="text-xs font-medium text-gray-600">{{ $location->temperature ?? '20-25°C' }}</span>
            </div>
            {{-- Humidity --}}
            <div class="flex items-center space-x-1.5">
                <div class="w-6 h-6 rounded bg-blue-50 flex items-center justify-center">
                    <svg class="w-3.5 h-3.5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"></path>
                    </svg>
                </div>
                <span class="text-xs font-medium text-gray-600">{{ $location->humidity ?? '45-65%' }}</span>
            </div>
        </div>
        <div class="flex items-center space-x-2">
            <a href="{{ route('inventory.locations.show', $location) }}" 
               class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-200 rounded-md transition-colors duration-150">
                <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                View
            </a>
            <a href="{{ route('inventory.locations.edit', $location) }}" 
               class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium text-indigo-600 bg-indigo-50 hover:bg-indigo-100 border border-transparent rounded-md transition-colors duration-150">
                <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit
            </a>
        </div>
    </div>
</div>
