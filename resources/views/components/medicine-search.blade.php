<div 
    x-data="{
        searchTerm: '',
        searchResults: [],
        loading: false,
        open: false,
        searchTimeout: null,
        searchUrl: '{{ route('inventory.medicines.search') }}',
        medicinesBaseUrl: '{{ url('inventory/medicines') }}',
        expandedLocations: {},
        
        init() {
            // Close dropdown when clicking outside
            document.addEventListener('click', (event) => {
                if (!this.$el.contains(event.target)) {
                    this.open = false;
                }
            });
            
            // Handle window resize to adjust dropdown position
            window.addEventListener('resize', () => {
                if (this.open) {
                    this.adjustDropdownPosition();
                }
            });
        },
        
        searchMedicines() {
            // Only search if the term is at least 3 characters
            if (this.searchTerm.length < 3) {
                this.searchResults = [];
                this.open = false;
                return;
            }
            
            this.loading = true;
            this.open = true;
            
            // Add debounce to prevent too many requests
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                fetch(`${this.searchUrl}?query=${encodeURIComponent(this.searchTerm)}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (Array.isArray(data)) {
                            this.searchResults = data;
                            // Initialize expanded state for each result
                            this.expandedLocations = {};
                            
                            // Adjust dropdown position after results are loaded
                            this.$nextTick(() => {
                                this.adjustDropdownPosition();
                            });
                        } else {
                            this.searchResults = [];
                            console.warn('Search results are not an array:', data);
                        }
                        this.loading = false;
                    })
                    .catch(error => {
                        console.error('Error searching medicines:', error);
                        this.searchResults = [];
                        this.loading = false;
                    });
            }, 300); // 300ms debounce
        },
        
        viewMedicine(medicineId) {
            if (medicineId) {
                window.location.href = this.medicinesBaseUrl + '/' + medicineId;
            }
        },
        
        toggleLocationExpand(idx, event) {
            // Stop propagation to prevent row click
            event.stopPropagation();
            
            // Toggle expanded state
            this.expandedLocations[idx] = !this.expandedLocations[idx];
        },
        
        isLocationExpanded(idx) {
            return this.expandedLocations[idx] === true;
        },
        
        adjustDropdownPosition() {
            // Get dropdown element
            const dropdown = this.$el.querySelector('.dropdown-results');
            if (!dropdown) return;

            // Check if we're in the admin navbar or elsewhere
            const isInNavbar = this.$el.closest('.medicine-search-wrapper') !== null;

            // Get viewport dimensions
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            // Get input position
            const inputRect = this.$el.querySelector('input').getBoundingClientRect();

            if (isInNavbar) {
                // In navbar - use fixed positioning with CSS classes
                // The CSS handles the positioning, we just need to ensure proper width
                let dropdownWidth;
                if (viewportWidth >= 1280) {
                    dropdownWidth = Math.min(1200, viewportWidth * 0.9);
                } else if (viewportWidth >= 1024) {
                    dropdownWidth = Math.min(1000, viewportWidth * 0.95);
                } else if (viewportWidth >= 768) {
                    dropdownWidth = Math.min(800, viewportWidth * 0.98);
                } else {
                    dropdownWidth = Math.min(600, viewportWidth * 0.96);
                }

                dropdown.style.width = `${dropdownWidth}px`;
                dropdown.style.maxHeight = `${Math.min(450, viewportHeight - 80)}px`;

                // For navbar, position is handled by CSS classes
                // Reset any inline positioning
                dropdown.style.left = '';
                dropdown.style.transform = '';
            } else {
                // In other contexts (like sales form), use original positioning logic
                const container = this.$el.closest('.w-full') || document.body;
                const containerRect = container.getBoundingClientRect();
                let dropdownWidth = Math.min(Math.max(containerRect.width, 900), 1200);

                dropdown.style.width = `${dropdownWidth}px`;

                // Calculate position to avoid overflow
                let left = inputRect.left;
                if (left + dropdownWidth > viewportWidth) {
                    left = Math.max(10, viewportWidth - dropdownWidth - 10);
                }

                dropdown.style.left = `${left}px`;
                dropdown.style.maxHeight = `${Math.min(450, viewportHeight - inputRect.bottom - 20)}px`;
            }
        }
    }"
    class="relative medicine-search-container"
>
    <div class="flex items-center w-full relative">
        <!-- Search Icon -->
        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
        </div>

        <input type="text"
            class="w-full pl-10 pr-10 py-2 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50 text-sm bg-gray-50 focus:bg-white transition-colors duration-200"
            placeholder="Search medicines..."
            x-model="searchTerm"
            @focus="open = true"
            @input="searchMedicines()"
            @keydown.escape="open = false">

        <!-- Loading Spinner -->
        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none" x-show="loading">
            <svg class="animate-spin h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
        </div>
    </div>
    
    <!-- Dropdown results as table -->
    <div class="absolute z-50 mt-1 bg-white shadow-lg rounded-md py-1 text-sm dropdown-results w-full border border-gray-200"
        x-show="open && Array.isArray(searchResults) && searchResults.length > 0"
        x-transition:enter="transition ease-out duration-100"
        x-transition:enter-start="transform opacity-0 scale-95"
        x-transition:enter-end="transform opacity-100 scale-100"
        x-transition:leave="transition ease-in duration-75"
        x-transition:leave-start="transform opacity-100 scale-100"
        x-transition:leave-end="transform opacity-0 scale-95"
        @click.away="open = false"
        style="max-height: min(450px, calc(100vh - 220px)); overflow-y: auto; left: 0;"
        x-init="$nextTick(() => adjustDropdownPosition())">
        
        <!-- Table Header -->
        <div class="px-4 py-2 bg-gray-50 border-b medicine-search-grid font-medium text-gray-600 sticky top-0">
            <div class="medicine-col-name px-1">Name</div>
            <div class="medicine-col-generic px-1">Generic Name</div>
            <div class="medicine-col-manufacturer px-1">Manufacturer</div>
            <div class="medicine-col-stock px-1">Stock</div>
            <div class="medicine-col-price px-1">Price</div>
            <div class="medicine-col-location px-1">Location</div>
        </div>
        
        <!-- Table Body -->
        <template x-for="(result, idx) in searchResults" :key="idx">
            <div class="border-b border-gray-100 last:border-0 hover:bg-gray-50">
                <!-- Main row (clickable) -->
                <a href="#" 
                    class="block px-4 py-2 cursor-pointer"
                    @click.prevent="viewMedicine(result.id)">
                    
                    <div class="medicine-search-grid items-center">
                        <!-- Name -->
                        <div class="font-medium medicine-col-name px-1" x-text="result.name + (result.dosage ? ' ' + result.dosage : '')"></div>
                        
                        <!-- Generic Name -->
                        <div class="text-gray-600 medicine-col-generic px-1" x-text="result.generic_name"></div>
                        
                        <!-- Manufacturer -->
                        <div class="text-gray-600 medicine-col-manufacturer px-1" x-text="result.manufacturer || '-'"></div>
                        
                        <!-- Stock -->
                        <div class="medicine-col-stock text-center px-1">
                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium" 
                                :class="{
                                    'bg-green-100 text-green-800': result.total_stock > 10,
                                    'bg-yellow-100 text-yellow-800': result.total_stock > 0 && result.total_stock <= 10,
                                    'bg-red-100 text-red-800': result.total_stock === 0
                                }">
                                <span x-text="result.total_stock"></span>
                            </span>
                        </div>
                        
                        <!-- Price -->
                        <div class="text-gray-600 medicine-col-price text-center px-1">৳ <span x-text="result.selling_price"></span></div>
                        
                        <!-- Location (with expand/collapse) -->
                        <div class="flex items-center medicine-col-location px-1">
                            <div x-show="result.has_location_data" class="w-full location-container">
                                <div class="flex items-center flex-nowrap overflow-visible">
                                    <svg class="w-3 h-3 mr-1 flex-shrink-0 text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    
                                    <!-- Locations display (up to 3 locations inline) -->
                                    <div class="flex-1 overflow-visible">
                                        <template x-if="result.locations && result.locations.length > 0">
                                            <div class="location-chips flex-nowrap overflow-visible">
                                                <!-- Show up to 3 locations inline -->
                                                <template x-for="(location, locIdx) in result.locations.slice(0, Math.min(3, result.locations.length))" :key="locIdx">
                                                    <span class="inline-block text-xs text-blue-700 bg-blue-50 px-2 py-0.5 rounded mr-1 mb-0 location-chip">
                                                        <span class="font-medium" x-text="location.name"></span>
                                                        <span class="text-blue-800" x-text="': ' + location.quantity"></span>
                                                    </span>
                                                </template>
                                                
                                                <!-- Show count of remaining locations -->
                                                <span 
                                                    x-show="result.locations.length > 3"
                                                    class="inline-block text-xs text-blue-700 bg-blue-50 px-2 py-0.5 rounded cursor-pointer hover:bg-blue-100 whitespace-nowrap"
                                                    @click.stop="toggleLocationExpand(idx, $event)">
                                                    +<span x-text="result.locations.length - 3"></span> more
                                                </span>
                                            </div>
                                        </template>
                                        <span x-show="!result.locations || result.locations.length === 0" class="text-gray-500 text-xs">No location data</span>
                                    </div>
                                    
                                    <!-- Expand button (if multiple locations) -->
                                    <button 
                                        x-show="result.locations && result.locations.length > 3"
                                        @click="toggleLocationExpand(idx, $event)" 
                                        class="ml-1 p-1 rounded-full hover:bg-gray-200 focus:outline-none flex-shrink-0">
                                        <svg class="w-3 h-3 transform transition-transform duration-200 text-blue-700" 
                                            :class="{'rotate-180': isLocationExpanded(idx)}"
                                            fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div x-show="!result.has_location_data" class="text-gray-500">-</div>
                        </div>
                    </div>
                </a>
                
                <!-- Expanded locations (conditionally shown) -->
                <div 
                    x-show="isLocationExpanded(idx) && result.locations && result.locations.length > 1"
                    class="px-4 py-2 bg-gray-50 border-t border-gray-100 ml-4 mr-4 mb-2 rounded">
                    <div class="text-xs text-gray-700 font-medium mb-1">All Locations:</div>
                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-1">
                        <template x-for="(location, locIdx) in result.locations" :key="locIdx">
                            <div class="flex items-center text-xs whitespace-nowrap overflow-visible">
                                <span class="w-5 h-5 flex items-center justify-center rounded-full bg-blue-100 text-blue-800 mr-2 flex-shrink-0">
                                    <span x-text="locIdx + 1"></span>
                                </span>
                                <span class="text-gray-800" x-text="location.name"></span>
                                <span class="ml-auto font-medium" x-text="location.quantity"></span>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </template>
    </div>
    
    <!-- No results message -->
    <div class="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md py-2 px-4 text-sm text-gray-500 border border-gray-200 left-0"
        x-show="open && searchTerm && searchTerm.length >= 3 && (!Array.isArray(searchResults) || searchResults.length === 0) && !loading"
        x-init="$nextTick(() => adjustDropdownPosition())">
        No medicines found
    </div>
</div>

<style>
    /* Add custom CSS to handle positioning in different layouts */
    .medicine-search-container {
        position: relative;
        z-index: 40;
        width: 100%;
    }
    
    .dropdown-results {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        max-width: min(100vw, 900px);
    }
    
    /* Admin header context - specific styling for the top navbar */
    .medicine-search-wrapper .dropdown-results {
        position: fixed !important;
        top: 4rem !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        width: 90vw !important;
        max-width: 1200px !important;
        min-width: 800px !important;
        z-index: 1000 !important;
    }

    @media (max-width: 1024px) {
        .medicine-search-wrapper .dropdown-results {
            width: 95vw !important;
            min-width: 600px !important;
        }
    }

    @media (max-width: 768px) {
        .medicine-search-wrapper .dropdown-results {
            width: 98vw !important;
            min-width: 400px !important;
            left: 1vw !important;
            transform: none !important;
        }
    }

    @media (max-width: 640px) {
        .medicine-search-wrapper .dropdown-results {
            width: 96vw !important;
            min-width: 300px !important;
            left: 2vw !important;
        }
    }
    
    /* Define medicine search grid layout with optimized column widths */
    .medicine-search-grid {
        display: grid;
        grid-template-areas: 
            "name name generic generic manufacturer manufacturer"
            "stock price location location location location";
        gap: 0.5rem;
        align-items: center;
    }
    
    /* Column classes for responsive behavior */
    .medicine-col-name { grid-area: name; }
    .medicine-col-generic { grid-area: generic; }
    .medicine-col-manufacturer { grid-area: manufacturer; }
    .medicine-col-stock { grid-area: stock; }
    .medicine-col-price { grid-area: price; }
    .medicine-col-location { grid-area: location; }
    
    /* Adjust for admin layout */
    .admin-layout .medicine-search-container .dropdown-results {
        left: 0;
    }
    
    /* Add positioning for the wrapper */
    .medicine-search-wrapper {
        position: relative;
        width: 100%;
    }
    
    /* Adjust dropdown position based on layout */
    .admin-layout .medicine-search-wrapper .dropdown-results {
        left: 0;
        right: auto;
        transform: none;
    }
    
    /* Location text styling */
    .location-container {
        max-width: 100%;
        width: 100%;
    }
    
    .location-chips {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        max-width: 100%;
    }
    
    .location-chip {
        white-space: nowrap;
        overflow: visible;
        text-overflow: clip;
    }
    
    /* Small screens */
    @media (min-width: 640px) {
        .medicine-search-grid {
            grid-template-areas: 
                "name name generic generic manufacturer manufacturer"
                "stock price location location location location";
        }
        
        .medicine-col-location {
            margin-top: 0.25rem;
        }
    }
    
    /* Medium screens */
    @media (min-width: 768px) {
        .medicine-search-grid {
            grid-template-areas: "name generic manufacturer stock price location";
            grid-template-columns: 1.4fr 1.6fr 1.8fr 0.6fr 0.7fr 2.4fr;
        }
        
        .medicine-col-location {
            margin-top: 0;
        }
        
        /* Show full content without truncation on a single line */
        .medicine-col-name, 
        .medicine-col-generic, 
        .medicine-col-manufacturer {
            min-width: 0;
            white-space: nowrap;
            overflow: visible;
            text-overflow: clip;
            display: block;
        }
        
        /* Ensure locations display properly */
        .location-chip {
            white-space: nowrap;
            overflow: visible;
            text-overflow: clip;
        }
    }
    
    /* Large screens */
    @media (min-width: 1024px) {
        .medicine-search-grid {
            grid-template-columns: 1.4fr 1.6fr 1.8fr 0.6fr 0.7fr 2.8fr;
            gap: 1rem;
        }
    }
    
    /* Extra large screens */
    @media (min-width: 1280px) {
        .medicine-search-grid {
            grid-template-columns: 1.4fr 1.6fr 1.8fr 0.6fr 0.7fr 3fr;
        }
    }
    
    /* 2XL screens */
    @media (min-width: 1536px) {
        .medicine-search-grid {
            grid-template-columns: 1.4fr 1.6fr 1.8fr 0.6fr 0.7fr 3.2fr;
        }
    }
</style> 