{{-- Medicine Sync Progress Component --}}
<div x-data="medicineSyncProgress()" 
     x-init="init()"
     x-show="showProgress"
     class="fixed bottom-4 right-4 z-50"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0 transform translate-y-2"
     x-transition:enter-end="opacity-100 transform translate-y-0"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100 transform translate-y-0"
     x-transition:leave-end="opacity-0 transform translate-y-2">
     
    <!-- Progress Card -->
    <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-4 min-w-80 max-w-sm">
        <!-- Header -->
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
                <div class="flex-shrink-0">
                    <svg x-show="syncStatus === 'running'" class="animate-spin h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <svg x-show="syncStatus === 'completed'" class="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <svg x-show="syncStatus === 'error'" class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <h3 class="text-sm font-medium text-gray-900" x-text="syncTitle">Medicine Sync</h3>
            </div>
            
            <!-- Close button -->
            <button @click="hideProgress()" 
                    class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
                    :disabled="syncStatus === 'running'">
                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
            </button>
        </div>
        
        <!-- Progress Bar -->
        <div x-show="syncStatus === 'running'" class="mb-3">
            <div class="flex justify-between text-xs text-gray-600 mb-1">
                <span x-text="progressText">Initializing...</span>
                <span x-text="progressPercentage + '%'">0%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
                     :style="'width: ' + progressPercentage + '%'"></div>
            </div>
        </div>
        
        <!-- Status Message -->
        <div class="text-sm text-gray-600 mb-3" x-text="statusMessage">
            Preparing to sync medicine data...
        </div>
        
        <!-- Stats -->
        <div x-show="syncStats.processed > 0" class="grid grid-cols-2 gap-4 text-xs text-gray-500">
            <div>
                <span class="font-medium" x-text="syncStats.processed">0</span>
                <span>medicines synced</span>
            </div>
            <div x-show="syncStats.duration">
                <span class="font-medium" x-text="formatDuration(syncStats.duration)">0s</span>
                <span>elapsed</span>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div x-show="syncStatus === 'completed' || syncStatus === 'error'" class="mt-3 flex space-x-2">
            <button x-show="syncStatus === 'error'" 
                    @click="retrySync()"
                    class="flex-1 px-3 py-1 text-xs font-medium text-white bg-blue-600 rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                Retry
            </button>
            <button @click="hideProgress()"
                    class="flex-1 px-3 py-1 text-xs font-medium text-gray-700 bg-gray-100 rounded hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">
                Close
            </button>
        </div>
    </div>
</div>

<script>
function medicineSyncProgress() {
    return {
        // State
        showProgress: false,
        syncStatus: 'idle', // idle, running, completed, error
        syncType: 'full', // full, incremental
        syncTitle: 'Medicine Sync',
        statusMessage: 'Preparing to sync medicine data...',
        progressText: 'Initializing...',
        progressPercentage: 0,
        
        // Stats
        syncStats: {
            processed: 0,
            total: 0,
            startTime: null,
            duration: 0
        },
        
        // Timers
        updateTimer: null,
        hideTimer: null,
        
        init() {
            // Listen for medicine sync events
            window.addEventListener('medicine-sync-started', (event) => {
                this.handleSyncStarted(event.detail);
            });
            
            window.addEventListener('medicine-sync-progress', (event) => {
                this.handleSyncProgress(event.detail);
            });
            
            window.addEventListener('medicine-sync-completed', (event) => {
                this.handleSyncCompleted(event.detail);
            });
            
            window.addEventListener('medicine-sync-failed', (event) => {
                this.handleSyncFailed(event.detail);
            });
            
            window.addEventListener('medicine-sync-retry', (event) => {
                this.handleSyncRetry(event.detail);
            });
        },
        
        handleSyncStarted(data) {
            this.showProgress = true;
            this.syncStatus = 'running';
            this.syncType = data.type || 'full';
            this.syncTitle = `${this.syncType === 'full' ? 'Full' : 'Incremental'} Medicine Sync`;
            this.statusMessage = 'Starting medicine synchronization...';
            this.progressText = 'Initializing...';
            this.progressPercentage = 0;
            
            // Reset stats
            this.syncStats = {
                processed: 0,
                total: 0,
                startTime: new Date(),
                duration: 0
            };
            
            // Start duration timer
            this.startDurationTimer();
            
            // Clear any existing hide timer
            if (this.hideTimer) {
                clearTimeout(this.hideTimer);
                this.hideTimer = null;
            }
        },
        
        handleSyncProgress(data) {
            if (data.stage) {
                this.progressText = this.getStageText(data.stage);
            }
            
            if (data.message) {
                this.statusMessage = data.message;
            }
            
            if (data.processed !== undefined) {
                this.syncStats.processed = data.processed;
            }
            
            if (data.total !== undefined) {
                this.syncStats.total = data.total;
            }
            
            // Calculate progress percentage
            if (this.syncStats.total > 0) {
                this.progressPercentage = Math.round((this.syncStats.processed / this.syncStats.total) * 100);
            } else if (data.stage === 'completed') {
                this.progressPercentage = 100;
            }
        },
        
        handleSyncCompleted(data) {
            this.syncStatus = 'completed';
            this.progressPercentage = 100;
            this.statusMessage = `Sync completed successfully! ${data.processed || this.syncStats.processed} medicines synchronized.`;
            this.progressText = 'Completed';
            
            this.stopDurationTimer();
            
            // Auto-hide after 5 seconds
            this.hideTimer = setTimeout(() => {
                this.hideProgress();
            }, 5000);
        },
        
        handleSyncFailed(data) {
            this.syncStatus = 'error';
            this.statusMessage = `Sync failed: ${data.error || 'Unknown error'}`;
            this.progressText = 'Failed';
            
            this.stopDurationTimer();
        },
        
        handleSyncRetry(data) {
            this.statusMessage = `Retrying sync (attempt ${data.attempt}/${data.maxRetries})...`;
            this.progressText = 'Retrying...';
            this.progressPercentage = 0;
        },
        
        getStageText(stage) {
            const stageTexts = {
                'starting': 'Initializing...',
                'fetching_reference_data': 'Syncing categories & manufacturers...',
                'fetching_medicines': 'Downloading medicines...',
                'processing': 'Processing medicines...',
                'optimizing_storage': 'Optimizing storage...',
                'completed': 'Completed'
            };
            
            return stageTexts[stage] || 'Processing...';
        },
        
        startDurationTimer() {
            this.updateTimer = setInterval(() => {
                if (this.syncStats.startTime) {
                    this.syncStats.duration = Math.floor((new Date() - this.syncStats.startTime) / 1000);
                }
            }, 1000);
        },
        
        stopDurationTimer() {
            if (this.updateTimer) {
                clearInterval(this.updateTimer);
                this.updateTimer = null;
            }
        },
        
        formatDuration(seconds) {
            if (seconds < 60) {
                return `${seconds}s`;
            } else {
                const minutes = Math.floor(seconds / 60);
                const remainingSeconds = seconds % 60;
                return `${minutes}m ${remainingSeconds}s`;
            }
        },
        
        hideProgress() {
            this.showProgress = false;
            this.stopDurationTimer();
            
            if (this.hideTimer) {
                clearTimeout(this.hideTimer);
                this.hideTimer = null;
            }
        },
        
        retrySync() {
            if (window.MedicineSyncScheduler) {
                if (this.syncType === 'full') {
                    window.MedicineSyncScheduler.triggerFullSync();
                } else {
                    window.MedicineSyncScheduler.triggerIncrementalSync();
                }
            }
        }
    };
}
</script>
