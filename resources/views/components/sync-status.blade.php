<div 
    x-data="{
        isSyncing: false,
        pendingItems: 0,
        lastSync: localStorage.getItem('lastSync') || 'Never',
        
        async updateSyncInfo() {
            try {
                if (typeof window.getSyncStatus === 'function') {
                    const status = await window.getSyncStatus();
                    this.pendingItems = status.pendingItemsCount;
                    this.lastSync = localStorage.getItem('lastSync') || 'Never';
                }
            } catch (error) {
                console.error('Error getting sync status:', error);
            }
        },
        
        async triggerSync() {
            if (this.isSyncing) return;
            
            if (!navigator.onLine) {
                if (typeof Livewire !== 'undefined') {
                    Livewire.dispatch('notify', { 
                        type: 'warning',
                        message: 'Cannot sync while offline'
                    });
                }
                return;
            }
            
            try {
                this.isSyncing = true;
                if (typeof window.forceSync === 'function') {
                    await window.forceSync();
                    if (typeof Livewire !== 'undefined') {
                        Livewire.dispatch('notify', { 
                            type: 'success',
                            message: 'Sync completed successfully'
                        });
                    }
                }
            } catch (error) {
                if (typeof Livewire !== 'undefined') {
                    Livewire.dispatch('notify', { 
                        type: 'error',
                        message: `Sync failed: ${error.message}`
                    });
                }
            } finally {
                this.isSyncing = false;
                this.updateSyncInfo();
            }
        },
        
        init() {
            this.updateSyncInfo();
            
            // Listen for sync status changes
            window.addEventListener('sync-status-changed', (event) => {
                this.isSyncing = event.detail.syncing;
                this.updateSyncInfo();
                
                // Show notification if there was an error
                if (event.detail.error && typeof Livewire !== 'undefined') {
                    Livewire.dispatch('notify', { 
                        type: 'error',
                        message: `Sync failed: ${event.detail.errorMessage || 'Unknown error'}`
                    });
                }
            });
            
            // Listen for service worker messages
            if (navigator.serviceWorker) {
                navigator.serviceWorker.addEventListener('message', (event) => {
                    if (event.data.type === 'sync-completed') {
                        this.isSyncing = false;
                        this.updateSyncInfo();
                        
                        if (typeof Livewire !== 'undefined') {
                            if (event.data.data.success) {
                                Livewire.dispatch('notify', { 
                                    type: 'success',
                                    message: `Sync completed: ${event.data.data.successCount} items synchronized`
                                });
                            } else {
                                Livewire.dispatch('notify', { 
                                    type: 'warning',
                                    message: `Sync completed with issues: ${event.data.data.failureCount} failures`
                                });
                            }
                        }
                    } else if (event.data.type === 'sync-started') {
                        this.isSyncing = true;
                    } else if (event.data.type === 'sync-error') {
                        this.isSyncing = false;
                        if (typeof Livewire !== 'undefined') {
                            Livewire.dispatch('notify', { 
                                type: 'error',
                                message: `Sync error: ${event.data.data.error || 'Unknown error'}`
                            });
                        }
                    }
                });
            }
        }
    }" 
    x-init="init()"
    class="relative"
>
    <!-- Hidden elements for backward compatibility with existing JavaScript -->
    <div id="syncStatus" style="display: none;">
        <span id="syncStatusText"></span>
    </div>
    
    <!-- Sync Status Button -->
    <button
        @click="triggerSync"
        class="relative flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-colors md:px-3 md:py-2 px-2 py-2"
        :class="{
            'bg-indigo-100 text-indigo-700 hover:bg-indigo-200': !isSyncing && pendingItems === 0,
            'bg-yellow-100 text-yellow-700 hover:bg-yellow-200': !isSyncing && pendingItems > 0,
            'bg-blue-100 text-blue-700': isSyncing
        }"
        :disabled="isSyncing"
        :title="isSyncing ? 'Syncing...' : (pendingItems > 0 ? `Sync (${pendingItems} pending)` : 'Synced')"
    >
        <!-- Sync Icon -->
        <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 transition-transform duration-700"
            :class="{ 'animate-spin': isSyncing }"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
        >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>

        <!-- Status Text - Hidden on mobile, visible on desktop -->
        <span class="hidden md:inline" x-text="isSyncing ? 'Syncing...' : (pendingItems > 0 ? `Sync (${pendingItems})` : 'Synced')"></span>

        <!-- Pending indicator dot for mobile -->
        <span
            x-show="pendingItems > 0 && !isSyncing"
            class="md:hidden absolute -top-1 -right-1 h-3 w-3 bg-yellow-400 border-2 border-white rounded-full"
        ></span>
    </button>
    
    <!-- Sync Details Popover -->
    <div 
        x-show="pendingItems > 0 || isSyncing"
        @click.away="$el.classList.add('hidden')"
        class="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg z-10 hidden"
        x-cloak
    >
        <div class="p-4">
            <h3 class="text-sm font-medium text-gray-900">Sync Status</h3>
            
            <div class="mt-2 text-xs text-gray-600">
                <p class="flex justify-between">
                    <span>Last Sync:</span>
                    <span x-text="lastSync"></span>
                </p>
                
                <p class="flex justify-between mt-1">
                    <span>Pending Items:</span>
                    <span x-text="pendingItems"></span>
                </p>
                
                <div class="mt-3" x-show="pendingItems > 0 && !isSyncing">
                    <button 
                        @click="triggerSync"
                        class="w-full px-3 py-1.5 text-xs font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                        Sync Now
                    </button>
                </div>
            </div>
        </div>
    </div>
</div> 