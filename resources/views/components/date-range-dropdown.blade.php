@props(['route', 'currentStartDate' => null, 'currentEndDate' => null])

@php
    // Handle both Carbon objects and strings
    $startDateFormatted = $currentStartDate instanceof \Carbon\Carbon
        ? $currentStartDate->format('Y-m-d')
        : $currentStartDate;
    $endDateFormatted = $currentEndDate instanceof \Carbon\Carbon
        ? $currentEndDate->format('Y-m-d')
        : $currentEndDate;
@endphp

<div x-data="dateRangeDropdown('{{ $route }}', '{{ $startDateFormatted }}', '{{ $endDateFormatted }}')" class="relative">
    <button type="button" 
            @click="open = !open"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
        Quick Dates
        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
    </button>

    <div x-show="open" 
         @click.away="open = false"
         x-transition:enter="transition ease-out duration-100"
         x-transition:enter-start="transform opacity-0 scale-95"
         x-transition:enter-end="transform opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="transform opacity-100 scale-100"
         x-transition:leave-end="transform opacity-0 scale-95"
         class="absolute z-50 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none"
         style="display: none;">
        <div class="py-1">
            <button type="button" @click="selectDateRange('today')" 
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                Today
            </button>
            <button type="button" @click="selectDateRange('last3days')" 
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                Last 3 Days
            </button>
            <button type="button" @click="selectDateRange('lastweek')" 
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                Last Week
            </button>
            <button type="button" @click="selectDateRange('lastmonth')" 
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                Last Month
            </button>
            <button type="button" @click="selectDateRange('thisyear')" 
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                This Year
            </button>
            <button type="button" @click="selectDateRange('lastyear')" 
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                Last Year
            </button>
        </div>
    </div>
</div>

<script>
// Ensure the dateRangeDropdown component is available
if (typeof window.Alpine !== 'undefined') {
    // If Alpine is already loaded, register immediately
    if (!window.Alpine.data('dateRangeDropdown')) {
        window.Alpine.data('dateRangeDropdown', (route, currentStartDate, currentEndDate) => ({
            open: false,

            selectDateRange(range) {
                console.log('Date range selected:', range);
                const today = new Date();
                let startDate, endDate;

                switch(range) {
                    case 'today':
                        startDate = new Date(today);
                        endDate = new Date(today);
                        break;

                    case 'last3days':
                        startDate = new Date(today);
                        startDate.setDate(today.getDate() - 2);
                        endDate = new Date(today);
                        break;

                    case 'lastweek':
                        startDate = new Date(today);
                        startDate.setDate(today.getDate() - 6);
                        endDate = new Date(today);
                        break;

                    case 'lastmonth':
                        startDate = new Date(today);
                        startDate.setMonth(today.getMonth() - 1);
                        endDate = new Date(today);
                        break;

                    case 'thisyear':
                        startDate = new Date(today.getFullYear(), 0, 1);
                        endDate = new Date(today);
                        break;

                    case 'lastyear':
                        startDate = new Date(today.getFullYear() - 1, 0, 1);
                        endDate = new Date(today.getFullYear() - 1, 11, 31);
                        break;
                }

                // Format dates as YYYY-MM-DD
                const formatDate = (date) => {
                    return date.getFullYear() + '-' +
                           String(date.getMonth() + 1).padStart(2, '0') + '-' +
                           String(date.getDate()).padStart(2, '0');
                };

                console.log('Formatted dates:', formatDate(startDate), formatDate(endDate));

                // Update the form inputs
                const startInput = document.getElementById('start_date');
                const endInput = document.getElementById('end_date');

                if (startInput) {
                    startInput.value = formatDate(startDate);
                    console.log('Updated start input:', startInput.value);
                } else {
                    console.warn('Start date input not found');
                }

                if (endInput) {
                    endInput.value = formatDate(endDate);
                    console.log('Updated end input:', endInput.value);
                } else {
                    console.warn('End date input not found');
                }

                // Close dropdown
                this.open = false;

                // Submit the form automatically - try multiple selectors
                let form = null;

                // First try to find form by action containing the route
                if (route) {
                    const routePath = route.split('/').pop(); // Get the last part of the route
                    form = document.querySelector(`form[action*="${routePath}"]`);
                    console.log('Form found by route path:', form);
                }

                // If not found, try to find the closest form to the inputs
                if (!form && startInput) {
                    form = startInput.closest('form');
                    console.log('Form found by closest to start input:', form);
                }

                // If still not found, try generic GET form selector
                if (!form) {
                    form = document.querySelector('form[method="GET"]');
                    console.log('Form found by GET method:', form);
                }

                // Last resort: find any form on the page
                if (!form) {
                    form = document.querySelector('form');
                    console.log('Form found as fallback:', form);
                }

                if (form) {
                    console.log('Submitting form:', form.action || form.getAttribute('action'));

                    // Preserve existing query parameters (like supplier_id) by adding hidden inputs
                    const currentParams = new URLSearchParams(window.location.search);
                    for (const [key, value] of currentParams) {
                        if (key !== 'start_date' && key !== 'end_date') {
                            let existingInput = form.querySelector(`input[name="${key}"], select[name="${key}"]`);
                            if (!existingInput) {
                                const hiddenInput = document.createElement('input');
                                hiddenInput.type = 'hidden';
                                hiddenInput.name = key;
                                hiddenInput.value = value;
                                form.appendChild(hiddenInput);
                            }
                        }
                    }

                    // Add a small delay to ensure inputs are updated
                    setTimeout(() => {
                        form.submit();
                    }, 100);
                } else {
                    console.error('No form found to submit');
                    // Manual redirect as fallback
                    if (route && startInput && endInput) {
                        const url = new URL(route, window.location.origin);
                        url.searchParams.set('start_date', startInput.value);
                        url.searchParams.set('end_date', endInput.value);

                        // Preserve existing query parameters (like supplier_id)
                        const currentParams = new URLSearchParams(window.location.search);
                        for (const [key, value] of currentParams) {
                            if (key !== 'start_date' && key !== 'end_date') {
                                url.searchParams.set(key, value);
                            }
                        }

                        console.log('Manual redirect to:', url.toString());
                        window.location.href = url.toString();
                    }
                }
            }
        }));
    }
} else {
    // If Alpine is not loaded yet, wait for it
    document.addEventListener('alpine:init', () => {
        if (!window.Alpine.data('dateRangeDropdown')) {
            window.Alpine.data('dateRangeDropdown', (route, currentStartDate, currentEndDate) => ({
                open: false,

                selectDateRange(range) {
                    console.log('Date range selected:', range);
                    const today = new Date();
                    let startDate, endDate;

                    switch(range) {
                        case 'today':
                            startDate = new Date(today);
                            endDate = new Date(today);
                            break;

                        case 'last3days':
                            startDate = new Date(today);
                            startDate.setDate(today.getDate() - 2);
                            endDate = new Date(today);
                            break;

                        case 'lastweek':
                            startDate = new Date(today);
                            startDate.setDate(today.getDate() - 6);
                            endDate = new Date(today);
                            break;

                        case 'lastmonth':
                            startDate = new Date(today);
                            startDate.setMonth(today.getMonth() - 1);
                            endDate = new Date(today);
                            break;

                        case 'thisyear':
                            startDate = new Date(today.getFullYear(), 0, 1);
                            endDate = new Date(today);
                            break;

                        case 'lastyear':
                            startDate = new Date(today.getFullYear() - 1, 0, 1);
                            endDate = new Date(today.getFullYear() - 1, 11, 31);
                            break;
                    }

                    // Format dates as YYYY-MM-DD
                    const formatDate = (date) => {
                        return date.getFullYear() + '-' +
                               String(date.getMonth() + 1).padStart(2, '0') + '-' +
                               String(date.getDate()).padStart(2, '0');
                    };

                    console.log('Formatted dates:', formatDate(startDate), formatDate(endDate));

                    // Update the form inputs
                    const startInput = document.getElementById('start_date');
                    const endInput = document.getElementById('end_date');

                    if (startInput) {
                        startInput.value = formatDate(startDate);
                        console.log('Updated start input:', startInput.value);
                    } else {
                        console.warn('Start date input not found');
                    }

                    if (endInput) {
                        endInput.value = formatDate(endDate);
                        console.log('Updated end input:', endInput.value);
                    } else {
                        console.warn('End date input not found');
                    }

                    // Close dropdown
                    this.open = false;

                    // Submit the form automatically - try multiple selectors
                    let form = null;

                    // First try to find form by action containing the route
                    if (route) {
                        const routePath = route.split('/').pop(); // Get the last part of the route
                        form = document.querySelector(`form[action*="${routePath}"]`);
                        console.log('Form found by route path:', form);
                    }

                    // If not found, try to find the closest form to the inputs
                    if (!form && startInput) {
                        form = startInput.closest('form');
                        console.log('Form found by closest to start input:', form);
                    }

                    // If still not found, try generic GET form selector
                    if (!form) {
                        form = document.querySelector('form[method="GET"]');
                        console.log('Form found by GET method:', form);
                    }

                    // Last resort: find any form on the page
                    if (!form) {
                        form = document.querySelector('form');
                        console.log('Form found as fallback:', form);
                    }

                    if (form) {
                        console.log('Submitting form:', form.action || form.getAttribute('action'));

                        // Preserve existing query parameters (like supplier_id) by adding hidden inputs
                        const currentParams = new URLSearchParams(window.location.search);
                        for (const [key, value] of currentParams) {
                            if (key !== 'start_date' && key !== 'end_date') {
                                let existingInput = form.querySelector(`input[name="${key}"], select[name="${key}"]`);
                                if (!existingInput) {
                                    const hiddenInput = document.createElement('input');
                                    hiddenInput.type = 'hidden';
                                    hiddenInput.name = key;
                                    hiddenInput.value = value;
                                    form.appendChild(hiddenInput);
                                }
                            }
                        }

                        // Add a small delay to ensure inputs are updated
                        setTimeout(() => {
                            form.submit();
                        }, 100);
                    } else {
                        console.error('No form found to submit');
                        // Manual redirect as fallback
                        if (route && startInput && endInput) {
                            const url = new URL(route, window.location.origin);
                            url.searchParams.set('start_date', startInput.value);
                            url.searchParams.set('end_date', endInput.value);

                            // Preserve existing query parameters (like supplier_id)
                            const currentParams = new URLSearchParams(window.location.search);
                            for (const [key, value] of currentParams) {
                                if (key !== 'start_date' && key !== 'end_date') {
                                    url.searchParams.set(key, value);
                                }
                            }

                            console.log('Manual redirect to:', url.toString());
                            window.location.href = url.toString();
                        }
                    }
                }
            }));
        }
    });
}
</script>


