<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="description" content="PharmaDek - Comprehensive Pharmacy Management System">

        <title>{{ config('app.name', 'PharmaDek') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

        <!-- Styles -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
        
        <style>
            body {
                font-family: 'Poppins', sans-serif;
                color: #2d3748;
                background-color: #f7fafc;
            }
            .hero-section {
                background: #5647e5;
                color: white;
                border-radius: 0 0 0 0;
                box-shadow: 0 10px 25px rgba(86, 71, 229, 0.2);
                position: relative;
                overflow: hidden;
            }
            .hero-section::before {
                display: none;
            }
            .feature-card {
                border-radius: 1rem;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.04);
                transition: all 0.3s ease;
                height: 100%;
                border: 1px solid rgba(229, 231, 235, 0.5);
                background: white;
                overflow: hidden;
            }
            .feature-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
                border-color: rgba(86, 71, 229, 0.2);
            }
            .feature-icon {
                background: linear-gradient(135deg, rgba(86, 71, 229, 0.1) 0%, rgba(86, 71, 229, 0.2) 100%);
                color: #5647e5;
                width: 64px;
                height: 64px;
                border-radius: 1rem;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 1.5rem;
                transition: all 0.3s ease;
            }
            .feature-card:hover .feature-icon {
                background: linear-gradient(135deg, rgba(86, 71, 229, 0.2) 0%, rgba(86, 71, 229, 0.3) 100%);
                transform: scale(1.05);
            }
            .btn-primary {
                background: linear-gradient(135deg, #3a5a92 0%, #2c4570 100%);
                color: white;
                padding: 0.75rem 1.5rem;
                border-radius: 0.5rem;
                font-weight: 500;
                transition: all 0.3s ease;
                border: none;
                position: relative;
                overflow: hidden;
                z-index: 1;
            }
            .btn-primary::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
                transition: all 0.5s ease;
                z-index: -1;
            }
            .btn-primary:hover {
                box-shadow: 0 5px 15px rgba(44, 69, 112, 0.3);
                transform: translateY(-1px);
            }
            .btn-primary:hover::before {
                left: 100%;
            }
            .contact-info {
                background-color: white;
                border-radius: 1rem;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.03);
                border: 1px solid rgba(229, 231, 235, 0.5);
            }
            .logo-container {
                display: none;
            }
            .section-title {
                position: relative;
                display: inline-block;
                padding-bottom: 10px;
            }
            .section-title::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 60px;
                height: 3px;
                background: #5647e5;
                border-radius: 3px;
            }
            .contact-icon {
                width: 42px;
                height: 42px;
                background: rgba(86, 71, 229, 0.1);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 1rem;
                color: #5647e5;
                transition: all 0.3s ease;
            }
            .contact-item:hover .contact-icon {
                background: rgba(86, 71, 229, 0.2);
                transform: scale(1.05);
            }
            .footer {
                background: #1a202c;
            }
            .cta-section {
                background: linear-gradient(135deg, rgba(86, 71, 229, 0.05) 0%, rgba(86, 71, 229, 0.1) 100%);
                border-radius: 1rem;
                border: 1px solid rgba(229, 231, 235, 0.5);
            }
            .business-hours-item {
                transition: all 0.3s ease;
                border-bottom: 1px solid rgba(229, 231, 235, 0.5);
                padding: 0.75rem 0;
            }
            .business-hours-item:last-child {
                border-bottom: none;
            }
            .business-hours-item:hover {
                background-color: rgba(86, 71, 229, 0.05);
                transform: translateX(5px);
                border-radius: 0.5rem;
                padding-left: 5px;
            }
            
            /* Button animations */
            .btn-animated {
                position: relative;
                overflow: hidden;
                transition: all 0.3s ease;
            }
            
            .btn-animated:hover {
                transform: translateY(-2px);
                box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
            }
            
            .btn-animated.btn-blue:hover {
                background-color: #4a3cd6; /* Slightly darker indigo on hover */
            }
            
            .btn-animated.btn-white:hover {
                background-color: #f9fafb; /* Slightly darker white on hover */
            }
            
            .btn-animated .btn-icon {
                transition: transform 0.3s ease;
            }
            
            .btn-animated:hover .btn-icon {
                transform: translateX(4px);
            }
            
            .btn-animated::after {
                content: '';
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                left: -100%;
                background: linear-gradient(
                    90deg,
                    rgba(255, 255, 255, 0) 0%,
                    rgba(255, 255, 255, 0.2) 50%,
                    rgba(255, 255, 255, 0) 100%
                );
                transition: left 0.7s ease;
            }
            
            .btn-animated:hover::after {
                left: 100%;
            }
        </style>
    </head>
    <body>
        <script>
            // Check if the user came from a secure page (like dashboard)
            if (document.referrer.includes('/dashboard') || 
                document.referrer.includes('/profile') || 
                document.referrer.includes('/inventory') || 
                document.referrer.includes('/sales') || 
                document.referrer.includes('/customers') || 
                document.referrer.includes('/reports')) {
                
                // If user has been logged out and is trying to access the welcome page from an authenticated page
                // This prevents the back button from showing authenticated content
                window.history.pushState(null, '', window.location.href);
                window.onpopstate = function() {
                    window.history.pushState(null, '', window.location.href);
                };
            }
        </script>
        
        <div class="min-h-screen flex flex-col">
            <!-- Navigation -->
            <nav class="bg-white sticky top-0 z-50 shadow-sm py-4">
                <div class="container mx-auto px-4 flex justify-between items-center">
                    <div class="flex items-center space-x-3">
                        <a href="{{ url('/') }}" class="flex items-center space-x-3 hover:opacity-90 transition-opacity">
                            <div class="bg-indigo-600 rounded-full w-10 h-10 flex items-center justify-center p-0 border-2 border-white overflow-hidden">
                                <x-pharmadesk-logo class="w-8 h-8 object-contain" />
                            </div>
                            <span class="text-xl font-bold text-indigo-600">PharmaDek</span>
                        </a>
                        </div>
                    <div>
                        @if (Route::has('login'))
                            <div class="space-x-2">
                                @auth
                                    <a href="{{ url('/dashboard') }}" class="inline-flex items-center justify-center px-5 py-2 bg-indigo-600 text-white rounded-md font-medium transition hover:bg-indigo-700 btn-animated btn-blue">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 btn-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                        </svg>
                                        Dashboard
                                    </a>
                                @else
                                    <a href="{{ route('register') }}" class="inline-flex items-center justify-center px-5 py-2 bg-indigo-600 text-white rounded-md font-medium transition hover:bg-indigo-700 btn-animated btn-blue">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 btn-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                                        </svg>
                                        Register
                                    </a>
                                    <a href="{{ route('login') }}" class="inline-flex items-center justify-center px-5 py-2 bg-white text-indigo-600 rounded-md font-medium border border-gray-200 transition hover:bg-gray-50 btn-animated btn-white">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 btn-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                                        </svg>
                                        Login
                                    </a>
                                @endauth
                            </div>
                        @endif
                    </div>
                </div>
            </nav>

            <!-- Hero Section -->
            <section class="hero-section py-16">
                <div class="container mx-auto px-4 text-center">
                    <h1 class="text-4xl md:text-5xl font-bold mb-4 leading-tight">Pharmacy Management System</h1>
                    <p class="text-lg md:text-xl max-w-2xl mx-auto mb-8">A comprehensive solution designed to streamline your pharmacy operations, manage inventory, and enhance customer service.</p>
                    <div class="flex flex-wrap justify-center gap-4 mt-4">
                        <a href="{{ route('register') }}" class="inline-flex items-center justify-center px-6 py-2 bg-indigo-600 text-white rounded-md font-medium transition hover:bg-indigo-700 btn-animated btn-blue">
                            Get Started
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 btn-icon" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                        </a>
                        <a href="{{ route('login') }}" class="inline-flex items-center justify-center px-6 py-2 bg-white text-indigo-600 rounded-md font-medium border border-gray-200 transition hover:bg-gray-50 btn-animated btn-white">
                            Login
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 btn-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                            </svg>
                        </a>
                    </div>
                </div>
            </section>

            <!-- Logo Section -->
            <section class="relative z-10 -mt-10">
                <div class="container mx-auto px-4 flex justify-center">
                    <div class="bg-indigo-600 rounded-full w-20 h-20 flex items-center justify-center p-0 border-4 border-white overflow-hidden shadow-lg">
                        <x-pharmadesk-logo class="w-16 h-16 object-contain" />
                    </div>
                </div>
            </section>

            <!-- Features -->
            <section class="py-16 bg-white">
                <div class="container mx-auto px-4">
                    <h2 class="text-3xl font-bold text-center mb-12 section-title">Key Features</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <!-- Feature 1 -->
                        <div class="feature-card p-6">
                            <div class="feature-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold mb-3">Inventory Management</h3>
                            <p class="text-gray-600">Track stock levels, expiry dates, and batch numbers. Get alerts for low stock and expired medications.</p>
                                </div>

                        <!-- Feature 2 -->
                        <div class="feature-card p-6">
                            <div class="feature-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold mb-3">Sales & Billing</h3>
                            <p class="text-gray-600">Process sales, generate invoices, and manage multiple payment methods with our intuitive POS system.</p>
                                        </div>

                        <!-- Feature 3 -->
                        <div class="feature-card p-6">
                            <div class="feature-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                        </div>
                            <h3 class="text-xl font-semibold mb-3">Customer Management</h3>
                            <p class="text-gray-600">Maintain customer profiles, prescription history, and loyalty programs to enhance customer retention.</p>
                                    </div>

                        <!-- Feature 4 -->
                        <div class="feature-card p-6">
                            <div class="feature-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                </div>
                            <h3 class="text-xl font-semibold mb-3">Financial Reports</h3>
                            <p class="text-gray-600">Generate comprehensive financial reports, profit/loss statements, and sales analytics.</p>
                                </div>

                        <!-- Feature 5 -->
                        <div class="feature-card p-6">
                            <div class="feature-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold mb-3">Offline Capabilities</h3>
                            <p class="text-gray-600">Continue operations even without internet connection with our robust offline mode and data synchronization.</p>
                                </div>

                        <!-- Feature 6 -->
                        <div class="feature-card p-6">
                            <div class="feature-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold mb-3">Security & Compliance</h3>
                            <p class="text-gray-600">Ensure data security with role-based access control and maintain regulatory compliance with audit trails.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- CTA Section -->
            <section class="py-10 bg-gray-50">
                <div class="container mx-auto px-4">
                    <div class="cta-section py-8 px-6 md:px-8 max-w-4xl mx-auto">
                        <div class="text-center mb-6">
                            <h2 class="text-2xl md:text-2xl font-bold mb-3">Ready to streamline your pharmacy operations?</h2>
                            <p class="text-gray-600 max-w-2xl mx-auto">Join thousands of pharmacies that have improved their efficiency and customer service with PharmaDek.</p>
                        </div>
                        <div class="flex flex-wrap justify-center gap-4">
                            <a href="{{ route('register') }}" class="inline-flex items-center justify-center px-6 py-2 bg-indigo-600 text-white rounded-md font-medium transition hover:bg-indigo-700 btn-animated btn-blue">
                                Register Now
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 btn-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                                </svg>
                            </a>
                            <a href="{{ route('login') }}" class="inline-flex items-center justify-center px-6 py-2 bg-white text-indigo-600 rounded-md font-medium border border-gray-200 transition hover:bg-gray-50 btn-animated btn-white">
                                Login
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 btn-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Contact Section -->
            <section class="py-16 bg-white">
                <div class="container mx-auto px-4">
                    <h2 class="text-3xl font-bold text-center mb-12 section-title">Contact Us</h2>
                    
                    <div class="max-w-5xl mx-auto">
                        <div class="contact-info p-8">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                                <div>
                                    <h3 class="text-xl font-semibold mb-4 text-gray-800">Get in Touch</h3>
                                    <p class="mb-6 text-gray-600">Have questions about PharmaDek? Our team is here to help you get started.</p>
                                    
                                    <div class="space-y-6">
                                        <div class="flex items-start contact-item">
                                            <div class="contact-icon">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                                </svg>
                                            </div>
                                            <div>
                                                <p class="font-medium text-gray-800">Phone</p>
                                                <p class="text-gray-600">+8801 788 544 788</p>
                                            </div>
                                </div>

                                        <div class="flex items-start contact-item">
                                            <div class="contact-icon">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                                </svg>
                                            </div>
                                            <div>
                                                <p class="font-medium text-gray-800">Email</p>
                                                <p class="text-gray-600"><EMAIL></p>
                                            </div>
                                        </div>
                                        
                                        <div class="flex items-start contact-item">
                                            <div class="contact-icon">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                                                </svg>
                                            </div>
                                            <div>
                                                <p class="font-medium text-gray-800">Website</p>
                                                <p class="text-gray-600">
                                                    <a href="https://neurotechsystem.com" class="text-indigo-600 hover:text-indigo-800 hover:underline transition-colors">www.neurotechsystem.com</a>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <h3 class="text-xl font-semibold mb-4 text-gray-800">Business Hours</h3>
                                    <div class="space-y-0">
                                        <div class="flex justify-between business-hours-item">
                                            <span class="text-gray-600">Monday - Friday</span>
                                            <span class="font-medium text-gray-800">9:00 AM - 6:00 PM</span>
                                        </div>
                                        <div class="flex justify-between business-hours-item">
                                            <span class="text-gray-600">Saturday</span>
                                            <span class="font-medium text-gray-800">10:00 AM - 4:00 PM</span>
                                        </div>
                                        <div class="flex justify-between business-hours-item">
                                            <span class="text-gray-600">Sunday</span>
                                            <span class="font-medium text-gray-800">Closed</span>
                                        </div>
                                        
                                        <div class="pt-6 mt-4">
                                            <h4 class="font-medium mb-2 text-gray-800">Support Hours</h4>
                                            <div class="p-3 bg-indigo-50 rounded-lg border border-indigo-100">
                                                <div class="flex items-start">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                                    <p class="text-gray-700">Our technical support team is available 24/7 to assist with any urgent issues.</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Footer -->
            <footer class="footer text-white py-10 mt-auto">
                <div class="container mx-auto px-4">
                    <div class="flex flex-col md:flex-row justify-between items-center">
                        <div class="mb-6 md:mb-0">
                            <div class="flex items-center">
                                <div class="mr-3 bg-indigo-600 rounded-full w-10 h-10 flex items-center justify-center p-0 border-2 border-white overflow-hidden">
                                    <x-pharmadesk-logo class="w-8 h-8 object-contain" />
                                </div>
                                <span class="text-xl font-bold">PharmaDek</span>
                            </div>
                            <p class="mt-2 text-gray-400">Streamlining pharmacy operations since 2025</p>
                        </div>
                        
                        <div class="text-center md:text-right">
                            <p>&copy; {{ date('Y') }} PharmaDek. All rights reserved.</p>
                            <p class="text-gray-400 mt-1">v{{ config('app.version', '1.0.0') }}</p>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </body>
</html>
