@extends('layouts.admin')

@section('title', 'Customers')

@section('content')
<div class="py-6">
    <div class="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="md:flex md:items-center md:justify-between mb-6">
            <div class="flex-1 min-w-0">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                    Customers
                </h2>
                <p class="mt-1 text-sm text-gray-500">
                    Manage your pharmacy customers and their information
                </p>
            </div>
            <div class="mt-4 flex md:mt-0 md:ml-4">
                <a href="{{ route('customers.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    New Customer
                </a>
            </div>
        </div>

        <!-- Success Message -->
        @if(session('success'))
        <div class="mb-6">
            <div class="rounded-md bg-green-50 p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3 flex-1">
                        <p class="text-sm font-medium text-green-800">
                            {{ session('success') }}
                        </p>
                    </div>
                    <div class="ml-auto pl-3">
                        <div class="-mx-1.5 -my-1.5">
                            <button type="button" class="inline-flex rounded-md p-1.5 text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-600">
                                <span class="sr-only">Dismiss</span>
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Filters and Search -->
        <div class="bg-white shadow-sm rounded-lg mb-6" x-data="{ 
            searchTerm: '{{ request('search') }}',
            searchResults: [],
            loading: false,
            open: false,
            init() {
                this.$watch('searchTerm', (value) => {
                    this.searchCustomers(value);
                });
            },
            async searchCustomers(query) {
                if (query.length < 3) {
                    this.searchResults = [];
                    this.open = false;
                    return;
                }

                this.loading = true;
                try {
                    const response = await fetch(`{{ route('customers.index') }}?search=${encodeURIComponent(query)}&format=json`);
                    if (!response.ok) throw new Error('Search failed');
                    const data = await response.json();
                    this.searchResults = data;
                    this.open = true;
                } catch (error) {
                    console.error('Search error:', error);
                    this.searchResults = [];
                } finally {
                    this.loading = false;
                }
            },
            selectCustomer(customer) {
                this.searchTerm = customer.name;
                this.open = false;
                window.location.href = `{{ route('customers.index') }}?search=${encodeURIComponent(customer.name)}`;
            }
        }">
            <div class="p-4">
                <form action="{{ route('customers.index') }}" method="GET" class="grid grid-cols-1 lg:grid-cols-12 gap-4">
                    <!-- Search -->
                    <div class="lg:col-span-6">
                        <label for="search" class="sr-only">Search customers</label>
                        <div class="relative">
                            <input type="search" name="search" id="search" 
                                x-model="searchTerm"
                                @focus="open = searchTerm.length >= 3"
                                @click.away="open = false"
                                class="block w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                                placeholder="Search customers by name, email, or phone...">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <template x-if="!loading">
                                    <button type="submit" class="p-1 focus:outline-none focus:shadow-outline">
                                        <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                </template>
                                <template x-if="loading">
                                    <svg class="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </template>
                            </div>
                            
                            <!-- Search Results Dropdown -->
                            <div class="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md py-1 text-sm" 
                                x-show="open && searchResults.length > 0" 
                                x-transition:enter="transition ease-out duration-100" 
                                x-transition:enter-start="transform opacity-0 scale-95" 
                                x-transition:enter-end="transform opacity-100 scale-100" 
                                x-transition:leave="transition ease-in duration-75" 
                                x-transition:leave-start="transform opacity-100 scale-100" 
                                x-transition:leave-end="transform opacity-0 scale-95"
                                style="max-height: 300px; overflow-y: auto;">
                                <template x-for="(result, idx) in searchResults" :key="idx">
                                    <a href="#" 
                                        class="block px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                        @click.prevent="selectCustomer(result)">
                                        <div class="font-medium" x-text="result.name"></div>
                                        <div class="flex justify-between items-center text-xs text-gray-500">
                                            <div>
                                                <span x-show="result.email" x-text="result.email"></span>
                                                <span x-show="result.phone" x-text="result.phone ? (result.email ? ' | ' + result.phone : result.phone) : ''"></span>
                                            </div>
                                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium" 
                                                :class="{
                                                    'bg-green-100 text-green-800': result.sales_count > 0,
                                                    'bg-gray-100 text-gray-800': result.sales_count === 0
                                                }">
                                                <span x-text="result.sales_count > 0 ? 'Active' : 'New'"></span>
                                            </span>
                                        </div>
                                    </a>
                                </template>
                            </div>

                            <!-- No Results Message -->
                            <div class="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md py-2 px-4 text-sm text-gray-500" 
                                x-show="open && searchTerm.length >= 3 && searchResults.length === 0 && !loading">
                                No customers found
                            </div>
                        </div>
                    </div>
                    <!-- Filters -->
                    <div class="lg:col-span-6">
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div>
                                <select name="status" class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" onchange="this.form.submit()">
                                    <option value="">All Customers</option>
                                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                    <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                </select>
                            </div>
                            <div>
                                <select name="sort" class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" onchange="this.form.submit()">
                                    <option value="name" {{ request('sort') == 'name' || !request('sort') ? 'selected' : '' }}>Sort by Name</option>
                                    <option value="recent" {{ request('sort') == 'recent' ? 'selected' : '' }}>Sort by Recent</option>
                                    <option value="sales" {{ request('sort') == 'sales' ? 'selected' : '' }}>Sort by Sales</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Customers List -->
        <div class="bg-white shadow-sm overflow-hidden rounded-lg">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr class="bg-gray-50">
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Customer
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Contact
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Sales
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th scope="col" class="relative px-6 py-3">
                                <span class="sr-only">Actions</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($customers as $customer)
                        <tr class="hover:bg-gray-50 transition duration-150">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <span class="inline-flex items-center justify-center h-10 w-10 rounded-full bg-indigo-50">
                                            <span class="text-sm font-medium leading-none text-indigo-600">
                                                {{ strtoupper(substr($customer->name, 0, 2)) }}
                                            </span>
                                        </span>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ $customer->name }}
                                        </div>
                                        @if($customer->insurance_provider)
                                        <div class="text-sm text-gray-500">
                                            Insurance: {{ $customer->insurance_provider }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $customer->phone ?? 'N/A' }}</div>
                                <div class="text-sm text-gray-500">{{ $customer->email ?? 'N/A' }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $customer->sales_count }} orders</div>
                                @if($customer->loyalty_points)
                                <div class="text-sm text-gray-500">
                                    {{ number_format($customer->loyalty_points) }} points
                                </div>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2.5 py-1 inline-flex text-xs leading-5 font-semibold rounded-full {{ $customer->sales_count > 0 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                    {{ $customer->sales_count > 0 ? 'Active' : 'New' }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-3">
                                    <a href="{{ route('customers.show', $customer) }}" class="text-indigo-600 hover:text-indigo-900 font-medium">
                                        View
                                    </a>
                                    <a href="{{ route('customers.edit', $customer) }}" class="text-gray-600 hover:text-gray-900">
                                        Edit
                                    </a>
                                    @if($customer->sales_count == 0)
                                    <form action="{{ route('customers.destroy', $customer) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-600 hover:text-red-900" onclick="return confirm('Are you sure you want to delete this customer?')">
                                            Delete
                                        </button>
                                    </form>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                No customers found.
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            @if($customers->hasPages())
            <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                {{ $customers->appends(request()->query())->links() }}
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
