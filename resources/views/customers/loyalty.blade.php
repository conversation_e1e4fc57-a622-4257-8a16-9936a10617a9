@extends('layouts.admin')

@section('title', 'Customer Loyalty Program')

@push('styles')
<style>
    .stats-card {
        @apply relative overflow-hidden transition-all duration-300 ease-in-out hover:shadow-lg;
    }
    .stats-card:hover {
        transform: translateY(-2px);
    }
    .stats-card::before {
        content: '';
        @apply absolute top-0 left-0 w-2 h-full bg-primary opacity-75;
    }
    .stats-icon {
        @apply transition-all duration-300 ease-in-out;
    }
    .stats-card:hover .stats-icon {
        transform: scale(1.1);
    }
    .progress-ring {
        @apply transition-all duration-300 ease-in-out;
    }
    .table-row {
        @apply transition-all duration-200 ease-in-out hover:bg-gray-50;
    }
    .program-rule-card {
        @apply relative overflow-hidden transition-all duration-300 ease-in-out hover:shadow-md;
    }
    .program-rule-card:hover {
        transform: translateY(-1px);
    }
</style>
@endpush

@section('content')
<div class="py-6">
    <div class="w-full px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="md:flex md:items-center md:justify-between mb-8">
            <div class="flex-1 min-w-0">
                <h2 class="text-3xl font-bold leading-7 text-gray-900 sm:text-4xl break-words">
                    Customer Loyalty Program
                </h2>
                <div class="mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6">
                    <div class="mt-2 flex items-center text-sm text-gray-500">
                        <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        Manage customer loyalty points and rewards
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="mt-4 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            <!-- Current Points Card -->
            <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 stats-icon">
                            <div class="p-3 bg-primary-100 rounded-full">
                                <svg class="h-8 w-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    Current Points Balance
                                </dt>
                                <dd class="flex items-baseline">
                                    <div class="text-3xl font-semibold text-gray-900">
                                        {{ number_format($totalPoints) }}
                                    </div>
                                    <div class="ml-2 flex items-baseline text-sm font-semibold text-primary-600">
                                        <span class="text-xs">Active</span>
                                    </div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Members Card -->
            <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 stats-icon">
                            <div class="p-3 bg-green-100 rounded-full">
                                <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    Active Members
                                </dt>
                                <dd class="flex items-baseline">
                                    <div class="text-3xl font-semibold text-gray-900">
                                        {{ number_format($activeMembers) }}
                                    </div>
                                    <div class="ml-2 flex items-baseline text-sm font-semibold text-green-600">
                                        <span class="sr-only">Members</span>
                                    </div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Points Redeemed Card -->
            <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 stats-icon">
                            <div class="p-3 bg-blue-100 rounded-full">
                                <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    Points Redeemed
                                </dt>
                                <dd class="flex items-baseline">
                                    <div class="text-3xl font-semibold text-gray-900">
                                        {{ number_format($pointsRedeemed) }}
                                    </div>
                                    <div class="ml-2 flex items-baseline text-sm font-semibold text-blue-600">
                                        <span class="text-xs">${{ number_format($totalDiscountGiven ?? 0, 2) }} saved</span>
                                    </div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gold Members Card -->
            <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 stats-icon">
                            <div class="p-3 bg-yellow-100 rounded-full">
                                <svg class="h-8 w-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    Gold Members
                                </dt>
                                <dd class="flex items-baseline">
                                    <div class="text-3xl font-semibold text-gray-900">
                                        {{ number_format($goldMembers ?? 0) }}
                                    </div>
                                    <div class="ml-2 flex items-baseline text-sm font-semibold text-yellow-600">
                                        <span class="text-xs">1000+ points</span>
                                    </div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customers List -->
        <div class="mt-8">
            <div class="bg-white shadow overflow-hidden sm:rounded-lg w-full">
                <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">
                            Loyalty Program Members
                        </h3>
                        <div class="flex-shrink-0">
                            <span class="inline-flex rounded-md shadow-sm">
                                <a href="{{ route('customers.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                    </svg>
                                    Add Member
                                </a>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="bg-white mb-6">
                    <div class="w-full mx-auto">
                        <div class="flex flex-col">
                            <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                                <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                                    <div class="overflow-hidden">
                                        <table class="min-w-full divide-y divide-gray-200">
                                            <thead class="bg-gray-50">
                                                <tr>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Customer
                                                    </th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Points Balance
                                                    </th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Last Purchase
                                                    </th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Status
                                                    </th>
                                                    <th scope="col" class="relative px-6 py-3">
                                                        <span class="sr-only">Actions</span>
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-white divide-y divide-gray-200">
                                                @forelse($customers as $customer)
                                                <tr class="table-row">
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="flex items-center">
                                                            <div class="flex-shrink-0 h-10 w-10">
                                                                <span class="h-10 w-10 rounded-full flex items-center justify-center
                                                                    @php
                                                                        $colors = [
                                                                            'bg-pink-100 text-pink-700',
                                                                            'bg-purple-100 text-purple-700',
                                                                            'bg-indigo-100 text-indigo-700',
                                                                            'bg-blue-100 text-blue-700',
                                                                            'bg-green-100 text-green-700',
                                                                            'bg-yellow-100 text-yellow-700',
                                                                            'bg-red-100 text-red-700',
                                                                            'bg-gray-100 text-gray-700'
                                                                        ];
                                                                        $colorIndex = ord(strtolower(substr($customer->name, 0, 1))) % count($colors);
                                                                        echo $colors[$colorIndex];
                                                                    @endphp
                                                                ">
                                                                    <span class="text-lg font-medium">
                                                                        {{ strtoupper(substr($customer->name, 0, 1)) }}
                                                                    </span>
                                                                </span>
                                                            </div>
                                                            <div class="ml-4">
                                                                <div class="text-sm font-medium text-gray-900">
                                                                    {{ $customer->name }}
                                                                </div>
                                                                <div class="text-sm text-gray-500">
                                                                    {{ $customer->email }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="flex items-center">
                                                            <div class="text-sm font-medium text-gray-900">
                                                                {{ number_format($customer->loyalty_points) }}
                                                            </div>
                                                            <div class="ml-2 flex-shrink-0">
                                                                <div class="relative w-16 bg-gray-200 rounded-full h-2">
                                                                    <div class="absolute top-0 left-0 h-2 rounded-full bg-primary-600" 
                                                                         style="width: {{ min(100, ($customer->loyalty_points / 1000) * 100) }}%">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="text-sm text-gray-900">
                                                            {{ $customer->last_purchase_date ? $customer->last_purchase_date->format('M d, Y') : 'Never' }}
                                                        </div>
                                                        @if($customer->last_purchase_date)
                                                        <div class="text-sm text-gray-500">
                                                            {{ $customer->last_purchase_date->diffForHumans() }}
                                                        </div>
                                                        @endif
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="flex items-center space-x-2">
                                                            <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full {{ $customer->loyalty_points >= 1000 ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800' }}">
                                                                {{ $customer->loyalty_points >= 1000 ? 'Gold' : 'Regular' }}
                                                            </span>
                                                            @if($customer->loyalty_points >= 1000)
                                                                <svg class="h-4 w-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                        <div class="flex justify-end space-x-2">
                                                            <a href="{{ route('customers.show', $customer) }}"
                                                               class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                                                <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                                                </svg>
                                                                View
                                                            </a>
                                                            <button onclick="showLoyaltyHistory({{ $customer->id }})"
                                                                    class="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                                                <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                                </svg>
                                                                History
                                                            </button>
                                                            @if($customer->loyalty_points >= 100)
                                                            <button onclick="showRedeemModal({{ $customer->id }}, {{ $customer->loyalty_points }})"
                                                                    class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                                                <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                                </svg>
                                                                Redeem
                                                            </button>
                                                            @endif
                                                        </div>
                                                    </td>
                                                </tr>
                                                @empty
                                                <tr>
                                                    <td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                                        No loyalty program members found
                                                    </td>
                                                </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4 px-4 sm:px-6 lg:px-8">
                            {{ $customers->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loyalty Program Rules -->
        <div class="mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div class="program-rule-card bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 flex items-center">
                        <svg class="mr-2 h-6 w-6 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                        </svg>
                        Points Earning
                    </h3>
                </div>
                <div class="px-4 py-5 sm:px-6">
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-6">
                        <div class="sm:col-span-1">
                            <dt class="text-sm font-medium text-gray-500">
                                Earning Rate
                            </dt>
                            <dd class="mt-1 text-sm text-gray-900 flex items-center">
                                <svg class="mr-1.5 h-5 w-5 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                1 point per $1 spent
                            </dd>
                        </div>
                        <div class="sm:col-span-1">
                            <dt class="text-sm font-medium text-gray-500">
                                Points Validity
                            </dt>
                            <dd class="mt-1 text-sm text-gray-900 flex items-center">
                                <svg class="mr-1.5 h-5 w-5 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                                12 months from earning
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>

            <div class="program-rule-card bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 flex items-center">
                        <svg class="mr-2 h-6 w-6 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                        </svg>
                        Membership Benefits
                    </h3>
                </div>
                <div class="px-4 py-5 sm:px-6">
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-6">
                        <div class="sm:col-span-1">
                            <dt class="text-sm font-medium text-gray-500">
                                Gold Member Threshold
                            </dt>
                            <dd class="mt-1 text-sm text-gray-900 flex items-center">
                                <svg class="mr-1.5 h-5 w-5 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"/>
                                </svg>
                                1,000 points
                            </dd>
                        </div>
                        <div class="sm:col-span-1">
                            <dt class="text-sm font-medium text-gray-500">
                                Redemption Rate
                            </dt>
                            <dd class="mt-1 text-sm text-gray-900 flex items-center">
                                <svg class="mr-1.5 h-5 w-5 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                100 points = $1 discount
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>

        <!-- Loyalty History Modal -->
        <div id="loyaltyHistoryModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900" id="modalCustomerName">Loyalty History</h3>
                        <button onclick="closeLoyaltyHistory()" class="text-gray-400 hover:text-gray-600">
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                    <div id="loyaltyHistoryContent" class="max-h-96 overflow-y-auto">
                        <!-- Content will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Redeem Points Modal -->
        <div id="redeemModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Redeem Points</h3>
                        <button onclick="closeRedeemModal()" class="text-gray-400 hover:text-gray-600">
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                    <form id="redeemForm">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Available Points</label>
                            <div class="text-2xl font-bold text-primary-600" id="availablePoints">0</div>
                        </div>
                        <div class="mb-4">
                            <label for="pointsToRedeem" class="block text-sm font-medium text-gray-700 mb-2">Points to Redeem</label>
                            <input type="number" id="pointsToRedeem" name="pointsToRedeem" min="100" step="100"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                                   oninput="calculateDiscount()">
                            <p class="text-xs text-gray-500 mt-1">Minimum 100 points (100 points = $1 discount)</p>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Discount Amount</label>
                            <div class="text-xl font-semibold text-green-600" id="discountAmount">$0.00</div>
                        </div>
                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="closeRedeemModal()"
                                    class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                                Cancel
                            </button>
                            <button type="submit"
                                    class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700">
                                Redeem Points
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
let currentCustomerId = null;

function showLoyaltyHistory(customerId) {
    currentCustomerId = customerId;
    document.getElementById('loyaltyHistoryModal').classList.remove('hidden');

    // Load loyalty history via AJAX
    fetch(`/customers/${customerId}/loyalty-history`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('modalCustomerName').textContent = `${data.customer.name} - Loyalty History`;
            document.getElementById('loyaltyHistoryContent').innerHTML = data.html;
        })
        .catch(error => {
            console.error('Error loading loyalty history:', error);
            document.getElementById('loyaltyHistoryContent').innerHTML =
                '<p class="text-red-600">Error loading loyalty history. Please try again.</p>';
        });
}

function closeLoyaltyHistory() {
    document.getElementById('loyaltyHistoryModal').classList.add('hidden');
}

function showRedeemModal(customerId, availablePoints) {
    currentCustomerId = customerId;
    document.getElementById('availablePoints').textContent = availablePoints.toLocaleString();
    document.getElementById('pointsToRedeem').max = availablePoints;
    document.getElementById('pointsToRedeem').value = '';
    document.getElementById('discountAmount').textContent = '$0.00';
    document.getElementById('redeemModal').classList.remove('hidden');
}

function closeRedeemModal() {
    document.getElementById('redeemModal').classList.add('hidden');
}

function calculateDiscount() {
    const points = parseInt(document.getElementById('pointsToRedeem').value) || 0;
    const discount = (points / 100).toFixed(2);
    document.getElementById('discountAmount').textContent = `$${discount}`;
}

document.getElementById('redeemForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const pointsToRedeem = parseInt(document.getElementById('pointsToRedeem').value);

    if (!pointsToRedeem || pointsToRedeem < 100) {
        alert('Please enter at least 100 points to redeem.');
        return;
    }

    // Here you would typically send an AJAX request to redeem points
    // For now, we'll just show a success message
    alert(`Successfully redeemed ${pointsToRedeem} points for $${(pointsToRedeem / 100).toFixed(2)} discount!`);
    closeRedeemModal();

    // Refresh the page to show updated points
    window.location.reload();
});

// Close modals when clicking outside
window.onclick = function(event) {
    const historyModal = document.getElementById('loyaltyHistoryModal');
    const redeemModal = document.getElementById('redeemModal');

    if (event.target === historyModal) {
        closeLoyaltyHistory();
    }
    if (event.target === redeemModal) {
        closeRedeemModal();
    }
}
</script>
@endpush

@endsection