@if($transactions->count() > 0)
    <div class="space-y-4">
        @foreach($transactions as $transaction)
            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div class="flex items-center space-x-4">
                    <div class="flex-shrink-0">
                        @if($transaction->type === 'earned')
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                </svg>
                            </div>
                        @elseif($transaction->type === 'redeemed')
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/>
                                </svg>
                            </div>
                        @elseif($transaction->type === 'expired')
                            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                        @else
                            <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                </svg>
                            </div>
                        @endif
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <p class="text-sm font-medium text-gray-900">
                                {{ $transaction->description }}
                            </p>
                            @if($transaction->sale)
                                <a href="{{ route('sales.show', $transaction->sale) }}" 
                                   class="text-xs text-primary-600 hover:text-primary-800">
                                    View Sale
                                </a>
                            @endif
                        </div>
                        <div class="flex items-center space-x-4 mt-1">
                            <p class="text-xs text-gray-500">
                                {{ $transaction->created_at->format('M d, Y \a\t g:i A') }}
                            </p>
                            @if($transaction->amount_spent)
                                <p class="text-xs text-gray-500">
                                    Amount: ${{ number_format($transaction->amount_spent, 2) }}
                                </p>
                            @endif
                            @if($transaction->discount_applied)
                                <p class="text-xs text-green-600">
                                    Saved: ${{ number_format($transaction->discount_applied, 2) }}
                                </p>
                            @endif
                            @if($transaction->expires_at)
                                <p class="text-xs {{ $transaction->isExpired() ? 'text-red-500' : 'text-gray-500' }}">
                                    {{ $transaction->isExpired() ? 'Expired' : 'Expires' }}: {{ $transaction->expires_at->format('M d, Y') }}
                                </p>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="flex-shrink-0">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        @if($transaction->type === 'earned') bg-green-100 text-green-800
                        @elseif($transaction->type === 'redeemed') bg-blue-100 text-blue-800
                        @elseif($transaction->type === 'expired') bg-red-100 text-red-800
                        @else bg-gray-100 text-gray-800 @endif">
                        {{ $transaction->formatted_points }} pts
                    </span>
                </div>
            </div>
        @endforeach
    </div>

    @if($transactions->hasPages())
        <div class="mt-4">
            {{ $transactions->links() }}
        </div>
    @endif
@else
    <div class="text-center py-8">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No loyalty transactions</h3>
        <p class="mt-1 text-sm text-gray-500">This customer hasn't earned or redeemed any loyalty points yet.</p>
    </div>
@endif
