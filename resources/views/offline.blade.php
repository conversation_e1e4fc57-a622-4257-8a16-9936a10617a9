<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - PharmaDesk</title>
    <meta name="description" content="You are currently offline. PharmaDesk works offline with previously synced data.">
    <meta name="offline-page" content="true">
    <meta name="testid" content="offline-page">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#4F46E5">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="PharmaDesk">
    <meta name="application-name" content="PharmaDesk">
    
    <!-- PWA Icons -->
    <link rel="manifest" href="/manifest.json">
    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/icons/icon-144x144.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/icons/icon-128x128.png">
    <link rel="apple-touch-icon" sizes="114x114" href="/icons/icon-96x96.png">
    <link rel="apple-touch-icon" sizes="72x72" href="/icons/icon-72x72.png">
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="192x192" href="/icons/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/icons/icon-72x72.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/icons/icon-72x72.png">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
        }
        .offline-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f3f4f6;
        }
        .offline-content {
            text-align: center;
            padding: 2rem;
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            max-width: 90%;
            width: 400px;
        }
        .offline-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 1rem;
            color: #4F46E5;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% {
                opacity: 1;
            }
            50% {
                opacity: 0.6;
            }
            100% {
                opacity: 1;
            }
        }
        .title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #111827;
            margin-bottom: 1rem;
        }
        .message {
            color: #4B5563;
            margin-bottom: 1.5rem;
        }
        .last-sync {
            font-size: 0.875rem;
            color: #6B7280;
            margin-bottom: 1rem;
        }
        .button {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 0.375rem;
            background-color: #4F46E5;
            color: white;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .button:hover {
            background-color: #4338CA;
        }
        .button:focus {
            outline: none;
            box-shadow: 0 0 0 2px #E0E7FF;
        }
        .cached-pages {
            margin-top: 1.5rem;
            border-top: 1px solid #E5E7EB;
            padding-top: 1rem;
        }
        .cached-pages h2 {
            font-size: 1rem;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        .cached-pages-list {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }
        .cached-page-link {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            background-color: #EEF2FF;
            color: #4F46E5;
            text-decoration: none;
            font-size: 0.875rem;
            transition: background-color 0.2s;
        }
        .cached-page-link:hover {
            background-color: #E0E7FF;
        }
    </style>
</head>
<body class="offline">
    <div class="offline-container" id="offline-page" data-testid="offline-page">
        <div class="offline-content">
            <svg class="offline-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" id="offline-icon" data-testid="offline-icon">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636a9 9 0 010 12.728m0 0l-2.829-2.829m2.829 2.829L21 21M15.536 8.464a5 5 0 010 7.072m0 0l-2.829-2.829m-4.243 2.829a4.978 4.978 0 01-1.414-2.83m-1.414 5.658a9 9 0 01-2.167-9.238m7.824 2.167a1 1 0 111.414 1.414m-1.414-1.414L3 3m8.293 8.293l1.414 1.414" />
            </svg>
            <h1 class="title" data-testid="offline-title">You're Offline</h1>
            <p class="message" data-testid="offline-message">Don't worry! PharmaDesk works offline. You can continue working with the data that was previously synced.</p>
            <div>
                <p class="last-sync" data-testid="last-sync">Last synced: <span id="lastSync">Loading...</span></p>
                <button onclick="checkOnlineStatus()" class="button" id="try-again-button" data-testid="try-again-button">
                    Try Again
                </button>
            </div>
            <div class="cached-pages" data-testid="cached-pages">
                <h2>Available Pages</h2>
                <p>These pages should be available offline:</p>
                <div class="cached-pages-list" id="cached-pages-list" data-testid="cached-pages-list">
                    <a href="/" class="cached-page-link" data-testid="cached-page-link">Home</a>
                    <a href="/dashboard" class="cached-page-link" data-testid="cached-page-link">Dashboard</a>
                    <a href="/inventory" class="cached-page-link" data-testid="cached-page-link">Inventory</a>
                    <a href="/sales" class="cached-page-link" data-testid="cached-page-link">Sales</a>
                    <a href="/customers" class="cached-page-link" data-testid="cached-page-link">Customers</a>
                </div>
            </div>
        </div>
    </div>
    <script>
        // Update last sync time
        document.addEventListener('DOMContentLoaded', () => {
            const lastSync = localStorage.getItem('lastSync') || 'Never';
            document.getElementById('lastSync').textContent = lastSync;
            
            // Check for online status periodically
            setInterval(checkOnlineStatus, 5000);
        });
        
        // Function to check online status
        function checkOnlineStatus() {
            if (navigator.onLine) {
                document.getElementById('try-again-button').textContent = 'Back Online - Return Home';
                document.getElementById('try-again-button').onclick = () => {
                    window.location.href = '/';
                };
            } else {
                document.getElementById('try-again-button').textContent = 'Try Again';
                document.getElementById('try-again-button').onclick = checkOnlineStatus;
            }
        }
        
        // Initial check
        checkOnlineStatus();
    </script>
</body>
</html> 