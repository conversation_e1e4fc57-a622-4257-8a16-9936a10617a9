@extends('layouts.admin')

@section('content')
<div class="py-6">
    <div class="w-full px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center">
            <h1 class="text-2xl font-semibold text-gray-900">Inventory Report</h1>
            <div class="hidden print:block print-title">
                Inventory Report - {{ now()->format('M d, Y') }}
                @if($statusFilter !== 'all')
                    <br><span class="text-sm">Filtered by: {{ ucfirst(str_replace('_', ' ', $statusFilter)) }}</span>
                @endif
            </div>
        </div>

        <!-- Filters -->
        <div class="mt-4 bg-white rounded-lg shadow p-4 no-print">
            <form action="{{ route('reports.inventory') }}" method="GET" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Status Filter -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                        <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            <option value="all" {{ $statusFilter === 'all' ? 'selected' : '' }}>All Items</option>
                            <option value="in_stock" {{ $statusFilter === 'in_stock' ? 'selected' : '' }}>In Stock</option>
                            <option value="low_stock" {{ $statusFilter === 'low_stock' ? 'selected' : '' }}>Low Stock</option>
                            <option value="out_of_stock" {{ $statusFilter === 'out_of_stock' ? 'selected' : '' }}>Out of Stock</option>
                            <option value="expiring_soon" {{ $statusFilter === 'expiring_soon' ? 'selected' : '' }}>Expiring Soon</option>
                            <option value="expired" {{ $statusFilter === 'expired' ? 'selected' : '' }}>Expired</option>
                        </select>
                    </div>

                    <!-- Category Filter -->
                    <div>
                        <label for="category_id" class="block text-sm font-medium text-gray-700">Category</label>
                        <select name="category_id" id="category_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            <option value="">All Categories</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" {{ $categoryFilter == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Manufacturer Filter -->
                    <div>
                        <label for="manufacturer_id" class="block text-sm font-medium text-gray-700">Manufacturer</label>
                        <select name="manufacturer_id" id="manufacturer_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            <option value="">All Manufacturers</option>
                            @foreach($manufacturers as $manufacturer)
                                <option value="{{ $manufacturer->id }}" {{ $manufacturerFilter == $manufacturer->id ? 'selected' : '' }}>
                                    {{ $manufacturer->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Search -->
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                        <input type="text" name="search" id="search" value="{{ $searchQuery }}"
                            placeholder="Search medicines..."
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                    </div>
                </div>

                <div class="flex justify-between items-center">
                    <div class="flex space-x-3">
                        <button type="submit" class="bg-indigo-600 px-4 py-2 text-sm text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            Apply Filters
                        </button>
                        <a href="{{ route('reports.inventory') }}" class="px-4 py-2 text-sm text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">
                            Clear Filters
                        </a>
                    </div>
                    <div class="flex space-x-2">
                        <button type="button" onclick="window.print()" class="px-4 py-2 text-sm text-gray-600 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                            </svg>
                            Print
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Current Filter Summary -->
        @if($statusFilter !== 'all' || $categoryFilter || $manufacturerFilter || $searchQuery)
            <div class="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4 no-print">
                <div class="flex items-center">
                    <svg class="h-5 w-5 text-blue-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                    </svg>
                    <h3 class="text-sm font-medium text-blue-800">Active Filters:</h3>
                </div>
                <div class="mt-2 flex flex-wrap gap-2">
                    @if($statusFilter !== 'all')
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Status: {{ ucfirst(str_replace('_', ' ', $statusFilter)) }}
                        </span>
                    @endif
                    @if($categoryFilter)
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Category: {{ $categories->find($categoryFilter)->name ?? 'Unknown' }}
                        </span>
                    @endif
                    @if($manufacturerFilter)
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Manufacturer: {{ $manufacturers->find($manufacturerFilter)->name ?? 'Unknown' }}
                        </span>
                    @endif
                    @if($searchQuery)
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Search: "{{ $searchQuery }}"
                        </span>
                    @endif
                </div>
            </div>
        @endif

        <!-- Summary Cards -->
        <div class="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Stock Value</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ number_format($statistics['total_inventory_value'], 2) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Products</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $statistics['total_products'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z">
                                </path>
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Low Stock Items</dt>
                                <dd class="text-lg font-medium text-red-600">{{ $statistics['low_stock_count'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Expiring Soon</dt>
                                <dd class="text-lg font-medium text-orange-600">{{ $statistics['expiring_soon_count'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-red-400" viewBox="0 0 300.99 372.17" fill="currentColor">
                                <g>
                                    <path d="M229.87,148.59l36.34,20.97c9.74,17.03,22.58,33.33,32.09,50.35,2.09,3.75,4.62,7.3.39,10.77l-31.02,18.42-.76,55.49-2.25,2.24-110.35,64.18c-1.99,1.25-4.44,1.59-6.57.51-33.66-20.58-68.84-38.76-102.51-59.28-3.68-2.24-7.67-4.23-10.76-7.21l-.85-55.4-31.25-18.94c-1.89-1.35-2.62-3.64-2.29-5.9l34.76-55.22,36.34-20.97C34.76,93.96,64.15,17.71,128.07,2.73c84.96-19.92,148.77,73.32,101.8,145.87ZM143.64,12.33c-59.13,4.85-94.7,69.7-66.6,122.53,31.02,58.3,115.25,58.57,146.69.47,31.5-58.22-14.62-128.37-80.09-123ZM249.33,174.1l-26.58-16.36c-37.17,44-107.33,43.92-144.45,0l-26.58,16.35,99.13,56.28,98.48-56.28ZM41.6,181.7l-26.18,40.43-.05,2.24,99.99,57.41,25.86-42.43-99.62-57.65ZM259.46,181.7l-99.62,57.65,25.86,42.43,99.99-57.41-.05-2.24-26.18-40.43ZM45.73,255.86v42.38l105.11,60.36,104.48-60.36v-42.38l-71.41,40.47-4.96-2.58-27.63-44.17-1.54-.47-29.74,46.33-3.54.89-70.78-40.48Z"/>
                                    <path d="M108.39,49.76c1.3-.39,2.85-.32,4.16.03,13.56,10.96,24.85,24.67,37.71,36.51l38.25-36.51c6.34-1.71,10.14,4.85,6.18,9.94l-35.13,35.35,36.47,38.31c1.49,6.16-3.69,10.63-9.21,6.97l-36.57-35.95-36.03,35.95c-5.51,3.66-10.7-.8-9.21-6.97l36.47-37.76-36.23-37.03c-1.37-2.95-.1-7.86,3.13-8.83Z"/>
                                </g>
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Out of Stock</dt>
                                <dd class="text-lg font-medium text-red-600">{{ $statistics['out_of_stock_count'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventory List -->
        <div class="mt-4">
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                @if($medicines->count() > 0)
                    <div class="px-6 py-4 border-b border-gray-200">
                        <p class="text-sm text-gray-600">
                            Showing {{ $medicines->count() }} of {{ $statistics['total_products'] }} products
                            @if($statusFilter !== 'all')
                                (filtered by: {{ ucfirst(str_replace('_', ' ', $statusFilter)) }})
                            @endif
                        </p>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Product Name</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Category</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Manufacturer</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Stock</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Unit Price</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Total Value</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Nearest Expiry</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($medicines as $item)
                                @if($item['medicine'])
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ $item['medicine']->name }}{{ $item['medicine']->dosage ? ' ' . $item['medicine']->dosage : '' }}
                                        </div>
                                        @if($item['medicine']->generic_name)
                                            <div class="text-sm text-gray-500">{{ $item['medicine']->generic_name }}</div>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $item['medicine']->category ? $item['medicine']->category->name : 'N/A' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $item['medicine']->manufacturer ? $item['medicine']->manufacturer->name : 'N/A' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">{{ number_format($item['total_stock']) }}</div>
                                        @if($item['medicine']->minimum_stock)
                                            <div class="text-xs text-gray-500">Min: {{ $item['medicine']->minimum_stock }}</div>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ number_format($item['medicine']->selling_price ?? $item['medicine']->unit_price ?? 0, 2) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ number_format($item['total_value'], 2) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        @if($item['nearest_expiry_date'])
                                            <div class="text-sm {{ \Carbon\Carbon::parse($item['nearest_expiry_date'])->diffInDays(now()) <= 90 ? 'text-orange-600 font-medium' : 'text-gray-900' }}">
                                                {{ \Carbon\Carbon::parse($item['nearest_expiry_date'])->format('M d, Y') }}
                                            </div>
                                            <div class="text-xs text-gray-500">
                                                {{ \Carbon\Carbon::parse($item['nearest_expiry_date'])->diffForHumans() }}
                                            </div>
                                        @else
                                            <span class="text-gray-400">-</span>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @php
                                            $statusConfig = [
                                                'in_stock' => ['bg-green-100 text-green-800', 'In Stock'],
                                                'low_stock' => ['bg-yellow-100 text-yellow-800', 'Low Stock'],
                                                'out_of_stock' => ['bg-gray-100 text-gray-800', 'Out of Stock'],
                                                'expiring_soon' => ['bg-orange-100 text-orange-800', 'Expiring Soon'],
                                                'expired' => ['bg-red-100 text-red-800', 'Expired']
                                            ];
                                            $config = $statusConfig[$item['stock_status']] ?? ['bg-gray-100 text-gray-800', 'Unknown'];
                                        @endphp
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $config[0] }}">
                                            {{ $config[1] }}
                                        </span>
                                        @if($item['expiring_batches_count'] > 0)
                                            <div class="text-xs text-orange-600 mt-1">
                                                {{ $item['expiring_batches_count'] }} batch(es) expiring
                                            </div>
                                        @endif
                                        @if($item['expired_batches_count'] > 0)
                                            <div class="text-xs text-red-600 mt-1">
                                                {{ $item['expired_batches_count'] }} batch(es) expired
                                            </div>
                                        @endif
                                    </td>
                                </tr>
                                @endif
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="px-6 py-12 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4a1 1 0 00-1-1H9a1 1 0 00-1 1v1m4 0h1m-6 0h1m5 8a2 2 0 100-4 2 2 0 000 4zm-8 2v2h8v-2H8z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No products found</h3>
                        <p class="mt-1 text-sm text-gray-500">
                            @if($statusFilter !== 'all' || $categoryFilter || $manufacturerFilter || $searchQuery)
                                No products match your current filters. Try adjusting your search criteria.
                            @else
                                No products available in inventory.
                            @endif
                        </p>
                        @if($statusFilter !== 'all' || $categoryFilter || $manufacturerFilter || $searchQuery)
                            <div class="mt-6">
                                <a href="{{ route('reports.inventory') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    Clear All Filters
                                </a>
                            </div>
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form when filters change (except search)
    const filterSelects = document.querySelectorAll('#status, #category_id, #manufacturer_id');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });

    // Debounced search
    const searchInput = document.getElementById('search');
    let searchTimeout;

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.form.submit();
            }, 500); // Wait 500ms after user stops typing
        });
    }

    // Print styles
    const printStyles = `
        <style media="print">
            @page { margin: 0.5in; }
            .no-print { display: none !important; }
            .print-title { font-size: 18px; font-weight: bold; margin-bottom: 20px; }
            table { font-size: 12px; }
            .bg-gray-50 { background-color: #f9fafb !important; -webkit-print-color-adjust: exact; }
            .text-red-600 { color: #dc2626 !important; -webkit-print-color-adjust: exact; }
            .text-orange-600 { color: #ea580c !important; -webkit-print-color-adjust: exact; }
            .text-green-600 { color: #16a34a !important; -webkit-print-color-adjust: exact; }
        </style>
    `;

    // Add print styles to head
    document.head.insertAdjacentHTML('beforeend', printStyles);
});
</script>
@endpush
@endsection
