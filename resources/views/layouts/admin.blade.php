<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full bg-gray-100">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#4F46E5">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="PharmaDesk">
    <meta name="application-name" content="PharmaDesk">
    <meta name="description" content="Complete Pharmacy Management System">

    <!-- PWA Icons -->
    <link rel="manifest" href="/manifest.json">
    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/icons/icon-144x144.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/icons/icon-128x128.png">
    <link rel="apple-touch-icon" sizes="114x114" href="/icons/icon-96x96.png">
    <link rel="apple-touch-icon" sizes="72x72" href="/icons/icon-72x72.png">
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="192x192" href="/icons/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/icons/icon-72x72.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/icons/icon-72x72.png">
    <!-- Microsoft Tile Icons -->
    <meta name="msapplication-TileImage" content="/icons/icon-144x144.png">
    <meta name="msapplication-TileColor" content="#4F46E5">

    <title>{{ config('app.name', 'PharmaDesk') }}</title>

    <!-- Alpine.js Initialization -->
    <script>
        // Create fallback sync service functions
        window.getSyncStatus = window.getSyncStatus || (() => Promise.resolve({ pendingItemsCount: 0 }));
        window.forceSync = window.forceSync || (() => Promise.resolve());
        
        document.addEventListener('alpine:init', () => {
            // Register offline data handlers with fallbacks
            Alpine.data('offlineManager', () => ({
                isSyncing: false,
                pendingItems: 0,
                lastSync: localStorage.getItem('lastSync') || 'Never',
                
                init() {
                    // Initialize
                    this.updateSyncInfo();
                    
                    // Listen for sync status changes
                    window.addEventListener('sync-status-changed', (event) => {
                        this.isSyncing = event.detail.syncing;
                        this.updateSyncInfo();
                        
                        // Show notification if there was an error
                        if (event.detail.error && typeof Livewire !== 'undefined') {
                            Livewire.dispatch('notify', { 
                                type: 'error',
                                message: `Sync failed: ${event.detail.errorMessage || 'Unknown error'}`
                            });
                        }
                    });
                    
                    // Listen for service worker messages
                    if (navigator.serviceWorker) {
                        navigator.serviceWorker.addEventListener('message', (event) => {
                            if (event.data.type === 'sync-completed') {
                                this.isSyncing = false;
                                this.updateSyncInfo();
                                
                                if (typeof Livewire !== 'undefined') {
                                    if (event.data.data.success) {
                                        Livewire.dispatch('notify', { 
                                            type: 'success',
                                            message: `Sync completed: ${event.data.data.successCount} items synchronized`
                                        });
                                    } else {
                                        Livewire.dispatch('notify', { 
                                            type: 'warning',
                                            message: `Sync completed with issues: ${event.data.data.failureCount} failures`
                                        });
                                    }
                                }
                            } else if (event.data.type === 'sync-started') {
                                this.isSyncing = true;
                            } else if (event.data.type === 'sync-error') {
                                this.isSyncing = false;
                                if (typeof Livewire !== 'undefined') {
                                    Livewire.dispatch('notify', { 
                                        type: 'error',
                                        message: `Sync error: ${event.data.data.error || 'Unknown error'}`
                                    });
                                }
                            }
                        });
                    }
                },
                
                async updateSyncInfo() {
                    try {
                        if (typeof window.getSyncStatus === 'function') {
                            const status = await window.getSyncStatus();
                            this.pendingItems = status.pendingItemsCount;
                            this.lastSync = localStorage.getItem('lastSync') || 'Never';
                        }
                    } catch (error) {
                        console.error('Error getting sync status:', error);
                    }
                },
                
                async triggerSync() {
                    if (this.isSyncing) return;
                    
                    if (!navigator.onLine) {
                        if (typeof Livewire !== 'undefined') {
                            Livewire.dispatch('notify', { 
                                type: 'warning',
                                message: 'Cannot sync while offline'
                            });
                        }
                        return;
                    }
                    
                    try {
                        this.isSyncing = true;
                        if (typeof window.forceSync === 'function') {
                            await window.forceSync();
                            if (typeof Livewire !== 'undefined') {
                                Livewire.dispatch('notify', { 
                                    type: 'success',
                                    message: 'Sync completed successfully'
                                });
                            }
                        }
                    } catch (error) {
                        if (typeof Livewire !== 'undefined') {
                            Livewire.dispatch('notify', { 
                                type: 'error',
                                message: `Sync failed: ${error.message}`
                            });
                        }
                    } finally {
                        this.isSyncing = false;
                        this.updateSyncInfo();
                    }
                }
            }));
        });
    </script>

    <!-- Legacy Offline/Sync Indicator Styles - DEPRECATED -->
    <!-- These styles are now handled by the unified status component -->
    <style>
        /* Legacy styles kept for compatibility but hidden */
        .offline-indicator {
            display: none !important; /* Force hide - now handled by unified status */
        }
        .sync-status {
            display: none !important; /* Force hide - now handled by unified status */
        }

        /* Layout Stabilization - Prevent stretching during navigation */
        .admin-layout {
            overflow-x: hidden;
            height: 100vh;
            display: flex;
        }

        .admin-layout-container {
            display: flex;
            width: 100%;
            height: 100vh;
            overflow: hidden; /* Keep hidden for horizontal, but allow vertical scrolling in children */
        }

        /* Sidebar Container - Independent positioning */
        .admin-sidebar {
            flex-shrink: 0;
            height: 100vh;
            position: relative;
            z-index: 50;
            transition: width 0.3s ease-in-out;
            will-change: width;
        }

        /* Prevent cross-fade effects on sidebar during navigation */
        .sidebar-no-fade {
            opacity: 1 !important;
            visibility: visible !important;
        }

        .sidebar-no-fade,
        .sidebar-no-fade * {
            transition-property: width, transform, margin, padding !important;
            transition-duration: 0.3s !important;
            transition-timing-function: ease-in-out !important;
        }

        /* Preserve legitimate sidebar animations */
        .sidebar-no-fade .transform {
            transition-property: transform !important;
        }

        .sidebar-no-fade svg.transform {
            transition-property: transform !important;
            transition-duration: 0.2s !important;
        }

        /* Main Content Container - Independent from sidebar */
        .admin-main-content {
            flex: 1;
            min-width: 0;
            height: 100vh;
            overflow-y: auto;
            overflow-x: hidden;
            display: flex;
            flex-direction: column;
            background-color: #f9fafb;
            position: relative;
        }

        /* Remove padding-based layout dependencies */
        .admin-main-content.no-padding-transition {
            padding-left: 0 !important;
        }

        /* Optimize transitions for better performance */
        .admin-layout .transition-all {
            transition-property: width, transform;
            will-change: width, transform;
        }

        /* Tablet Layout (641px - 1024px) */
        @media (max-width: 1024px) and (min-width: 641px) {
            /* Tablet sidebar behavior - collapsible but visible */
            .admin-sidebar {
                transition: width 0.3s ease-in-out;
            }

            /* Ensure proper content spacing on tablets */
            .admin-main-content {
                transition: margin-left 0.3s ease-in-out;
            }
        }

        /* Mobile Layout (≤ 768px) - Enhanced for better mobile experience */
        @media (max-width: 768px) {

            /* Mobile layout adjustments */
            .admin-layout-container {
                flex-direction: row; /* Keep row for overlay functionality */
                position: relative;
            }

            /* Mobile sidebar - simple and clean */
            .admin-sidebar {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                height: 100vh !important;
                width: 280px !important;
                z-index: 1000 !important;
                background-color: white !important;
                border-right: 1px solid #e5e7eb !important;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
                transition: transform 0.3s ease-in-out !important;
                transform: translateX(-100%) !important;
                overflow-y: auto !important;
                -webkit-overflow-scrolling: touch !important;
            }

            /* Show sidebar when open */
            .admin-sidebar.open {
                transform: translateX(0) !important;
            }

            /* Ensure main content is pushed down on mobile */
            .admin-main-content {
                width: 100% !important;
                margin-left: 0 !important;
            }

            .admin-main-content {
                width: 100%;
                height: 100vh;
                padding-top: 0;
                overflow-y: auto;
                overflow-x: hidden;
                -webkit-overflow-scrolling: touch;
            }

            /* Mobile backdrop overlay */
            .mobile-backdrop {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                height: 100% !important;
                background-color: rgba(0, 0, 0, 0.5) !important;
                z-index: 999 !important;
                opacity: 0 !important;
                visibility: hidden !important;
                transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out !important;
            }

            .mobile-backdrop.show {
                opacity: 1 !important;
                visibility: visible !important;
            }
        }

        /* Prevent horizontal overflow during transitions */
        .admin-layout nav > div {
            min-width: 0;
        }

        /* Smooth content transitions and scrolling */
        .admin-content-wrapper {
            transition: none;
            width: 100%;
            max-width: 100%;
            min-height: 0;
            flex: 1;
            overflow-y: auto; /* Enable vertical scrolling for content wrapper */
            overflow-x: hidden; /* Prevent horizontal scrolling */
            padding-bottom: 2rem; /* Add bottom padding for better scrolling experience */
            /* Ensure content can expand beyond viewport */
            min-height: calc(100vh - 8rem); /* Account for navigation and padding */
        }

        /* Prevent flash of unstyled content */
        [x-cloak] {
            display: none !important;
        }

        /* Prevent cross-fade effects during navigation transitions */
        .admin-sidebar,
        .admin-sidebar *,
        .admin-sidebar nav,
        .admin-sidebar nav *,
        .admin-sidebar a,
        .admin-sidebar button,
        .admin-sidebar div {
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* Override any global transition effects that might cause cross-fade */
        .admin-sidebar * {
            transition-property: width, transform, margin, padding, color, background-color !important;
        }

        /* Ensure sidebar content remains visible during all transitions */
        .admin-sidebar .space-y-1,
        .admin-sidebar .space-y-1 * {
            opacity: 1 !important;
        }

        /* Prevent any fade transitions on sidebar navigation elements */
        .admin-sidebar nav a,
        .admin-sidebar nav button,
        .admin-sidebar nav div,
        .admin-sidebar nav span,
        .admin-sidebar nav svg {
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* Override Alpine.js x-show transitions that might cause fade effects */
        .admin-sidebar [x-show] {
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* Ensure menu expansion animations don't cause cross-fade */
        .admin-sidebar .transform {
            transition: transform 0.2s ease-in-out !important;
            opacity: 1 !important;
        }

        /* Mobile Navigation Layout (≤ 768px) */
        @media (max-width: 768px) {
            .admin-nav-container {
                padding-left: 0.75rem;
                padding-right: 0.75rem;
            }

            /* Mobile navbar height adjustment */
            .admin-nav-container > div {
                height: 3.5rem; /* 56px - slightly shorter for mobile */
            }


            /* Mobile hamburger button styling */
            .admin-nav-flex button {
                margin-right: 0.5rem;
                padding: 0.5rem;
                min-width: 40px;
                min-height: 40px;
            }

            /* Sync status mobile styling */
            .admin-nav-flex .flex-shrink-0 {
                margin-left: 0.5rem;
            }

            /* Ensure sync status button is properly sized on mobile */
            .admin-nav-flex .flex-shrink-0 button {
                min-height: 40px !important;
                padding: 0.5rem !important;
            }

            /* Mobile New Sale button - make it more compact */
            .admin-nav-container a[href*="sales.create"] {
                padding: 0.5rem 0.75rem !important;
                font-size: 0.875rem !important;
                margin-right: 0.5rem !important;
                min-height: 40px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            .admin-nav-container a[href*="sales.create"] svg {
                width: 1rem !important;
                height: 1rem !important;
                margin-right: 0.25rem !important;
                margin-left: 0 !important;
                flex-shrink: 0 !important;
            }

            /* Mobile user dropdown styling */
            .admin-nav-container .relative button span {
                padding: 0.5rem 0.75rem !important;
                font-size: 0.875rem !important;
            }

            .admin-nav-container .relative button svg {
                width: 1rem !important;
                height: 1rem !important;
            }

            /* Mobile dropdown positioning */
            .admin-nav-container .relative > div[x-show] {
                right: 0 !important;
                left: auto !important;
                width: 12rem !important;
            }
        }

        /* Small Mobile Layout (≤ 640px) - Extra optimizations */
        @media (max-width: 640px) {
            .admin-nav-container {
                padding-left: 0.5rem;
                padding-right: 0.5rem;
            }

            /* Even more compact on very small screens */
            .admin-nav-container > div {
                height: 3rem; /* 48px */
            }

            /* Hide "New Sale" text on very small screens, keep icon */
            .admin-nav-container a[href*="sales.create"] {
                padding: 0.5rem !important;
                margin-right: 0.25rem !important;
                min-width: 40px !important;
                min-height: 40px !important;
            }

            .admin-nav-container a[href*="sales.create"] svg {
                margin-right: 0 !important;
                width: 1.125rem !important;
                height: 1.125rem !important;
            }

            .admin-nav-container a[href*="sales.create"] span:not(.sr-only) {
                display: none !important;
            }

            /* Make user name shorter on small screens */
            .admin-nav-container .relative button span {
                max-width: 6rem;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .admin-content-wrapper {
                padding: 0.75rem;
                min-height: calc(100vh - 3rem); /* Adjust for shorter navbar */
                overflow-y: auto;
                overflow-x: hidden;
                -webkit-overflow-scrolling: touch;
            }

            /* Optimize sidebar for small screens */
            .admin-sidebar {
                width: 260px !important; /* Slightly smaller for small screens */
            }

            /* Touch-friendly button sizes */
            .admin-sidebar nav a,
            .admin-sidebar nav button {
                min-height: 44px; /* Minimum touch target size */
                padding: 0.75rem 0.5rem;
            }

            /* Improve text readability on small screens */
            .admin-sidebar nav a span,
            .admin-sidebar nav button span {
                font-size: 0.875rem;
                line-height: 1.25rem;
            }

            /* Mobile profile section styling */
            .sidebar-profile-section {
                border-top: 1px solid #e5e7eb;
                background-color: #f9fafb;
            }

            .sidebar-profile-section button,
            .sidebar-profile-section a {
                min-height: 44px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }



        /* Large Desktop Layout (≥ 1025px) - Enhanced for desktop experience */
        @media (min-width: 1025px) {
            .admin-sidebar {
                position: relative;
                height: 100vh;
                transition: width 0.3s ease-in-out;
            }

            .admin-main-content {
                transition: none; /* No transition needed on desktop */
            }

            /* Enhanced desktop navigation */
            .admin-sidebar nav a,
            .admin-sidebar nav button {
                padding: 0.5rem 0.75rem;
                margin-bottom: 0.125rem;
            }
        }

        /* Ensure proper scrolling behavior */
        .admin-main-content {
            scroll-behavior: smooth;
            /* Ensure scrolling works on all browsers */
            -webkit-overflow-scrolling: touch;
            scrollbar-width: thin;
            scrollbar-color: #cbd5e0 #f7fafc;
        }

        /* Custom scrollbar styling for webkit browsers */
        .admin-main-content::-webkit-scrollbar {
            width: 8px;
        }

        .admin-main-content::-webkit-scrollbar-track {
            background: #f7fafc;
            border-radius: 4px;
        }

        .admin-main-content::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 4px;
        }

        .admin-main-content::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        /* Ensure content can scroll properly */
        .admin-main-content main {
            flex: 1;
            min-height: 0;
            overflow-y: auto; /* Enable scrolling for main content */
            overflow-x: hidden; /* Prevent horizontal scrolling */
            /* Ensure proper height calculation for scrolling */
            height: calc(100vh - 4rem); /* Account for navigation bar */
        }

        /* Handle long content pages */
        .admin-main-content .w-full {
            min-height: fit-content;
        }

        /* Prevent content from being cut off */
        .admin-main-content > * {
            flex-shrink: 0;
        }

        .admin-main-content main {
            flex-shrink: 1;
            min-height: 0;
            /* Ensure scrollable content area */
            position: relative;
        }

        /* Optimize for better performance during transitions */
        .admin-sidebar,
        .admin-main-content {
            backface-visibility: hidden;
            transform: translateZ(0);
        }

        /* Layout initialization state - Remove opacity transitions to prevent cross-fade */
        body:not(.layout-initialized) .admin-layout-container {
            visibility: hidden;
        }

        body.layout-initialized .admin-layout-container {
            visibility: visible;
        }

        /* Additional scrolling improvements */
        .admin-main-content {
            /* Ensure proper scrolling container */
            position: relative;
            /* Fix for potential scrolling issues on some browsers */
            contain: layout style;
        }

        /* Ensure content wrapper allows proper expansion */
        .admin-content-wrapper > * {
            max-width: 100%;
            word-wrap: break-word;
        }

        /* Fix for tables and wide content */
        .admin-content-wrapper table {
            table-layout: auto;
            width: 100%;
            max-width: 100%;
        }

        /* Ensure forms and inputs don't break layout */
        .admin-content-wrapper input,
        .admin-content-wrapper select,
        .admin-content-wrapper textarea {
            max-width: 100%;
            box-sizing: border-box;
        }

        /* Improve scrolling performance */
        .admin-main-content,
        .admin-content-wrapper {
            /* Enable hardware acceleration for smooth scrolling */
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
            /* Optimize scrolling performance */
            will-change: scroll-position;
        }

        /* Touch interface optimizations */
        @media (hover: none) and (pointer: coarse) {
            /* Touch device specific styles */
            .admin-sidebar nav a,
            .admin-sidebar nav button {
                min-height: 48px; /* Larger touch targets for touch devices */
                padding: 0.875rem 0.75rem;
            }

            /* Improve button hover states for touch */
            .admin-sidebar nav a:hover,
            .admin-sidebar nav button:hover {
                background-color: rgba(59, 130, 246, 0.1);
                transform: none; /* Remove hover transforms on touch devices */
            }
        }

        /* Responsive navigation improvements */
        .admin-nav-container {
            transition: padding 0.3s ease-in-out;
        }

        /* Mobile navbar enhancements */
        @media (max-width: 768px) {
            /* Ensure navbar elements don't wrap */
            .admin-nav-container > div {
                flex-wrap: nowrap;
            }

            /* Improve touch targets */
            .admin-nav-container button,
            .admin-nav-container a {
                min-height: 44px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            /* Better spacing for mobile */
            .admin-nav-flex {
                flex: 1;
                min-width: 0; /* Allow flex items to shrink */
            }

            /* Ensure right side doesn't overflow */
            .admin-nav-container .flex.items-center:last-child {
                flex-shrink: 0;
            }
        }

        /* Very small screens - extra compact */
        @media (max-width: 480px) {
            .admin-nav-container {
                padding-left: 0.25rem;
                padding-right: 0.25rem;
            }

            /* Make elements even more compact */
            .admin-nav-container button,
            .admin-nav-container a {
                padding: 0.375rem;
            }

            /* Hide user name on very small screens, show only dropdown arrow */
            .admin-nav-container .relative button span span {
                display: none;
            }

            .admin-nav-container .relative button span {
                padding: 0.5rem;
                min-width: 44px;
            }
        }

        /* Ensure proper z-index layering */
        .admin-sidebar {
            z-index: 50; /* Desktop z-index, mobile overrides this */
            display: flex;
            flex-direction: column;
        }

        .mobile-backdrop {
            z-index: 999; /* Mobile backdrop */
        }

        .admin-main-content {
            z-index: 10;
        }

        /* Medicine Search in Navbar - Ensure dropdown appears on top */
        .medicine-search-wrapper {
            position: relative;
            z-index: 100; /* Higher than sidebar and other elements */
        }

        .medicine-search-wrapper .dropdown-results {
            z-index: 1000; /* Ensure search results appear on top of everything */
            position: fixed; /* Use fixed positioning for navbar context */
            top: 4rem; /* Position below navbar */
            left: 50%;
            transform: translateX(-50%);
            width: 90vw;
            max-width: 1200px;
            min-width: 800px;
        }

        /* Unified Status Component - Ensure dropdown appears above all content */
        .unified-status-wrapper {
            position: relative;
            z-index: 110; /* Higher than medicine search wrapper */
        }

        /* Unified Status Dropdown - Highest priority for visibility */
        .unified-status-dropdown {
            z-index: 1100 !important; /* Higher than medicine search dropdown */
            position: fixed !important;
            /* Ensure dropdown is not clipped by any container */
            max-height: 90vh;
            overflow-y: auto;
            /* Smooth scrolling for long content */
            -webkit-overflow-scrolling: touch;
            /* Prevent dropdown from being affected by transforms */
            transform: none !important;
            /* Ensure proper layering */
            isolation: isolate;
        }

        /* Ensure dropdown shadow is visible */
        .unified-status-dropdown {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
            border: 1px solid #e5e7eb !important;
        }

        /* Mobile-specific positioning for unified status dropdown */
        @media (max-width: 768px) {
            .unified-status-dropdown {
                /* On mobile, use full width with margins */
                left: 1rem !important;
                right: 1rem !important;
                width: auto !important;
                max-width: none !important;
                /* Ensure it's below the navbar */
                top: 4rem !important;
                /* Prevent horizontal scrolling */
                box-sizing: border-box;
            }
        }

        /* Tablet-specific positioning */
        @media (min-width: 769px) and (max-width: 1024px) {
            .unified-status-dropdown {
                /* On tablets, ensure it doesn't go off-screen */
                max-width: 20rem;
                min-width: 18rem;
            }
        }

        /* Desktop positioning */
        @media (min-width: 1025px) {
            .unified-status-dropdown {
                /* On desktop, use fixed width */
                width: 20rem;
            }
        }

        /* Responsive adjustments for medicine search in navbar */
        @media (max-width: 1024px) {
            .medicine-search-wrapper .dropdown-results {
                width: 95vw;
                min-width: 600px;
            }
        }

        @media (max-width: 768px) {
            .medicine-search-wrapper .dropdown-results {
                width: 98vw;
                min-width: 400px;
                left: 1vw;
                transform: none;
            }
        }

        @media (max-width: 640px) {
            .medicine-search-wrapper .dropdown-results {
                width: 96vw;
                min-width: 300px;
                left: 2vw;
            }
        }

        /* Sidebar Profile Section Styling */
        .sidebar-profile-section {
            margin-top: auto;
            flex-shrink: 0;
        }

        /* Profile section responsive behavior */
        @media (max-width: 1024px) {
            .sidebar-profile-section {
                position: sticky;
                bottom: 0;
                background-color: #f9fafb;
                border-top: 1px solid #e5e7eb;
            }
        }

        /* Profile tooltips positioning */
        .sidebar-profile-section .relative:hover .absolute {
            display: block;
        }

        /* Ensure profile section stays at bottom */
        .admin-sidebar nav {
            flex: 1;
            overflow-y: auto;
        }

        /* Profile dropdown upward positioning */
        .sidebar-profile-section .absolute {
            bottom: 100%;
            margin-bottom: 0.5rem;
            min-width: 200px;
        }

        /* Collapsed mode dropdown positioning */
        .sidebar-profile-section .absolute.transform.-translate-x-1\/2 {
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            margin-bottom: 0.75rem;
            width: auto;
            min-width: auto;
        }

        /* Icon-only dropdown styling - vertical alignment */
        .sidebar-profile-section .space-y-1 {
            min-width: 48px; /* Ensure consistent width for icon buttons */
        }

        .sidebar-profile-section .space-y-1 a,
        .sidebar-profile-section .space-y-1 button {
            width: 40px;
            height: 40px;
            min-width: 40px;
            min-height: 40px;
        }

        /* Ensure dropdown doesn't get cut off on mobile */
        @media (max-width: 640px) {
            .sidebar-profile-section .absolute {
                left: 0.5rem;
                right: 0.5rem;
                margin-bottom: 0.75rem;
            }

            .sidebar-profile-section .absolute.transform.-translate-x-1\/2 {
                left: 0.5rem;
                right: 0.5rem;
                transform: none;
                width: auto;
            }
        }

        /* Collapsed profile dropdown positioning */
        .sidebar-profile-section .absolute[class*="left-full"] {
            left: 100%;
            margin-left: 0.5rem;
            bottom: 0;
            min-width: 12rem;
        }

        /* Ensure collapsed dropdown doesn't go off-screen */
        @media (max-width: 1280px) {
            .sidebar-profile-section .absolute[class*="left-full"] {
                right: 0.5rem;
                left: auto;
                margin-left: 0;
                margin-right: 0.5rem;
            }
        }

        /* Mobile collapsed profile dropdown */
        @media (max-width: 768px) {
            .sidebar-profile-section .absolute[class*="left-full"] {
                position: fixed;
                bottom: 4rem;
                left: 1rem;
                right: 1rem;
                margin: 0;
                width: auto;
            }
        }



        /* Responsive text sizing */
        @media (max-width: 640px) {
            .admin-sidebar .text-2xl {
                font-size: 1.5rem;
                line-height: 2rem;
            }

            .admin-sidebar .text-xl {
                font-size: 1.25rem;
                line-height: 1.75rem;
            }
        }

        /* Prevent text selection on navigation elements */
        .admin-sidebar nav a,
        .admin-sidebar nav button {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* Smooth transitions for responsive changes */
        .admin-layout-container,
        .admin-sidebar,
        .admin-main-content {
            transition-property: width, transform, margin, padding;
            transition-duration: 0.3s;
            transition-timing-function: ease-in-out;
        }

        /* Focus styles for accessibility */
        .admin-sidebar nav a:focus,
        .admin-sidebar nav button:focus {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
        }


    </style>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Styles -->
    @livewireStyles
    @stack('styles')

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <!-- Early Scripts (for patches and polyfills) -->
    @stack('head-scripts')
    
    <!-- Sync Service Patch -->
    <script src="{{ asset('js/sync-service-patch.js') }}"></script>
</head>
<body class="h-full admin-layout">
    <!-- Offline Indicator -->
    <div class="offline-indicator">
        <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636a9 9 0 010 12.728m0 0l-2.829-2.829m2.829 2.829L21 21M15.536 8.464a5 5 0 010 7.072m0 0l-2.829-2.829m-4.243 2.829a4.978 4.978 0 01-1.414-2.83m-1.414 5.658a9 9 0 01-2.167-9.238m7.824 2.167a1 1 0 111.414 1.414m-1.414-1.414L3 3m8.293 8.293l1.414 1.414" />
        </svg>
        <span>You're offline</span>
    </div>

    <div x-data="{
        sidebarOpen: {{ !request()->routeIs('sales.create') ? 'localStorage.getItem(\'sidebarOpen\') === \'false\' ? false : true' : 'false' }},
        activeMenu: '{{ request()->segment(1) }}',
        isMobile: false,
        isTablet: false,
        isDesktop: false,
        showMobileBackdrop: false,

        toggleSidebar() {
            this.sidebarOpen = !this.sidebarOpen;

            // Handle mobile backdrop and body scroll
            if (this.isMobile) {
                this.showMobileBackdrop = this.sidebarOpen;

                // Prevent body scroll when sidebar is open on mobile
                if (this.sidebarOpen) {
                    document.body.classList.add('sidebar-open');
                    document.body.style.overflow = 'hidden';
                    document.body.style.position = 'fixed';
                    document.body.style.width = '100%';
                } else {
                    document.body.classList.remove('sidebar-open');
                    document.body.style.overflow = '';
                    document.body.style.position = '';
                    document.body.style.width = '';
                }
            } else {
                // Save state for desktop/tablet
                localStorage.setItem('sidebarOpen', this.sidebarOpen);
            }
        },

        closeSidebar() {
            if (this.isMobile) {
                this.sidebarOpen = false;
                this.showMobileBackdrop = false;
                document.body.classList.remove('sidebar-open');
                document.body.style.overflow = '';
                document.body.style.position = '';
                document.body.style.width = '';
            }
        },

        handleResize() {
            const width = window.innerWidth;
            const wasMobile = this.isMobile;

            this.isMobile = width <= 768;
            this.isTablet = width > 768 && width <= 1024;
            this.isDesktop = width > 1024;

            // Auto-hide sidebar on mobile, restore on desktop
            if (this.isMobile && !wasMobile) {
                // Switched to mobile
                this.sidebarOpen = false;
                this.showMobileBackdrop = false;
                document.body.classList.remove('sidebar-open');
                document.body.style.overflow = '';
                document.body.style.position = '';
                document.body.style.width = '';
            } else if (!this.isMobile && wasMobile) {
                // Switched from mobile to desktop/tablet
                this.showMobileBackdrop = false;
                document.body.classList.remove('sidebar-open');
                document.body.style.overflow = '';
                document.body.style.position = '';
                document.body.style.width = '';
                // Restore saved state for desktop
                const savedState = localStorage.getItem('sidebarOpen');
                this.sidebarOpen = savedState === 'false' ? false : true;
            }
        },

        init() {
            // Ensure layout is stable on initialization without cross-fade effects
            document.body.classList.add('layout-initialized');

            // Initialize responsive behavior
            this.handleResize();

            // Listen for window resize
            window.addEventListener('resize', () => {
                this.handleResize();
            });

            // Force sidebar visibility to prevent any fade effects
            this.$nextTick(() => {
                const sidebar = this.$el.querySelector('.admin-sidebar');
                if (sidebar) {
                    sidebar.style.opacity = '1';
                    if (!this.isMobile) {
                        sidebar.style.visibility = 'visible';
                    }
                }


            });
        }
    }" class="admin-layout-container" x-cloak>
        <!-- Mobile Backdrop -->
        <div x-show="showMobileBackdrop"
             @click="closeSidebar()"
             class="mobile-backdrop"
             :class="{'show': showMobileBackdrop}"
             x-transition:enter="transition-opacity ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0">
        </div>

        <!-- Sidebar -->
        <div :class="{
                'w-64': sidebarOpen && !isMobile,
                'w-16': !sidebarOpen && !isMobile,
                'open': sidebarOpen
             }"
             class="admin-sidebar bg-white border-r overflow-y-auto sidebar-no-fade"
             x-cloak>
            <!-- Logo -->
            <div class="flex items-center justify-center h-16 bg-indigo-600">
                <span x-show="sidebarOpen" class="text-2xl font-semibold text-white">PharmaDesk</span>
                <span x-show="!sidebarOpen" class="text-xl font-semibold text-white">PD</span>
            </div>

            <!-- Navigation -->
            <nav class="mt-5 px-2 space-y-1 flex-1 overflow-y-auto">
                <!-- Dashboard -->
                <a href="{{ route('dashboard') }}" class="group flex items-center px-2 py-2 text-base leading-6 font-medium rounded-md text-gray-900 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('dashboard') ? 'bg-gray-100' : '' }}" :class="{'justify-center': !sidebarOpen}">
                    <svg class="h-7 w-7 text-gray-500" :class="{'mr-2': sidebarOpen, 'mr-0': !sidebarOpen}" style="vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
                        <path d="M0 0h1024v1024H0z" fill="#FFFFFF" opacity="0" />
                        <path d="M336 384H448v256H336zM576 384h112v256H576z" fill="currentColor" />
                        <path d="M832 192v448H192V192h640m128-128H64v704h896V64zM128 832h768v128H128z" fill="currentColor" />
                    </svg>
                    <span x-show="sidebarOpen">Dashboard</span>
                </a>

                <!-- Inventory Management -->
                @can('view inventory')
                <div x-data="{ open: {{ request()->segment(1) === 'inventory' ? 'true' : 'false' }} }" 
                     x-init="$watch('open', value => { if(value) $dispatch('menu-expanded', 'inventory') })"
                     @menu-expanded.window="if($event.detail !== 'inventory') open = false"
                     class="space-y-1">
                    <button @click="open = !open" class="w-full flex items-center justify-between text-left px-2 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150" :class="{'justify-center': !sidebarOpen}">
                        <div class="flex items-center" :class="{'justify-center w-full': !sidebarOpen}">
                            <svg class="h-8 w-8 text-gray-500" :class="{'mr-2': sidebarOpen, 'mr-0': !sidebarOpen}" style="vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
                                <path d="M853.333333 405.333333v384a42.666667 42.666667 0 0 1-42.666666 42.666667H213.333333a42.666667 42.666667 0 0 1-42.666666-42.666667v-384h682.666666z m-298.666666 256H256v42.666667h298.666667v-42.666667z m213.333333-128H256v42.666667h512v-42.666667z m-277.333333-341.333333v170.666667H183.765333a12.8 12.8 0 0 1-9.813333-20.992L285.866667 207.36a42.666667 42.666667 0 0 1 32.768-15.36H490.666667z m214.869333 0a42.666667 42.666667 0 0 1 32.597333 15.104l113.706667 134.485333a12.8 12.8 0 0 1-9.770667 21.077334H533.333333v-170.666667h172.245334z" />
                            </svg>
                            <span x-show="sidebarOpen">Inventory</span>
                        </div>
                        <svg x-show="sidebarOpen" :class="{'transform rotate-90': open}" class="ml-auto h-5 w-5 transform" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                    <div x-show="open" class="space-y-1" :class="{'pl-8': sidebarOpen, 'pl-2': !sidebarOpen}">
                        <!-- medicines -->
                        <a href="{{ route('inventory.medicines.index') }}" 
                           class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.medicines.*') ? 'bg-gray-100 text-gray-900' : '' }}"
                           :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" style="vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
                                <path d="M941.162216 471.8542 471.969936 942.985053C419.9757 995.21076 350.881524 1023.971066 277.360459 1023.971066 203.839394 1023.971066 134.716284 995.21076 82.722048 942.985053 30.727812 890.78828 2.112175 821.37583 2.083242 747.536492 2.083242 673.697155 30.727812 604.284705 82.722048 552.087932L551.885395 80.957079C603.87963 28.760306 673.002741 0 746.523805 0 820.04487 0 889.167981 28.760306 941.162216 80.957079 993.156452 133.153853 1021.801023 202.566302 1021.801023 276.40564 1021.801023 350.244978 993.156452 419.657427 941.162216 471.8542ZM501.251053 722.740132C512.390608 733.908621 530.3875 733.908621 541.469187 722.740132 552.579808 711.600576 552.579808 693.516883 541.469187 682.377328 530.3875 671.208838 512.390608 671.208838 501.251053 682.377328 490.169365 693.516883 490.169365 711.600576 501.251053 722.740132ZM423.794976 800.514481C434.905597 811.68297 452.902489 811.68297 464.013111 800.514481 475.152666 789.40386 475.152666 771.320166 464.013111 760.122743 452.902489 749.012122 434.905597 749.012122 423.82391 760.180611 412.713289 771.320166 412.713289 789.40386 423.794976 800.514481ZM421.97214 602.346133C433.082761 613.485688 451.050719 613.485688 462.190274 602.346133 473.300896 591.177644 473.300896 573.09395 462.190274 561.983329 451.050719 550.81484 433.082761 550.785906 421.97214 561.983329 410.861518 573.09395 410.890452 591.177644 421.97214 602.346133ZM346.657173 877.970557C357.796728 889.139047 375.79362 889.139047 386.904241 877.970557 398.014863 866.831002 398.014863 848.747309 386.904241 837.578819 375.79362 826.439264 357.796728 826.439264 346.686107 837.607753 335.575485 848.747309 335.575485 866.831002 346.657173 877.970557ZM188.678025 638.195247C177.596338 627.055692 159.599446 627.055692 148.459891 638.195247 137.378203 649.363737 137.378203 667.418496 148.488825 678.586985 159.599446 689.726541 177.625272 689.726541 188.678025 678.615919 199.817581 667.44743 199.817581 649.363737 188.678025 638.195247ZM186.913057 797.968297C175.83137 809.107852 175.83137 827.191546 186.941991 838.360035 198.052612 849.49959 216.049504 849.49959 227.160125 838.360035 238.270747 827.191546 238.270747 809.107852 227.160125 797.968297 216.049504 786.799808 198.052612 786.799808 186.913057 797.968297ZM265.815829 560.739171C254.705208 549.570682 236.708316 549.599616 225.626628 560.739171 214.516007 571.90766 214.516007 589.991354 225.626628 601.101975 236.708316 612.299398 254.734141 612.299398 265.844763 601.101975 276.955384 589.991354 276.955384 571.90766 265.815829 560.739171ZM267.37826 717.213755C256.238705 728.324376 256.238705 746.40807 267.37826 757.576559 278.459947 768.716114 296.485773 768.716114 307.625328 757.576559 318.707016 746.40807 318.678082 728.324376 307.625328 717.213755 296.485773 706.045266 278.488881 706.045266 267.37826 717.213755ZM343.300839 482.964822C332.161284 471.796332 314.164392 471.796332 303.082705 482.935888 291.943149 494.104377 291.943149 512.18807 303.082705 523.35656 314.164392 534.496115 332.190218 534.496115 343.329773 523.35656 354.382527 512.18807 354.382527 494.104377 343.300839 482.964822ZM344.487129 639.728745C333.376508 650.897234 333.376508 668.980927 344.487129 680.120483 355.597751 691.288972 373.623577 691.288972 384.705264 680.120483 395.815885 668.980927 395.815885 650.897234 384.705264 639.728745 373.623577 628.589189 355.597751 628.589189 344.487129 639.728745ZM905.342036 116.921929C862.924924 74.331214 806.5038 50.865813 746.523805 50.865813 686.514877 50.865813 630.122687 74.331214 587.676641 116.921929L371.048459 334.475997 688.68492 653.443418 905.37097 435.88935C947.788082 393.298635 971.166681 336.674974 971.137747 276.40564 971.137747 216.16524 947.759148 159.541578 905.342036 116.921929Z" />
                            </svg>
                            <span x-show="sidebarOpen" class="truncate">Medicines</span>
                        </a>

                        <!-- Manufacturers -->
                        <a href="{{ route('inventory.manufacturers.index') }}" 
                           class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.manufacturers.*') ? 'bg-gray-100 text-gray-900' : '' }}"
                           :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" style="vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
                                <path d="M680.937 457.146h111.285V568.43H680.937V457.146zM235.799 234.577h111.284V345.86H235.8V234.577zM402.726 234.577H514.01V345.86H402.726V234.577zM235.799 457.146h111.284V568.43H235.8V457.146zM402.726 457.146H514.01V568.43H402.726V457.146zM235.799 679.715h111.284v111.284H235.8V679.715zM402.726 679.715H514.01v111.284H402.726V679.715zM680.937 679.715h111.285v111.284H680.937V679.715z" />
                                <path d="M929.316 900.68h-25.81V373.708c0-22.306-8.695-43.28-24.507-59.01-15.758-15.759-36.68-24.454-58.957-24.454H625.295V151.14c0-22.306-8.694-43.28-24.506-59.01-15.758-15.759-36.68-24.454-58.957-24.454H207.978c-22.278 0-43.254 8.694-58.956 24.426-15.813 15.73-24.507 36.705-24.507 59.037V900.68H94.684c-15.377 0-27.822 12.444-27.822 27.822s12.444 27.822 27.822 27.822h834.634c15.377 0 27.822-12.444 27.822-27.822s-12.446-27.822-27.824-27.822zM820.042 345.888c7.444 0 14.4 2.907 19.67 8.178 5.271 5.243 8.152 12.225 8.152 19.642V900.68h-222.57V345.888h194.748zM180.157 151.14c0-7.443 2.88-14.426 8.15-19.67 5.271-5.244 12.227-8.151 19.671-8.151h333.854c7.443 0 14.399 2.907 19.67 8.178 5.271 5.243 8.152 12.225 8.152 19.642V900.68H180.157V151.14z" />
                            </svg>
                            <span x-show="sidebarOpen" class="truncate">Manufacturers</span>
                        </a>

                        <!-- Categories -->
                        <a href="{{ route('inventory.categories.index') }}" 
                           class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.categories.*') ? 'bg-gray-100 text-gray-900' : '' }}"
                           :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" style="vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
                                <path d="M367.018667 480.426667h-183.466667A113.493333 113.493333 0 0 1 70.4 367.274667v-183.466667a113.493333 113.493333 0 0 1 113.194667-113.152h183.466666a113.493333 113.493333 0 0 1 113.152 113.152v183.466667a113.493333 113.493333 0 0 1-113.194666 113.152zM944.213333 349.269333l-119.466666 119.466667a104.533333 104.533333 0 0 1-147.328 0l-119.466667-119.466667a104.533333 104.533333 0 0 1 0-147.370666l119.466667-119.466667a104.533333 104.533333 0 0 1 147.370666 0l119.424 119.466667c40.533333 40.533333 40.533333 106.837333 0 147.370666zM367.061333 955.648h-183.466666a113.493333 113.493333 0 0 1-113.194667-113.152v-183.466667a113.493333 113.493333 0 0 1 113.194667-113.152h183.466666a113.493333 113.493333 0 0 1 113.152 113.152v183.466667a113.493333 113.493333 0 0 1-113.194666 113.152z m475.733334 0h-183.466667a113.493333 113.493333 0 0 1-113.152-113.152v-183.466667a113.493333 113.493333 0 0 1 113.194667-113.152h183.466666a113.493333 113.493333 0 0 1 113.152 113.152v183.466667a113.493333 113.493333 0 0 1-113.152 113.152z" />
                            </svg>
                            <span x-show="sidebarOpen" class="truncate">Categories</span>
                        </a>

                        <!-- Unit Types -->
                        <a href="{{ route('inventory.unit-types.index') }}" 
                           class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.unit-types.*') ? 'bg-gray-100 text-gray-900' : '' }}"
                           :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3"/>
                            </svg>
                            <span x-show="sidebarOpen" class="truncate">Unit Types</span>
                        </a>

                        <!-- Stock Management -->
                        <a href="{{ route('inventory.stock.index') }}" 
                           class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.stock.index') ? 'bg-gray-100 text-gray-900' : '' }}"
                           :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
                            </svg>
                            <span x-show="sidebarOpen" class="truncate">Stock Management</span>
                        </a>

                        <!-- Stock Transfer -->
                        <a href="{{ route('inventory.stock.transfer') }}" 
                           class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.stock.transfer') ? 'bg-gray-100 text-gray-900' : '' }}"
                           :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"/>
                            </svg>
                            <span x-show="sidebarOpen" class="truncate">Stock Transfer</span>
                        </a>

                        <!-- Locations -->
                        <a href="{{ route('inventory.locations.index') }}" 
                           class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.locations.*') ? 'bg-gray-100 text-gray-900' : '' }}"
                           :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            <span x-show="sidebarOpen" class="truncate">Locations</span>
                        </a>

                        <!-- Suppliers -->
                        <a href="{{ route('inventory.suppliers.index') }}" 
                           class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.suppliers.*') ? 'bg-gray-100 text-gray-900' : '' }}"
                           :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" style="vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 59.9 59.89" xmlns="http://www.w3.org/2000/svg">
                                <path d="M40.02,17.53l1.16-4.9,6.28-.06,2.49,10v4.89h4.97v4.97h-3.07c3.76,2.95,7.16,6.86,7.87,11.78.33,2.28.1,4.62.17,6.92h-2.63c.98,6.19-5.44,10.84-11.05,7.77-2.36-1.29-3.6-3.67-3.85-6.3l-1.85,3.51h-16.6s-1.72-4.95-1.72-4.95l-3.48-.02v2.41c0,3.31-4.38,6.4-7.52,6.36s-7.37-3.08-7.37-6.36v-2.41H0v-8.25c0-3.02,3.61-6.52,6.64-6.65h15.76s5.06,14.84,5.06,14.84l9.84.04.23-.21c2.2-5.08,5.69-9.9,6.83-15.37.57-2.72.53-5.29.48-8.06h-6.21l-13.15-7.15-4.6,7.15h7.89l6.57,19.86h-5.26l-5.04-14.9H0V15.04h15.04v12.27l8.69-13.57,16.21,8.75h6.06l-1.17-4.97h-4.82ZM9.93,20.01h-4.97v7.45h4.97v-7.45ZM54.92,46.15c-.13-3.97-2.7-6.97-5.62-9.34-.5,1.3-.8,2.67-1.28,3.98-.69,1.86-1.69,3.55-2.45,5.37h9.35ZM20.45,46.15l-1.7-4.95H7.1c-.93,0-2.13,1.38-2.13,2.24v2.7h15.48ZM9.15,51.14c-.86,1.08-.3,2.73.83,3.37,2.21,1.25,4.64-1.04,3.47-3.32l-4.29-.05ZM52.13,51.13l-4.37.06c-1.82,4.85,5.97,4.91,4.37-.06Z"/>
                                <path d="M29.32.04c7.83-.81,9.43,10.98,1.82,12.34C22.57,13.91,20.88.92,29.32.04Z"/>
                                <polygon points="17.53 0 17.53 3.8 0 2.48 0 0 17.53 0"/>
                                <polygon points="20.01 11.25 4.97 9.86 4.97 7.67 5.18 7.45 19.79 7.45 20.01 7.67 20.01 11.25"/>
                            </svg>
                            <span x-show="sidebarOpen" class="truncate">Suppliers</span>
                        </a>

                        <!-- Purchases -->
                        <a href="{{ route('inventory.purchases.index') }}" 
                           class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.purchases.*') ? 'bg-gray-100 text-gray-900' : '' }}"
                           :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"/>
                            </svg>
                            <span x-show="sidebarOpen" class="truncate">Purchase Orders</span>
                        </a>

                        <!-- Batch History -->
                        <a href="{{ route('inventory.batch.history') }}" 
                           class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.batch.history') ? 'bg-gray-100 text-gray-900' : '' }}"
                           :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <span x-show="sidebarOpen" class="truncate">Batch History</span>
                        </a>

                        <!-- Unit Conversions -->
                        <a href="{{ route('inventory.units.index') }}" 
                           class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.units.*') ? 'bg-gray-100 text-gray-900' : '' }}"
                           :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" style="vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
                                <path d="M810.666667 0a85.333333 85.333333 0 0 1 85.333333 85.333333v853.333334a85.333333 85.333333 0 0 1-85.333333 85.333333H213.333333a85.333333 85.333333 0 0 1-85.333333-85.333333V85.333333a85.333333 85.333333 0 0 1 85.333333-85.333333h597.333334zM368 816h-96v96h96v-96z m192 0h-96v96h96v-96zM768 810.666667h-128v96h128V810.666667z m-400-186.666667h-96v96h96v-96z m192 0h-96v96h96v-96z m192 0h-96v96h96v-96z m-384-192h-96v96h96v-96z m192 0h-96v96h96v-96z m192 0h-96v96h96v-96z m5.333333-336H266.666667a42.666667 42.666667 0 0 0-42.666667 42.666667v116.778666a42.666667 42.666667 0 0 0 42.666667 42.666667h490.666666a42.666667 42.666667 0 0 0 42.666667-42.666667V138.666667a42.666667 42.666667 0 0 0-42.666667-42.666667z" />
                            </svg>
                            <span x-show="sidebarOpen" class="truncate">Unit Conversions</span>
                        </a>
                    </div>
                </div>
                @endcan

                <!-- Sales Management -->
                @can('view sales')
                <div x-data="{ open: {{ request()->segment(1) === 'sales' ? 'true' : 'false' }} }"
                     x-init="$watch('open', value => { if(value) $dispatch('menu-expanded', 'sales') })"
                     @menu-expanded.window="if($event.detail !== 'sales') open = false"
                     class="space-y-1">
                    <button @click="open = !open" class="w-full group flex items-center px-2 py-2 text-base leading-6 font-medium rounded-md text-gray-900 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150" :class="{'justify-center': !sidebarOpen}">
                        <div class="flex items-center" :class="{'justify-center w-full': !sidebarOpen}">
                            <svg class="h-7 w-7 text-gray-500" :class="{'mr-2': sidebarOpen, 'mr-0': !sidebarOpen}" viewBox="0 0 1028 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" fill="currentColor">
                                <path d="M332.8 790.528q19.456 0 36.864 7.168t30.208 19.968 20.48 30.208 7.68 36.864-7.68 36.864-20.48 30.208-30.208 20.48-36.864 7.68q-20.48 0-37.888-7.68t-30.208-20.48-20.48-30.208-7.68-36.864 7.68-36.864 20.48-30.208 30.208-19.968 37.888-7.168zM758.784 792.576q19.456 0 37.376 7.168t30.72 19.968 20.48 30.208 7.68 36.864-7.68 36.864-20.48 30.208-30.72 20.48-37.376 7.68-36.864-7.68-30.208-20.48-20.48-30.208-7.68-36.864 7.68-36.864 20.48-30.208 30.208-19.968 36.864-7.168zM930.816 210.944q28.672 0 44.544 7.68t22.528 18.944 6.144 24.064-3.584 22.016-13.312 37.888-22.016 62.976-23.552 68.096-18.944 53.248q-13.312 40.96-33.28 56.832t-49.664 15.872l-35.84 0-65.536 0-86.016 0-96.256 0-253.952 0 14.336 92.16 517.12 0q49.152 0 49.152 41.984 0 20.48-9.728 35.84t-38.4 14.336l-49.152 0-94.208 0-118.784 0-119.808 0-99.328 0-55.296 0q-20.48 0-34.304-9.216t-23.04-24.064-14.848-32.256-8.704-32.768q-1.024-6.144-5.632-29.696t-11.264-58.88-14.848-78.848-16.384-87.552q-19.456-103.424-44.032-230.4l-76.8 0q-15.36 0-25.6-7.68t-16.896-18.432-9.216-23.04-2.56-22.528q0-20.48 13.824-33.792t37.376-12.288l103.424 0q20.48 0 32.768 6.144t19.456 15.36 10.24 18.944 5.12 16.896q2.048 8.192 4.096 23.04t4.096 30.208q3.072 18.432 6.144 38.912l700.416 0zM892.928 302.08l-641.024-2.048 35.84 185.344 535.552 1.024z"/>
                            </svg>
                            <span x-show="sidebarOpen">Sales</span>
                        </div>
                        <svg x-show="sidebarOpen" :class="{'transform rotate-90': open}" class="ml-auto h-5 w-5 transform" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                        </svg>
                    </button>
                    <div x-show="open" class="space-y-1" :class="{'pl-8': sidebarOpen, 'pl-2': !sidebarOpen}">
                        <a href="{{ route('sales.create') }}" 
                           class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('sales.create') ? 'bg-gray-100 text-gray-900' : '' }}"
                           :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-7 w-7 text-gray-500" :class="{'mr-2': sidebarOpen, 'mr-0': !sidebarOpen}" style="vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
                                <path d="M213.81 469.333l54.97 367.014 0.471 6.32c0 5.427 5.493 10.666 13.252 10.666h475.303c7.758 0 13.251-5.239 13.251-10.666l0.47-6.32 39.61-264.452c3.49-23.304 25.212-39.367 48.516-35.876 23.305 3.49 39.367 25.212 35.876 48.516l-39.208 261.77c-1.967 51.76-45.739 92.362-98.515 92.362H282.503c-52.777 0-96.549-40.602-98.516-92.362l-55.516-370.652a43.012 43.012 0 0 1-0.477-6.32h-42.66c-23.565 0-42.667-19.102-42.667-42.666 0-23.564 19.102-42.667 42.666-42.667h853.334c23.564 0 42.666 19.103 42.666 42.667s-19.102 42.666-42.666 42.666H213.809z m449.04-140.496l-90.51-90.51c-33.325-33.325-87.355-33.325-120.68 0l-90.51 90.51c-16.662 16.662-43.677 16.662-60.34 0-16.662-16.663-16.662-43.678 0-60.34l90.51-90.51c66.65-66.65 174.71-66.65 241.36 0l90.51 90.51c16.662 16.662 16.662 43.677 0 60.34-16.663 16.662-43.678 16.662-60.34 0zM339.732 609.875c0-23.564 19.103-42.667 42.667-42.667s42.667 19.103 42.667 42.667v135.45c0 23.564-19.103 42.666-42.667 42.666s-42.667-19.102-42.667-42.666v-135.45z m150.578 0c0-23.564 19.103-42.667 42.667-42.667s42.666 19.103 42.666 42.667v135.45c0 23.564-19.102 42.666-42.666 42.666-23.564 0-42.667-19.102-42.667-42.666v-135.45z m150.578 0c0-23.564 19.102-42.667 42.667-42.667 23.564 0 42.666 19.103 42.666 42.667v135.45c0 23.564-19.102 42.666-42.666 42.666-23.565 0-42.667-19.102-42.667-42.666v-135.45z" />
                            </svg>
                            <span x-show="sidebarOpen">New Sale</span>
                        </a>
                        <a href="{{ route('sales.index') }}" 
                           class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('sales.index') ? 'bg-gray-100 text-gray-900' : '' }}"
                           :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-7 w-7 text-gray-500" :class="{'mr-2': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                            </svg>
                            <span x-show="sidebarOpen">Sales History</span>
                        </a>
                    </div>
                </div>
                @endcan

                <!-- Customer Management -->
                @can('view customers')
                <div x-data="{ open: {{ request()->segment(1) === 'customers' || request()->segment(1) === 'prescriptions' ? 'true' : 'false' }} }"
                     x-init="$watch('open', value => { if(value) $dispatch('menu-expanded', 'customers') })"
                     @menu-expanded.window="if($event.detail !== 'customers') open = false"
                     class="space-y-1">
                    <button @click="open = !open" class="w-full group flex items-center px-2 py-2 text-base leading-6 font-medium rounded-md text-gray-900 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150" :class="{'justify-center': !sidebarOpen}">
                        <div class="flex items-center" :class="{'justify-center w-full': !sidebarOpen}">
                            <svg class="h-7 w-7 text-gray-500" :class="{'mr-2': sidebarOpen, 'mr-0': !sidebarOpen}" style="vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
                                <path d="M512 624c102.2 0 185-82.8 185-185s-82.8-185-185-185-185 82.8-185 185 82.8 185 185 185z m0-280c52.4 0 95 42.6 95 95s-42.6 95-95 95-95-42.6-95-95 42.6-95 95-95z m318.2 482.4c1-1 2-2.1 3-3.1l1.5-1.5c1-1 2-2.1 3-3.1 0.4-0.5 0.9-0.9 1.3-1.4l4.2-4.5c1.4-1.5 2.7-3 4.1-4.6 0.4-0.5 0.8-0.9 1.2-1.4 1-1.1 1.9-2.2 2.8-3.3 0.5-0.5 0.9-1.1 1.4-1.6 0.9-1.1 1.9-2.2 2.8-3.3 0.4-0.5 0.8-1 1.2-1.4 2.6-3.2 5.2-6.4 7.7-9.6l1.2-1.5c0.9-1.1 1.8-2.3 2.6-3.4 0.4-0.6 0.9-1.1 1.3-1.7 0.9-1.2 1.7-2.3 2.6-3.5 0.4-0.5 0.8-1 1.1-1.5 1.2-1.7 2.4-3.3 3.6-5 1.2-1.7 2.4-3.4 3.5-5.1 0.4-0.5 0.7-1 1.1-1.6l2.4-3.6 1.2-1.8 2.4-3.6c0.3-0.5 0.7-1.1 1-1.6 2.2-3.5 4.4-7 6.5-10.5 0.3-0.5 0.7-1.1 1-1.6 0.7-1.2 1.5-2.5 2.2-3.7 0.4-0.6 0.7-1.2 1.1-1.9 0.7-1.2 1.4-2.5 2.2-3.8 0.3-0.6 0.6-1.1 1-1.7 2-3.6 4-7.2 5.9-10.9 0.3-0.6 0.6-1.1 0.9-1.7 0.7-1.3 1.3-2.6 2-3.9 0.3-0.6 0.6-1.3 1-1.9 0.7-1.3 1.3-2.6 1.9-3.9 0.3-0.6 0.6-1.2 0.9-1.7 1.8-3.7 3.6-7.5 5.3-11.3 0.3-0.6 0.5-1.2 0.8-1.8 0.6-1.3 1.2-2.7 1.8-4 0.3-0.7 0.6-1.3 0.9-2 0.6-1.3 1.1-2.7 1.7-4 0.2-0.6 0.5-1.2 0.7-1.8 0.8-1.9 1.6-3.9 2.3-5.8 0.8-1.9 1.5-3.9 2.3-5.8 0.2-0.6 0.5-1.2 0.7-1.8 0.5-1.4 1-2.7 1.5-4.1 0.2-0.7 0.5-1.4 0.7-2.1 0.5-1.4 1-2.8 1.5-4.1 0.2-0.6 0.4-1.2 0.6-1.9 0.7-2 1.3-4 2-5.9 0.6-2 1.3-4 1.9-6 0.2-0.6 0.4-1.2 0.6-1.9 0.4-1.4 0.9-2.8 1.3-4.2l0.6-2.1c0.4-1.4 0.8-2.8 1.2-4.3 0.2-0.6 0.4-1.3 0.5-1.9 0.6-2 1.1-4.1 1.6-6.1s1-4.1 1.5-6.2c0.2-0.6 0.3-1.3 0.5-1.9 0.3-1.4 0.7-2.9 1-4.3 0.2-0.7 0.3-1.4 0.5-2.2 0.3-1.4 0.6-2.9 0.9-4.4 0.1-0.6 0.3-1.3 0.4-1.9 0.4-2.1 0.8-4.2 1.2-6.2l1.2-6.3c0.1-0.7 0.2-1.3 0.3-2 0.3-1.5 0.5-2.9 0.7-4.4 0.1-0.7 0.2-1.5 0.4-2.2 0.2-1.5 0.5-3 0.7-4.4 0.1-0.7 0.2-1.3 0.3-2 0.6-4.3 1.1-8.5 1.6-12.8 0.1-0.7 0.1-1.3 0.2-2 0.2-1.5 0.3-3 0.5-4.5 0.1-0.8 0.1-1.5 0.2-2.3 0.1-1.5 0.3-3 0.4-4.5 0.1-0.7 0.1-1.4 0.2-2 0.2-2.2 0.3-4.3 0.5-6.5 0.1-2.2 0.3-4.4 0.4-6.5 0-0.7 0.1-1.4 0.1-2 0.1-1.5 0.1-3.1 0.2-4.6 0-0.8 0.1-1.5 0.1-2.3 0-1.5 0.1-3.1 0.1-4.6v-2.1c0-2.2 0.1-4.4 0.1-6.6 0-247.4-200.6-448-448-448s-448 200.6-448 448c0 2.2 0 4.4 0.1 6.6v2.1c0 1.5 0.1 3.1 0.1 4.6 0 0.8 0 1.5 0.1 2.3 0.1 1.5 0.1 3.1 0.2 4.6 0 0.7 0.1 1.3 0.1 2 0.1 2.2 0.2 4.4 0.4 6.5v0.3c0.1 2.1 0.3 4.2 0.4 6.2 0.1 0.7 0.1 1.4 0.2 2 0.1 1.5 0.3 3 0.4 4.5 0.1 0.7 0.1 1.5 0.2 2.2 0.2 1.5 0.3 3.1 0.5 4.6 0.1 0.6 0.1 1.3 0.2 1.9 0.2 2.2 0.5 4.3 0.8 6.4 0 0.1 0 0.3 0.1 0.4 0.3 2 0.5 4 0.8 6 0.1 0.7 0.2 1.3 0.3 2 0.2 1.5 0.4 3 0.7 4.5l0.3 2.1c0.3 1.5 0.5 3.1 0.8 4.6l0.3 1.8c0.4 2.1 0.8 4.2 1.2 6.4 0 0.2 0.1 0.4 0.1 0.5 0.4 1.9 0.8 3.8 1.2 5.8 0.1 0.6 0.3 1.3 0.4 1.9 0.3 1.5 0.6 3 1 4.5 0.2 0.7 0.3 1.4 0.5 2 0.4 1.6 0.7 3.1 1.1 4.7 0.1 0.5 0.3 1.1 0.4 1.6 0.5 2.1 1 4.2 1.6 6.3 0 0.2 0.1 0.4 0.1 0.5 0.5 1.9 1 3.8 1.5 5.6 0.2 0.6 0.3 1.2 0.5 1.8 0.4 1.5 0.8 3 1.3 4.4 0.2 0.6 0.4 1.3 0.6 1.9 0.5 1.6 0.9 3.1 1.4 4.7 0.1 0.5 0.3 0.9 0.4 1.4 0.6 2.1 1.3 4.1 1.9 6.1 0.1 0.2 0.1 0.3 0.2 0.5 0.6 1.8 1.2 3.7 1.8 5.5 0.2 0.6 0.4 1.1 0.6 1.7 0.5 1.5 1 3 1.6 4.4 0.2 0.6 0.4 1.2 0.6 1.7l1.8 4.8c0.1 0.4 0.3 0.8 0.4 1.2 0.8 2 1.5 4 2.3 6 0.1 0.1 0.1 0.3 0.2 0.4 0.7 1.8 1.5 3.6 2.2 5.4l0.6 1.5c0.6 1.5 1.2 3 1.9 4.5 0.2 0.5 0.4 1 0.7 1.6 0.7 1.6 1.4 3.3 2.2 4.9 0.1 0.3 0.3 0.6 0.4 0.9 0.9 2 1.8 4 2.7 5.9 0.1 0.1 0.1 0.2 0.2 0.3 0.8 1.8 1.7 3.6 2.6 5.3 0.2 0.5 0.4 0.9 0.7 1.3 0.7 1.5 1.5 3 2.2 4.5 0.2 0.5 0.5 0.9 0.7 1.4 0.8 1.7 1.7 3.3 2.6 5l0.3 0.6c1 1.9 2.1 3.9 3.1 5.8 0 0.1 0.1 0.2 0.1 0.2 1 1.8 1.9 3.5 2.9 5.3 0.2 0.4 0.5 0.8 0.7 1.2 0.8 1.5 1.7 3 2.6 4.4 0.2 0.4 0.5 0.8 0.7 1.2l3 5.1c0.1 0.1 0.1 0.2 0.2 0.3 1.2 1.9 2.3 3.8 3.5 5.7l0.1 0.1c1.1 1.7 2.2 3.5 3.3 5.2 0.2 0.3 0.4 0.7 0.7 1 1 1.5 1.9 3 2.9 4.4 0.2 0.3 0.4 0.7 0.7 1 1.2 1.7 2.3 3.5 3.5 5.2 1.3 1.8 2.6 3.7 3.9 5.5v0.1c1.2 1.7 2.4 3.4 3.7 5.1l0.6 0.9c1.1 1.5 2.2 2.9 3.3 4.4 0.2 0.3 0.4 0.5 0.6 0.8 3.9 5.2 8 10.2 12.1 15.2 0.2 0.2 0.4 0.5 0.6 0.7 1.2 1.4 2.4 2.9 3.7 4.3l0.6 0.6c4.2 4.9 8.6 9.8 13 14.5l0.6 0.6c1.3 1.4 2.7 2.8 4 4.2l0.5 0.5c4.6 4.7 9.2 9.3 14 13.8 0.2 0.2 0.4 0.4 0.6 0.5 1.4 1.4 2.9 2.7 4.3 4l0.4 0.4c4.8 4.4 9.8 8.8 14.8 13 0.2 0.2 0.4 0.3 0.5 0.5 1.5 1.3 3.1 2.6 4.7 3.8 0.1 0.1 0.2 0.2 0.4 0.3 5.1 4.1 10.3 8.2 15.6 12.1 0.2 0.1 0.4 0.3 0.6 0.4 1.6 1.2 3.3 2.4 4.9 3.6 0.1 0.1 0.2 0.2 0.3 0.2 31.8 22.8 66.8 41.5 104.1 55.4 0.6 0.2 1.2 0.4 1.8 0.7 1 0.4 2 0.7 2.9 1.1 0.7 0.2 1.3 0.5 2 0.7 0.9 0.3 1.9 0.7 2.9 1 0.7 0.2 1.4 0.5 2.1 0.7 0.9 0.3 1.9 0.6 2.8 0.9 0.7 0.2 1.4 0.5 2.2 0.7 0.9 0.3 1.9 0.6 2.8 0.9 0.7 0.2 1.5 0.5 2.2 0.7 0.9 0.3 1.9 0.6 2.8 0.9 0.7 0.2 1.5 0.4 2.2 0.7 0.9 0.3 1.9 0.6 2.8 0.8 0.7 0.2 1.5 0.4 2.2 0.7 0.9 0.3 1.9 0.5 2.8 0.8 0.7 0.2 1.5 0.4 2.2 0.6 0.9 0.3 1.9 0.5 2.8 0.8l2.1 0.6c1.9 0.5 3.8 1 5.6 1.4 0.7 0.2 1.5 0.4 2.2 0.5l2.7 0.6c0.8 0.2 1.6 0.4 2.5 0.6l2.7 0.6c0.8 0.2 1.7 0.4 2.5 0.6l2.7 0.6c0.9 0.2 1.7 0.4 2.6 0.5 0.9 0.2 1.8 0.4 2.6 0.5 0.9 0.2 1.7 0.3 2.6 0.5 0.9 0.2 1.7 0.3 2.6 0.5 0.9 0.2 1.8 0.3 2.7 0.5 0.9 0.2 1.7 0.3 2.6 0.5 0.9 0.2 1.8 0.3 2.7 0.5 0.8 0.1 1.7 0.3 2.5 0.4 0.9 0.2 1.9 0.3 2.9 0.5 0.8 0.1 1.6 0.3 2.4 0.4 1 0.2 2.1 0.3 3.1 0.5 0.7 0.1 1.5 0.2 2.2 0.3 1.4 0.2 2.7 0.4 4.1 0.5 0.6 0.1 1.2 0.2 1.8 0.2 1.6 0.2 3.2 0.4 4.7 0.6 0.7 0.1 1.3 0.1 2 0.2 1.1 0.1 2.3 0.2 3.4 0.4 0.8 0.1 1.5 0.1 2.3 0.2 1.1 0.1 2.1 0.2 3.2 0.3 0.8 0.1 1.6 0.1 2.4 0.2 1 0.1 2.1 0.2 3.1 0.3 0.8 0.1 1.6 0.1 2.4 0.2 1 0.1 2 0.1 3.1 0.2 0.8 0.1 1.6 0.1 2.4 0.2 1 0.1 2.1 0.1 3.1 0.2 0.8 0 1.6 0.1 2.4 0.1 1.1 0.1 2.1 0.1 3.2 0.1 0.8 0 1.6 0.1 2.3 0.1 1.1 0 2.2 0.1 3.3 0.1 0.7 0 1.5 0 2.2 0.1 1.2 0 2.5 0.1 3.7 0.1h14.7c1.2 0 2.5 0 3.7-0.1 0.7 0 1.5 0 2.2-0.1 1.1 0 2.2-0.1 3.3-0.1 0.8 0 1.6-0.1 2.4-0.1 1 0 2.1-0.1 3.1-0.1 0.8 0 1.6-0.1 2.5-0.1 1-0.1 2-0.1 3-0.2 0.8-0.1 1.7-0.1 2.5-0.2 1-0.1 1.9-0.1 2.9-0.2 0.9-0.1 1.7-0.1 2.6-0.2 1-0.1 1.9-0.2 2.9-0.2 0.9-0.1 1.7-0.1 2.6-0.2 0.9-0.1 1.9-0.2 2.8-0.3l2.7-0.3 2.7-0.3 2.7-0.3c0.9-0.1 1.7-0.2 2.6-0.3 0.9-0.1 1.9-0.2 2.8-0.3 0.4 0 0.7-0.1 1.1-0.1 2.5-0.3 5-0.7 7.5-1l1.8-0.3c1.1-0.2 2.2-0.3 3.3-0.5 0.7-0.1 1.3-0.2 2-0.3l3.3-0.6c0.7-0.1 1.3-0.2 2-0.3l3.3-0.6c0.6-0.1 1.3-0.2 1.9-0.4l3.3-0.6c0.6-0.1 1.2-0.2 1.8-0.4 1.1-0.2 2.3-0.5 3.4-0.7 0.6-0.1 1.1-0.2 1.7-0.4 1.2-0.2 2.3-0.5 3.5-0.8l1.5-0.3c1.2-0.3 2.4-0.6 3.6-0.8l1.2-0.3c1.3-0.3 2.6-0.6 4-1 0.2 0 0.4-0.1 0.6-0.1 8-2 15.9-4.2 23.8-6.6 0.1 0 0.1 0 0.2-0.1 1.5-0.4 2.9-0.9 4.4-1.4 0.2-0.1 0.4-0.1 0.6-0.2 1.4-0.4 2.8-0.9 4.1-1.4 0.3-0.1 0.5-0.2 0.8-0.2 1.4-0.5 2.7-0.9 4.1-1.4 0.3-0.1 0.5-0.2 0.8-0.3 1.4-0.5 2.7-0.9 4.1-1.4 0.2-0.1 0.4-0.2 0.7-0.2l4.2-1.5c0.2-0.1 0.3-0.1 0.5-0.2 1.5-0.5 2.9-1.1 4.4-1.7 33.9-13 65.8-30 95.1-50.4 0.5-0.4 1-0.7 1.5-1.1 1.2-0.8 2.4-1.7 3.6-2.5 0.6-0.4 1.2-0.8 1.7-1.2 1.2-0.8 2.3-1.7 3.5-2.6 0.5-0.4 1-0.7 1.5-1.1 3.3-2.5 6.6-5 9.8-7.5l1.5-1.2c1.1-0.9 2.3-1.8 3.4-2.7 0.6-0.4 1.1-0.9 1.7-1.3 1.1-0.9 2.2-1.8 3.3-2.8 0.5-0.4 1-0.8 1.4-1.2 3.1-2.6 6.2-5.3 9.3-8.1 0.5-0.4 0.9-0.8 1.4-1.3 1.1-1 2.1-1.9 3.2-2.9 0.5-0.5 1-1 1.6-1.4 1.1-1 2.1-2 3.2-3 0.5-0.4 0.9-0.9 1.4-1.3 3-2.8 5.9-5.7 8.8-8.6-0.7 0.1-0.3-0.3 0.2-0.8zM181.6 372.2c18-42.6 43.9-80.9 76.8-113.8 32.9-32.9 71.2-58.7 113.8-76.8 44.1-18.6 91-28.1 139.3-28.1s95.2 9.5 139.3 28.1c42.6 18 80.9 43.9 113.8 76.8 32.9 32.9 58.7 71.2 76.8 113.8 18.6 44.1 28.1 91 28.1 139.3s-9.5 95.2-28.1 139.3c-18 42.6-43.9 80.9-76.8 113.8-1.8 1.8-3.7 3.6-5.6 5.4-13.7-68.6-74.6-120.7-147.1-120.7H412c-72.7 0-133.8 52.5-147.2 121.5-2.1-2-4.3-4.1-6.4-6.2-32.9-32.9-58.7-71.2-76.8-113.8-18.6-44.1-28.1-91-28.1-139.3s9.5-95.2 28.1-139.3z m577.5 397.9c-1 1-2 1.9-3.1 2.9 1.1-0.9 2.1-1.9 3.1-2.9z m-5.6 5.3c-1.1 1-2.1 1.9-3.2 2.9 1-1 2.1-1.9 3.2-2.9z m-5.7 5.1c-1.1 1-2.2 1.9-3.3 2.9 1.1-1 2.2-1.9 3.3-2.9z m-5.6 4.8c-2.2 1.9-4.4 3.7-6.7 5.5 2.2-1.8 4.5-3.6 6.7-5.5z m-8.7 7.1c-1.3 1-2.5 2-3.8 3 1.3-1 2.6-2 3.8-3z m-5.9 4.6c-1.3 1-2.6 2-4 3 1.4-1 2.7-2 4-3z m-5.7 4.2c-4.7 3.4-9.5 6.8-14.4 10 4.9-3.2 9.7-6.5 14.4-10z m-416 3.5c-1.3-0.9-2.6-1.8-3.9-2.8 1.4 1 2.7 1.9 3.9 2.8z m-6.3-4.6c-1.2-0.9-2.3-1.7-3.5-2.6 1.2 0.9 2.4 1.8 3.5 2.6z m-6.4-4.8l-3-2.4 3 2.4z m-8.5-6.8c-0.8-0.7-1.6-1.3-2.4-2 0.8 0.7 1.5 1.3 2.4 2z m-6.1-5.1l-2.4-2.1 2.4 2.1z m-6.1-5.4c-0.7-0.6-1.4-1.3-2.1-1.9 0.6 0.6 1.3 1.3 2.1 1.9z m99.7 63.4c-2.2-0.9-4.4-1.9-6.5-2.9-1-0.4-2-0.9-3-1.3-0.8-0.4-1.7-0.8-2.5-1.2-2.7-1.3-5.5-2.6-8.2-3.9-7.9-4-15.7-8.2-23.3-12.7 7.6 4.5 15.4 8.8 23.3 12.7v-32.7c0-15.9 6.3-30.9 17.7-42.3 11.4-11.4 26.4-17.7 42.3-17.7h200c15.9 0 30.9 6.3 42.3 17.7 11.4 11.4 17.7 26.4 17.7 42.3v32.2c-2.8 1.4-5.6 2.8-8.5 4.1-1 0.5-2 1-3.1 1.4-1 0.4-1.9 0.9-2.9 1.3-2.2 1-4.4 2-6.7 2.9-44.1 18.6-91 28.1-139.3 28.1s-95.2-9.3-139.3-28z m-64.9-35.8c3.3 2.3 6.6 4.5 9.9 6.7-3.3-2.2-6.6-4.4-9.9-6.7z m9.9 6.7c1.7 1.1 3.3 2.1 5 3.2-1.6-1.1-3.3-2.1-5-3.2z m6.1 3.8c1.7 1.1 3.5 2.1 5.2 3.2-1.7-1-3.5-2.1-5.2-3.2z m383.1-4.2c-1.6 1-3.2 2.1-4.8 3.1 1.6-1 3.2-2 4.8-3.1z m-6 3.8c-7.2 4.5-14.6 8.7-22.1 12.7 7.6-4 14.9-8.2 22.1-12.7z m-22.8 13.1c-1.9 1-3.7 1.9-5.6 2.9 1.9-1 3.7-2 5.6-2.9z" fill="currentColor"/>
                            </svg>
                            <span x-show="sidebarOpen">Customers</span>
                        </div>
                        <svg x-show="sidebarOpen" class="ml-auto h-5 w-5 transform transition-transform duration-200" :class="{'rotate-90': open}" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                        </svg>
                    </button>
                    <div x-show="open" class="space-y-1" :class="{'pl-8': sidebarOpen, 'pl-2': !sidebarOpen}">
                        <a href="{{ route('customers.index') }}" 
                           class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('customers.*') ? 'bg-gray-100 text-gray-900' : '' }}"
                           :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                            </svg>
                            <span x-show="sidebarOpen">All Customers</span>
                        </a>
                        <a href="{{ route('prescriptions.index') }}" 
                           class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('prescriptions.*') ? 'bg-gray-100 text-gray-900' : '' }}"
                           :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                            <span x-show="sidebarOpen">Prescriptions</span>
                        </a>                        
                        <a href="{{ route('customers.loyalty') }}" 
                           class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('customers.loyalty') ? 'bg-gray-100 text-gray-900' : '' }}"
                           :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <span x-show="sidebarOpen">Loyalty Program</span>
                        </a>
                    </div>
                </div>
                @endcan

                <!-- Reports -->
                @can('view reports')
                <div x-data="{ open: {{ request()->segment(1) === 'reports' ? 'true' : 'false' }} }"
                     x-init="$watch('open', value => { if(value) $dispatch('menu-expanded', 'reports') })"
                     @menu-expanded.window="if($event.detail !== 'reports') open = false"
                     class="space-y-1">
                    <button @click="open = !open" class="w-full group flex items-center px-2 py-2 text-base leading-6 font-medium rounded-md text-gray-900 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150" :class="{'justify-center': !sidebarOpen}">
                        <div class="flex items-center" :class="{'justify-center w-full': !sidebarOpen}">
                            <svg class="h-7 w-7 text-gray-500" :class="{'mr-2': sidebarOpen, 'mr-0': !sidebarOpen}" style="vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
                                <path d="M912.6912 861.8496h-798.72c-16.9472 0-30.72 13.7728-30.72 30.72s13.7728 30.72 30.72 30.72h798.72c16.9472 0 30.72-13.7728 30.72-30.72s-13.7728-30.72-30.72-30.72zM707.584 788.5312h85.248c31.0784 0 56.3712-25.2928 56.3712-56.3712V178.2272c0-31.0784-25.2928-56.3712-56.3712-56.3712H707.584c-31.0784 0-56.3712 25.2928-56.3712 56.3712v553.984c0.0512 31.0784 25.344 56.32 56.3712 56.32zM468.1216 788.5312h85.248c31.0784 0 56.3712-25.2928 56.3712-56.3712V344.6784c0-31.0784-25.2928-56.3712-56.3712-56.3712H468.1216c-31.0784 0-56.3712 25.2928-56.3712 56.3712v387.5328c0.0512 31.0784 25.2928 56.32 56.3712 56.32zM210.432 788.5312h85.248c31.0784 0 56.3712-25.2928 56.3712-56.3712V422.0928c0-31.0784-25.2928-56.3712-56.3712-56.3712H210.432c-31.0784 0-56.3712 25.2928-56.3712 56.3712v310.1184c0 31.0784 25.2928 56.32 56.3712 56.32z" />
                            </svg>
                            <span x-show="sidebarOpen">Reports</span>
                        </div>
                        <svg x-show="sidebarOpen" class="ml-auto h-5 w-5 transform transition-transform duration-200" :class="{'rotate-90': open}" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                        </svg>
                    </button>
                    <div x-show="open" class="space-y-1" :class="{'pl-8': sidebarOpen, 'pl-2': !sidebarOpen}">
                        <a href="{{ route('reports.sales') }}" class="group flex items-center px-2 py-2 text-sm leading-5 font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150" :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <span x-show="sidebarOpen">Sales Reports</span>
                        </a>
                        <a href="{{ route('reports.due') }}" class="group flex items-center px-2 py-2 text-sm leading-5 font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150" :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span x-show="sidebarOpen">Due Reports</span>
                        </a>
                        <a href="{{ route('reports.inventory') }}" class="group flex items-center px-2 py-2 text-sm leading-5 font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150" :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                            </svg>
                            <span x-show="sidebarOpen">Inventory Reports</span>
                        </a>
                        <a href="{{ route('reports.financial') }}" class="group flex items-center px-2 py-2 text-sm leading-5 font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('reports.financial') ? 'bg-gray-100 text-gray-900' : '' }}" :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                            </svg>
                            <span x-show="sidebarOpen">Financial Reports</span>
                        </a>
                        <!-- Add Supplier Payments Report Link -->
                        <a href="{{ route('reports.supplier-payments') }}" class="group flex items-center px-2 py-2 text-sm leading-5 font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('reports.supplier-payments') ? 'bg-gray-100 text-gray-900' : '' }}" :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            <span x-show="sidebarOpen">Supplier Payments</span>
                        </a>
                        <!-- Add Profit & Loss Report Link -->
                        <a href="{{ route('reports.profit-loss') }}" class="group flex items-center px-2 py-2 text-sm leading-5 font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('reports.profit-loss') ? 'bg-gray-100 text-gray-900' : '' }}" :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                            <span x-show="sidebarOpen">Profit & Loss</span>
                        </a>
                    </div>
                </div>
                @endcan

                <!-- User Management -->
                @can('manage users')
                <div x-data="{ open: {{ request()->segment(1) === 'users' || request()->segment(1) === 'roles' ? 'true' : 'false' }} }"
                     x-init="$watch('open', value => { if(value) $dispatch('menu-expanded', 'users') })"
                     @menu-expanded.window="if($event.detail !== 'users') open = false"
                     class="space-y-1">
                    <button @click="open = !open" class="w-full group flex items-center px-2 py-2 text-base leading-6 font-medium rounded-md text-gray-900 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150" :class="{'justify-center': !sidebarOpen}">
                        <div class="flex items-center" :class="{'justify-center w-full': !sidebarOpen}">
                            <svg class="h-7 w-7 text-gray-500" :class="{'mr-2': sidebarOpen, 'mr-0': !sidebarOpen}" style="vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 392.95 414.08" version="1.1" xmlns="http://www.w3.org/2000/svg">
                            <path d="M187.05,357.01c-36.64-19.49-36.25-68.06,0-87.87-3.89-11.9-2.96-23.47,1.09-35.16-36.59,1.89-75.11-5.56-109.91,7.93C33.98,259.06,3.18,302.13.19,349.4c-.2,3.18-.31,7.83,0,10.95,2.35,23.68,25.86,53.73,51.22,53.73h155.64l-10.45-8.94c-11.63-13.67-15.05-30.98-9.54-48.13Z"/>
                            <path d="M274.84,149.06C312.99,78.28,256.68-6.7,177.01.42,105.73,6.79,63.52,84.79,97.13,148.41c37.33,70.65,139.79,70.99,177.7.65Z"/>
                            <path d="M384.76,299.44c-6.75-3.64-14.35-.63-21.64-2.23l-9.47-22.41c7.48-9.05,22.63-19.97,10.03-32.88-11.81-12.11-24.77.86-32.96,9.85-6.92-4-14.21-7.41-22.13-8.96-.53-4.68.35-9.66-.11-14.32-1.28-12.83-13.65-20.81-25.19-13.28-9.99,6.52-5.72,17.62-6.72,27.6-8.46.96-15.51,5.64-23.13,8.89-4.95-3.53-8.68-10.08-14.17-12.67-15.13-7.13-29.27,9.54-19.72,23.71,1.81,2.69,11.39,10.2,11.21,12.32-3.83,7.12-7.78,14.33-8.89,22.48-11.87-.53-29.53-1.68-30.69,14.64-1.17,16.47,18.49,18.89,30.69,17.4,1.69,7.6,4.4,15.08,8.92,21.48.31,1.9-9.49,9.73-11.24,12.32-10.36,15.36,6.98,33.24,21.82,22.83,2.33-1.63,10.59-11.57,11.91-11.68l22.42,9.15c2.07,1.54.54,13.54.95,16.96,1.39,11.6,15.77,17.29,25.23,10.28,8.49-6.29,6.38-18.48,6.65-27.67,8.15-.7,15.25-5.16,22.35-8.72,8.64,8.71,19.69,22.33,32.34,10.2,12.85-12.32-.82-24.19-9.7-32.86,3.27-7.26,8.17-14.12,8.72-22.36,9.19-.39,20.54,2.01,27.06-6.26,6.2-7.87,4.43-19-4.52-23.84ZM292.07,353.81c-50.76-.4-53.28-74.4-6.71-80.77,59.34-8.12,63,81.22,6.71,80.77Z"/>
                            </svg>
                            <span x-show="sidebarOpen">Staff Management</span>
                        </div>
                        <svg x-show="sidebarOpen" class="ml-auto h-5 w-5 transform transition-transform duration-200" :class="{'rotate-90': open}" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                        </svg>
                    </button>
                    <div x-show="open" class="space-y-1" :class="{'pl-8': sidebarOpen, 'pl-2': !sidebarOpen}">
                        <a href="{{ route('users.index') }}" class="group flex items-center px-2 py-2 text-sm leading-5 font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150" :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"/>
                            </svg>
                            <span x-show="sidebarOpen">All Staff</span>
                        </a>
                        <a href="{{ route('users.create') }}" class="group flex items-center px-2 py-2 text-sm leading-5 font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150" :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                            </svg>
                            <span x-show="sidebarOpen">Add New Staff</span>
                        </a>
                        <a href="{{ route('roles.index') }}" class="group flex items-center px-2 py-2 text-sm leading-5 font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150" :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                            </svg>
                            <span x-show="sidebarOpen">Roles & Permissions</span>
                        </a>
                    </div>
                </div>
                @endcan

                <!-- Settings -->
                @can('manage settings')
                <div x-data="{ open: {{ request()->segment(1) === 'settings' ? 'true' : 'false' }} }"
                     x-init="$watch('open', value => { if(value) $dispatch('menu-expanded', 'settings') })"
                     @menu-expanded.window="if($event.detail !== 'settings') open = false"
                     class="space-y-1">
                    <button @click="open = !open" class="w-full group flex items-center px-2 py-2 text-base leading-6 font-medium rounded-md text-gray-900 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150" :class="{'justify-center': !sidebarOpen}">
                        <div class="flex items-center" :class="{'justify-center w-full': !sidebarOpen}">
                            <svg class="h-7 w-7 text-gray-500" :class="{'mr-2': sidebarOpen, 'mr-0': !sidebarOpen}" style="vertical-align: middle;fill: currentColor;overflow: hidden;" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1030 1030">
                                <path d="M512 42.666667c35.072 0 69.248 3.84 102.122667 11.136l15.210666 39.786666a170.666667 170.666667 0 0 0 186.346667 107.584l41.941333-6.698666a468.842667 468.842667 0 0 1 102.250667 176.853333l-26.858667 33.066667a170.666667 170.666667 0 0 0 0 215.189333l26.88 33.109333a468.842667 468.842667 0 0 1-102.272 176.832l-41.941333-6.698666a170.666667 170.666667 0 0 0-186.346667 107.584l-15.210666 39.786666A470.997333 470.997333 0 0 1 512 981.333333c-35.072 0-69.248-3.84-102.122667-11.136l-15.210666-39.786666a170.666667 170.666667 0 0 0-186.346667-107.584l-41.941333 6.698666a468.842667 468.842667 0 0 1-102.250667-176.853333l26.858667-33.066667a170.666667 170.666667 0 0 0 0-215.189333l-26.88-33.109333A468.842667 468.842667 0 0 1 166.4 194.474667l41.941333 6.698666A170.666667 170.666667 0 0 0 394.666667 93.589333l15.210666-39.786666A470.997333 470.997333 0 0 1 512 42.666667z m0 256c-117.824 0-213.333333 95.509333-213.333333 213.333333s95.509333 213.333333 213.333333 213.333333 213.333333-95.509333 213.333333-213.333333-95.509333-213.333333-213.333333-213.333333z m0 85.333333a128 128 0 1 1 0 256 128 128 0 0 1 0-256z" />
                            </svg>
                            <span x-show="sidebarOpen">Settings</span>
                        </div>
                        <svg x-show="sidebarOpen" class="ml-auto h-5 w-5 transform transition-transform duration-200" :class="{'rotate-90': open}" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                        </svg>
                    </button>
                    <div x-show="open" class="space-y-1" :class="{'pl-8': sidebarOpen, 'pl-2': !sidebarOpen}">
                        <a href="{{ route('settings.general') }}" class="group flex items-center px-2 py-2 text-sm leading-5 font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('settings.general') ? 'bg-gray-100 text-gray-900' : '' }}" :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c-.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            <span x-show="sidebarOpen">General Settings</span>
                        </a>
                        <a href="{{ route('settings.payment') }}" class="group flex items-center px-2 py-2 text-sm leading-5 font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('settings.payment') ? 'bg-gray-100 text-gray-900' : '' }}" :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                            </svg>
                            <span x-show="sidebarOpen">Payment Methods</span>
                        </a>
                        <a href="{{ route('settings.tax') }}" class="group flex items-center px-2 py-2 text-sm leading-5 font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('settings.tax') ? 'bg-gray-100 text-gray-900' : '' }}" :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2z"/>
                            </svg>
                            <span x-show="sidebarOpen">Tax Settings</span>
                        </a>
                        <a href="{{ route('settings.notifications') }}" class="group flex items-center px-2 py-2 text-sm leading-5 font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('settings.notifications') ? 'bg-gray-100 text-gray-900' : '' }}" :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                            </svg>
                            <span x-show="sidebarOpen">Email & SMS</span>
                        </a>
                        <a href="{{ route('settings.backup') }}" class="group flex items-center px-2 py-2 text-sm leading-5 font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('settings.backup') ? 'bg-gray-100 text-gray-900' : '' }}" :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" style="vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
                                <path d="M512 960c-212.032 0-384-57.376-384-128 0-38.624 0-80.864 0-128 0-11.136 5.568-21.696 13.568-32 42.816 55.136 191.68 96 370.432 96s327.616-40.864 370.432-96c8 10.304 13.568 20.864 13.568 32 0 37.056 0 76.384 0 128 0 70.624-172 128-384 128zM512 704c-212.032 0-384-57.376-384-128 0-38.656 0-80.832 0-128 0-6.784 2.56-13.376 6.016-19.904l0 0c1.952-4.096 4.512-8.128 7.552-12.096 42.816 55.104 191.68 96 370.432 96s327.616-40.896 370.432-96c3.072 3.968 5.632 8 7.552 12.096l0 0c3.424 6.528 6.016 13.12 6.016 19.904 0 37.056 0 76.352 0 128 0 70.624-172 128-384 128zM512 448c-212.032 0-384-57.344-384-128 0-20.224 0-41.6 0-64 0-20.352 0-41.472 0-64 0-70.656 171.968-128 384-128s384 57.344 384 128c0 19.968 0 41.152 0 64 0 19.584 0 40.256 0 64 0 70.656-172 128-384 128zM512 128c-141.376 0-256 28.608-256 64s114.624 64 256 64 256-28.608 256-64-114.624-64-256-64z" />
                            </svg>
                            <span x-show="sidebarOpen">Backup & Restore</span>
                        </a>
                        <a href="{{ route('settings.security') }}" class="group flex items-center px-2 py-2 text-sm leading-5 font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('settings.security') ? 'bg-gray-100 text-gray-900' : '' }}" :class="{'justify-center': !sidebarOpen}">
                            <svg class="h-5 w-5 text-gray-500" :class="{'mr-3': sidebarOpen, 'mr-0': !sidebarOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                            </svg>
                            <span x-show="sidebarOpen">Security Settings</span>
                        </a>
                    </div>
                </div>
                @endcan
            </nav>

            <!-- User Profile Section - Bottom of Sidebar -->
            <div class="sidebar-profile-section mt-auto border-t border-gray-200 bg-gray-50">
                <div class="p-3 relative" x-data="{ profileOpen: false }"
                     @click.away="profileOpen = false">
                    <!-- User Info Display -->
                    <div class="flex items-center" :class="{'justify-center': !sidebarOpen}">
                        <!-- User Avatar - Clickable in collapsed mode -->
                        <button @click="profileOpen = !profileOpen"
                                class="flex-shrink-0 focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded-full transition-colors duration-200"
                                :class="{'hover:bg-gray-200 p-1': !sidebarOpen}">
                            <div class="h-8 w-8 rounded-full bg-indigo-600 flex items-center justify-center">
                                <span class="text-sm font-medium text-white">
                                    {{ strtoupper(substr(Auth::user()->name, 0, 1)) }}
                                </span>
                            </div>
                        </button>

                        <!-- User Name and Actions (shown when sidebar is open) -->
                        <div x-show="sidebarOpen" class="ml-3 flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">
                                        {{ Auth::user()->name }}
                                    </p>
                                    <p class="text-xs text-gray-500 truncate">
                                        {{ Auth::user()->email }}
                                    </p>
                                </div>

                                <!-- Profile Actions Button -->
                                <button @click="profileOpen = !profileOpen"
                                        class="ml-2 p-1 rounded-full hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors duration-200"
                                        :class="{'rotate-180': profileOpen}">
                                    <svg class="h-4 w-4 text-gray-500 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Profile Actions Dropdown - Works for both expanded and collapsed modes -->
                    <div x-show="profileOpen"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform translate-y-2 scale-95"
                         x-transition:enter-end="opacity-100 transform translate-y-0 scale-100"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0 scale-100"
                         x-transition:leave-end="opacity-0 transform translate-y-2 scale-95"
                         class="absolute bottom-full mb-2 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-50"
                         :class="{
                             'left-0 right-0': sidebarOpen,
                             'left-1/2 transform -translate-x-1/2': !sidebarOpen,
                             'min-w-[200px]': sidebarOpen,
                             'w-auto': !sidebarOpen
                         }">

                        <!-- Expanded Mode Content -->
                        <div x-show="sidebarOpen">
                            <!-- Profile Link -->
                            <a href="{{ route('profile') }}"
                               class="flex items-center px-4 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-gray-50 transition-colors duration-150 {{ request()->routeIs('profile') ? 'bg-gray-50 text-gray-900' : '' }}">
                                <svg class="h-4 w-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                {{ __('My Profile') }}
                            </a>

                            <!-- Logout Button -->
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit"
                                        class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-gray-50 transition-colors duration-150">
                                    <svg class="h-4 w-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                    </svg>
                                    {{ __('Sign out') }}
                                </button>
                            </form>
                        </div>

                        <!-- Collapsed Mode Content - Icons Only (Vertical) -->
                        <div x-show="!sidebarOpen" class="space-y-1 px-1 py-1">
                            <!-- Profile Link - Icon Only -->
                            <a href="{{ route('profile') }}"
                               class="flex items-center justify-center p-2 text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 transition-colors duration-150 {{ request()->routeIs('profile') ? 'bg-gray-50 text-gray-900' : '' }}"
                               title="{{ __('My Profile') }}">
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                            </a>

                            <!-- Logout Button - Icon Only -->
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit"
                                        class="flex items-center justify-center w-full p-2 text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 transition-colors duration-150"
                                        title="{{ __('Sign out') }}">
                                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                    </svg>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="admin-main-content no-padding-transition">
            <!-- Top Navigation -->
            <nav class="bg-white shadow-sm flex-shrink-0">
                <div class="admin-nav-container">
                    <div class="flex items-center h-16 gap-2 md:gap-4">
                        <!-- Left side - Hamburger and Sync Status -->
                        <div class="flex items-center flex-shrink-0">
                            <button @click="toggleSidebar()"
                                    class="p-2 text-gray-500 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:text-gray-600 focus:bg-gray-100 rounded-md transition-all duration-300 min-w-[44px] min-h-[44px] flex items-center justify-center"
                                    :class="{'rotate-90': !sidebarOpen}"
                                    aria-label="Toggle sidebar">
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                                </svg>
                            </button>

                            <!-- Unified Status Indicator -->
                            <div class="ml-2 flex-shrink-0">
                                @include('components.unified-status')
                            </div>
                        </div>

                        <!-- Center - Medicine Search -->
                        <div class="flex-1 max-w-2xl mx-2 md:mx-4 medicine-search-wrapper">
                            @include('components.medicine-search')
                        </div>

                        <!-- Right side - Actions and User Menu -->
                        <div class="flex items-center space-x-2 md:space-x-4 flex-shrink-0">
                            <!-- New Sale Button -->
                            @can('view sales')
                            <a href="{{ route('sales.create') }}"
                               class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
                               title="Create New Sale">
                                <svg class="mr-2 -ml-0.5 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                                </svg>
                                <span class="hidden sm:inline">New Sale</span>
                                <span class="sm:hidden sr-only">New Sale</span>
                            </a>
                            @endcan
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Page Content -->
            <main class="flex-1 admin-content-wrapper">
                <div class="w-full max-w-none min-h-0 px-4 py-6">
                    @if(isset($slot))
                        {{ $slot }}
                    @else
                        @yield('content')
                    @endif
                </div>
            </main>
        </div>
    </div>
    @livewireScripts
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    
    <!-- Add offline/sync status handling -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Create sync status elements if they don't exist
            let syncStatus = document.getElementById('syncStatus');
            let syncStatusText = document.getElementById('syncStatusText');
            
            if (!syncStatus) {
                syncStatus = document.createElement('div');
                syncStatus.id = 'syncStatus';
                syncStatus.style.display = 'none';
                document.body.appendChild(syncStatus);
            }
            
            if (!syncStatusText) {
                syncStatusText = document.createElement('span');
                syncStatusText.id = 'syncStatusText';
                syncStatus.appendChild(syncStatusText);
            }
            
            // Handle sync status updates
            window.addEventListener('sync-status-changed', (event) => {
                if (!event || !event.detail) return;
                
                try {
                    if (event.detail.syncing) {
                        console.log('Sync in progress');
                    } else if (event.detail.error) {
                        console.log('Sync error:', event.detail.errorMessage || 'Unknown error');
                        
                        // Show notification using Livewire if available
                        if (typeof Livewire !== 'undefined') {
                            Livewire.dispatch('notify', { 
                                type: 'error',
                                message: `Sync failed: ${event.detail.errorMessage || 'Unknown error'}`
                            });
                        }
                    } else {
                        console.log('Sync completed');
                    }
                } catch (e) {
                    console.warn('Error handling sync status event:', e);
                }
            });

            // Handle offline status
            window.addEventListener('connection-changed', (event) => {
                if (!event || !event.detail) return;
                
                try {
                    if (!event.detail.online) {
                        // Show offline notification using the existing notification system
                        if (typeof Livewire !== 'undefined') {
                            Livewire.dispatch('notify', { 
                                type: 'warning',
                                message: 'You are currently offline. Some features may be limited.'
                            });
                        }
                    }
                } catch (e) {
                    console.warn('Error handling connection change event:', e);
                }
            });
        });
    </script>

    <!-- Medicine Sync Progress Component -->
    @include('components.medicine-sync-progress')

    @stack('scripts')
</body>
</html>