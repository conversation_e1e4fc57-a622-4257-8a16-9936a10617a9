<!-- Dashboard -->
<a href="{{ route('dashboard') }}" 
   class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('dashboard') ? 'bg-gray-100 text-gray-900' : '' }}">
    <svg class="mr-3 h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
    </svg>
    Dashboard
</a>

<!-- Inventory -->
<div x-data="{ open: {{ request()->routeIs('inventory.*') ? 'true' : 'false' }} }" class="space-y-1">
    <button @click="open = !open" class="w-full group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150">
        <svg class="mr-3 h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
        </svg>
        <span class="flex-1 text-left">Inventory</span>
        <svg class="ml-2 h-5 w-5 transform transition-transform duration-200" :class="{'rotate-90': open}" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
        </svg>
    </button>
    <div x-show="open" class="pl-4 space-y-1">
        <a href="{{ route('inventory.medicines.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.medicines.*') ? 'bg-gray-100 text-gray-900' : '' }}">
            Medicines
        </a>
        <a href="{{ route('inventory.manufacturers.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.manufacturers.*') ? 'bg-gray-100 text-gray-900' : '' }}">
            Manufacturers
        </a>
        <a href="{{ route('inventory.categories.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.categories.*') ? 'bg-gray-100 text-gray-900' : '' }}">
            Categories
        </a>
        <a href="{{ route('inventory.unit-types.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.unit-types.*') ? 'bg-gray-100 text-gray-900' : '' }}">
            Unit Types
        </a>
        <a href="{{ route('inventory.stock.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.stock.*') ? 'bg-gray-100 text-gray-900' : '' }}">
            Stock Management
        </a>
        <a href="{{ route('inventory.stock.transfer') }}" class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.stock.transfer') ? 'bg-gray-100 text-gray-900' : '' }}">
            Stock Transfer
        </a>
        <a href="{{ route('inventory.locations.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.locations.*') ? 'bg-gray-100 text-gray-900' : '' }}">
            Locations
        </a>
        <a href="{{ route('inventory.suppliers.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.suppliers.*') ? 'bg-gray-100 text-gray-900' : '' }}">
            Suppliers
        </a>
        <a href="{{ route('inventory.purchases.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.purchases.*') ? 'bg-gray-100 text-gray-900' : '' }}">
            Purchase Orders
        </a>
        <a href="{{ route('inventory.batch.history') }}" class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.batch.*') ? 'bg-gray-100 text-gray-900' : '' }}">
            Batch History
        </a>
        <a href="{{ route('inventory.units.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('inventory.units.*') ? 'bg-gray-100 text-gray-900' : '' }}">
            Unit Conversions
        </a>
    </div>
</div>

<!-- Sales -->
<a href="{{ route('sales.index') }}" 
   class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('sales.*') ? 'bg-gray-100 text-gray-900' : '' }}">
    <svg class="mr-3 h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
    </svg>
    Sales
</a>

<!-- Customers -->
<a href="{{ route('customers.index') }}" 
   class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('customers.*') ? 'bg-gray-100 text-gray-900' : '' }}">
    <svg class="mr-3 h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
    </svg>
    Customers
</a>

<!-- Reports -->
<div x-data="{ open: {{ request()->routeIs('reports.*') ? 'true' : 'false' }} }" class="space-y-1">
    <button @click="open = !open" class="w-full group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150">
        <svg class="mr-3 h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
        </svg>
        <span class="flex-1 text-left">Reports</span>
        <svg class="ml-2 h-5 w-5 transform transition-transform duration-200" :class="{'rotate-90': open}" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
        </svg>
    </button>
    <div x-show="open" class="pl-4 space-y-1">
        <a href="{{ route('reports.sales') }}" class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('reports.sales') ? 'bg-gray-100 text-gray-900' : '' }}">
            Sales Reports
        </a>
        <a href="{{ route('reports.due') }}" class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('reports.due') ? 'bg-gray-100 text-gray-900' : '' }}">
            Due Reports
        </a>
        <a href="{{ route('reports.inventory') }}" class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('reports.inventory') ? 'bg-gray-100 text-gray-900' : '' }}">
            Inventory Reports
        </a>
        <a href="{{ route('reports.financial') }}" class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('reports.financial') ? 'bg-gray-100 text-gray-900' : '' }}">
            Financial Reports
        </a>
        <a href="{{ route('reports.profit-loss') }}" class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('reports.profit-loss') ? 'bg-gray-100 text-gray-900' : '' }}">
            Profit & Loss
        </a>
        <a href="{{ route('reports.supplier-payments') }}" class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('reports.supplier-payments') ? 'bg-gray-100 text-gray-900' : '' }}">
            Supplier Payments
        </a>
    </div>
</div>

<!-- Settings -->
<a href="{{ route('profile.edit') }}" 
   class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition ease-in-out duration-150 {{ request()->routeIs('profile.*') ? 'bg-gray-100 text-gray-900' : '' }}">
    <svg class="mr-3 h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
    </svg>
    Settings
</a> 