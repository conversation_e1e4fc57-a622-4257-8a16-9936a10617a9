<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'PharmaDek') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <style>
            body {
                font-family: 'Poppins', sans-serif;
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            }
            .login-container {
                box-shadow: 0 10px 25px rgba(86, 71, 229, 0.08);
                border-radius: 12px;
                overflow: hidden;
                background: white;
            }
            .login-header {
                background: #5647e5;
                color: white;
                padding: 1.5rem;
                text-align: center;
            }
            .pharmacy-accent {
                border-left: 4px solid #5647e5;
                padding-left: 1rem;
                margin: 1.5rem 0;
            }
            .btn-pharmacy {
                background-color: #5647e5;
                color: white;
                padding: 0.75rem 1.5rem;
                border-radius: 0.5rem;
                font-weight: 500;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }
            .btn-pharmacy:hover {
                background-color: #4a3cd6;
                transform: translateY(-1px);
                box-shadow: 0 5px 15px rgba(86, 71, 229, 0.2);
            }
            .btn-pharmacy::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
                transition: all 0.5s ease;
                z-index: 0;
            }
            .btn-pharmacy:hover::before {
                left: 100%;
            }
            .form-input {
                border: 1px solid #e2e8f0;
                border-radius: 0.5rem;
                padding: 0.75rem 1rem;
                width: 100%;
                transition: all 0.3s ease;
            }
            .form-input:focus {
                border-color: #5647e5;
                box-shadow: 0 0 0 3px rgba(86, 71, 229, 0.1);
                outline: none;
            }
            .remember-me {
                display: flex;
                align-items: center;
                font-size: 0.875rem;
                color: #64748b;
            }
            .remember-me input {
                margin-right: 0.5rem;
            }
            .forgot-password {
                color: #5647e5;
                text-decoration: none;
                font-size: 0.875rem;
                transition: all 0.3s ease;
            }
            .forgot-password:hover {
                text-decoration: underline;
            }
            .logo-container {
                width: 120px;
                height: 120px;
                margin: 0 auto;
                background-color: #5647e5;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1), inset 0 0 0 4px rgba(255, 255, 255, 0.2);
                padding: 0;
                overflow: hidden;
                position: relative;
                border: 4px solid white;
            }
            .logo-container img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                mask-image: radial-gradient(circle, black 100%, transparent 100%);
                -webkit-mask-image: radial-gradient(circle, black 100%, transparent 100%);
            }
        </style>
    </head>
    <body>
        <div class="min-h-screen flex flex-col items-center justify-center p-4">
            <div class="login-container w-full max-w-md">
                <div class="login-header">
                    <div class="logo-container">
                        <x-pharmadesk-logo />
                    </div>
                    <h1 class="mt-4 text-xl font-semibold">Pharmacy Management System</h1>
                </div>
                
                <div class="p-6">
                    <div class="pharmacy-accent">
                        <h2 class="text-xl font-medium text-gray-800">{{ request()->routeIs('login') ? 'Welcome Back' : 'Create Account' }}</h2>
                        <p class="text-gray-500 text-sm mt-1">{{ request()->routeIs('login') ? 'Login to access your dashboard' : 'Register to get started with PharmaDek' }}</p>
                    </div>
                    
                    {{ $slot }}
                </div>
            </div>
            
            <div class="mt-6 text-center text-gray-500 text-sm">
                &copy; {{ date('Y') }} PharmaDek. All rights reserved.
            </div>
        </div>
    </body>
</html>
