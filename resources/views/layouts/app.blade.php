<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <!-- PWA Meta Tags -->
        <meta name="theme-color" content="#4F46E5">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="default">
        <meta name="apple-mobile-web-app-title" content="PharmaDesk">
        <meta name="application-name" content="PharmaDesk">
        <meta name="description" content="Complete Pharmacy Management System">

        <!-- PWA Icons -->
        <link rel="manifest" href="/manifest.json">
        <!-- Apple Touch Icons -->
        <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png">
        <link rel="apple-touch-icon" sizes="144x144" href="/icons/icon-144x144.png">
        <link rel="apple-touch-icon" sizes="120x120" href="/icons/icon-128x128.png">
        <link rel="apple-touch-icon" sizes="114x114" href="/icons/icon-96x96.png">
        <link rel="apple-touch-icon" sizes="72x72" href="/icons/icon-72x72.png">
        <!-- Favicon -->
        <link rel="icon" type="image/png" sizes="192x192" href="/icons/icon-192x192.png">
        <link rel="icon" type="image/png" sizes="32x32" href="/icons/icon-72x72.png">
        <link rel="icon" type="image/png" sizes="16x16" href="/icons/icon-72x72.png">
        <!-- Microsoft Tile Icons -->
        <meta name="msapplication-TileImage" content="/icons/icon-144x144.png">
        <meta name="msapplication-TileColor" content="#4F46E5">

        <title>{{ config('app.name', 'PharmaDesk') }}</title>

        <!-- Legacy Offline Indicator Style - DEPRECATED -->
        <!-- This is now handled by the unified status component -->
        <style>
            .offline-indicator {
                display: none !important; /* Force hide - now handled by unified status */
            }
            @keyframes pulse {
                0% {
                    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
                }
                70% {
                    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
                }
                100% {
                    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
                }
            }
            /* Add subtle offline indication to the entire UI */
            body.offline {
                position: relative;
            }
            body.offline::after {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                height: 3px;
                background-color: #EF4444;
                z-index: 100;
                animation: pulseBar 2s infinite;
            }
            @keyframes pulseBar {
                0% {
                    opacity: 1;
                }
                50% {
                    opacity: 0.5;
                }
                100% {
                    opacity: 1;
                }
            }
        </style>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
        
        <!-- Livewire Styles -->
        @livewireStyles
        
        <!-- Initial offline check -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Check offline status on page load
                checkAndUpdateOfflineStatus();
                
                // Add event listeners for online/offline events
                window.addEventListener('online', function() {
                    document.body.classList.remove('offline');
                    console.log('Browser is online');
                    // Attempt to reload if we were previously offline
                    if (sessionStorage.getItem('wasOffline') === 'true') {
                        sessionStorage.removeItem('wasOffline');
                        // Only reload if we're not on the offline page
                        if (!window.location.pathname.includes('/offline')) {
                            console.log('Reloading page after returning online');
                            window.location.reload();
                        }
                    }
                });
                
                window.addEventListener('offline', function() {
                    document.body.classList.add('offline');
                    console.log('Browser is offline');
                    sessionStorage.setItem('wasOffline', 'true');
                    
                    // Legacy offline indicator handling - now handled by unified status component
                    // const indicator = document.getElementById('offline-indicator');
                    // if (indicator) {
                    //     indicator.style.display = 'flex';
                    // }
                });
                
                // Handle custom connection-changed events from app.js
                window.addEventListener('connection-changed', function(event) {
                    if (event.detail && event.detail.online === false) {
                        document.body.classList.add('offline');
                        sessionStorage.setItem('wasOffline', 'true');
                        
                        // Legacy offline indicator handling - now handled by unified status component
                        // const indicator = document.getElementById('offline-indicator');
                        // if (indicator) {
                        //     indicator.style.display = 'flex';
                        // }
                    } else {
                        document.body.classList.remove('offline');
                    }
                });
                
                // Check offline status periodically
                setInterval(checkAndUpdateOfflineStatus, 5000);
            });
            
            function checkAndUpdateOfflineStatus() {
                // Check if we're offline
                if (!navigator.onLine) {
                    document.body.classList.add('offline');
                    sessionStorage.setItem('wasOffline', 'true');
                    
                    // Legacy offline indicator handling - now handled by unified status component
                    // const indicator = document.getElementById('offline-indicator');
                    // if (indicator) {
                    //     indicator.style.display = 'flex';
                    // }
                } else {
                    document.body.classList.remove('offline');
                }
                
                // Additional check: try to fetch a small resource
                fetch('/manifest.json', { method: 'HEAD', cache: 'no-store' })
                    .catch(function() {
                        // If fetch fails, we're likely offline
                        document.body.classList.add('offline');
                        sessionStorage.setItem('wasOffline', 'true');
                        
                        // Legacy offline indicator handling - now handled by unified status component
                        // const indicator = document.getElementById('offline-indicator');
                        // if (indicator) {
                        //     indicator.style.display = 'flex';
                        // }
                    });
            }
        </script>
    </head>
    <body class="font-sans antialiased app-layout">
        <!-- Legacy Offline Indicator - DEPRECATED -->
        <!-- This is now handled by the unified status component in the navigation -->
        <!--
        <div class="offline-indicator" id="offline-indicator" data-testid="offline-indicator" onclick="window.location.href='/offline'">
            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636a9 9 0 010 12.728m0 0l-2.829-2.829m2.829 2.829L21 21M15.536 8.464a5 5 0 010 7.072m0 0l-2.829-2.829m-4.243 2.829a4.978 4.978 0 01-1.414-2.83m-1.414 5.658a9 9 0 01-2.167-9.238m7.824 2.167a1 1 0 111.414 1.414m-1.414-1.414L3 3m8.293 8.293l1.414 1.414" />
            </svg>
            <span>You're offline</span>
        </div>
        -->

        <div class="min-h-screen bg-gray-100 dark:bg-gray-900">
            <livewire:layout.navigation />

            <!-- Medicine Search Component (Mobile) -->
            <div class="bg-white dark:bg-gray-800 shadow p-2 md:hidden">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative medicine-search-wrapper">
                    @include('components.medicine-search')
                </div>
            </div>

            <!-- Page Heading -->
            @if (isset($header))
                <header class="bg-white dark:bg-gray-800 shadow">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        {{ $header }}
                    </div>
                </header>
            @endif

            <!-- Page Content -->
            <main>
                {{ $slot }}
            </main>
        </div>
        
        <!-- Livewire Scripts -->
        @livewireScripts
        
        <!-- Legacy offline detection script - DEPRECATED -->
        <!-- Offline status is now handled by the unified status component -->
        <script>
            // Initialize offline detection on page load
            if (!navigator.onLine) {
                document.body.classList.add('offline');
            }

            // Legacy offline indicator handling - now handled by unified status component
            // document.getElementById('offline-indicator').addEventListener('click', function() {
            //     window.location.href = '/offline';
            // });
        </script>
    </body>
</html>
