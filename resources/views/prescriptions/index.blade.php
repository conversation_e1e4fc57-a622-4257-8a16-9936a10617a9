@extends('layouts.admin')

@section('title', 'Prescriptions')

@section('content')
<div class="py-6">
    <div class="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="md:flex md:items-center md:justify-between mb-6">
            <div class="flex-1 min-w-0">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                    Prescriptions
                </h2>
                <p class="mt-1 text-sm text-gray-500">
                    View and manage prescription records
                </p>
            </div>
        </div>

        <!-- Success Message -->
        @if(session('success'))
        <div class="mb-6">
            <div class="rounded-md bg-green-50 p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-800">
                            {{ session('success') }}
                        </p>
                    </div>
                    <div class="ml-auto pl-3">
                        <div class="-mx-1.5 -my-1.5">
                            <button type="button" class="inline-flex rounded-md p-1.5 text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-600">
                                <span class="sr-only">Dismiss</span>
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Error Message -->
        @if(session('error'))
        <div class="mb-6">
            <div class="rounded-md bg-red-50 p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-red-800">
                            {{ session('error') }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Filters and Search -->
        <div class="bg-white shadow-sm rounded-lg mb-6">
            <div class="p-4">
                <form action="{{ route('prescriptions.index') }}" method="GET">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <!-- Search with Suggestions -->
                        <div x-data="{ 
                            searchTerm: '{{ request('search') }}',
                            searchResults: [],
                            loading: false,
                            open: false,
                            init() {
                                this.$watch('searchTerm', (value) => {
                                    this.searchPrescriptions(value);
                                });
                            },
                            async searchPrescriptions(query) {
                                if (query.length < 3) {
                                    this.searchResults = [];
                                    this.open = false;
                                    return;
                                }

                                this.loading = true;
                                try {
                                    const response = await fetch(`{{ route('prescriptions.index') }}?search=${encodeURIComponent(query)}&format=json`);
                                    if (!response.ok) throw new Error('Search failed');
                                    const data = await response.json();
                                    this.searchResults = data;
                                    this.open = true;
                                } catch (error) {
                                    console.error('Search error:', error);
                                    this.searchResults = [];
                                } finally {
                                    this.loading = false;
                                }
                            },
                            selectResult(result) {
                                this.searchTerm = result.customer_name;
                                this.open = false;
                                this.$refs.searchForm.submit();
                            }
                        }">
                            <label for="search" class="sr-only">Search prescriptions</label>
                            <div class="relative">
                                <input type="search" 
                                    name="search" 
                                    id="search" 
                                    x-model="searchTerm"
                                    @focus="searchTerm.length >= 3 && (open = true)"
                                    @click.away="open = false"
                                    class="block w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                                    placeholder="Search by customer, doctor or hospital...">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <template x-if="!loading">
                                        <button type="submit" class="p-1 focus:outline-none focus:shadow-outline">
                                            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                            </svg>
                                        </button>
                                    </template>
                                    <template x-if="loading">
                                        <svg class="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                    </template>
                                </div>

                                <!-- Search Results Dropdown -->
                                <div class="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md py-1 text-sm" 
                                    x-show="open && searchResults.length > 0" 
                                    x-transition:enter="transition ease-out duration-100" 
                                    x-transition:enter-start="transform opacity-0 scale-95" 
                                    x-transition:enter-end="transform opacity-100 scale-100" 
                                    x-transition:leave="transition ease-in duration-75" 
                                    x-transition:leave-start="transform opacity-100 scale-100" 
                                    x-transition:leave-end="transform opacity-0 scale-95"
                                    style="display: none; max-height: 300px; overflow-y: auto;">
                                    <template x-for="result in searchResults" :key="result.id">
                                        <a href="#" 
                                            class="block px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                            @click.prevent="selectResult(result)">
                                            <div class="flex justify-between items-start">
                                                <div>
                                                    <div class="font-medium" x-text="result.customer_name"></div>
                                                    <div class="text-xs text-gray-500">
                                                        <span x-text="result.doctor_name"></span>
                                                        <span x-text="result.hospital_name ? ' | ' + result.hospital_name : ''"></span>
                                                    </div>
                                                    <div class="text-xs text-gray-500" x-show="result.customer_phone" x-text="result.customer_phone"></div>
                                                </div>
                                                <div class="text-xs text-gray-500" x-text="result.date"></div>
                                            </div>
                                        </a>
                                    </template>
                                </div>

                                <!-- No Results Message -->
                                <div class="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md py-2 px-4 text-sm text-gray-500" 
                                    x-show="open && searchTerm.length >= 3 && searchResults.length === 0 && !loading"
                                    style="display: none;">
                                    No prescriptions found
                                </div>
                            </div>
                        </div>

                        <!-- Date Filter -->
                        <div>
                            <input type="date" 
                                name="date" 
                                value="{{ request('date') }}"
                                class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" 
                                placeholder="Filter by date">
                        </div>
                        <!-- Clear Filters -->
                        <div class="lg:col-span-2 flex items-center justify-end">
                            @if(request()->hasAny(['search', 'date']))
                                <a href="{{ route('prescriptions.index') }}" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    Clear Filters
                                </a>
                            @endif
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Prescriptions Table -->
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Doctor</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hospital</th>
                            <th scope="col" class="relative px-6 py-3">
                                <span class="sr-only">Actions</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($prescriptions as $prescription)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $prescription->prescription_date->format('d M Y') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <span class="inline-flex items-center justify-center h-10 w-10 rounded-full bg-indigo-100">
                                                <span class="font-medium text-indigo-800">
                                                    @if($prescription->sale && $prescription->sale->customer)
                                                        {{ substr($prescription->sale->customer->name, 0, 2) }}
                                                    @else
                                                        --
                                                    @endif
                                                </span>
                                            </span>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                @if($prescription->sale && $prescription->sale->customer)
                                                    {{ $prescription->sale->customer->name }}
                                                @else
                                                    Walk-in Customer
                                                @endif
                                            </div>
                                            @if($prescription->sale && $prescription->sale->customer && $prescription->sale->customer->phone)
                                                <div class="text-sm text-gray-500">
                                                    {{ $prescription->sale->customer->phone }}
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $prescription->doctor_name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $prescription->hospital_name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="{{ route('prescriptions.show', $prescription) }}" class="text-indigo-600 hover:text-indigo-900">
                                        View Details
                                    </a>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                    No prescriptions found.
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            @if($prescriptions->hasPages())
                <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                    {{ $prescriptions->links() }}
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
