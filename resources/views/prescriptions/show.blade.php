@extends('layouts.admin')

@section('title', 'Prescription Details')

@section('content')
<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="md:flex md:items-center md:justify-between mb-6">
            <div class="flex-1 min-w-0">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                    Prescription Details
                </h2>
            </div>
            <div class="mt-4 flex md:mt-0 md:ml-4">
                <a href="{{ route('prescriptions.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Back to Prescriptions
                </a>
            </div>
        </div>

        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <!-- Customer and Basic Info -->
                    <div>
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Basic Information</h3>
                        <div class="bg-white overflow-hidden border border-gray-200 sm:rounded-lg">
                            <div class="px-4 py-5 sm:p-6">
                                <dl class="grid grid-cols-1 gap-x-4 gap-y-4">
                                    <div class="sm:col-span-1">
                                        <dt class="text-sm font-medium text-gray-500">Customer</dt>
                                        <dd class="mt-1 text-sm text-gray-900">
                                            @if($prescription->sale && $prescription->sale->customer)
                                                <a href="{{ route('customers.show', $prescription->sale->customer) }}" class="text-indigo-600 hover:text-indigo-900">
                                                    {{ $prescription->sale->customer->name }}
                                                </a>
                                            @else
                                                Walk-in Customer
                                            @endif
                                        </dd>
                                    </div>
                                    <div class="sm:col-span-1">
                                        <dt class="text-sm font-medium text-gray-500">Doctor</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $prescription->doctor_name }}</dd>
                                    </div>
                                    <div class="sm:col-span-1">
                                        <dt class="text-sm font-medium text-gray-500">Hospital</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $prescription->hospital_name }}</dd>
                                    </div>
                                    <div class="sm:col-span-1">
                                        <dt class="text-sm font-medium text-gray-500">Date</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $prescription->prescription_date->format('M d, Y') }}</dd>
                                    </div>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <!-- Sale Information -->
                    @if($prescription->sale)
                    <div>
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Sale Information</h3>
                        <div class="bg-white overflow-hidden border border-gray-200 sm:rounded-lg">
                            <div class="px-4 py-5 sm:p-6">
                                <dl class="grid grid-cols-1 gap-x-4 gap-y-4">
                                    <div class="sm:col-span-1">
                                        <dt class="text-sm font-medium text-gray-500">Invoice Number</dt>
                                        <dd class="mt-1 text-sm text-gray-900">
                                            <a href="{{ route('sales.show', $prescription->sale) }}" class="text-indigo-600 hover:text-indigo-900">
                                                {{ $prescription->sale->invoice_number }}
                                            </a>
                                        </dd>
                                    </div>
                                    <div class="sm:col-span-1">
                                        <dt class="text-sm font-medium text-gray-500">Sale Date</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $prescription->sale->created_at->format('M d, Y') }}</dd>
                                    </div>
                                    <div class="sm:col-span-1">
                                        <dt class="text-sm font-medium text-gray-500">Total Amount</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ number_format($prescription->sale->total_amount, 2) }}</dd>
                                    </div>
                                </dl>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Prescription Image -->
                @if($prescription->prescription_image)
                <div class="mt-8">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Prescription Image</h3>
                    <div class="mt-2">
                        <img src="{{ Storage::url($prescription->prescription_image) }}" 
                             alt="Prescription" 
                             class="max-w-full h-auto rounded-lg shadow-sm">
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
