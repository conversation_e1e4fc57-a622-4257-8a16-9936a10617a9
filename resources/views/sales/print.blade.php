<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice #{{ $sale->invoice_number }}</title>
    <style>
        @page {
            margin: 0;
        }
        
        body {
            font-family: system-ui, -apple-system, sans-serif;
            line-height: 1.5;
            margin: 0;
            padding: 0;
        }
        
        .invoice-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 10mm;
            background: white;
        }
        
        .company-header {
            text-align: right;
            margin-bottom: 2rem;
        }
        
        .company-name {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .company-details {
            font-size: 0.875rem;
            color: #4B5563;
        }
        
        .invoice-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #E5E7EB;
        }
        
        .billed-to {
            flex: 1;
        }
        
        .invoice-details {
            text-align: right;
        }
        
        .invoice-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 2rem;
        }
        
        .items-table th {
            background-color: #F9FAFB;
            padding: 0.75rem;
            text-align: left;
            font-size: 0.75rem;
            text-transform: uppercase;
            color: #6B7280;
            border-bottom: 1px solid #E5E7EB;
        }
        
        .items-table td {
            padding: 0.75rem;
            border-bottom: 1px solid #E5E7EB;
        }
        
        .items-table .description {
            font-size: 0.875rem;
            color: #111827;
        }
        
        .items-table .batch {
            font-size: 0.75rem;
            color: #6B7280;
        }
        
        .totals {
            width: 100%;
            max-width: 300px;
            margin-left: auto;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            font-size: 0.875rem;
        }
        
        .total-row.final {
            font-weight: bold;
            border-top: 1px solid #E5E7EB;
            margin-top: 0.5rem;
            padding-top: 1rem;
        }
        
        .notes {
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #E5E7EB;
            font-size: 0.875rem;
            color: #6B7280;
        }
        
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .no-print {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="no-print" style="position: fixed; top: 1rem; right: 1rem; display: flex; gap: 0.5rem;">
        <button onclick="window.print()" style="padding: 0.5rem 1rem; background: #4F46E5; color: white; border: none; border-radius: 0.375rem; cursor: pointer; display: inline-flex; align-items: center; white-space: nowrap;">
            <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
            </svg>
            Print Invoice
        </button>
        <button onclick="window.close()" style="padding: 0.5rem 1rem; background: #DC2626; color: white; border: none; border-radius: 0.375rem; cursor: pointer; display: inline-flex; align-items: center; white-space: nowrap;">
            <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
            × Close
        </button>
    </div>

    <div class="invoice-container">
        <!-- Company Header -->
        <div class="company-header">
            <div class="company-name">{{ config('app.name') }}</div>
            <div class="company-details">
                <div>1234 Your Street</div>
                <div>City, California 90210</div>
                <div>United States</div>
                <div>**************</div>
            </div>
        </div>

        <!-- Invoice Header -->
        <div class="invoice-header">
            <div class="billed-to">
                <div style="color: #6B7280; margin-bottom: 0.5rem;">Billed To</div>
                <div style="font-weight: 500;">{{ $sale->customer?->name ?? 'Walk-in Customer' }}</div>
                @if($sale->customer)
                    @if($sale->customer->phone)
                        <div style="font-size: 0.875rem;">{{ $sale->customer->phone }}</div>
                    @endif
                    @if($sale->customer->email)
                        <div style="font-size: 0.875rem;">{{ $sale->customer->email }}</div>
                    @endif
                @endif
            </div>
            <div class="invoice-details">
                <div class="invoice-title">INVOICE</div>
                <div style="font-size: 0.875rem; margin-bottom: 0.25rem;">
                    Date Issued: {{ $sale->created_at->format('m/d/Y') }}
                </div>
                <div style="font-size: 0.875rem;">
                    Invoice Number: {{ $sale->invoice_number }}
                </div>
            </div>
        </div>

        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th style="text-align: center;">Qty</th>
                    <th style="text-align: right;">Rate</th>
                    <th style="text-align: right;">Amount</th>
                </tr>
            </thead>
            <tbody>
                @foreach($sale->items as $item)
                    <tr>
                        <td>
                            <div class="description">{{ $item->medicine->name }}{{ $item->medicine->dosage ? ' ' . $item->medicine->dosage : '' }}</div>
                            <div class="batch">Batch: {{ $item->batch_number }}</div>
                        </td>
                        <td style="text-align: center;">{{ $item->quantity }}</td>
                        <td style="text-align: right;">{{ number_format($item->unit_price, 2) }}</td>
                        <td style="text-align: right;">{{ number_format($item->quantity * $item->unit_price, 2) }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>

        <!-- Totals -->
        <div class="totals">
            <div class="total-row">
                <span>Subtotal:</span>
                <span>{{ number_format($sale->total_amount, 2) }}</span>
            </div>
            <div class="total-row">
                <span>Tax Amount:</span>
                <span>{{ number_format($sale->tax_amount, 2) }}</span>
            </div>
            <div class="total-row">
                <span>Discount Amount:</span>
                <span>{{ number_format($sale->discount_amount, 2) }}</span>
            </div>
            <div class="total-row final">
                <span>Total:</span>
                <span>{{ number_format($sale->total_amount + $sale->tax_amount - $sale->discount_amount, 2) }}</span>
            </div>
        </div>

        <!-- Notes -->
        <div class="notes">
            <div style="font-weight: 500; margin-bottom: 0.5rem;">Notes:</div>
            <div>Thank you for your business!</div>
            @if($sale->prescription)
                <div style="margin-top: 1rem;">
                    <div style="font-weight: 500; margin-bottom: 0.5rem;">Prescription Details:</div>
                    <div>Doctor: {{ $sale->prescription->doctor_name }}</div>
                    <div>Hospital: {{ $sale->prescription->hospital_name }}</div>
                    <div>Date: {{ $sale->prescription->prescription_date }}</div>
                </div>
            @endif
        </div>
    </div>
</body>
</html> 