@extends('layouts.admin')

@section('title', 'New Sale')

{{-- This page loads with the sidebar collapsed by default for better use of screen space --}}

@push('styles')
<!-- 
    Instead of using internal CSS with @apply directives, we should use Tailwind CSS 
    utility classes directly in the HTML elements. This approach is more in line with 
    Tailwind's utility-first philosophy and makes the code more maintainable.
-->
<style>
    /* Medicine search dropdown styling */
    .medicine-search-dropdown {
        position: fixed !important; /* Use fixed positioning to escape stacking contexts */
        max-width: min(100%, 900px); /* Limit width to prevent excessive blank space */
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        z-index: 99999 !important; /* Extremely high z-index */
        overflow-y: auto !important; /* Force vertical scrolling */
        overflow-x: hidden; /* Hide horizontal overflow */
        transform-origin: top left;
        contain: content;
        isolation: isolate; /* Create a new stacking context */
        pointer-events: auto !important; /* Ensure clicks work */
        font-size: 0.875rem; /* Slightly smaller font for better fit */
        max-height: 300px; /* Default max height */
        scrollbar-width: thin; /* Firefox */
        scrollbar-color: rgba(0, 0, 0, 0.3) transparent; /* Firefox */
        border-radius: 6px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        background-color: white;
    }
    
    /* Webkit scrollbar styling */
    .medicine-search-dropdown::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }
    
    .medicine-search-dropdown::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.3);
        border-radius: 3px;
    }
    
    .medicine-search-dropdown::-webkit-scrollbar-track {
        background: transparent;
    }
    
    /* Styling for dropdown positioned above the input */
    .dropdown-position-above {
        transform-origin: bottom left;
        box-shadow: 0 -10px 15px -3px rgba(0, 0, 0, 0.1), 0 -4px 6px -2px rgba(0, 0, 0, 0.05);
    }
    
    /* Ensure table header sticks properly */
    .medicine-search-dropdown .sticky {
        position: sticky;
        top: 0;
        z-index: 2;
        background-color: #f9fafb; /* Ensures header is opaque */
        border-bottom: 1px solid #e5e7eb;
    }
    
    /* Table header should remain at the top even when dropdown is above */
    .dropdown-position-above .medicine-search-grid.sticky {
        top: 0;
    }
    
    /* Fix for dropdown positioning in table cells */
    td .medicine-search-dropdown {
        position: fixed !important;
        max-height: 80vh !important;
    }
    
    /* Ensure dropdown appears above everything else */
    body:has(.medicine-search-dropdown[style*="display: block"]) .medicine-search-dropdown {
        z-index: 999999 !important;
    }
    
    /* Add a backdrop when dropdown is open to ensure it's visible */
    .medicine-search-dropdown-backdrop {
        position: fixed;
        inset: 0;
        background-color: rgba(0, 0, 0, 0.1);
        z-index: 99990;
        pointer-events: none;
    }
    
    /* Ensure the dropdown doesn't overflow the left column */
    @media (min-width: 1024px) {
        .medicine-search-dropdown {
            max-width: calc(100% - 450px) !important; /* Account for right column width (430px) and some padding */
            width: auto !important; /* Allow the dropdown to size according to content */
        }
    }
    
    /* Define medicine search grid layout with optimized column widths */
    .medicine-search-grid {
        display: grid;
        grid-template-areas: 
            "name name generic generic manufacturer manufacturer"
            "stock price location location location location";
        gap: 0.5rem;
        align-items: center;
    }
    
    /* Column classes for responsive behavior */
    .medicine-col-name { grid-area: name; }
    .medicine-col-generic { grid-area: generic; }
    .medicine-col-manufacturer { grid-area: manufacturer; }
    .medicine-col-stock { grid-area: stock; }
    .medicine-col-price { grid-area: price; }
    .medicine-col-location { grid-area: location; }
    
    /* Make columns more compact */
    .medicine-search-dropdown .medicine-col-name,
    .medicine-search-dropdown .medicine-col-generic,
    .medicine-search-dropdown .medicine-col-manufacturer {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    /* Ensure stock and price columns are compact */
    .medicine-search-dropdown .medicine-col-stock,
    .medicine-search-dropdown .medicine-col-price {
        text-align: center;
        white-space: nowrap;
    }
    
    /* Location text styling */
    .location-container {
        max-width: 100%;
        width: 100%;
    }
    
    .location-chips {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        max-width: 100%;
    }
    
    .location-chip {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
    }
    
    /* Small screens */
    @media (min-width: 640px) {
        .medicine-search-grid {
            grid-template-areas: 
                "name name generic generic manufacturer manufacturer"
                "stock price location location location location";
        }
        
        .medicine-col-location {
            margin-top: 0.25rem;
    }
    }
    
    /* Medium screens */
    @media (min-width: 768px) {
        .medicine-search-grid {
            grid-template-areas: "name generic manufacturer stock price location";
            grid-template-columns: 1.2fr 1.2fr 1.2fr 0.6fr 0.6fr 2fr;
        }
        
        .medicine-col-location {
            margin-top: 0;
        }
    }
    
    /* Large screens */
    @media (min-width: 1024px) {
        .medicine-search-grid {
            grid-template-columns: 1.2fr 1.2fr 1.2fr 0.6fr 0.6fr 2fr;
        }
    }
    
    /* Extra large screens */
    @media (min-width: 1280px) {
        .medicine-search-grid {
            grid-template-columns: 1.2fr 1.2fr 1.2fr 0.6fr 0.6fr 2.2fr;
        }
    }
    
    /* Active dropdown styling */
    .active-dropdown {
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.15), 0 8px 10px -6px rgba(0, 0, 0, 0.1) !important;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    /* Batch dropdown width fix */
    select[x-model="item.batch_number"], 
    .batch-select {
        max-width: 160px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    /* Ensure batch dropdown options are properly truncated */
    .batch-select option {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 160px;
    }
    
    /* Batch column width fix */
    .batch-column {
        width: 160px;
        max-width: 160px;
    }
    
    /* Unit type column width fix */
    .unit-type-column {
        width: 120px;
        max-width: 120px;
    }
    
    /* Stock indicator in search box */
    .medicine-search-input-container {
        position: relative;
    }
    
    .medicine-stock-indicator {
        position: absolute;
        right: 6px;
        top: 50%;
        transform: translateY(-50%);
        padding: 1px 6px;
        border-radius: 9999px;
        font-size: 0.7rem;
        font-weight: 600;
        z-index: 10;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 20px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        border: 1px solid;
    }
    
    /* Adjust search input to accommodate stock indicator */
    .medicine-search-input {
        padding-right: 70px !important;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    /* Prefix for stock indicator */
    .medicine-stock-indicator::before {
        content: "Stock:";
        margin-right: 2px;
        font-size: 0.65rem;
    }
    
    /* Different border colors based on stock level */
    .medicine-stock-indicator.bg-green-100 {
        border-color: rgba(0, 128, 0, 0.3);
    }
    
    .medicine-stock-indicator.bg-yellow-100 {
        border-color: rgba(255, 170, 0, 0.3);
    }
    
    .medicine-stock-indicator.bg-red-100 {
        border-color: rgba(220, 38, 38, 0.3);
    }
    
    /* Make table layout fixed to ensure columns respect their widths */
    table.min-w-full {
        table-layout: fixed;
            width: 100%;
        }
    
    /* Medicine column width for proper stock indicator display */
    .medicine-column {
        width: 180px;
        max-width: 180px;
    }
    
    /* Batch column width fix */
    .batch-column {
        width: 150px;
        max-width: 150px;
    }
    
    /* Unit type column width fix */
    .unit-type-column {
        width: 110px;
        max-width: 110px;
    }
    
    /* Quantity column width */
    .qty-column {
        width: 80px;
        max-width: 80px;
        text-align: center;
    }
    
    /* Price column width */
    .price-column {
        width: 90px;
        max-width: 90px;
        text-align: right;
    }
    
    /* Discount column width */
    .disc-column {
        width: 90px;
        max-width: 90px;
        text-align: center;
    }
    
    /* Total column width */
    .total-column {
        width: 100px;
        max-width: 100px;
        text-align: right;
    }
    
    /* Newly added row highlight */
    .newly-added-row {
        background-color: rgba(79, 70, 229, 0.05) !important;
        box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.2);
        transition: background-color 0.5s ease, box-shadow 0.5s ease;
    }
    
    /* Make medicine name display with ellipsis if too long */
    .medicine-search-input::placeholder {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }
</style>
@endpush

@push('scripts')
<script>
    function saleForm() {
        return {
            formData: {
                customer_id: '',
                payment_method: '',
                paid_amount: '',
                due_date: new Date().toISOString().split('T')[0],
                items: [],
                doctor_name: '',
                hospital_name: '',
                prescription_date: '',
                invoice_discount_type: 'percentage',
                invoice_discount_value: 0,
                loyalty_points_to_redeem: 0,
            },
            processing: false,
            errors: {},
            initialized: false,
            selectedFile: null,
            showNewCustomerModal: false,
            activeTab: 'sale-details',
            expandedLocations: {}, // Track expanded locations for search results
            newlyAddedRow: null, // Track the index of the newly added row
            customers: @json($customers), // Customer data for loyalty points
            newCustomer: {
                name: '',
                phone: '',
                email: '',
                address: '',
            },
            
            init() {
                if (this.initialized) {
                    console.log('Sale form already initialized');
                    return;
                }
                
                console.log('Initializing sale form');
                this.initialized = true;
                
                // Set default payment method and ensure due date is set
                this.formData.payment_method = 'cash';
                if (!this.formData.due_date) {
                    this.formData.due_date = new Date().toISOString().split('T')[0];
                }
                
                if (this.formData.items.length === 0) {
                    this.addItem(false);
                }

                // Initialize file input handling
                const fileInput = this.$refs.prescription_image;
                if (fileInput) {
                    fileInput.addEventListener('change', (e) => {
                        const file = e.target.files[0];
                        if (file) {
                            // Update UI to show selected file
                            this.selectedFile = file.name;
                            console.log('Prescription image selected:', file.name);
                        }
                    });
                }
                
                // Add window resize event listener to reposition dropdowns
                window.addEventListener('resize', () => {
                    this.recalculateAllDropdownPositions();
                });
                
                // Add scroll event listener to reposition dropdowns
                window.addEventListener('scroll', () => {
                    this.recalculateAllDropdownPositions();
                }, { passive: true });

                // Watch for customer changes to reset loyalty points
                this.$watch('formData.customer_id', () => {
                    this.resetLoyaltyPoints();
                });
                
                // Add a scroll handler to ensure dropdown content is visible when scrolling the table
                document.addEventListener('scroll', (event) => {
                    // If we're scrolling inside a dropdown
                    if (event.target.closest && event.target.closest('.medicine-search-dropdown')) {
                        // Do nothing - allow the dropdown's internal scrolling to work
                        return;
                    }
                    
                    // For page scrolling, recalculate all dropdown positions
                    if (event.target === document) {
                        this.recalculateAllDropdownPositions();
                    }
                }, { capture: true, passive: true });
            },
            
            // Helper function to recalculate all dropdown positions
            recalculateAllDropdownPositions() {
                this.formData.items.forEach((item, index) => {
                    if (item.open && Array.isArray(item.searchResults) && item.searchResults.length > 0) {
                        this.positionDropdownStrictly(index);
                    }
                });
            },

            // Helper function to set dropdown z-index based on row position
            // Lower rows should have higher z-index to appear above higher rows
            getDropdownZIndex(index) {
                // Base z-index is 99999
                // Add the row index (multiplied by 10) to ensure lower rows have higher z-index
                return 99999 + (index * 10);
            },
            
            setActiveTab(tabName) {
                this.activeTab = tabName;
            },
            
            addItem(isAutomatic = false) {
                this.formData.items.push({
                    medicine_id: '',
                    batch_number: '',
                    quantity: 1,
                    unit: 'unit', // Set default unit to 'unit'
                    unit_price: 0,
                    discount: 0,
                    batches: [],
                    enabled_units: ['unit'], // Initialize with 'unit' as default
                    searchTerm: '',
                    open: false,
                    searchResults: [],
                    loading: false
                });
                
                // Initialize the search results for the new item
                const newItemIndex = this.formData.items.length - 1;
                
                // Track this as the newly added row if it was added automatically
                if (isAutomatic) {
                    this.newlyAddedRow = newItemIndex;
                }
                
                this.$nextTick(() => {
                    // Ensure the searchResults property is reactive
                    this.formData.items[newItemIndex].searchResults = [];
                    console.log('Added new item with index:', newItemIndex);
                    
                    // Scroll to the newly added row if it was added automatically
                    // Use a small timeout to ensure the DOM has updated
                    setTimeout(() => {
                        const newRow = document.querySelector(`[x-model="formData.items[${newItemIndex}].searchTerm"]`);
                        if (newRow) {
                            newRow.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            // Focus on the search input if it was added automatically (not on initial load)
                            if (this.initialized && isAutomatic) {
                                newRow.focus();
                                
                                // Clear the newly added row indicator after a delay
                                setTimeout(() => {
                                    this.newlyAddedRow = null;
                                }, 2000);
                            }
                        }
                    }, 100);
                });
            },
            
            removeItem(index) {
                this.formData.items.splice(index, 1);
                if (this.formData.items.length === 0) {
                    this.addItem();
                }
            },
            
            medicineChanged(index) {
                const item = this.formData.items[index];
                const select = event.target;
                const option = select.options[select.selectedIndex];
                
                if (option) {
                    const medicine = {
                        batches: JSON.parse(option.dataset.batches || '[]'),
                        default_batch: option.dataset.defaultBatch,
                        default_price: parseFloat(option.dataset.defaultPrice || 0)
                    };
                    
                    // Get enabled units from the selected medicine
                    const medicineElement = document.querySelector(`option[value="${item.medicine_id}"]`);
                    item.enabled_units = medicineElement ? JSON.parse(medicineElement.dataset.enabledUnits || '[]') : ['unit'];
                    
                    item.batches = medicine.batches;
                    item.batch_number = medicine.default_batch;
                    item.unit = 'unit'; // Set default unit to 'unit'
                    
                    this.updatePriceForUnit(index);
                } else {
                    item.batches = [];
                    item.batch_number = '';
                    item.unit_price = 0;
                    item.enabled_units = [];
                    item.unit = '';
                }
            },
            
            updatePriceForUnit(index) {
                const item = this.formData.items[index];
                
                // If we have direct access to the medicine properties (from search results)
                if (item.retail_price_box !== undefined && item.retail_price_strip !== undefined && item.retail_price_unit !== undefined) {
                    switch (item.unit) {
                        case 'box':
                            item.unit_price = parseFloat(item.retail_price_box) || 0;
                            break;
                        case 'strip':
                            item.unit_price = parseFloat(item.retail_price_strip) || 0;
                            break;
                        case 'piece':
                        case 'unit':
                            item.unit_price = parseFloat(item.retail_price_unit) || 0;
                            break;
                        default:
                            item.unit_price = parseFloat(item.retail_price_unit) || 0;
                    }
                    console.log(`Updated price for ${item.unit} to ${item.unit_price} (direct properties)`);
                    return;
                }
                
                // Fall back to DOM-based approach
                const medicineElement = document.querySelector(`option[value="${item.medicine_id}"]`);
                
                if (!medicineElement || !item.unit) {
                    console.log('Could not find medicine element or unit is not set');
                    return;
                }
                
                const medicine = {
                    retail_price_box: parseFloat(medicineElement.dataset.retailPriceBox || 0),
                    retail_price_strip: parseFloat(medicineElement.dataset.retailPriceStrip || 0),
                    retail_price_unit: parseFloat(medicineElement.dataset.retailPriceUnit || 0),
                    strips_per_box: parseInt(medicineElement.dataset.stripsPerBox || 0),
                    pieces_per_strip: parseInt(medicineElement.dataset.piecesPerStrip || 0)
                };
                
                switch (item.unit) {
                    case 'box':
                        item.unit_price = medicine.retail_price_box;
                        break;
                    case 'strip':
                        item.unit_price = medicine.retail_price_strip;
                        break;
                    case 'piece':
                    case 'unit':
                        item.unit_price = medicine.retail_price_unit;
                        break;
                    default:
                        item.unit_price = medicine.retail_price_unit;
                }
                console.log(`Updated price for ${item.unit} to ${item.unit_price} (DOM approach)`);
            },
            
            getAvailableQuantity(index) {
                const item = this.formData.items[index];
                if (!item.batch_number) return 0;
                
                const batch = item.batches.find(b => b.number === item.batch_number);
                if (!batch) return 0;
                
                const medicineElement = document.querySelector(`option[value="${item.medicine_id}"]`);
                if (!medicineElement) return batch.quantity;
                
                const medicine = {
                    strips_per_box: parseInt(medicineElement.dataset.stripsPerBox || 0),
                    pieces_per_strip: parseInt(medicineElement.dataset.piecesPerStrip || 0)
                };
                
                // Convert stock quantity based on selected unit
                switch (item.unit) {
                    case 'box':
                        return Math.floor(batch.quantity / (medicine.strips_per_box * medicine.pieces_per_strip));
                    case 'strip':
                        return Math.floor(batch.quantity / medicine.pieces_per_strip);
                    case 'piece':
                    default:
                        return batch.quantity;
                }
            },
            
            calculateItemTotal(item) {
                const subtotal = item.quantity * item.unit_price;
                const discount = subtotal * (item.discount / 100);
                return subtotal - discount;
            },
            
            get subtotal() {
                return this.formData.items.reduce((total, item) => total + this.calculateItemTotal(item), 0);
            },
            
            get invoiceDiscount() {
                if (this.formData.invoice_discount_type === 'percentage') {
                    return this.subtotal * (this.formData.invoice_discount_value / 100);
                }
                return Math.min(this.formData.invoice_discount_value, this.subtotal);
            },

            // Loyalty Points Helper Methods
            get selectedCustomer() {
                return this.customers.find(customer => customer.id == this.formData.customer_id) || null;
            },

            get customerLoyaltyPoints() {
                return this.selectedCustomer ? this.selectedCustomer.loyalty_points : 0;
            },

            get loyaltyDiscount() {
                // 100 points = $1 discount (or ৳1 in this case)
                return (this.formData.loyalty_points_to_redeem / 100);
            },

            get maxRedeemablePoints() {
                // Can't redeem more points than customer has
                // Can't redeem more than the subtotal after invoice discount (in points equivalent)
                const maxByBalance = this.customerLoyaltyPoints;
                const maxByAmount = Math.floor(this.subtotalAfterDiscount * 100); // Convert amount to points
                return Math.min(maxByBalance, maxByAmount);
            },
            
            get subtotalAfterDiscount() {
                return Math.max(0, this.subtotal - this.invoiceDiscount);
            },

            get subtotalAfterAllDiscounts() {
                return Math.max(0, this.subtotalAfterDiscount - this.loyaltyDiscount);
            },

            get tax() {
                return this.subtotalAfterAllDiscounts * 0;  // Changed from 0.15 to 0
            },

            get total() {
                return this.subtotalAfterAllDiscounts + this.tax;
            },
            
            get remainingBalance() {
                return Math.max(0, this.total - this.formData.paid_amount);
            },
            
            get changeAmount() {
                return Math.max(0, this.formData.paid_amount - this.total);
            },
            
            formatCurrency(value) {
                // Ensure value is a number before calling toFixed
                const numValue = parseFloat(value) || 0;
                return '৳ ' + numValue.toFixed(2);
            },

            // Reset loyalty points when customer changes
            resetLoyaltyPoints() {
                this.formData.loyalty_points_to_redeem = 0;
            },

            // Validate loyalty points input
            validateLoyaltyPoints() {
                if (this.formData.loyalty_points_to_redeem > this.maxRedeemablePoints) {
                    this.formData.loyalty_points_to_redeem = this.maxRedeemablePoints;
                }
                if (this.formData.loyalty_points_to_redeem < 0) {
                    this.formData.loyalty_points_to_redeem = 0;
                }
            },
            
            async submitForm() {
                this.processing = true;
                this.errors = {};

                try {
                    // Filter out empty items (items without medicine_id)
                    const validItems = this.formData.items.filter(item =>
                        item.medicine_id &&
                        item.medicine_id !== '' &&
                        item.batch_number &&
                        item.batch_number !== '' &&
                        item.quantity > 0
                    );

                    console.log('Valid items to submit:', validItems.length, 'out of', this.formData.items.length);

                    if (validItems.length === 0) {
                        throw new Error('Please add at least one item to the sale.');
                    }

                    const formData = new FormData();
                    formData.append('customer_id', this.formData.customer_id);
                    formData.append('payment_method', this.formData.payment_method);
                    formData.append('paid_amount', this.formData.paid_amount);
                    formData.append('due_date', this.formData.due_date);
                    formData.append('invoice_discount_type', this.formData.invoice_discount_type);
                    formData.append('invoice_discount_value', this.formData.invoice_discount_value);
                    formData.append('loyalty_points_to_redeem', this.formData.loyalty_points_to_redeem);

                    validItems.forEach((item, index) => {
                        formData.append(`items[${index}][medicine_id]`, item.medicine_id);
                        formData.append(`items[${index}][batch_number]`, item.batch_number);
                        formData.append(`items[${index}][quantity]`, item.quantity);
                        formData.append(`items[${index}][unit]`, item.unit);
                        formData.append(`items[${index}][unit_price]`, item.unit_price);
                        formData.append(`items[${index}][discount]`, item.discount || 0);
                    });
                    
                    if (this.formData.doctor_name) {
                        formData.append('prescription[doctor_name]', this.formData.doctor_name);
                        formData.append('prescription[hospital_name]', this.formData.hospital_name);
                        formData.append('prescription[date]', this.formData.prescription_date);
                        
                        const prescriptionFile = this.$refs.prescription_image.files[0];
                        if (prescriptionFile) {
                            formData.append('prescription[image]', prescriptionFile);
                            console.log('Appending prescription image:', prescriptionFile.name);
                        }
                    }
                    
                    console.log('Submitting form with prescription data:', {
                        doctor: this.formData.doctor_name,
                        hospital: this.formData.hospital_name,
                        date: this.formData.prescription_date,
                        hasFile: !!this.$refs.prescription_image?.files[0]
                    });

                    // Get the form action URL directly from the form element
                    const formAction = "{{ route('sales.store') }}";
                    console.log('Form action URL:', formAction);

                    const response = await fetch(formAction, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                            'Accept': 'application/json'
                        }
                    });
                    
                    const result = await response.json();
                    
                    if (!response.ok) {
                        if (response.status === 422 && result.errors) {
                            this.errors = result.errors;
                            throw new Error('Please correct the errors in the form.');
                        }
                        throw new Error(result.message || 'Failed to create sale');
                    }
                    
                    if (result.success && result.redirect) {
                        window.location.href = result.redirect;
                    } else {
                        throw new Error('Invalid response from server');
                    }
                    
                } catch (error) {
                    console.error('Sale creation failed:', error);

                    // Show detailed error message if available
                    let errorMessage = error.message || 'Failed to create sale. Please try again.';

                    // If we have validation errors, show them in the errors object
                    if (Object.keys(this.errors).length > 0) {
                        errorMessage = 'Please correct the validation errors shown above.';

                        // Scroll to the top to show the error messages
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                    }

                    alert(errorMessage);
                } finally {
                    this.processing = false;
                }
            },
            async createNewCustomer() {
                try {
                    const response = await fetch('{{ route('customers.store') }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                        },
                        body: JSON.stringify(this.newCustomer)
                    });

                    if (!response.ok) {
                        const error = await response.json();
                        throw new Error(error.message || 'Failed to create customer');
                    }

                    const data = await response.json();
                    
                    // Add the new customer to the customers list - fixed approach
                    try {
                    const select = document.querySelector('select[x-model="formData.customer_id"]');
                        if (select) {
                            // Create a new option element
                            const option = document.createElement('option');
                            option.value = data.customer.id;
                            option.text = data.customer.name;
                            
                            // Add the option to the select element
                            select.appendChild(option);
                    
                    // Select the newly created customer
                    this.formData.customer_id = data.customer.id;
                            
                            console.log('New customer added to dropdown:', data.customer.name);
                        } else {
                            console.error('Customer select element not found');
                        }
                    } catch (err) {
                        console.error('Error adding customer to dropdown:', err);
                    }
                    
                    // Reset the form and close the modal
                    this.newCustomer = {
                        name: '',
                        phone: '',
                        email: '',
                        address: ''
                    };
                    this.showNewCustomerModal = false;
                    
                    // Show success message
                    this.$dispatch('notify', {
                        type: 'success',
                        message: 'Customer created successfully'
                    });
                } catch (error) {
                    console.error('Error creating customer:', error);
                    this.$dispatch('notify', {
                        type: 'error',
                        message: error.message || 'Failed to create customer'
                    });
                }
            },
            // Search medicines function
            searchMedicines(index) {
                const item = this.formData.items[index];
                const searchTerm = item.searchTerm;
                
                console.log('Searching for:', searchTerm, 'in item index:', index);
                
                // Only search if the term is at least 3 characters
                if (searchTerm.length < 3) {
                    item.searchResults = [];
                    item.open = false;
                    return;
                }
                
                item.loading = true;
                item.open = true;
                
                // Add debounce to prevent too many requests
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    // Use the correct route name
                    const url = "{{ route('inventory.medicines.search') }}";
                    console.log('Searching medicines at URL:', url);
                    
                    fetch(`${url}?query=${encodeURIComponent(searchTerm)}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Network response was not ok');
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log('Search results for item', index, ':', data);
                            
                            // Ensure we're updating the correct item (in case of race conditions)
                            const currentItem = this.formData.items[index];
                            if (currentItem) {
                                // Make sure searchResults is reactive by using array methods
                                if (!Array.isArray(currentItem.searchResults)) {
                                    currentItem.searchResults = [];
                                }
                                
                                // Clear existing results and add new ones
                                currentItem.searchResults.splice(0, currentItem.searchResults.length);
                                data.forEach(result => currentItem.searchResults.push(result));
                                
                                currentItem.loading = false;
                                
                                // Force Alpine to recognize the change
                                this.$nextTick(() => {
                                    console.log('Updated searchResults:', currentItem.searchResults.length, 'items');
                                    
                                    // Position the dropdown considering the available space
                                    this.positionDropdownStrictly(index);
                                    
                                    // Check if the dropdown is in the viewport, adjust if needed
                                    setTimeout(() => {
                                        const inputElement = document.querySelector(`[x-model="formData.items[${index}].searchTerm"]`);
                                        const dropdownElement = inputElement?.closest('div').querySelector('.medicine-search-dropdown');
                                        if (dropdownElement) {
                                            const dropdownRect = dropdownElement.getBoundingClientRect();
                                            const viewportHeight = window.innerHeight;
                                            
                                            // For rows below index 3, be more aggressive about positioning above
                                            const shouldPositionAbove = (index > 3) || 
                                                                       (dropdownRect.bottom > viewportHeight - 50);
                                            
                                            if (shouldPositionAbove && 
                                                inputElement.getBoundingClientRect().top > 150) {
                                                // Force repositioning above for lower rows
                                                dropdownElement.classList.add('dropdown-position-above');
                                                const inputRect = inputElement.getBoundingClientRect();
                                                dropdownElement.style.top = `${inputRect.top + window.scrollY - dropdownElement.offsetHeight - 5}px`;
                                                console.log(`Forced dropdown above for row ${index}`);
                                            }
                                            
                                            // Ensure the dropdown is visible in the viewport
                                            if (dropdownRect.top < 0) {
                                                // If too high, move it down
                                                dropdownElement.style.top = `${window.scrollY + 10}px`;
                                            }
                                        }
                                    }, 50);
                                });
                            }
                        })
                        .catch(error => {
                            console.error('Error searching medicines:', error);
                            item.loading = false;
                        });
                }, 300); // 300ms debounce
            },
            
            // Handle location expansion
            toggleLocationExpand(idx, itemIndex, event) {
                // Stop propagation to prevent row click
                if (event) event.stopPropagation();
                
                // Create a unique key for this item and result
                const key = `item_${itemIndex}_result_${idx}`;
                
                // Toggle expanded state
                this.expandedLocations[key] = !this.expandedLocations[key];
                
                // Prevent the click from selecting the medicine
                if (event) {
                    event.preventDefault();
                }
            },
            
            // Check if location is expanded
            isLocationExpanded(idx, itemIndex) {
                const key = `item_${itemIndex}_result_${idx}`;
                return this.expandedLocations[key] === true;
            },
            
            // Strict dropdown positioning: never overflows viewport, always visible, scrolls if needed
            positionDropdownStrictly(index) {
                this.$nextTick(() => {
                    const item = this.formData.items[index];
                    if (!item || !item.open) return;

                    const inputElement = document.querySelector(`[x-model="formData.items[${index}].searchTerm"]`);
                    if (!inputElement) return;

                    const dropdownElement = inputElement.closest('div').querySelector('.medicine-search-dropdown');
                    if (!dropdownElement) return;

                    const headerElement = dropdownElement.querySelector('.sticky-header');
                    const scrollArea = dropdownElement.querySelector('.dropdown-scroll-area');
                    const inputRect = inputElement.getBoundingClientRect();
                    const viewportHeight = window.innerHeight;
                    const viewportWidth = window.innerWidth;
                    const margin = 10;
                    const minHeight = 120;
                    const maxHeight = 320;

                    const spaceBelow = viewportHeight - inputRect.bottom - margin;
                    const spaceAbove = inputRect.top - margin;

                    let showAbove = false;
                    let availableHeight = 0;

                    if (spaceBelow >= minHeight) {
                        showAbove = false;
                        availableHeight = Math.min(spaceBelow, maxHeight);
                    } else if (spaceAbove >= minHeight) {
                        showAbove = true;
                        availableHeight = Math.min(spaceAbove, maxHeight);
                    } else {
                        if (spaceBelow >= spaceAbove) {
                            showAbove = false;
                            availableHeight = Math.max(spaceBelow, minHeight);
                        } else {
                            showAbove = true;
                            availableHeight = Math.max(spaceAbove, minHeight);
                        }
                    }

                    // Set dropdown width and left position
                    const dropdownWidth = Math.max(inputRect.width, 400);
                    let left = inputRect.left + window.scrollX;
                    if (left + dropdownWidth > viewportWidth - margin) {
                        left = viewportWidth - dropdownWidth - margin;
                    }
                    dropdownElement.style.width = `${dropdownWidth}px`;
                    dropdownElement.style.left = `${left}px`;

                    // Set dropdown top position
                    if (showAbove) {
                        dropdownElement.style.top = `${inputRect.top + window.scrollY - availableHeight - 5}px`;
                        dropdownElement.classList.add('dropdown-position-above');
                    } else {
                        dropdownElement.style.top = `${inputRect.bottom + window.scrollY + 5}px`;
                        dropdownElement.classList.remove('dropdown-position-above');
                    }

                    // Measure sticky header height
                    let headerHeight = 0;
                    if (headerElement) {
                        headerHeight = headerElement.offsetHeight;
                    }
                    // Set scroll area maxHeight to availableHeight minus header
                    if (scrollArea) {
                        scrollArea.style.maxHeight = `${Math.max(availableHeight - headerHeight, 60)}px`;
                        scrollArea.style.overflowY = 'auto';
                    }
                    // Remove maxHeight from dropdown itself
                    dropdownElement.style.maxHeight = 'none';
                    dropdownElement.style.overflowY = 'visible';
                    dropdownElement.style.position = 'fixed';
                    dropdownElement.style.display = 'block';
                    dropdownElement.style.zIndex = this.getDropdownZIndex(index).toString();
                    dropdownElement.classList.add('active-dropdown');
                });
            },
            
            // Select medicine function
            selectMedicine(medicine, index) {
                try {
                    console.log('Selecting medicine:', medicine.name + (medicine.hasOwnProperty('dosage') && medicine.dosage ? ' ' + medicine.dosage : ''), 'for index:', index);
                    const item = this.formData.items[index];
                    
                    // Update the medicine ID
                    item.medicine_id = medicine.id;
                    
                    // Update the search input value
                    item.searchTerm = medicine.name + (medicine.hasOwnProperty('dosage') && medicine.dosage ? ' ' + medicine.dosage : '');
                    
                    // Close the dropdown
                    item.open = false;
                    
                    // Update the item with medicine data
                    item.batches = medicine.batches || [];
                    item.enabled_units = medicine.enabled_units || ['unit'];
                    
                    // Store retail prices directly on the item for easier access
                    item.retail_price_box = parseFloat(medicine.retail_price_box) || 0;
                    item.retail_price_strip = parseFloat(medicine.retail_price_strip) || 0;
                    item.retail_price_unit = parseFloat(medicine.retail_price_unit) || 0;
                    item.strips_per_box = parseInt(medicine.strips_per_box) || 0;
                    item.pieces_per_strip = parseInt(medicine.pieces_per_strip) || 0;
                    
                    // Set default batch if available
                    if (medicine.default_batch) {
                        item.batch_number = medicine.default_batch;
                    } else if (item.batches.length > 0) {
                        item.batch_number = item.batches[0].number;
                    } else {
                        item.batch_number = '';
                    }
                    
                    // Set default unit to 'unit'
                    if (item.enabled_units && item.enabled_units.includes('unit')) {
                        item.unit = 'unit';
                    } else if (item.enabled_units && item.enabled_units.length > 0) {
                        item.unit = item.enabled_units[0];
                    } else {
                        item.unit = 'unit'; // Fallback to unit
                    }
                    
                    // Set price directly based on the unit using our updatePriceForUnit function
                    this.updatePriceForUnit(index);
                    
                    console.log('Medicine selected successfully:', {
                        name: medicine.name + (medicine.hasOwnProperty('dosage') && medicine.dosage ? ' ' + medicine.dosage : ''),
                        unit: item.unit,
                        price: item.unit_price,
                        batch: item.batch_number
                    });
                    
                    // Check if this is the last row in the list
                    // If so, automatically add a new row for convenience
                    if (index === this.formData.items.length - 1) {
                        this.$nextTick(() => {
                            this.addItem(true); // Pass true to indicate this is an automatic addition
                            console.log('Automatically added new row after medicine selection in last row');
                        });
                    }
                } catch (error) {
                    console.error('Error in selectMedicine:', error);
                }
            }
        }
    }
</script>
@endpush

@section('content')
<div class="mx-auto" 
    x-data="saleForm()"
    x-init="init()">
    @if($errors->any())
        <div class="rounded-md bg-red-50 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">There were errors with your submission</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <ul class="list-disc pl-5 space-y-1">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Dynamic Error Display for AJAX Validation -->
    <div x-show="Object.keys(errors).length > 0" class="rounded-md bg-red-50 p-4 mb-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                <div class="mt-2 text-sm text-red-700">
                    <ul class="list-disc pl-5 space-y-1">
                        <template x-for="(errorMessages, field) in errors" :key="field">
                            <template x-for="message in (Array.isArray(errorMessages) ? errorMessages : [errorMessages])" :key="message">
                                <li x-text="message"></li>
                            </template>
                        </template>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="md:flex md:items-center md:justify-between mb-6">
        <div class="flex-1 min-w-0">
            <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">New Sale</h2>
        </div>
        <div class="mt-4 flex md:mt-0 md:ml-4">
            <a href="{{ route('sales.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
                Back to Sales
            </a>
        </div>
    </div>

    <form action="{{ route('sales.store') }}" method="POST" @submit.prevent="submitForm" class="space-y-6">
        @csrf
        
        <!-- Tab Navigation -->
        <div class="flex border-b mb-6">
                        <button type="button" 
                    @click="setActiveTab('sale-details')" 
                    class="py-3 px-6 text-sm font-medium focus:outline-none transition-colors flex-1 text-center" 
                    :class="{'bg-white text-indigo-700 font-medium border-b-2 border-indigo-600': activeTab === 'sale-details', 'text-gray-500 hover:text-gray-700 bg-gray-50': activeTab !== 'sale-details'}">
                Sale Details
                        </button>
                        <button type="button" 
                    @click="setActiveTab('payment-prescription')" 
                    class="py-3 px-6 text-sm font-medium focus:outline-none transition-colors flex-1 text-center" 
                    :class="{'bg-white text-indigo-700 font-medium border-b-2 border-indigo-600': activeTab === 'payment-prescription', 'text-gray-500 hover:text-gray-700 bg-gray-50': activeTab !== 'payment-prescription'}">
                Payment & Prescription
                        </button>
                    </div>
        
        <!-- Main Content Area with Flex Layout -->
        <div class="flex flex-wrap lg:flex-nowrap lg:gap-2 lg:items-start">
            <!-- Left side - Main content -->
            <div class="w-full lg:flex-1 pr-0">
                <!-- Tab Content -->
                <div x-show="activeTab === 'sale-details'">
                    <!-- Sale Details Tab Content -->
                    <!-- Sale Details Card -->
                    <div class="bg-white rounded-lg shadow overflow-hidden">
                        <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    Customer Information
                                </h3>
                                <span class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                    Required
                                </span>
                </div>
                            <p class="mt-1 text-sm text-gray-500">
                                Select an existing customer or leave as walk-in
                            </p>
                        </div>
                        <div class="px-4 py-5 sm:p-6">
                            <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Customer</label>
                                    <div class="flex space-x-2">
                                        <div class="flex-grow">
                                            <select class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50 sm:text-sm transition duration-150 ease-in-out" x-model="formData.customer_id">
                                            <option value="">Walk-in Customer</option>
                                            @foreach($customers as $customer)
                                                <option value="{{ $customer->id }}">{{ $customer->name }}</option>
                                            @endforeach
                                        </select>
                                        </div>
                                        <button type="button" 
                                            @click="showNewCustomerModal = true"
                                            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                            <svg class="h-5 w-5 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                                </svg>
                                            New
                                        </button>
                            </div>
                                    
                                    <!-- Customer Details Preview (when selected) -->
                                    <div x-show="formData.customer_id" class="mt-3 p-3 bg-gray-50 rounded-md border border-gray-200">
                                        <div class="text-sm">
                                            <template x-for="customer in {{ json_encode($customers) }}">
                                                <div x-show="customer.id == formData.customer_id" class="space-y-1">
                                                    <div class="flex items-center">
                                                        <svg class="w-4 h-4 text-gray-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                                        </svg>
                                                        <span class="text-gray-600" x-text="customer.email || 'No email provided'"></span>
                        </div>
                                                    <div class="flex items-center">
                                                        <svg class="w-4 h-4 text-gray-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                                        </svg>
                                                        <span class="text-gray-600" x-text="customer.phone || 'No phone provided'"></span>
                    </div>
                                                    <div class="flex items-center" x-show="customer.address">
                                                        <svg class="w-4 h-4 text-gray-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                                        <span class="text-gray-600" x-text="customer.address"></span>
                            </div>
                        </div>
                                            </template>
                    </div>
                </div>
            </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
                                    <select class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50 sm:text-sm transition duration-150 ease-in-out" 
                                            x-model="formData.payment_method" 
                                            :class="{'border-red-300': errors.payment_method}"
                                            required>
                                            <option value="">Select Payment Method</option>
                                            <option value="cash" selected>Cash</option>
                                            <option value="card">Card</option>
                                            <option value="bank_transfer">Bank Transfer</option>
                                        </select>
                                        <p class="mt-2 text-sm text-gray-500">Choose how the customer will pay</p>
                                        <p x-show="errors.payment_method" class="mt-1 text-sm text-red-600" x-text="errors.payment_method"></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Medicine Items Card -->
                    <div class="bg-white rounded-lg shadow p-6 mt-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-indigo-600" viewBox="0 0 408.6 408.87" xmlns="http://www.w3.org/2000/svg" fill="currentColor">
                                    <g id="Layer_1-2" data-name="Layer 1">
                                        <path d="M118.21,408.87h-16.76l-23.15-4.39c-37.25-10.94-66.72-41.57-75.3-79.59L.06,305.07c.13-2.9-.18-5.89,0-8.78,1.78-28.38,12.94-51.49,31.91-72.28,26.83-29.4,58.22-56.21,85.44-85.42l1.6-.82,100.53,100.74c-27.52,34.55-33.97,80.72-17.12,121.68-10.91,10.34-20.62,22.21-33.2,30.64-13.86,9.29-34.33,16.7-51.02,18.04Z"/>
                                        <path d="M292.88.31c80.87-5.99,139.14,76.77,106.48,151.2-8.94,20.37-24.62,34.85-39.28,50.99-6.91-1.49-13.75-4.74-20.86-6.29-37.03-8.07-73.27,1.26-102.47,24.83l-100.96-100.81L233.24,23.69C250.33,10.53,271.21,1.91,292.88.31Z"/>
                                        <path d="M301.03,218.84v188.43c-62.1-6.81-100.59-71.71-77.03-130.13,12.67-31.4,43.11-55.16,77.03-58.3Z"/>
                                        <path d="M324.98,407.27v-188.43c33.64,2.98,65.34,28.33,77.25,59.67,22.09,58.13-15.9,121.55-77.25,128.76Z"/>
                                    </g>
                                </svg>
                                Medicine Items
                            </h3>
                        <button type="button" 
                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                @click="addItem(false)">
                                <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                            </svg>
                            Add Item
                        </button>
                    </div>

                        <div class="overflow-x-auto rounded-lg border border-gray-200">
                    <table class="min-w-full divide-y divide-gray-200">
                                <thead>
                                    <tr class="bg-gray-50">
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider medicine-column">Medicine</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider batch-column">Batch</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider unit-type-column">Unit Type</th>
                                        <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider qty-column">Qty</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider price-column">Price</th>
                                        <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider disc-column">Disc %</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider total-column">Total</th>
                                        <th class="px-4 py-3 w-16"></th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <template x-for="(item, index) in formData.items" :key="index">
                                        <tr :class="{'bg-gray-50': index % 2 === 1, 'newly-added-row': newlyAddedRow === index}">
                                            <td class="px-4 py-3 medicine-column">
                                                <div class="relative medicine-search-input-container">
                                                    <!-- Search input -->
                                                    <div class="flex">
                                                        <input type="text" 
                                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 text-sm medicine-search-input" 
                                                            placeholder="Search medicine..."
                                                            x-model="item.searchTerm"
                                                            @focus="item.open = true"
                                                            @click.away="item.open = false"
                                                            @input="searchMedicines(index)"
                                                            @keydown.escape="item.open = false"
                                                            :class="{'pr-20': item.medicine_id && item.batch_number && !item.loading}">
                                                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none" x-show="item.loading">
                                                            <svg class="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                            </svg>
                                            </div>
                                                        
                                                        <!-- Stock indicator (shown when medicine is selected) -->
                                                        <div 
                                                            class="medicine-stock-indicator" 
                                                            :class="{
                                                                'bg-green-100 text-green-800': getAvailableQuantity(index) > 10,
                                                                'bg-yellow-100 text-yellow-800': getAvailableQuantity(index) > 0 && getAvailableQuantity(index) <= 10,
                                                                'bg-red-100 text-red-800': item.medicine_id && getAvailableQuantity(index) === 0
                                                            }"
                                                            x-show="item.medicine_id && item.batch_number && !item.loading"
                                                            style="right: 5px;">
                                                            <span x-text="getAvailableQuantity(index)"></span>
                                                </div>
                                                </div>
                                                    
                                                    <!-- Dropdown results -->
                                                    <div class="fixed z-[99999] mt-1 bg-white shadow-lg rounded-md py-1 text-sm medicine-search-dropdown" 
                                                        x-show="item.open && Array.isArray(item.searchResults) && item.searchResults.length > 0" 
                                                        x-transition:enter="transition ease-out duration-100" 
                                                        x-transition:enter-start="transform opacity-0 scale-95" 
                                                        x-transition:enter-end="transform opacity-100 scale-100" 
                                                        x-transition:leave="transition ease-in duration-75" 
                                                        x-transition:leave-start="transform opacity-100 scale-100" 
                                                        x-transition:leave-end="transform opacity-0 scale-95"
                                                        x-init="$nextTick(() => { 
                                                            if (item.open && Array.isArray(item.searchResults) && item.searchResults.length > 0) {
                                                                positionDropdownStrictly(index);
                                                            }
                                                        })"
                                                        @click.away="item.open = false"
                                                        :data-dropdown-index="'item-' + index">
                                                        <!-- Table Header (sticky, outside scroll area) -->
                                                        <div class="px-3 py-2 bg-gray-50 border-b medicine-search-grid font-medium text-gray-600 sticky-header" style="position: sticky; top: 0; z-index: 2;">
                                                <div class="medicine-col-name">Name</div>
                                                <div class="medicine-col-generic">Generic</div>
                                                <div class="medicine-col-manufacturer">Manufacturer</div>
                                                <div class="medicine-col-stock">Stock</div>
                                                <div class="medicine-col-price">Price</div>
                                                <div class="medicine-col-location">Location</div>
                                            </div>
                                                        <!-- Scrollable Results Area -->
                                                        <div class="dropdown-scroll-area" style="overflow-y: auto;">
                                                            <template x-for="(result, idx) in item.searchResults" :key="idx">
                                                                <div class="border-b border-gray-100 last:border-0 hover:bg-gray-50">
                                                                    <a href="#" 
                                                                        class="block px-3 py-2 cursor-pointer"
                                                                        @click.prevent="selectMedicine(result, index)">
                                                                        <div class="medicine-search-grid items-center">
                                                                            <div class="font-medium truncate medicine-col-name" x-text="result.name + (result.hasOwnProperty('dosage') && result.dosage ? ' ' + result.dosage : '')"></div>
                                                                            <div class="text-gray-600 truncate medicine-col-generic" x-text="result.generic_name"></div>
                                                                            <div class="text-gray-600 truncate medicine-col-manufacturer" x-text="result.manufacturer || '-' "></div>
                                                                            <div class="medicine-col-stock">
                                                                                <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium" 
                                                                                    :class="{
                                                                                        'bg-green-100 text-green-800': result.total_stock > 10,
                                                                                        'bg-yellow-100 text-yellow-800': result.total_stock > 0 && result.total_stock <= 10,
                                                                                        'bg-red-100 text-red-800': result.total_stock === 0
                                                                                    }">
                                                                                    <span x-text="result.total_stock"></span>
                                                                                </span>
                                                                            </div>
                                                                            <div class="text-gray-600 medicine-col-price text-sm">৳ <span x-text="result.selling_price"></span></div>
                                                                            <div class="flex items-center medicine-col-location">
                                                                                <div x-show="result.has_location_data" class="w-full location-container">
                                                                                    <div class="flex items-center">
                                                                                        <svg class="w-3 h-3 mr-1 flex-shrink-0 text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                                                        </svg>
                                                                                        <div class="flex-1 overflow-hidden">
                                                                                            <template x-if="result.locations && result.locations.length > 0">
                                                                <div class="location-chips">
                                                                                                    <template x-for="(location, locIdx) in result.locations.slice(0, Math.min(3, result.locations.length))" :key="locIdx">
                                                                                                        <span class="inline-block text-xs text-blue-700 bg-blue-50 px-1.5 py-0.5 rounded mr-1 mb-1 location-chip">
                                                                                                            <span class="font-medium" x-text="location.name"></span>
                                                                                                            <span class="text-blue-800" x-text="': ' + location.quantity"></span>
                                                                                                        </span>
                                                                    </template>
                                                                                                    <span 
                                                                                                        x-show="result.locations.length > 3"
                                                                                                        class="inline-block text-xs text-blue-700 bg-blue-50 px-1.5 py-0.5 rounded cursor-pointer hover:bg-blue-100"
                                                                                                        @click.stop="toggleLocationExpand(idx, index, $event)">
                                                                                                        +<span x-text="result.locations.length - 3"></span>
                                                                                                    </span>
                                                                </div>
                                                                                            </template>
                                                                                            <span x-show="!result.locations || result.locations.length === 0" class="text-gray-500 text-xs">No location data</span>
                                                            </div>
                                                        </div>
                                                                                </div>
                                                                                <div x-show="!result.has_location_data" class="text-gray-500">-</div>
                                                                            </div>
                                                                        </div>
                                                                    </a>
                                                    </div>
                                                </template>
                                            </div>
                                                    </div>
                                                    
                                                    <!-- No results message -->
                                                    <div class="fixed z-[99999] mt-1 bg-white shadow-lg rounded-md py-2 px-4 text-sm text-gray-500" 
                                                        x-show="item.open && item.searchTerm && item.searchTerm.length >= 3 && (!Array.isArray(item.searchResults) || item.searchResults.length === 0) && !item.loading"
                                                        x-init="$nextTick(() => { 
                                                            const input = document.querySelector(`[x-model='formData.items[${index}].searchTerm']`);
                                                            if (input) {
                                                                const rect = input.getBoundingClientRect();
                                                                
                                                                // Find the left column container to respect its boundaries
                                                                const leftColumn = document.querySelector('.w-full.lg\\:flex-1');
                                                                const leftColumnRect = leftColumn ? leftColumn.getBoundingClientRect() : null;
                                                                
                                                                // Get the right column for more precise calculations
                                                                const rightColumn = document.querySelector('.w-full.lg\\:w-\\[430px\\]');
                                                                const rightColumnRect = rightColumn ? rightColumn.getBoundingClientRect() : null;
                                                                
                                                                // Calculate maximum allowed width based on left column boundaries
                                                                let maxAllowedWidth;
                                                                
                                                                if (window.innerWidth >= 1024) {
                                                                    // On large screens with the two-column layout
                                                                    if (leftColumnRect && rightColumnRect) {
                                                                        // Calculate the space between the input's left edge and the right column's left edge
                                                                        maxAllowedWidth = Math.max(300, rightColumnRect.left - rect.left - 40);
                                                                    } else if (leftColumnRect) {
                                                                        // If we only have the left column
                                                                        maxAllowedWidth = leftColumnRect.width - 40;
                                                                    } else {
                                                                        // Fallback to a percentage of viewport width
                                                                        maxAllowedWidth = window.innerWidth * 0.7;
                                                                    }
                                                                } else {
                                                                    // On smaller screens, use a percentage of the viewport width
                                                                    maxAllowedWidth = Math.min(window.innerWidth * 0.9, 500);
                                                                }
                                                                
                                                                // Calculate dropdown width - ensure it's not too wide or too narrow
                                                                const dropdownWidth = Math.min(maxAllowedWidth, Math.max(rect.width, 300));
                                                                
                                                                // Position directly under the input with a small offset
                                                                const verticalOffset = 5; // 5px offset from the input
                                                                $el.style.top = `${rect.bottom + window.scrollY + verticalOffset}px`;
                                                                $el.style.left = `${rect.left + window.scrollX}px`;
                                                                $el.style.width = `${dropdownWidth}px`;
                                                                
                                                                // Check if would overflow to the right
                                                                if (rect.left + dropdownWidth > (leftColumnRect ? leftColumnRect.right - 20 : window.innerWidth)) {
                                                                    // Align right edge with right edge of left column or input
                                                                    const rightEdge = leftColumnRect ? leftColumnRect.right - 20 : rect.right;
                                                                    $el.style.left = `${rightEdge - dropdownWidth + window.scrollX}px`;
                                                                }
                                                                
                                                                $el.style.position = 'fixed';
                                                                $el.style.zIndex = '99999';
                                                                $el.classList.add('active-dropdown');
                                                            }
                                                        })"
                                                        @click.away="item.open = false">
                                                        No medicines found
                                                    </div>
                                                    
                                                    <!-- Hidden select for form submission -->
                                                    <select 
                                                        class="hidden" 
                                                    x-model="item.medicine_id" 
                                                    @change="medicineChanged(index)" 
                                                    required>
                                                    <option value="">Select Medicine</option>
                                                    @foreach($medicines as $medicine)
                                                        <option value="{{ $medicine['id'] }}" 
                                                            data-price="{{ $medicine['selling_price'] }}"
                                                            data-batches="{{ json_encode($medicine['batches']) }}"
                                                            data-default-batch="{{ $medicine['default_batch'] }}"
                                                            data-default-price="{{ $medicine['default_price'] }}"
                                                            data-enabled-units="{{ json_encode($medicine['enabled_units']) }}"
                                                            data-retail-price-box="{{ $medicine['retail_price_box'] }}"
                                                            data-retail-price-strip="{{ $medicine['retail_price_strip'] }}"
                                                            data-retail-price-unit="{{ $medicine['retail_price_unit'] }}"
                                                            data-strips-per-box="{{ $medicine['strips_per_box'] }}"
                                                            data-pieces-per-strip="{{ $medicine['pieces_per_strip'] }}">
                                                            {{ $medicine['name'] }}{{ isset($medicine['dosage']) && $medicine['dosage'] ? ' ' . $medicine['dosage'] : '' }}{{ !empty($medicine['generic_name']) ? ' (' . $medicine['generic_name'] . ')' : '' }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                        </div>
                                    </td>
                                            <td class="px-4 py-3 batch-column">
                                                <select class="block w-full pl-3 pr-10 py-2 text-sm border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 batch-select" 
                                                    x-model="item.batch_number" 
                                                    required>
                                                <option value="">Select Batch</option>
                                                    <template x-for="batch in item.batches" :key="batch.number">
                                                        <option :value="batch.number" x-text="`${batch.number} (Exp: ${batch.expiry_formatted})`"></option>
                                                </template>
                                            </select>
                                    </td>
                                            <td class="px-4 py-3 unit-type-column">
                                                <select class="block w-full rounded-md shadow-sm text-sm border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" 
                                                    x-model="item.unit" 
                                                    @change="updatePriceForUnit(index)"
                                                    required>
                                                    <option value="">Select Unit</option>
                                                <template x-for="unit in item.enabled_units" :key="unit">
                                                    <option :value="unit" x-text="unit"></option>
                                                </template>
                                            </select>
                                    </td>
                                            <td class="px-4 py-3 qty-column">
                                                <div class="flex flex-col items-center space-y-1">
                                                        <input type="number" 
                                                        class="block w-20 text-center border-gray-300 rounded-md shadow-sm text-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" 
                                                            x-model.number="item.quantity" 
                                                            min="1" 
                                                            :max="getAvailableQuantity(index)"
                                                            @input="if(item.quantity > getAvailableQuantity(index)) item.quantity = getAvailableQuantity(index)"
                                                        :class="{'border-red-300 ring-red-200': item.quantity > getAvailableQuantity(index)}"
                                                            required>
                                                    
                                                    <!-- Only show exceeds stock warning when actually exceeding -->
                                                    <div x-show="item.quantity > getAvailableQuantity(index) && getAvailableQuantity(index) > 0" 
                                                        class="text-xs text-red-600 text-center">
                                                        Exceeds stock
                                        </div>
                                        </div>
                                    </td>
                                            <td class="px-4 py-3 price-column">
                                                <span x-text="formatCurrency(item.unit_price)" class="font-medium"></span>
                                            </td>
                                            <td class="px-4 py-3 disc-column">
                                                <div class="flex rounded-md shadow-sm justify-center">
                                                    <input type="number" 
                                                        class="block w-16 border-gray-300 rounded-l-md text-sm py-1 px-2 text-right focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                                        x-model.number="item.discount" 
                                                        min="0" 
                                                        max="100" 
                                                        step="0.01">
                                                    <span class="inline-flex items-center px-2 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">%</span>
                                        </div>
                                    </td>
                                            <td class="px-4 py-3 total-column" x-text="formatCurrency(calculateItemTotal(item))"></td>
                                            <td class="px-4 py-3 text-center">
                                                <button type="button" 
                                                    class="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-red-50 transition-colors"
                                                    @click="removeItem(index)">
                                                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                    </svg>
                                                </button>
                                    </td>
                                        </tr>
                                    </template>
                                    
                                    <!-- Empty state row when no items -->
                                    <tr x-show="formData.items.length === 0" class="bg-gray-50">
                                        <td colspan="8" class="px-4 py-8 text-center text-gray-500">
                                            <div class="flex flex-col items-center">
                                                <svg class="w-12 h-12 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                                            </svg>
                                                <p class="text-sm font-medium">No items added to this sale</p>
                                                <button type="button" 
                                                    @click="addItem()"
                                                    class="mt-3 inline-flex items-center px-3 py-1.5 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                                    <svg class="-ml-0.5 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                                                    </svg>
                                                    Add First Item
                                        </button>
                                            </div>
                                    </td>
                                </tr>
                        </tbody>
                    </table>
                </div>
            </div>
                </div>

                <!-- Payment Details Card -->
                <div x-show="activeTab === 'payment-prescription'">
                    <!-- Payment & Prescription Tab Content -->
                    
                    <!-- Payment Details Card -->
                    <div class="bg-white rounded-lg shadow overflow-hidden">
                        <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                    Payment Details
                                </h3>
                                <span class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                    Required
                                </span>
                            </div>
                            <p class="mt-1 text-sm text-gray-500">
                                Enter payment information for this sale
                            </p>
                        </div>
                        <div class="px-4 py-5 sm:p-6">
                            <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Paid Amount</label>
            <div class="flex flex-col space-y-2">
                                            <div class="flex rounded-md shadow-sm">
                                            <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">৳</span>
                                                <input type="number" 
                                                class="form-input rounded-none rounded-r-md block w-full border-gray-300 focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50" 
                                                    x-model.number="formData.paid_amount" 
                                                    min="0" 
                                                    step="0.01"
                                                placeholder="0"
                                                    :class="{
                                                        'border-red-300': errors.paid_amount,
                                                        'border-green-300': formData.paid_amount >= total,
                                                        'border-yellow-300': formData.paid_amount > 0 && formData.paid_amount < total
                                                    }">
                                            </div>
                                            <div class="text-xs" :class="{
                                                'text-green-600': formData.paid_amount >= total,
                                            'text-orange-500': formData.paid_amount > 0 && formData.paid_amount < total,
                                            'text-red-500': !formData.paid_amount || formData.paid_amount === 0
                                            }">
                                            <!-- Only show when paid amount equals or exceeds total -->
                                            <span x-show="formData.paid_amount >= total && formData.paid_amount > 0" class="flex items-center">
                                                <svg class="w-4 h-4 mr-1 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                                <span class="font-medium">Full payment received</span>
                                            </span>
                                            
                                            <!-- Only show when paid amount is greater than 0 but less than total -->
                                            <span x-show="formData.paid_amount > 0 && formData.paid_amount < total" class="flex items-center">
                                                <svg class="w-4 h-4 mr-1 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                <span class="font-medium">Partial payment received</span>
                                            </span>
                                            
                                            <!-- Only show when paid amount is empty or exactly 0 -->
                                            <span x-show="!formData.paid_amount || formData.paid_amount === 0" class="flex items-center">
                                                <svg class="w-4 h-4 mr-1 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                <span class="font-medium">No payment received yet</span>
                                            </span>
                                            </div>
                                            <p x-show="errors.paid_amount" class="mt-1 text-sm text-red-600" x-text="errors.paid_amount"></p>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Remaining Balance</label>
                                        <div class="flex flex-col space-y-2">
                                            <div class="flex rounded-md shadow-sm">
                                            <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">৳</span>
                                                <input type="text" 
                                                class="form-input rounded-none rounded-r-md block w-full bg-gray-50 border-gray-300" 
                                                :value="remainingBalance.toFixed(2)" 
                                                    :class="{
                                                    'bg-green-50 text-green-700 border-green-300': remainingBalance === 0,
                                                    'bg-yellow-50 text-yellow-700 border-yellow-300': remainingBalance > 0
                                                    }"
                                                    readonly>
                                            </div>
                                            <div class="text-xs" :class="{
                                                'text-green-600': remainingBalance === 0,
                                                'text-yellow-600': remainingBalance > 0
                                            }">
                                            <span x-show="remainingBalance === 0" class="flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                <span>No balance remaining</span>
                                            </span>
                                            <span x-show="remainingBalance > 0" class="flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                <span>Balance due</span>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1" x-show="remainingBalance > 0">Due Date</label>
                                    <label class="block text-sm font-medium text-gray-700 mb-1" x-show="remainingBalance <= 0">Change Amount</label>
                                    <div class="flex flex-col space-y-2" x-show="remainingBalance > 0">
                                        <div class="relative rounded-md shadow-sm">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                </svg>
                                            </div>
                                            <input type="date" 
                                                class="form-input pl-10 block w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50" 
                                                x-model="formData.due_date"
                                                :min="new Date().toISOString().split('T')[0]"
                                                :class="{
                                                    'border-red-300': errors.due_date,
                                                    'border-yellow-300': remainingBalance > 0 && formData.due_date
                                                }"
                                                :required="remainingBalance > 0">
                                        </div>
                                            <div class="text-xs text-gray-500" x-show="remainingBalance > 0 && formData.due_date">
                                            <svg class="w-4 h-4 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                                Payment due on: <span x-text="new Date(formData.due_date).toLocaleDateString()"></span>
                                            </div>
                                            <p x-show="errors.due_date" class="mt-1 text-sm text-red-600" x-text="errors.due_date"></p>
                                        </div>
                                    
                                    <!-- Change Amount (shown when no remaining balance) -->
                                    <div class="flex flex-col space-y-2" x-show="remainingBalance <= 0">
                                        <div class="flex rounded-md shadow-sm">
                                            <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">৳</span>
                                            <input type="text" 
                                                class="form-input rounded-none rounded-r-md block w-full font-medium" 
                                                :value="changeAmount.toFixed(2)" 
                                                :class="{
                                                    'bg-blue-50 text-blue-700 border-blue-300': changeAmount > 0,
                                                    'bg-gray-100 text-gray-500 border-gray-300': changeAmount === 0
                                                }"
                                                readonly>
                                        </div>
                                        <div class="text-xs" :class="{
                                            'text-blue-600': changeAmount > 0,
                                            'text-gray-500': changeAmount === 0
                                        }">
                                            <svg x-show="changeAmount > 0" class="w-4 h-4 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                            </svg>
                                            <span x-text="changeAmount > 0 ? 'Change to return to customer' : 'No change to return'"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Prescription Details Card -->
                    <div class="bg-white rounded-lg shadow overflow-hidden mt-6">
                        <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
                <div class="flex items-center justify-between">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    Prescription Details
                                </h3>
                                <span class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                    Optional
                                </span>
                </div>
                            <p class="mt-1 text-sm text-gray-500">
                                Add prescription information if this sale requires it
                            </p>
                        </div>
                        <div class="px-4 py-5 sm:p-6">
                            <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Doctor Name</label>
                                    <div class="relative rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                            </svg>
                                        </div>
                                        <input type="text" 
                                            class="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50 sm:text-sm" 
                                            x-model="formData.doctor_name" 
                                            placeholder="Enter doctor's name">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Hospital/Clinic</label>
                                    <div class="relative rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                            </svg>
                                        </div>
                                        <input type="text" 
                                            class="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50 sm:text-sm" 
                                            x-model="formData.hospital_name" 
                                            placeholder="Enter hospital/clinic name">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Prescription Date</label>
                                    <div class="relative rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                        <input type="date" 
                                            class="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50 sm:text-sm" 
                                            x-model="formData.prescription_date">
                                    </div>
                                </div>
                                <div class="md:col-span-3">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Prescription Image</label>
                                    <div class="mt-1">
                                        <div class="flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-indigo-500 transition-colors duration-150 ease-in-out"
                                            :class="{'border-indigo-500 bg-indigo-50': selectedFile}">
                                            <div class="space-y-1 text-center">
                                                <template x-if="!selectedFile">
                                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                    </svg>
                                                </template>
                                                <template x-if="selectedFile">
                                                    <div class="flex items-center justify-center">
                                                        <svg class="h-10 w-10 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                        </svg>
                                                    </div>
                                                </template>
                                                <div class="flex text-sm text-gray-600 justify-center">
                                                    <label for="prescription-image" class="relative cursor-pointer rounded-md font-medium text-indigo-600 hover:text-indigo-700 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500 transition duration-150 ease-in-out">
                                                        <span x-text="selectedFile ? 'Change file' : 'Upload a file'"></span>
                                                        <input id="prescription-image" 
                                                            x-ref="prescription_image" 
                                                            type="file" 
                                                            class="sr-only"
                                                            accept="image/*"
                                                            @change="selectedFile = $event.target.files[0]?.name">
                                                    </label>
                                                    <p class="pl-1" x-text="selectedFile ? selectedFile : 'or drag and drop'"></p>
                                                </div>
                                                <p class="text-xs text-gray-500">
                                                    PNG, JPG, GIF up to 10MB
                                                </p>
                                                <template x-if="errors['prescription.image']">
                                                    <p class="mt-2 text-sm text-red-600" x-text="errors['prescription.image']"></p>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right side - Sale Summary -->
            <div class="w-full lg:w-[430px] mt-6 lg:mt-0 lg:pl-2">
                <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 sticky top-4 w-full">
                    <h3 class="text-xl font-bold text-gray-900 mb-1 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        Sale Summary
                    </h3>
                    <p class="text-sm text-gray-500 mb-5" x-text="formData.items.length + ' items in cart'"></p>
                    
                    <!-- Summary Items -->
                    <div class="flex flex-col space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-900">Subtotal</span>
                            <span class="text-sm text-right font-medium" x-text="'৳ ' + subtotal.toFixed(2)"></span>
                        </div>
                        
                        <!-- Discount Section -->
                        <div>
                            <div class="flex justify-between items-center mb-1">
                                <span class="text-sm font-medium text-gray-900">Invoice Discount</span>
                <div class="flex items-center space-x-2">
                                        <select 
                                        class="rounded-md border-gray-300 text-gray-700 text-sm py-1 focus:ring-indigo-500 focus:border-indigo-500"
                                        x-model="formData.invoice_discount_type">
                                            <option value="percentage">Percentage</option>
                                            <option value="flat">Flat Amount</option>
                                        </select>
                                    
                                    <div class="flex border border-gray-300 rounded-md overflow-hidden w-24">
                                        <input type="number" 
                                            class="w-full border-0 text-sm py-1 px-2 text-center focus:outline-none focus:ring-0"
                                            x-model.number="formData.invoice_discount_value"
                                            min="0"
                                            :max="formData.invoice_discount_type === 'percentage' ? 100 : subtotal"
                                            step="0.01">
                                        <span class="inline-flex items-center justify-center px-1 bg-gray-50 text-gray-500 text-sm border-l border-gray-300" x-text="formData.invoice_discount_type === 'percentage' ? '%' : '৳'"></span>
                        </div>
                    </div>
                        </div>
                            
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-500">Discount Amount</span>
                                <span class="text-sm text-right text-red-600" x-text="'-৳ ' + invoiceDiscount.toFixed(2)"></span>
                    </div>
                        </div>
                        
                        <!-- Loyalty Points Redemption Section -->
                        <div x-show="formData.customer_id && customerLoyaltyPoints > 0" class="space-y-3 pt-4 border-t border-gray-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"/>
                                    </svg>
                                    <span class="text-sm font-medium text-gray-900">Loyalty Points</span>
                                </div>
                                <div class="text-xs text-gray-500">
                                    Available: <span class="font-medium text-primary-600" x-text="customerLoyaltyPoints.toLocaleString()"></span> pts
                                </div>
                            </div>

                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Redeem Points</span>
                                <div class="flex items-center space-x-2">
                                    <div class="flex border border-gray-300 rounded-md overflow-hidden w-20">
                                        <input type="number"
                                            class="w-full border-0 text-sm py-1 px-2 text-center focus:outline-none focus:ring-0"
                                            x-model.number="formData.loyalty_points_to_redeem"
                                            @input="validateLoyaltyPoints()"
                                            min="0"
                                            :max="maxRedeemablePoints"
                                            step="100"
                                            placeholder="0">
                                    </div>
                                    <span class="text-xs text-gray-500">pts</span>
                                </div>
                            </div>

                            <div x-show="formData.loyalty_points_to_redeem > 0" class="flex justify-between items-center">
                                <span class="text-sm text-gray-500">Points Discount</span>
                                <span class="text-sm text-right text-green-600" x-text="'-৳ ' + loyaltyDiscount.toFixed(2)"></span>
                            </div>

                            <div class="text-xs text-gray-400 bg-gray-50 p-2 rounded">
                                <div class="flex items-center space-x-1">
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    <span>100 points = ৳1 discount</span>
                                </div>
                                <div x-show="maxRedeemablePoints < customerLoyaltyPoints" class="mt-1 text-orange-600">
                                    Max redeemable: <span x-text="maxRedeemablePoints"></span> pts (limited by order total)
                                </div>
                            </div>
                        </div>

                        <!-- Subtotal After All Discounts -->
                        <div class="space-y-2 pt-4 border-t border-gray-200">
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-900">Subtotal After All Discounts</span>
                                <span class="text-sm text-right font-medium" x-text="'৳ ' + subtotalAfterAllDiscounts.toFixed(2)"></span>
                    </div>
                        
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-900">Tax (0%)</span>
                                <span class="text-sm text-right font-medium" x-text="'৳ ' + tax.toFixed(2)"></span>
                </div>
            </div>
                        
                        <!-- Total -->
                        <div class="flex justify-between items-center pt-4 border-t-2 border-gray-300">
                            <span class="text-base font-extrabold text-gray-900">Final Total</span>
                            <div class="text-right">
                                <div class="text-base text-right font-extrabold" x-text="'৳ ' + total.toFixed(2)"></div>
                                <div x-show="formData.loyalty_points_to_redeem > 0" class="text-xs text-green-600">
                                    (Saved ৳<span x-text="loyaltyDiscount.toFixed(2)"></span> with points)
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Information (if paid) -->
                        <template x-if="formData.paid_amount > 0">
                            <div class="space-y-2 pt-4 border-t border-gray-200">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium text-gray-900">Paid Amount</span>
                                    <span class="text-sm text-right font-medium" x-text="'৳ ' + formData.paid_amount.toFixed(2)"></span>
                                </div>
                                
                                <div class="flex justify-between items-center" x-show="changeAmount > 0">
                                    <span class="text-sm font-medium text-gray-900">Change</span>
                                    <span class="text-sm text-right text-green-600" x-text="'৳ ' + changeAmount.toFixed(2)"></span>
                                </div>
                                
                                <div class="flex justify-between items-center" x-show="remainingBalance > 0">
                                    <span class="text-sm font-medium text-gray-900">Remaining</span>
                                    <span class="text-sm text-right text-red-600" x-text="'৳ ' + remainingBalance.toFixed(2)"></span>
                                </div>
                            </div>
                        </template>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="pt-4 mt-4">
                        <div class="grid grid-cols-2 gap-3">
                            <button type="button"
                                onclick="window.location.reload()"
                                class="flex justify-center items-center px-3 py-2.5 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 whitespace-nowrap">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Cancel Sale
                            </button>

                            <!-- Proceed to Payment button - shows in Sale Details tab -->
                            <button type="button"
                                x-show="activeTab === 'sale-details'"
                                @click="activeTab = 'payment-prescription'"
                                class="flex justify-center items-center px-3 py-2.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 whitespace-nowrap">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                </svg>
                                Proceed to Payment
                            </button>

                            <!-- Complete Sale button - shows in Payment & Prescription tab -->
                            <button type="submit"
                                x-show="activeTab === 'payment-prescription'"
                                @click="submitForm"
                                class="flex justify-center items-center px-3 py-2.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 whitespace-nowrap"
                                :class="{'opacity-75 cursor-not-allowed': processing}"
                                :disabled="processing">
                                <template x-if="processing">
                                    <svg class="animate-spin -ml-1 mr-1.5 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </template>
                                <template x-if="!processing">
                                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                    </svg>
                                </template>
                                Complete Sale
                            </button>
                        </div>
                    </div>
    </div>
</div>
        </div>
        
        <!-- Form Actions - Only visible on mobile -->
        <div class="flex justify-between space-x-4 mt-6 lg:hidden">
            <button type="button"
                onclick="window.history.back()"
                class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                Cancel
            </button>
            <button type="button"
                x-show="activeTab === 'sale-details'"
                @click="activeTab = 'payment-prescription'"
                class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 whitespace-nowrap">
                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
                Proceed to Payment
            </button>
            <button type="submit"
                x-show="activeTab === 'payment-prescription'"
                @click="submitForm"
                class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 whitespace-nowrap"
                :class="{'opacity-75 cursor-not-allowed': processing}"
                :disabled="processing">
                <template x-if="processing">
                    <svg class="animate-spin -ml-1 mr-1.5 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </template>
                <span>Complete Sale</span>
            </button>
        </div>
    </form>

    <!-- New Customer Modal -->
    <div x-show="showNewCustomerModal" 
        class="fixed inset-0 z-50 overflow-y-auto" 
        x-cloak>
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <form @submit.prevent="createNewCustomer">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                                    Create New Customer
                                </h3>
                                <div class="space-y-4">
                                    <!-- Name -->
                                    <div>
                                        <label for="new_customer_name" class="block text-sm font-medium text-gray-700">Name<span class="text-red-500">*</span></label>
                                        <input type="text" 
                                            id="new_customer_name" 
                                            x-model="newCustomer.name" 
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 sm:text-sm"
                                            required>
                                    </div>
                                    
                                    <!-- Phone -->
                                    <div>
                                        <label for="new_customer_phone" class="block text-sm font-medium text-gray-700">Phone</label>
                                        <input type="text" 
                                            id="new_customer_phone" 
                                            x-model="newCustomer.phone" 
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 sm:text-sm">
                                    </div>
                                    
                                    <!-- Email -->
                                    <div>
                                        <label for="new_customer_email" class="block text-sm font-medium text-gray-700">Email</label>
                                        <input type="email" 
                                            id="new_customer_email" 
                                            x-model="newCustomer.email" 
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 sm:text-sm">
                                    </div>
                                    
                                    <!-- Address -->
                                    <div>
                                        <label for="new_customer_address" class="block text-sm font-medium text-gray-700">Address</label>
                                        <textarea 
                                            id="new_customer_address" 
                                            x-model="newCustomer.address" 
                                            rows="3" 
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 sm:text-sm"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="submit" 
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm">
                            Create Customer
                        </button>
                        <button type="button" 
                            @click="showNewCustomerModal = false" 
                            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Global dropdown backdrop -->
    <div class="medicine-search-dropdown-backdrop" 
        x-show="formData.items.some(item => item.open && Array.isArray(item.searchResults) && item.searchResults.length > 0)"
        x-transition:enter="transition ease-out duration-100"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="transition ease-in duration-75"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"></div>
</div>
@endsection