<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Test Email from {{ $appName }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #4f46e5;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            background-color: #f9fafb;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 0 0 5px 5px;
        }
        .info {
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .info-item {
            margin-bottom: 10px;
        }
        .success {
            color: #059669;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Test Email from {{ $appName }}</h1>
    </div>
    
    <div class="content">
        <p>This is a test email to verify your email configuration. If you're receiving this email, it means your email settings are working correctly! 🎉</p>
        
        <div class="info">
            <div class="info-item">
                <strong>Test Time:</strong> {{ $testTime }}
            </div>
            <div class="info-item">
                <strong>SMTP Host:</strong> {{ $smtpHost }}
            </div>
            <div class="info-item">
                <strong>From Address:</strong> {{ $fromAddress }}
            </div>
            <div class="info-item">
                <strong>Status:</strong> <span class="success">✓ Successfully Delivered</span>
            </div>
        </div>
        
        <p style="margin-top: 20px;">
            You can now proceed with saving your email settings. Your notification system is ready to use!
        </p>
    </div>
</body>
</html> 