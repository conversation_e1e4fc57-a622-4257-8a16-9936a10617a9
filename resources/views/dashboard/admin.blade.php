@extends('layouts.admin')

@section('content')
<div class="px-6 py-6">
    <!-- Sync indicator will be dynamically created by sync-ui-handler.js -->
    <livewire:dashboard.admin-dashboard />
</div>
@endsection

@push('head-scripts')
    <script>
        // Patch for classList error - Applied early in the head
        (function() {
            // Override the classList property to prevent errors
            try {
                const originalClassListDescriptor = Object.getOwnPropertyDescriptor(Element.prototype, 'classList');
                Object.defineProperty(Element.prototype, 'classList', {
                    get: function() {
                        try {
                            return originalClassListDescriptor.get.call(this);
                        } catch (e) {
                            // Return a dummy DOMTokenList-like object
                            return {
                                add: function() {},
                                remove: function() {},
                                toggle: function() {},
                                contains: function() { return false; }
                            };
                        }
                    }
                });
                console.log('ClassList patch applied');
                
                // Create sync indicator elements if they don't exist
                document.addEventListener('DOMContentLoaded', () => {
                    let syncStatus = document.getElementById('syncStatus');
                    let syncStatusText = document.getElementById('syncStatusText');
                    
                    if (!syncStatus) {
                        syncStatus = document.createElement('div');
                        syncStatus.id = 'syncStatus';
                        syncStatus.style.display = 'none';
                        syncStatus.classList = {
                            add: function() {},
                            remove: function() {},
                            toggle: function() {},
                            contains: function() { return false; }
                        };
                        document.body.appendChild(syncStatus);
                    }
                    
                    if (!syncStatusText) {
                        syncStatusText = document.createElement('span');
                        syncStatusText.id = 'syncStatusText';
                        syncStatus.appendChild(syncStatusText);
                    }
                    
                    // Override the problematic function in sync-service.js
                    window.addEventListener('load', () => {
                        if (window.dispatchSyncStatusEvent) {
                            const originalDispatchSyncStatusEvent = window.dispatchSyncStatusEvent;
                            window.dispatchSyncStatusEvent = function(syncing, error = false, errorMessage = null) {
                                try {
                                    // Only dispatch the event without any DOM manipulation
                                    const syncEvent = new CustomEvent('sync-status-changed', {
                                        detail: {
                                            syncing,
                                            error,
                                            errorMessage,
                                            timestamp: new Date().toISOString()
                                        }
                                    });
                                    window.dispatchEvent(syncEvent);
                                    console.log('Safe sync status event dispatched:', syncing ? 'syncing' : (error ? 'error' : 'completed'));
                                } catch (e) {
                                    console.warn('Error in safe dispatchSyncStatusEvent:', e);
                                }
                            };
                            console.log('Sync service dispatch function patched');
                        }
                    });
                });
            } catch (e) {
                console.warn('Error applying classList patch:', e);
            }
        })();
    </script>
@endpush

@push('scripts')
    <script>
        // Unified Chart Management System
        window.DashboardCharts = {
            initialized: false,
            salesChart: null,
            paymentChart: null,

            init() {
                if (this.initialized) {
                    console.log('Dashboard charts already initialized');
                    return;
                }

                console.log('Initializing dashboard charts...');
                this.initialized = true;

                // Wait for Livewire to be ready
                if (window.Livewire) {
                    this.setupCharts();
                } else {
                    window.addEventListener('livewire:init', () => {
                        this.setupCharts();
                    });
                }
            },

            setupCharts() {
                // Setup Livewire event listeners
                Livewire.on('onPointClick', (event) => {
                    console.log('Point clicked', event);
                });

                Livewire.on('onSliceClick', (event) => {
                    console.log('Slice clicked', event);
                });

                // Setup refresh listener
                Livewire.on('refreshDashboard', () => {
                    console.log('Dashboard refresh requested, re-rendering charts...');
                    setTimeout(() => this.renderCharts(), 100);
                });

                // Initial chart rendering
                setTimeout(() => this.renderCharts(), 500);
            },

            async renderCharts() {
                console.log('Rendering dashboard charts...');

                const livewireComponent = document.querySelector('[wire\\:id]');
                if (!livewireComponent) {
                    console.error('Livewire component not found');
                    return;
                }

                const componentId = livewireComponent.getAttribute('wire:id');
                if (!componentId) {
                    console.error('Component ID not found');
                    return;
                }

                try {
                    const component = window.Livewire.find(componentId);
                    if (!component) {
                        console.error('Livewire component instance not found');
                        return;
                    }

                    // Render sales chart
                    if (typeof component.getSalesLineChartModel === 'function') {
                        const salesData = await component.getSalesLineChartModel();
                        this.renderSalesChart(salesData);
                    }

                    // Render payment methods chart
                    if (typeof component.getPaymentMethodsPieChartModel === 'function') {
                        const paymentData = await component.getPaymentMethodsPieChartModel();
                        this.renderPaymentChart(paymentData);
                    }
                } catch (error) {
                    console.error('Error rendering charts:', error);
                }
            },

            renderSalesChart(data) {
                const container = document.getElementById('sales-trend-chart');
                if (!container) {
                    console.error('Sales chart container not found');
                    return;
                }

                // Destroy existing chart
                if (this.salesChart) {
                    this.salesChart.destroy();
                    this.salesChart = null;
                }

                // Clear container
                container.innerHTML = '';

                if (!data || !data.data || data.data.length === 0) {
                    this.showNoDataMessage(container, 'No sales data available');
                    return;
                }

                if (typeof ApexCharts === 'undefined') {
                    console.error('ApexCharts not loaded');
                    return;
                }

                const options = {
                    chart: {
                        type: 'line',
                        height: '100%',
                        toolbar: { show: false },
                        zoom: { enabled: false }
                    },
                    series: [{
                        name: data.title || 'Daily Sales',
                        data: data.data.map(item => item.value)
                    }],
                    xaxis: {
                        categories: data.data.map(item => item.title),
                        labels: { style: { colors: '#64748b' } }
                    },
                    yaxis: {
                        labels: {
                            style: { colors: '#64748b' },
                            formatter: value => '৳' + value.toLocaleString()
                        }
                    },
                    stroke: { curve: 'smooth', width: 2 },
                    colors: ['#6366f1'],
                    grid: { borderColor: '#e2e8f0', strokeDashArray: 4 },
                    tooltip: {
                        y: { formatter: value => '৳' + value.toLocaleString() }
                    }
                };

                try {
                    this.salesChart = new ApexCharts(container, options);
                    this.salesChart.render();
                    console.log('Sales chart rendered successfully');
                } catch (error) {
                    console.error('Error rendering sales chart:', error);
                }
            },

            renderPaymentChart(data) {
                const container = document.getElementById('payment-methods-chart');
                if (!container) {
                    console.error('Payment methods chart container not found');
                    return;
                }

                // Destroy existing chart
                if (this.paymentChart) {
                    this.paymentChart.destroy();
                    this.paymentChart = null;
                }

                // Clear container
                container.innerHTML = '';

                if (!data || !data.data || data.data.length === 0) {
                    this.showNoDataMessage(container, 'No payment methods data available');
                    return;
                }

                if (typeof ApexCharts === 'undefined') {
                    console.error('ApexCharts not loaded');
                    return;
                }

                const options = {
                    chart: {
                        type: 'pie',
                        height: '100%'
                    },
                    series: data.data.map(item => item.value),
                    labels: data.data.map(item => item.title),
                    colors: ['#6366f1', '#10b981', '#f59e0b', '#ef4444'],
                    legend: {
                        position: 'bottom',
                        labels: { colors: '#64748b' }
                    },
                    tooltip: {
                        y: { formatter: value => value.toLocaleString() + ' orders' }
                    },
                    responsive: [{
                        breakpoint: 480,
                        options: {
                            chart: { height: 300 },
                            legend: { position: 'bottom' }
                        }
                    }]
                };

                try {
                    this.paymentChart = new ApexCharts(container, options);
                    this.paymentChart.render();
                    console.log('Payment methods chart rendered successfully');
                } catch (error) {
                    console.error('Error rendering payment methods chart:', error);
                }
            },

            showNoDataMessage(container, message) {
                const messageEl = document.createElement('div');
                messageEl.style.cssText = `
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    width: 100%;
                    color: #64748b;
                    font-size: 16px;
                    font-weight: 500;
                    text-align: center;
                `;
                messageEl.innerHTML = `<span style="margin-right: 6px;">ℹ</span>${message}`;
                container.appendChild(messageEl);
            },

            // Cleanup method for when component is destroyed
            destroy() {
                console.log('Cleaning up dashboard charts...');
                if (this.salesChart) {
                    this.salesChart.destroy();
                    this.salesChart = null;
                }
                if (this.paymentChart) {
                    this.paymentChart.destroy();
                    this.paymentChart = null;
                }
                this.initialized = false;
            }
        };

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => window.DashboardCharts.init(), 100);
            });
        } else {
            setTimeout(() => window.DashboardCharts.init(), 100);
        }

        // Cleanup when page is unloaded
        window.addEventListener('beforeunload', () => {
            if (window.DashboardCharts) {
                window.DashboardCharts.destroy();
            }
        });
    </script>
@endpush

@push('styles')
    <style>
        .apexcharts-canvas {
            margin: 0 auto;
        }
        
        /* Sales Trend Chart Scrollbar Styles */
        .sales-trend-chart {
            overflow-x: hidden;
        }
        
        .sales-trend-chart:hover {
            overflow-x: auto;
        }
        
        /* Custom Scrollbar Styling */
        .sales-trend-chart::-webkit-scrollbar {
            height: 6px;
        }
        
        .sales-trend-chart::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        
        .sales-trend-chart::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 3px;
        }
        
        .sales-trend-chart::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        
        /* Sync indicator styles */
        #sync-indicator {
            transition: all 0.3s ease;
            background-color: #f3f4f6;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        #sync-indicator.syncing {
            background-color: #dbeafe;
            display: flex;
        }
        
        #sync-indicator.error {
            background-color: #fee2e2;
            display: flex;
        }
        
        #sync-indicator::before {
            content: "";
            display: inline-block;
            width: 0.75rem;
            height: 0.75rem;
            border-radius: 50%;
            background-color: #9ca3af;
        }
        
        #sync-indicator.syncing::before {
            background-color: #3b82f6;
            animation: pulse 1.5s infinite;
        }
        
        #sync-indicator.error::before {
            background-color: #ef4444;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
            }
            
            70% {
                transform: scale(1);
                box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
            }
            
            100% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
            }
        }
        
        /* Fix font-weight and opacity issues */
        :root {
            --tw-font-normal: 400;
            --tw-font-medium: 500;
            --tw-font-semibold: 600;
            --tw-font-bold: 700;
            --tw-opacity-0: 0;
            --tw-opacity-25: 0.25;
            --tw-opacity-50: 0.5;
            --tw-opacity-75: 0.75;
            --tw-opacity-100: 1;
        }

        [class~="font-normal"] {
            font-weight: var(--tw-font-normal);
        }
        [class~="font-medium"] {
            font-weight: var(--tw-font-medium);
        }
        [class~="font-semibold"] {
            font-weight: var(--tw-font-semibold);
        }
        [class~="font-bold"] {
            font-weight: var(--tw-font-bold);
        }
        
        [class~="opacity-0"] {
            opacity: var(--tw-opacity-0);
        }
        [class~="opacity-25"] {
            opacity: var(--tw-opacity-25);
        }
        [class~="opacity-50"] {
            opacity: var(--tw-opacity-50);
        }
        [class~="opacity-75"] {
            opacity: var(--tw-opacity-75);
        }
        [class~="opacity-100"] {
            opacity: var(--tw-opacity-100);
        }
    </style>
@endpush
