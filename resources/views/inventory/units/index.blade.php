@extends('layouts.admin')

@push('styles')
<style>
    /* Modal Styles */
    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 100;
        justify-content: center;
        align-items: center;
        overflow-y: auto;
        padding: 1rem;
    }
    
    .modal.show {
        display: flex !important;
    }
    
    .modal-content {
        background-color: white;
        border-radius: 0.5rem;
        max-width: 36rem;
        width: 100%;
        max-height: calc(100vh - 2rem);
        overflow-y: auto;
    }
    
    /* Hide Alpine bindings before Alpine loads */
    [x-cloak] { 
        display: none !important; 
    }
</style>
@endpush

@section('content')
    <div class="space-y-6">
        <div class="flex justify-between items-center">
            <h2 class="text-2xl font-semibold text-gray-900">{{ __('Unit Conversions') }}</h2>
            <button onclick="document.getElementById('addConversionModal').classList.add('show'); document.body.classList.add('overflow-hidden');" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                </svg>
                {{ __('Add New Conversion') }}
            </button>
        </div>

        @if (session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                <span class="block sm:inline">{{ session('success') }}</span>
            </div>
        @endif

        @if (session('error'))
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                <span class="block sm:inline">{{ session('error') }}</span>
            </div>
        @endif

        <!-- Base Units -->
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">{{ __('Base Units') }}</h3>
                <div class="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-3">
                    @foreach($baseUnits as $unit)
                        <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400">
                            <div class="flex-1 min-w-0">
                                <span class="absolute inset-0" aria-hidden="true"></span>
                                <p class="text-sm font-medium text-gray-900">{{ $unit->name }}</p>
                                <p class="text-sm text-gray-500 truncate">{{ $unit->code }}</p>
                                <p class="text-xs text-gray-500">{{ ucfirst($unit->category) }}</p>
                            </div>
                            <div class="flex-shrink-0">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    {{ __('Base Unit') }}
                                </span>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Conversion Rules -->
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">{{ __('Conversion Rules') }}</h3>
                <div class="mt-4">
                    <div class="flex flex-col">
                        <div class="-mx-6 overflow-x-auto">
                            <div class="align-middle inline-block min-w-full">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('From Unit') }}</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('To Unit') }}</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Conversion Factor') }}</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Status') }}</th>
                                            <th scope="col" class="relative px-6 py-3">
                                                <span class="sr-only">{{ __('Actions') }}</span>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        @forelse($conversionRules as $rule)
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {{ $rule->fromUnit->name }}
                                                    <span class="text-gray-500">({{ $rule->fromUnit->code }})</span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {{ $rule->toUnit->name }}
                                                    <span class="text-gray-500">({{ $rule->toUnit->code }})</span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    1 {{ $rule->fromUnit->code }} = {{ $rule->conversion_factor }} {{ $rule->toUnit->code }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $rule->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                        {{ ucfirst($rule->status) }}
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                    <button onclick="editRule({{ $rule->id }})" class="text-indigo-600 hover:text-indigo-900">{{ __('Edit') }}</button>
                                                    <button onclick="deleteRule({{ $rule->id }})" class="ml-4 text-red-600 hover:text-red-900">{{ __('Delete') }}</button>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                                    {{ __('No conversion rules found') }}
                                                </td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    @if(isset($conversionRules) && method_exists($conversionRules, 'links'))
                        <div class="mt-4">
                            {{ $conversionRules->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Unit Converter -->
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">{{ __('Unit Converter') }}</h3>
                <div class="mt-4">
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-4">
                        <div>
                            <label for="from_value" class="block text-sm font-medium text-gray-700">{{ __('Value') }}</label>
                            <input type="number" 
                                   name="from_value" 
                                   id="from_value" 
                                   value="0"
                                   class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                        </div>
                        <div>
                            <label for="from_unit" class="block text-sm font-medium text-gray-700">{{ __('From Unit') }}</label>
                            <select id="from_unit" 
                                    name="from_unit" 
                                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                <option value="">{{ __('Select unit') }}</option>
                                @foreach($allUnits as $unit)
                                    <option value="{{ $unit->id }}">{{ $unit->name }} ({{ $unit->code }})</option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            <label for="to_unit" class="block text-sm font-medium text-gray-700">{{ __('To Unit') }}</label>
                            <select id="to_unit" 
                                    name="to_unit" 
                                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                <option value="">{{ __('Select unit') }}</option>
                                @foreach($allUnits as $unit)
                                    <option value="{{ $unit->id }}">{{ $unit->name }} ({{ $unit->code }})</option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ __('Result') }}</label>
                            <div class="mt-1 relative rounded-md shadow-sm">
                                <div class="block w-full px-3 py-2 sm:text-sm border border-gray-300 rounded-md bg-gray-50">
                                    <span>0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Conversion Modal -->
    <div class="mt-8 w-full">
        <div id="addConversionModal" class="modal">
            <div class="modal-content w-full">
                <form action="{{ route('inventory.units.store') }}" method="POST" class="bg-white rounded-lg shadow-lg w-full max-w-full">
                    @csrf
                    <div class="border-b border-gray-200">
                        <div class="px-6 py-4">
                            <h3 class="text-lg font-medium leading-6 text-gray-900">{{ __('Add New Conversion') }}</h3>
                        </div>
                    </div>
                    
                    <div class="p-6 space-y-4">
                        <div>
                            <label for="from_unit_id" class="block text-sm font-medium text-gray-700">{{ __('From Unit') }}</label>
                            <select id="from_unit_id" name="from_unit_id" required class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                <option value="">{{ __('Select unit') }}</option>
                                @foreach($allUnits as $unit)
                                    <option value="{{ $unit->id }}">{{ $unit->name }} ({{ $unit->code }})</option>
                                @endforeach
                            </select>
                        </div>

                        <div>
                            <label for="to_unit_id" class="block text-sm font-medium text-gray-700">{{ __('To Unit') }}</label>
                            <select id="to_unit_id" name="to_unit_id" required class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                <option value="">{{ __('Select unit') }}</option>
                                @foreach($allUnits as $unit)
                                    <option value="{{ $unit->id }}">{{ $unit->name }} ({{ $unit->code }})</option>
                                @endforeach
                            </select>
                        </div>

                        <div>
                            <label for="conversion_factor" class="block text-sm font-medium text-gray-700">{{ __('Conversion Factor') }}</label>
                            <input type="number" step="0.000001" name="conversion_factor" id="conversion_factor" required class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                            <p class="mt-1 text-sm text-gray-500">{{ __('Enter the value to multiply the \'from\' unit by to get the \'to\' unit') }}</p>
                        </div>
                    </div>

                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex flex-col-reverse sm:flex-row-reverse gap-3">
                        <button type="submit" class="w-full sm:w-auto inline-flex justify-center rounded-md border border-transparent shadow-sm px-6 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:text-sm">
                            {{ __('Save') }}
                        </button>
                        <button type="button" onclick="document.getElementById('addConversionModal').classList.remove('show'); document.body.classList.remove('overflow-hidden');" class="w-full sm:w-auto inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-6 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:text-sm">
                            {{ __('Cancel') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Conversion Modal -->
    <div class="mt-8 w-full">
        <div id="editConversionModal" class="modal">
            <div class="modal-content w-full">
                <form id="editForm" method="POST" class="bg-white rounded-lg shadow-lg w-full max-w-full">
                    @csrf
                    @method('PUT')
                    <div class="border-b border-gray-200">
                        <div class="px-6 py-4">
                            <h3 class="text-lg font-medium leading-6 text-gray-900">{{ __('Edit Conversion') }}</h3>
                        </div>
                    </div>
                    
                    <div class="p-6 space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">{{ __('From Unit') }}</label>
                                <p id="editFromUnitDisplay" class="mt-1 block w-full p-2 bg-gray-100 border border-gray-300 rounded-md"></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">{{ __('To Unit') }}</label>
                                <p id="editToUnitDisplay" class="mt-1 block w-full p-2 bg-gray-100 border border-gray-300 rounded-md"></p>
                            </div>
                        </div>

                        <div>
                            <label for="edit_conversion_factor" class="block text-sm font-medium text-gray-700">{{ __('Conversion Factor') }}</label>
                            <input type="number" step="0.000001" name="conversion_factor" id="edit_conversion_factor" required class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                        </div>

                        <div>
                            <label for="edit_status" class="block text-sm font-medium text-gray-700">{{ __('Status') }}</label>
                            <select id="edit_status" name="status" required class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                <option value="active">{{ __('Active') }}</option>
                                <option value="inactive">{{ __('Inactive') }}</option>
                            </select>
                        </div>
                    </div>

                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex flex-col-reverse sm:flex-row-reverse gap-3">
                        <button type="submit" class="w-full sm:w-auto inline-flex justify-center rounded-md border border-transparent shadow-sm px-6 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:text-sm">
                            {{ __('Save') }}
                        </button>
                        <button type="button" onclick="document.getElementById('editConversionModal').classList.remove('show'); document.body.classList.remove('overflow-hidden');" class="w-full sm:w-auto inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-6 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:text-sm">
                            {{ __('Cancel') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="mt-8 w-full">
        <div x-ref="deleteConversionModal" class="modal">
            <div class="modal-content w-full max-w-md">
                <form x-ref="deleteForm" method="POST" class="bg-white rounded-lg shadow-lg w-full">
                    @csrf
                    @method('DELETE')
                    <div class="p-6">
                        <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">{{ __('Confirm Deletion') }}</h3>
                        <p class="text-sm text-gray-500">
                            {{ __('Are you sure you want to delete this unit conversion? This action cannot be undone.') }}
                        </p>
                    </div>
                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex flex-col-reverse sm:flex-row-reverse gap-3">
                        <button type="submit" class="w-full sm:w-auto inline-flex justify-center rounded-md border border-transparent shadow-sm px-6 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:text-sm">
                            {{ __('Delete') }}
                        </button>
                        <button type="button" @click="$refs.deleteConversionModal.hide()" class="w-full sm:w-auto inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-6 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:text-sm">
                            {{ __('Cancel') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

@push('scripts')
<script>
    // Debug modal visibility
    function logEvent(message) {
        console.log(`[DEBUG] ${new Date().toISOString()}: ${message}`);
    }

    // Global variables for form data
    let editFromUnit = '';
    let editToUnit = '';
    let editConversionFactor = 0;
    let editStatus = 'active';
    
    // Edit rule function
    function editRule(id) {
        logEvent(`Editing rule with ID: ${id}`);
        
        // Fetch the rule data
        const url = `/inventory/units/${id}/edit`;
        logEvent(`Fetching data from: ${url}`);
        
        fetch(url, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(response => {
            logEvent(`Response status: ${response.status}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch rule data: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            logEvent(`Data received: ${JSON.stringify(data)}`);
            
            // Set the form data
            editFromUnit = data.from_unit_name + ' (' + data.from_unit_code + ')';
            editToUnit = data.to_unit_name + ' (' + data.to_unit_code + ')';
            editConversionFactor = data.conversion_factor;
            editStatus = data.status;
            
            // Set the form action URL
            const form = document.getElementById('editForm');
            if (form) {
                form.action = `/inventory/units/${id}`;
            }
            
            // Update the DOM with the new values
            document.querySelector('#editFromUnitDisplay').textContent = editFromUnit;
            document.querySelector('#editToUnitDisplay').textContent = editToUnit;
            document.querySelector('#edit_conversion_factor').value = editConversionFactor;
            document.querySelector('#edit_status').value = editStatus;
            
            // Show the modal
            document.getElementById('editConversionModal').classList.add('show');
            document.body.classList.add('overflow-hidden');
        })
        .catch(error => {
            console.error('Error fetching rule data:', error);
            logEvent(`Error: ${error.message}`);
            alert('Failed to load conversion rule data. Please try again.');
        });
    }
    
    // Delete rule function
    function deleteRule(id) {
        logEvent(`Deleting rule with ID: ${id}`);
        if (confirm('{{ __("Are you sure you want to delete this conversion rule?") }}')) {
            // Create a form
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/inventory/units/${id}`;
            
            // Add CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = '_token';
            csrfInput.value = csrfToken;
            form.appendChild(csrfInput);
            
            // Add method override
            const methodInput = document.createElement('input');
            methodInput.type = 'hidden';
            methodInput.name = '_method';
            methodInput.value = 'DELETE';
            form.appendChild(methodInput);
            
            // Submit the form
            document.body.appendChild(form);
            form.submit();
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Unit converter setup
        const fromValueInput = document.getElementById('from_value');
        const fromUnitSelect = document.getElementById('from_unit');
        const toUnitSelect = document.getElementById('to_unit');
        const resultDisplay = document.querySelector('.block.w-full.px-3.py-2 span');
        
        // Convert function
        async function convert() {
            const fromValue = fromValueInput.value;
            const fromUnit = fromUnitSelect.value;
            const toUnit = toUnitSelect.value;
            
            if (!fromValue || !fromUnit || !toUnit) {
                resultDisplay.textContent = '0';
                return;
            }
            
            try {
                const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                const response = await fetch('{{ route('inventory.units.convert') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: JSON.stringify({
                        value: fromValue,
                        from_unit_id: fromUnit,
                        to_unit_id: toUnit
                    })
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || 'Error converting units');
                }
                
                const data = await response.json();
                if (data.result !== undefined) {
                    resultDisplay.textContent = `${fromValue} ${data.from_unit} = ${data.result} ${data.to_unit}`;
                } else {
                    throw new Error('Invalid response format');
                }
            } catch (error) {
                console.error('Error converting units:', error);
                resultDisplay.textContent = error.message || 'Error converting units';
            }
        }
        
        // Add event listeners
        if (fromValueInput) fromValueInput.addEventListener('input', convert);
        if (fromUnitSelect) fromUnitSelect.addEventListener('change', convert);
        if (toUnitSelect) toUnitSelect.addEventListener('change', convert);
    });
</script>
@endpush
@endsection
