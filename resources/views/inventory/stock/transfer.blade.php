@extends('inventory.stock.layout')

@section('title', 'Stock Transfer')

@section('stock-content')
    <div class="bg-white rounded-lg shadow-sm p-6">
        <!-- Header with <PERSON> Button -->
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-semibold text-gray-800">Stock Transfer</h2>
            <a href="{{ route('inventory.stock.index') }}" class="text-gray-600 hover:text-gray-800">
                <span class="text-sm">← Back to Stock Management</span>
            </a>
        </div>

        <!-- Flash Messages -->
        @if (session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                <span class="block sm:inline">{{ session('success') }}</span>
            </div>
        @endif

        @if (session('error'))
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                <span class="block sm:inline">{{ session('error') }}</span>
            </div>
        @endif

        <!-- Debug Info (only in local environment) -->
        {{-- @if(app()->environment('local'))
            <div class="bg-gray-100 border border-gray-400 text-gray-700 px-4 py-3 rounded relative mb-4">
                <h3 class="font-bold">Debug Information</h3>
                <p>Has Success: {{ session()->has('success') ? 'Yes' : 'No' }}</p>
                <p>Success Message: {{ session('success') }}</p>
                <p>Has Error: {{ session()->has('error') ? 'Yes' : 'No' }}</p>
                <p>Error Message: {{ session('error') }}</p>
                <p>Current URL: {{ url()->current() }}</p>
                <p>Previous URL: {{ url()->previous() }}</p>
            </div>
        @endif --}}

        <!-- Stock Transfer Form -->
        <form action="{{ route('inventory.stock.transfer.store') }}" method="POST" class="space-y-6">
            @csrf

            <!-- Location Selection -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- From Location -->
                <div class="space-y-2">
                    <label for="source_location_id" class="block text-sm font-medium text-gray-700">From Location</label>
                    <select name="source_location_id" id="source_location_id" required
                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 rounded-md shadow-sm">
                        <option value="">Select location</option>
                        @foreach($locations as $location)
                            <option value="{{ $location->id }}">{{ $location->name }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- To Location -->
                <div class="space-y-2">
                    <label for="destination_location_id" class="block text-sm font-medium text-gray-700">To Location</label>
                    <select name="destination_location_id" id="destination_location_id" required
                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 rounded-md shadow-sm">
                        <option value="">Select location</option>
                        @foreach($locations as $location)
                            <option value="{{ $location->id }}">{{ $location->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <!-- Medicine Selection -->
            <div class="space-y-2">
                <label for="medicine_id" class="block text-sm font-medium text-gray-700">Medicine</label>
                <select name="medicine_id" id="medicine_id" required
                        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 rounded-md shadow-sm">
                    <option value="">Select medicine</option>
                    @foreach($medicines as $medicine)
                        <option value="{{ $medicine->id }}">{{ $medicine->name }}{{ $medicine->dosage ? ' ' . $medicine->dosage : '' }}</option>
                    @endforeach
                </select>
            </div>

            <!-- Batch Number -->
            <div class="space-y-2">
                <label for="batch_number" class="block text-sm font-medium text-gray-700">Batch Number</label>
                <select name="batch_number" id="batch_number" required
                        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 rounded-md shadow-sm">
                    <option value="">Select batch</option>
                </select>
            </div>

            <!-- Transfer Details -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Quantity -->
                <div class="space-y-2">
                    <label for="quantity" class="block text-sm font-medium text-gray-700">Quantity</label>
                    <input type="number" name="quantity" id="quantity" required min="1"
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                    <p class="text-sm text-gray-500">Available: <span id="available-quantity">0</span></p>
                </div>

                <!-- Transfer Date -->
                <div class="space-y-2">
                    <label for="movement_date" class="block text-sm font-medium text-gray-700">Transfer Date</label>
                    <input type="date" name="movement_date" id="movement_date" required
                           value="{{ date('Y-m-d') }}"
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                </div>
            </div>

            <!-- Notes -->
            <div class="space-y-2">
                <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                <textarea name="notes" id="notes" rows="3"
                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                          placeholder="Add any additional notes here..."></textarea>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-3">
                <button type="button" onclick="window.history.back()"
                        class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Cancel
                </button>
                <button type="submit"
                        class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Transfer Stock
                </button>
            </div>
        </form>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sourceLocationSelect = document.getElementById('source_location_id');
            const destinationLocationSelect = document.getElementById('destination_location_id');
            const medicineSelect = document.getElementById('medicine_id');
            const batchSelect = document.getElementById('batch_number');
            const availableQuantitySpan = document.getElementById('available-quantity');
            const quantityInput = document.getElementById('quantity');

            // Update medicine list when source location changes
            sourceLocationSelect.addEventListener('change', function() {
                resetSelects('medicine');
                if (this.value) {
                    fetch(`/inventory/stock/medicines/${this.value}`)
                        .then(response => response.json())
                        .then(data => {
                            medicineSelect.innerHTML = '<option value="">Select medicine</option>';
                            data.forEach(medicine => {
                                const option = new Option(medicine.name, medicine.id);
                                medicineSelect.add(option);
                            });
                        })
                        .catch(error => console.error('Error:', error));
                }
            });

            // Update batch numbers when both location and medicine are selected
            function updateBatchNumbers() {
                const locationId = sourceLocationSelect.value;
                const medicineId = medicineSelect.value;

                if (locationId && medicineId) {
                    fetch(`/inventory/stock/batches?location_id=${locationId}&medicine_id=${medicineId}`)
                        .then(response => response.json())
                        .then(data => {
                            batchSelect.innerHTML = '<option value="">Select batch</option>';
                            data.batches.forEach(batch => {
                                const option = new Option(
                                    `${batch.batch_number} (Qty: ${batch.quantity}, Expires: ${batch.expiry_date})`, 
                                    batch.batch_number
                                );
                                option.dataset.quantity = batch.quantity;
                                batchSelect.add(option);
                            });
                        })
                        .catch(error => console.error('Error:', error));
                }
            }

            // Update available quantity when batch is selected
            batchSelect.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                if (selectedOption && selectedOption.dataset.quantity) {
                    const maxQuantity = parseInt(selectedOption.dataset.quantity);
                    availableQuantitySpan.textContent = maxQuantity;
                    quantityInput.max = maxQuantity;
                    quantityInput.value = ''; // Reset quantity input
                } else {
                    availableQuantitySpan.textContent = '0';
                    quantityInput.removeAttribute('max');
                    quantityInput.value = '';
                }
            });

            // Reset dependent selects
            function resetSelects(startFrom) {
                if (startFrom === 'medicine' || startFrom === 'all') {
                    medicineSelect.innerHTML = '<option value="">Select medicine</option>';
                    batchSelect.innerHTML = '<option value="">Select batch</option>';
                    availableQuantitySpan.textContent = '0';
                    quantityInput.value = '';
                }
                if (startFrom === 'batch' || startFrom === 'all') {
                    batchSelect.innerHTML = '<option value="">Select batch</option>';
                    availableQuantitySpan.textContent = '0';
                    quantityInput.value = '';
                }
            }

            // Event listeners
            medicineSelect.addEventListener('change', function() {
                resetSelects('batch');
                updateBatchNumbers();
            });

            // Prevent selecting same location
            destinationLocationSelect.addEventListener('change', function() {
                if (this.value === sourceLocationSelect.value) {
                    alert('Source and destination locations cannot be the same');
                    this.value = '';
                }
            });

            sourceLocationSelect.addEventListener('change', function() {
                if (this.value === destinationLocationSelect.value) {
                    destinationLocationSelect.value = '';
                }
            });
        });

        // Check for flash messages in URL parameters
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const flashSuccess = urlParams.get('flash_success');
            const flashError = urlParams.get('flash_error');
            
            if (flashSuccess) {
                // Create and show a success message
                const successDiv = document.createElement('div');
                successDiv.className = 'bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4';
                successDiv.setAttribute('role', 'alert');
                successDiv.innerHTML = `<span class="block sm:inline">${decodeURIComponent(flashSuccess)}</span>`;
                
                // Insert at the top of the form
                const form = document.querySelector('form');
                form.parentNode.insertBefore(successDiv, form);
                
                // Remove after 5 seconds
                setTimeout(() => {
                    successDiv.remove();
                }, 5000);
            }
            
            if (flashError) {
                // Create and show an error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4';
                errorDiv.setAttribute('role', 'alert');
                errorDiv.innerHTML = `<span class="block sm:inline">${decodeURIComponent(flashError)}</span>`;
                
                // Insert at the top of the form
                const form = document.querySelector('form');
                form.parentNode.insertBefore(errorDiv, form);
                
                // Remove after 5 seconds
                setTimeout(() => {
                    errorDiv.remove();
                }, 5000);
            }
        });
    
        // Debug session flash messages
        console.log('Session data:', {
            success: "{{ session('success') }}",
            error: "{{ session('error') }}",
            hasSuccess: "{{ session()->has('success') }}",
            hasError: "{{ session()->has('error') }}"
        });
    </script>
    @endpush
@endsection
