@extends('layouts.admin')

@section('content')
<div class="w-full">
    <div class="flex flex-col space-y-6">
        <div class="bg-white shadow-sm rounded-lg overflow-hidden">
            <!-- Header -->
            <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Edit Purchase Order</h3>
                        <p class="mt-1 text-sm text-gray-500">PO Number: {{ $purchase->purchase_number }}</p>
                    </div>
                </div>
            </div>

            <!-- Form -->
            <form action="{{ route('inventory.purchases.update', $purchase) }}" method="POST" class="divide-y divide-gray-200">
                @csrf
                @method('PUT')

                <div class="px-4 py-5 sm:p-6 space-y-6">
                    <!-- Basic Information -->
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        <!-- Supplier -->
                        <div>
                            <label for="supplier_id" class="block text-sm font-medium text-gray-700">Supplier</label>
                            <select name="supplier_id" id="supplier_id" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                <option value="">Select Supplier</option>
                                @foreach($suppliers as $supplier)
                                    <option value="{{ $supplier->id }}" {{ $purchase->supplier_id == $supplier->id ? 'selected' : '' }}>
                                        {{ $supplier->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('supplier_id')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Order Date -->
                        <div>
                            <label for="order_date" class="block text-sm font-medium text-gray-700">Order Date</label>
                            <input type="date" name="order_date" id="order_date" value="{{ $purchase->order_date->format('Y-m-d') }}" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            @error('order_date')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Expected Date -->
                        <div>
                            <label for="expected_date" class="block text-sm font-medium text-gray-700">Expected Delivery Date</label>
                            <input type="date" name="expected_date" id="expected_date" value="{{ $purchase->expected_date->format('Y-m-d') }}" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            @error('expected_date')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Notes -->
                        <div>
                            <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                            <textarea name="notes" id="notes" rows="2" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">{{ $purchase->notes }}</textarea>
                            @error('notes')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Items -->
                    <div>
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-lg font-medium text-gray-900">Order Items</h4>
                            <button type="button" onclick="addItem()" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                </svg>
                                Add Item
                            </button>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead>
                                    <tr class="bg-gray-50">
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Medicine</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                        <th scope="col" class="px-6 py-3"></th>
                                    </tr>
                                </thead>
                                <tbody id="items-container" class="bg-white divide-y divide-gray-200">
                                    @foreach($purchase->items as $index => $item)
                                        <tr class="item-row">
                                            <td class="px-6 py-4">
                                                <select name="items[{{ $index }}][medicine_id]" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                                    <option value="">Select Medicine</option>
                                                    @foreach($medicines as $medicine)
                                                        <option value="{{ $medicine->id }}" {{ $item->medicine_id == $medicine->id ? 'selected' : '' }}>
                                                            {{ $medicine->name }}{{ $medicine->dosage ? ' ' . $medicine->dosage : '' }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </td>
                                            <td class="px-6 py-4">
                                                <input type="number" name="items[{{ $index }}][quantity]" value="{{ $item->quantity }}" min="1" onchange="calculateItemTotal(this)" class="block w-24 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                            </td>
                                            <td class="px-6 py-4">
                                                <input type="number" name="items[{{ $index }}][unit_price]" value="{{ $item->unit_price }}" min="0" step="0.01" onchange="calculateItemTotal(this)" class="block w-32 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                            </td>
                                            <td class="px-6 py-4 text-right">
                                                <input type="hidden" name="items[{{ $index }}][total_amount]" value="{{ $item->total_amount }}" class="item-total">
                                                <span class="item-total-display">{{ number_format($item->total_amount, 2) }}</span>
                                            </td>
                                            <td class="px-6 py-4 text-right">
                                                <button type="button" onclick="removeItem(this)" class="text-red-600 hover:text-red-900">
                                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                                    </svg>
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        <div class="space-y-4">
                            <!-- Tax -->
                            <div>
                                <label for="tax_percentage" class="block text-sm font-medium text-gray-700">Tax Percentage (%)</label>
                                <input type="number" name="tax_percentage" id="tax_percentage" value="{{ $purchase->tax_percentage }}" min="0" max="100" step="0.01" onchange="calculateTotals()" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                @error('tax_percentage')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Discount -->
                            <div>
                                <label for="discount_percentage" class="block text-sm font-medium text-gray-700">Discount Percentage (%)</label>
                                <input type="number" name="discount_percentage" id="discount_percentage" value="{{ $purchase->discount_percentage }}" min="0" max="100" step="0.01" onchange="calculateTotals()" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                @error('discount_percentage')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Shipping Cost -->
                            <div>
                                <label for="shipping_cost" class="block text-sm font-medium text-gray-700">Shipping Cost</label>
                                <input type="number" name="shipping_cost" id="shipping_cost" value="{{ $purchase->shipping_cost }}" min="0" step="0.01" onchange="calculateTotals()" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                @error('shipping_cost')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div class="bg-gray-50 px-4 py-5 rounded-lg">
                            <dl class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Subtotal</dt>
                                    <dd class="text-sm font-medium text-gray-900" id="subtotal-display">{{ number_format($purchase->total_amount, 2) }}</dd>
                                </div>
                                <div class="flex items-center justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Tax</dt>
                                    <dd class="text-sm font-medium text-gray-900" id="tax-display">{{ number_format($purchase->tax_amount, 2) }}</dd>
                                </div>
                                <div class="flex items-center justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Discount</dt>
                                    <dd class="text-sm font-medium text-gray-900" id="discount-display">{{ number_format($purchase->discount_amount, 2) }}</dd>
                                </div>
                                <div class="flex items-center justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Shipping</dt>
                                    <dd class="text-sm font-medium text-gray-900" id="shipping-display">{{ number_format($purchase->shipping_cost, 2) }}</dd>
                                </div>
                                <div class="border-t border-gray-200 pt-4 flex items-center justify-between">
                                    <dt class="text-base font-medium text-gray-900">Order Total</dt>
                                    <dd class="text-base font-medium text-gray-900" id="total-display">{{ number_format($purchase->final_amount, 2) }}</dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="px-4 py-3 bg-gray-50 text-right sm:px-6 space-x-2">
                    <a href="{{ route('inventory.purchases.show', $purchase) }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Cancel
                    </a>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Update Purchase Order
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Medicine options for dynamic rows
    const medicineOptions = @json($medicines->map(function($medicine) {
        return [
            'id' => $medicine->id,
            'name' => $medicine->name . ($medicine->dosage ? ' ' . $medicine->dosage : '')
        ];
    }));

    function createMedicineOptions() {
        let options = '<option value="">Select Medicine</option>';
        medicineOptions.forEach(medicine => {
            options += `<option value="${medicine.id}">${medicine.name}</option>`;
        });
        return options;
    }

    let itemRowTemplate = `
        <tr class="item-row">
            <td class="px-6 py-4">
                <select name="items[INDEX][medicine_id]" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    ${createMedicineOptions()}
                </select>
            </td>
            <td class="px-6 py-4">
                <input type="number" name="items[INDEX][quantity]" value="1" min="1" onchange="calculateItemTotal(this)" class="block w-24 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
            </td>
            <td class="px-6 py-4">
                <input type="number" name="items[INDEX][unit_price]" value="0" min="0" step="0.01" onchange="calculateItemTotal(this)" class="block w-32 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
            </td>
            <td class="px-6 py-4 text-right">
                <input type="hidden" name="items[INDEX][total_amount]" value="0" class="item-total">
                <span class="item-total-display">0.00</span>
            </td>
            <td class="px-6 py-4 text-right">
                <button type="button" onclick="removeItem(this)" class="text-red-600 hover:text-red-900">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                    </svg>
                </button>
            </td>
        </tr>
    `;

    function addItem() {
        const container = document.getElementById('items-container');
        const index = container.getElementsByClassName('item-row').length;
        const newRow = itemRowTemplate.replace(/INDEX/g, index);
        container.insertAdjacentHTML('beforeend', newRow);
        calculateTotals();
    }

    function removeItem(button) {
        const row = button.closest('tr');
        row.remove();
        reindexItems();
        calculateTotals();
    }

    function reindexItems() {
        const rows = document.getElementsByClassName('item-row');
        Array.from(rows).forEach((row, index) => {
            row.querySelectorAll('[name^="items["]').forEach(input => {
                input.name = input.name.replace(/items\[\d+\]/, `items[${index}]`);
            });
        });
    }

    function calculateItemTotal(input) {
        const row = input.closest('tr');
        const quantity = parseFloat(row.querySelector('input[name$="[quantity]"]').value) || 0;
        const unitPrice = parseFloat(row.querySelector('input[name$="[unit_price]"]').value) || 0;
        const total = quantity * unitPrice;
        
        row.querySelector('input[name$="[total_amount]"]').value = total.toFixed(2);
        row.querySelector('.item-total-display').textContent = total.toFixed(2);
        
        calculateTotals();
    }

    function calculateTotals() {
        const items = document.getElementsByClassName('item-row');
        let subtotal = 0;
        
        Array.from(items).forEach(row => {
            subtotal += parseFloat(row.querySelector('input[name$="[total_amount]"]').value) || 0;
        });

        const taxPercentage = parseFloat(document.getElementById('tax_percentage').value) || 0;
        const discountPercentage = parseFloat(document.getElementById('discount_percentage').value) || 0;
        const shippingCost = parseFloat(document.getElementById('shipping_cost').value) || 0;

        const taxAmount = subtotal * (taxPercentage / 100);
        const discountAmount = subtotal * (discountPercentage / 100);
        const total = subtotal + taxAmount - discountAmount + shippingCost;

        document.getElementById('subtotal-display').textContent = subtotal.toFixed(2);
        document.getElementById('tax-display').textContent = taxAmount.toFixed(2);
        document.getElementById('discount-display').textContent = discountAmount.toFixed(2);
        document.getElementById('shipping-display').textContent = shippingCost.toFixed(2);
        document.getElementById('total-display').textContent = total.toFixed(2);
    }
</script>
@endpush
@endsection 