@extends('layouts.admin')

@section('content')
<div class="w-full" x-data="{ 
    showConfirmModal: false, 
    confirmTitle: '', 
    confirmMessage: '', 
    confirmAction: null,
    confirmButtonText: 'Confirm',
    showToast(message, type = 'success') {
        window.dispatchEvent(new CustomEvent(type, {
            detail: { message }
        }));
    }
}">
    <div class="flex flex-col space-y-6">
        <div class="bg-white shadow-sm rounded-lg overflow-hidden">
            <!-- Header -->
            <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Purchase Order Details</h3>
                        <p class="mt-1 text-sm text-gray-500">PO Number: {{ $purchase->purchase_number }}</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <!-- Back to Purchase Orders Button -->
                        <a href="{{ route('inventory.purchases.index') }}"
                           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                            </svg>
                            Back to Purchase Orders
                        </a>
                        @if($purchase->status === 'pending')
                            <a href="#"
                               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                               @click.prevent="
                                   confirmTitle = 'Confirm Edit';
                                   confirmMessage = 'Are you sure you want to edit this purchase order?';
                                   confirmAction = () => window.location.href = '{{ route('inventory.purchases.edit', $purchase) }}';
                                   confirmButtonText = 'Edit';
                                   showConfirmModal = true;
                               ">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                </svg>
                                Edit
                            </a>
                            <form action="{{ route('inventory.purchases.order', $purchase) }}" method="POST" class="inline" id="order-form">
                                @csrf
                                <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                    @click="
                                        confirmTitle = 'Confirm Order';
                                        confirmMessage = 'Are you sure you want to place this purchase order? Once ordered, it cannot be edited.';
                                        confirmAction = () => {
                                            document.getElementById('order-form').submit();
                                            showToast('Purchase order placed successfully', 'success');
                                        };
                                        confirmButtonText = 'Place Order';
                                        showConfirmModal = true;
                                    ">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    Place Order
                                </button>
                            </form>
                            <form action="{{ route('inventory.purchases.destroy', $purchase) }}" method="POST" class="inline" id="delete-form">
                                @csrf
                                @method('DELETE')
                                <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                    @click="
                                        confirmTitle = 'Confirm Delete';
                                        confirmMessage = 'Are you sure you want to delete this purchase order? This action cannot be undone.';
                                        confirmAction = () => {
                                            document.getElementById('delete-form').submit();
                                            showToast('Purchase order deleted successfully', 'success');
                                        };
                                        confirmButtonText = 'Delete';
                                        showConfirmModal = true;
                                    ">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                    </svg>
                                    Delete
                                </button>
                            </form>
                        @endif
                        @if(in_array($purchase->status, ['ordered', 'partially_received']))
                            <a href="{{ route('inventory.purchases.receive.show', $purchase) }}"
                               class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4-8-4m16 0v10l-8 4-8-4V7"/>
                                </svg>
                                {{ $purchase->status === 'partially_received' ? 'Receive Remaining Items' : 'Receive Items' }}
                            </a>
                        @endif
                        @if(in_array($purchase->status, ['pending', 'ordered']))
                            <form action="{{ route('inventory.purchases.cancel', $purchase) }}" method="POST" class="inline" id="cancel-form">
                                @csrf
                                <button type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                    @click="
                                        confirmTitle = 'Confirm Cancel';
                                        confirmMessage = 'Are you sure you want to cancel this purchase order? This action cannot be undone.';
                                        confirmAction = () => {
                                            document.getElementById('cancel-form').submit();
                                            showToast('Purchase order cancelled successfully', 'warning');
                                        };
                                        confirmButtonText = 'Cancel Order';
                                        showConfirmModal = true;
                                    ">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                    </svg>
                                    Cancel
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <!-- Purchase Information -->
                    <div class="space-y-6">
                        <div>
                            <h4 class="text-lg font-medium text-gray-900">Purchase Information</h4>
                            <dl class="mt-4 space-y-4">
                                <div class="flex items-center justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                                    <dd>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{
                                            $purchase->status === 'received' ? 'bg-green-100 text-green-800' :
                                            ($purchase->status === 'partially_received' ? 'bg-orange-100 text-orange-800' :
                                            ($purchase->status === 'ordered' ? 'bg-blue-100 text-blue-800' :
                                            ($purchase->status === 'cancelled' ? 'bg-red-100 text-red-800' :
                                            'bg-yellow-100 text-yellow-800')))
                                        }}">
                                            {{ $purchase->status === 'partially_received' ? 'Partially Received' : ucfirst($purchase->status) }}
                                        </span>
                                    </dd>
                                </div>
                                <div class="flex items-center justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Payment Status</dt>
                                    <dd>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ 
                                            $purchase->payment_status === 'paid' ? 'bg-green-100 text-green-800' :
                                            ($purchase->payment_status === 'partial' ? 'bg-yellow-100 text-yellow-800' :
                                            ($purchase->payment_status === 'overdue' ? 'bg-red-100 text-red-800' :
                                            'bg-gray-100 text-gray-800'))
                                        }}">
                                            {{ ucfirst($purchase->payment_status) }}
                                        </span>
                                    </dd>
                                </div>
                                <div class="flex items-center justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Order Date</dt>
                                    <dd class="text-sm text-gray-900">{{ $purchase->order_date->format('M d, Y') }}</dd>
                                </div>
                                <div class="flex items-center justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Expected Date</dt>
                                    <dd class="text-sm text-gray-900">{{ $purchase->expected_date->format('M d, Y') }}</dd>
                                </div>
                                @if($purchase->notes)
                                    <div class="flex items-start justify-between">
                                        <dt class="text-sm font-medium text-gray-500">Notes</dt>
                                        <dd class="text-sm text-gray-900 text-right">{{ $purchase->notes }}</dd>
                                    </div>
                                @endif
                            </dl>
                        </div>

                        <div>
                            <h4 class="text-lg font-medium text-gray-900">Supplier Information</h4>
                            <dl class="mt-4 space-y-4">
                                <div class="flex items-center justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Name</dt>
                                    <dd class="text-sm text-gray-900">{{ $purchase->supplier->name }}</dd>
                                </div>
                                <div class="flex items-center justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Contact Person</dt>
                                    <dd class="text-sm text-gray-900">{{ $purchase->supplier->contact_person }}</dd>
                                </div>
                                <div class="flex items-center justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Phone</dt>
                                    <dd class="text-sm text-gray-900">{{ $purchase->supplier->phone }}</dd>
                                </div>
                                <div class="flex items-center justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                                    <dd class="text-sm text-gray-900">{{ $purchase->supplier->email }}</dd>
                                </div>
                            </dl>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-lg font-medium text-gray-900">Order Summary</h4>
                        <dl class="mt-4 space-y-4">
                            <div class="flex items-center justify-between">
                                <dt class="text-sm font-medium text-gray-500">Subtotal</dt>
                                <dd class="text-sm text-gray-900">{{ number_format($purchase->total_amount, 2) }}</dd>
                            </div>
                            <div class="flex items-center justify-between">
                                <dt class="text-sm font-medium text-gray-500">Tax ({{ $purchase->tax_percentage }}%)</dt>
                                <dd class="text-sm text-gray-900">{{ number_format($purchase->tax_amount, 2) }}</dd>
                            </div>
                            <div class="flex items-center justify-between">
                                <dt class="text-sm font-medium text-gray-500">Discount ({{ $purchase->discount_percentage }}%)</dt>
                                <dd class="text-sm text-gray-900">{{ number_format($purchase->discount_amount, 2) }}</dd>
                            </div>
                            <div class="flex items-center justify-between">
                                <dt class="text-sm font-medium text-gray-500">Shipping Cost</dt>
                                <dd class="text-sm text-gray-900">{{ number_format($purchase->shipping_cost, 2) }}</dd>
                            </div>
                            <div class="pt-4 border-t border-gray-200">
                                <div class="flex items-center justify-between font-medium">
                                    <dt class="text-base text-gray-900">Total</dt>
                                    <dd class="text-base text-gray-900">{{ number_format($purchase->final_amount, 2) }}</dd>
                                </div>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Items -->
                <div class="mt-8">
                    <h4 class="text-lg font-medium text-gray-900">Order Items</h4>
                    <div class="mt-4 overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead>
                                <tr class="bg-gray-50">
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Medicine</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Batch Number</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiry Date</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($purchase->items as $item)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $item->medicine->name }}{{ $item->medicine->dosage ? ' ' . $item->medicine->dosage : '' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $item->batch_number ?? '-' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $item->expiry_date ? $item->expiry_date->format('M d, Y') : '-' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                            {{ number_format($item->quantity) }}
                                            @if($item->received_quantity > 0)
                                                <span class="text-xs text-gray-500">({{ number_format($item->received_quantity) }} received)</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                            {{ number_format($item->unit_price, 2) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                            {{ number_format($item->total_amount, 2) }}
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">
                                            No items found for this purchase order.
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Confirmation Modal -->
    <div x-show="showConfirmModal" 
         class="fixed z-10 inset-0 overflow-y-auto" 
         aria-labelledby="modal-title" 
         role="dialog" 
         aria-modal="true"
         x-cloak>
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" 
                 aria-hidden="true"
                 x-show="showConfirmModal"
                 x-transition:enter="ease-out duration-300"
                 x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100"
                 x-transition:leave="ease-in duration-200"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"></div>

            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            
            <div x-show="showConfirmModal"
                 x-transition:enter="ease-out duration-300"
                 x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave="ease-in duration-200"
                 x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                            <svg class="h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" x-text="confirmTitle"></h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500" x-text="confirmMessage"></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" 
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                            @click="confirmAction(); showConfirmModal = false"
                            x-text="confirmButtonText">
                    </button>
                    <button type="button" 
                            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                            @click="showConfirmModal = false">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>


</div>
@endsection 