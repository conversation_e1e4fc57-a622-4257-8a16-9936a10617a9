@extends('layouts.admin')

@section('content')
<div class="w-full">
    <div class="flex flex-col space-y-6">
        <!-- Page Header -->
        <div class="bg-white shadow-sm rounded-lg overflow-hidden">
            <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
                <div class="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                    <div>
                        <h3 class="text-lg leading-6 font-medium text-gray-900">
                            {{ $purchase->status === 'partially_received' ? 'Receive Remaining Items' : 'Receive Items' }}
                        </h3>
                        <p class="mt-1 text-sm text-gray-500">
                            PO Number: {{ $purchase->purchase_number }} | Supplier: {{ $purchase->supplier->name }}
                        </p>
                    </div>
                    <div class="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
                        <a href="{{ route('inventory.purchases.show', $purchase) }}"
                           class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                            </svg>
                            Back to Purchase Order
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Livewire Receiving Component -->
        @livewire('inventory.receive-purchase', ['purchase' => $purchase])


    </div>
</div>
@endsection
