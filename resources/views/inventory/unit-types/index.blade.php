@extends('inventory.unit-types.layout')

@section('title', 'Unit Types')

@section('unit-type-content')
    <div class="space-y-6">
        <div class="flex justify-between items-center">
            <h2 class="text-2xl font-semibold text-gray-900">{{ __('Unit Types') }}</h2>
            <a href="{{ route('inventory.unit-types.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                {{ __('Add New Unit Type') }}
            </a>
        </div>

        @if (session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                <span class="block sm:inline">{{ session('success') }}</span>
            </div>
        @endif

        @if (session('error'))
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                <span class="block sm:inline">{{ session('error') }}</span>
            </div>
        @endif

        <!-- Search and Filters -->
        <div class="bg-white rounded-lg shadow p-4">
            <form action="{{ route('inventory.unit-types.index') }}" method="GET" id="filterForm" x-ref="searchForm">
                <!-- Filter Labels -->
                <div class="grid grid-cols-1 md:grid-cols-6 gap-4 mb-2">
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                    </div>
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700">Category</label>
                    </div>
                    <div>
                        <label for="is_base" class="block text-sm font-medium text-gray-700">Base Unit</label>
                    </div>
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                    </div>
                    <div>
                        <label for="sort" class="block text-sm font-medium text-gray-700">Sort By</label>
                    </div>
                    <div class="hidden md:block">
                        <!-- Empty label for Clear All Filters button alignment -->
                    </div>
                </div>
                
                <!-- Filter Controls -->
                <div class="flex flex-wrap items-center space-y-2 md:space-y-0">
                    <!-- Search with Suggestions -->
                    <div class="w-full md:w-1/6 md:pr-2">
                        <div class="relative" x-data="{ 
                            searchTerm: '{{ request('search') }}',
                            searchResults: [],
                            loading: false,
                            open: false,
                            noResults: false,
                            
                            init() {
                                this.$watch('searchTerm', (value) => {
                                    if (value.length >= 3) {
                                        this.fetchResults(value);
                                    } else {
                                        this.searchResults = [];
                                        this.open = false;
                                        this.noResults = false;
                                    }
                                });
                            },
                            
                            async fetchResults(query) {
                                this.loading = true;
                                this.noResults = false;
                                try {
                                    const response = await fetch(`{{ route('inventory.unit-types.index') }}?search=${encodeURIComponent(query)}&format=json`);
                                    if (!response.ok) throw new Error('Search failed');
                                    this.searchResults = await response.json();
                                    this.open = this.searchResults.length > 0;
                                    this.noResults = this.searchResults.length === 0;
                                } catch (error) {
                                    console.error('Search error:', error);
                                    this.searchResults = [];
                                    this.noResults = true;
                                } finally {
                                    this.loading = false;
                                }
                            },
                            
                            selectUnitType(name) {
                                this.searchTerm = name;
                                this.open = false;
                                this.noResults = false;
                                setTimeout(() => {
                                    document.getElementById('filterForm').submit();
                                }, 100);
                            }
                        }">
                            <input type="text" 
                                   name="search" 
                                   id="search" 
                                   x-model="searchTerm"
                                   @focus="searchTerm.length >= 3 && fetchResults(searchTerm)"
                                   @click.away="open = false"
                                   placeholder="Search by name, code, or abbreviation"
                                   class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                <template x-if="!loading">
                                    <button type="submit" class="p-1 focus:outline-none focus:shadow-outline">
                                        <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                </template>
                                <template x-if="loading">
                                    <svg class="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </template>
                            </div>

                            <!-- Search Results Dropdown -->
                            <div class="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md py-1 text-sm" 
                                x-show="open" 
                                x-transition:enter="transition ease-out duration-100" 
                                x-transition:enter-start="transform opacity-0 scale-95" 
                                x-transition:enter-end="transform opacity-100 scale-100" 
                                x-transition:leave="transition ease-in duration-75" 
                                x-transition:leave-start="transform opacity-100 scale-100" 
                                x-transition:leave-end="transform opacity-0 scale-95"
                                style="display: none; max-height: 300px; overflow-y: auto;">
                                <template x-for="result in searchResults" :key="result.id">
                                    <a href="#" 
                                        class="block px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                        @click.prevent="selectUnitType(result.name)">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <div class="font-medium" x-text="result.name"></div>
                                                <div class="text-xs text-gray-500" x-text="'Code: ' + result.code + ' | Abbreviation: ' + result.abbreviation"></div>
                                                <div class="text-xs text-gray-500" x-show="result.description" x-text="result.description.substring(0, 50) + (result.description.length > 50 ? '...' : '')"></div>
                                            </div>
                                            <div>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" 
                                                      :class="result.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                                                      x-text="result.is_active ? 'Active' : 'Inactive'"></span>
                                            </div>
                                        </div>
                                    </a>
                                </template>
                            </div>

                            <!-- No Results Message -->
                            <div class="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md py-2 px-4 text-sm text-gray-500" 
                                x-show="noResults"
                                style="display: none;">
                                No unit types found
                            </div>
                        </div>
                    </div>

                    <!-- Category Filter -->
                    <div class="w-full md:w-1/6 md:px-2">
                        <select name="category" id="category" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" onchange="document.getElementById('filterForm').submit()">
                            <option value="">All Categories</option>
                            @foreach($categories as $category)
                                <option value="{{ $category }}" {{ request('category') == $category ? 'selected' : '' }}>{{ Str::ucfirst($category) }}</option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Base Unit Filter -->
                    <div class="w-full md:w-1/6 md:px-2">
                        <select name="is_base" id="is_base" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" onchange="document.getElementById('filterForm').submit()">
                            <option value="">All Units</option>
                            <option value="yes" {{ request('is_base') === 'yes' ? 'selected' : '' }}>Base Units</option>
                            <option value="no" {{ request('is_base') === 'no' ? 'selected' : '' }}>Non-Base Units</option>
                        </select>
                    </div>

                    <!-- Status Filter -->
                    <div class="w-full md:w-1/6 md:px-2">
                        <select name="status" id="status" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" onchange="document.getElementById('filterForm').submit()">
                            <option value="">All Status</option>
                            <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                    </div>
                    
                    <!-- Sort Filter -->
                    <div class="w-full md:w-1/6 md:px-2">
                        <select name="sort" id="sort" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" onchange="document.getElementById('filterForm').submit()">
                            <option value="newest" {{ request('sort') === 'newest' || !request('sort') ? 'selected' : '' }}>Newest</option>
                            <option value="oldest" {{ request('sort') === 'oldest' ? 'selected' : '' }}>Oldest</option>
                            <option value="name_asc" {{ request('sort') === 'name_asc' ? 'selected' : '' }}>Name (A-Z)</option>
                            <option value="name_desc" {{ request('sort') === 'name_desc' ? 'selected' : '' }}>Name (Z-A)</option>
                        </select>
                    </div>
                    
                    <!-- Clear Filters -->
                    <div class="w-full md:w-1/6 md:pl-2 flex justify-end">
                        @if(request()->hasAny(['search', 'category', 'status', 'is_base', 'sort']))
                            <a href="{{ route('inventory.unit-types.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 whitespace-nowrap">
                                <svg xmlns="http://www.w3.org/2000/svg" class="-ml-1 mr-2 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                                Clear All Filters
                            </a>
                        @endif
                    </div>
                </div>
            </form>
        </div>

        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="bg-white">
                @if($unitTypes->isEmpty())
                    <div class="text-center py-8">
                        <h3 class="text-lg font-medium text-gray-900">No unit types found</h3>
                        <p class="mt-1 text-sm text-gray-500">Get started by adding a new unit type.</p>
                    </div>
                @else
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Abbreviation</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Base Unit</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($unitTypes as $unitType)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ $unitType->name }}</div>
                                            @if($unitType->description)
                                                <div class="text-xs text-gray-500">{{ Str::limit($unitType->description, 50) }}</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $unitType->code }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $unitType->abbreviation }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ Str::ucfirst($unitType->category) }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $unitType->is_base ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                                {{ $unitType->is_base ? 'Yes' : 'No' }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $unitType->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                {{ $unitType->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <a href="{{ route('inventory.unit-types.edit', $unitType) }}" class="text-indigo-600 hover:text-indigo-900 mr-3">Edit</a>
                                            <form action="{{ route('inventory.unit-types.destroy', $unitType) }}" method="POST" class="inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-red-600 hover:text-red-900" onclick="return confirm('Are you sure you want to delete this unit type?')">Delete</button>
                                            </form>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @endif
            </div>
        </div>

        <div class="mt-4">
            {{ $unitTypes->links() }}
        </div>
    </div>
@endsection
