@extends('layouts.admin')

@section('content')
<div class="container mx-auto px-4 py-6"
     x-data="medicineDetailOffline({{ $medicine->id }})"
     x-init="init()">
    <!-- Offline Status Banner -->
    <div x-show="isOffline && !isOnline"
         class="mb-4 bg-yellow-50 border border-yellow-200 rounded-md p-4"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform -translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">Viewing Offline Data</h3>
                <div class="mt-2 text-sm text-yellow-700">
                    <p>You're currently offline. This data was last synced <span x-text="lastSyncFormatted"></span>. Some features may be unavailable.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Stale Data Warning -->
    <div x-show="isDataStale"
         class="mb-4 bg-orange-50 border border-orange-200 rounded-md p-4"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform -translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-orange-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-orange-800">Data May Be Outdated</h3>
                <div class="mt-2 text-sm text-orange-700">
                    <p>This data was last updated <span x-text="lastSyncFormatted"></span>. Connect to the internet to get the latest information.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="flex justify-between items-center mb-6">
        <div class="flex items-center space-x-3">
            <h1 class="text-2xl font-bold text-gray-900" x-text="medicineData.name + (medicineData.dosage ? ' ' + medicineData.dosage : '')">{{ $medicine->name }}{{ $medicine->dosage ? ' ' . $medicine->dosage : '' }}</h1>
            <!-- Offline indicator badge -->
            <span x-show="isOffline && !isOnline"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-gray-400" fill="currentColor" viewBox="0 0 8 8">
                    <circle cx="4" cy="4" r="3" />
                </svg>
                Offline
            </span>
        </div>
        <div class="flex space-x-2">
            <a href="{{ route('inventory.medicines.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
                Back to Medicines
            </a>

            <!-- Edit Button - Disabled when offline -->
            <template x-if="isOnline">
                <a href="{{ route('inventory.medicines.edit', $medicine->id) }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                    </svg>
                    Edit Medicine
                </a>
            </template>

            <!-- Disabled Edit Button when offline -->
            <template x-if="!isOnline">
                <button disabled class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-gray-400 bg-gray-200 cursor-not-allowed" title="Editing is not available offline">
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                    </svg>
                    Edit Medicine (Offline)
                </button>
            </template>

            <!-- History Button - Disabled when offline -->
            <template x-if="isOnline">
                <a href="{{ route('inventory.medicines.history', $medicine->id) }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                    </svg>
                    View History
                </a>
            </template>

            <!-- Disabled History Button when offline -->
            <template x-if="!isOnline">
                <button disabled class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-gray-400 bg-gray-200 cursor-not-allowed" title="History is not available offline">
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                    </svg>
                    View History (Offline)
                </button>
            </template>
        </div>
    </div>

    <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Medicine Information</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">Details about the medicine and its properties.</p>
                </div>
                <!-- Data freshness indicator -->
                <div x-show="!isOnline" class="flex items-center text-sm text-gray-500">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span x-text="'Cached ' + lastSyncFormatted"></span>
                </div>
            </div>
        </div>
        <div class="border-t border-gray-200">
            <dl>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Medicine Name</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2" x-text="medicineData.name + (medicineData.dosage ? ' ' + medicineData.dosage : '')">{{ $medicine->name }}{{ $medicine->dosage ? ' ' . $medicine->dosage : '' }}</dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Generic Name</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2" x-text="medicineData.generic_name || 'Not specified'">{{ $medicine->generic_name }}</dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Manufacturer</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2" x-text="medicineData.manufacturer?.name || 'Not specified'">{{ $medicine->manufacturer->name ?? 'Not specified' }}</dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Category</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2" x-text="medicineData.category?.name || 'Not categorized'">{{ $medicine->category->name ?? 'Not categorized' }}</dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Retail Price (Unit)</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2" x-text="'৳ ' + (medicineData.retail_price_unit ? parseFloat(medicineData.retail_price_unit).toFixed(2) : '0.00')">৳ {{ number_format($medicine->retail_price_unit, 2) }}</dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Prescription Required</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <span x-show="medicineData.prescription_required" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Yes</span>
                        <span x-show="!medicineData.prescription_required" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">No</span>
                        <!-- Fallback for server-side rendering -->
                        <noscript>
                            @if($medicine->prescription_required)
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Yes</span>
                            @else
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">No</span>
                            @endif
                        </noscript>
                    </dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Controlled Substance</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <span x-show="medicineData.controlled_substance" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Yes</span>
                        <span x-show="!medicineData.controlled_substance" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">No</span>
                        <!-- Fallback for server-side rendering -->
                        <noscript>
                            @if($medicine->controlled_substance)
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Yes</span>
                            @else
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">No</span>
                            @endif
                        </noscript>
                    </dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Total Stock</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                              :class="{
                                  'bg-green-100 text-green-800': totalStock > 10,
                                  'bg-yellow-100 text-yellow-800': totalStock > 0 && totalStock <= 10,
                                  'bg-red-100 text-red-800': totalStock === 0
                              }"
                              x-text="totalStock + ' units'">
                            {{ $total_stock }} units
                        </span>
                    </dd>
                </div>
            </dl>
        </div>
    </div>

    <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Inventory Batches</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">Current stock by batch and location.</p>
                </div>
                <!-- Loading indicator -->
                <div x-show="isLoading" class="flex items-center text-sm text-gray-500">
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Loading...
                </div>
            </div>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Batch #</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiry Date</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- Dynamic batch rows -->
                    <template x-for="batch in batchesData" :key="batch.id">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" x-text="batch.batch_number"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span x-text="formatExpiryDate(batch.expiry_date)"></span>
                                <span x-show="isExpired(batch.expiry_date)" class="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Expired</span>
                                <span x-show="isExpiringSoon(batch.expiry_date)" class="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Expiring Soon</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span x-text="batch.location?.name || 'Unknown'"></span>
                                <span x-show="batch.rack_number" class="text-xs text-gray-500" x-text="'(Rack: ' + batch.rack_number + ')'"></span>
                                <span x-show="batch.bin_location" class="text-xs text-gray-500" x-text="'(Bin: ' + batch.bin_location + ')'"></span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="batch.quantity"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="'৳ ' + parseFloat(batch.unit_price || 0).toFixed(2)"></td>
                        </tr>
                    </template>

                    <!-- No batches message -->
                    <tr x-show="!isLoading && batchesData.length === 0">
                        <td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">No batches found</td>
                    </tr>

                    <!-- Fallback server-side rendered rows for no-JS users -->
                    <noscript>
                        @forelse($batches as $batch)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $batch->batch_number }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ $batch->expiry_date ? $batch->expiry_date->format('d M Y') : 'N/A' }}
                                    @if($batch->expiry_date && $batch->expiry_date->isPast())
                                        <span class="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Expired</span>
                                    @elseif($batch->expiry_date && $batch->expiry_date->diffInMonths(now()) <= 3)
                                        <span class="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Expiring Soon</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ $batch->location->name ?? 'Unknown' }}
                                    @if($batch->rack_number)
                                        <span class="text-xs text-gray-500">(Rack: {{ $batch->rack_number }})</span>
                                    @endif
                                    @if($batch->bin_location)
                                        <span class="text-xs text-gray-500">(Bin: {{ $batch->bin_location }})</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $batch->quantity }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">৳ {{ number_format($batch->unit_price, 2) }}</td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">No batches found</td>
                            </tr>
                        @endforelse
                    </noscript>
                </tbody>
            </table>
        </div>
    </div>

    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Pricing Information</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">Retail and supplier pricing for different units.</p>
        </div>
        <div class="border-t border-gray-200">
            <dl>
                <!-- Retail Price (Box) -->
                <div x-show="medicineData.retail_price_box" class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Retail Price (Box)</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2" x-text="'৳ ' + (medicineData.retail_price_box ? parseFloat(medicineData.retail_price_box).toFixed(2) : '0.00')"></dd>
                </div>

                <!-- Retail Price (Strip) -->
                <div x-show="medicineData.retail_price_strip" class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Retail Price (Strip)</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2" x-text="'৳ ' + (medicineData.retail_price_strip ? parseFloat(medicineData.retail_price_strip).toFixed(2) : '0.00')"></dd>
                </div>

                <!-- Retail Price (Unit) -->
                <div x-show="medicineData.retail_price_unit" class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Retail Price (Unit)</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2" x-text="'৳ ' + (medicineData.retail_price_unit ? parseFloat(medicineData.retail_price_unit).toFixed(2) : '0.00')"></dd>
                </div>

                <!-- Supplier Price (Box) -->
                <div x-show="medicineData.supplier_price_box" class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Supplier Price (Box)</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2" x-text="'৳ ' + (medicineData.supplier_price_box ? parseFloat(medicineData.supplier_price_box).toFixed(2) : '0.00')"></dd>
                </div>

                <!-- Supplier Price (Strip) -->
                <div x-show="medicineData.supplier_price_strip" class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Supplier Price (Strip)</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2" x-text="'৳ ' + (medicineData.supplier_price_strip ? parseFloat(medicineData.supplier_price_strip).toFixed(2) : '0.00')"></dd>
                </div>

                <!-- Supplier Price (Unit) -->
                <div x-show="medicineData.supplier_price_unit" class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Supplier Price (Unit)</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2" x-text="'৳ ' + (medicineData.supplier_price_unit ? parseFloat(medicineData.supplier_price_unit).toFixed(2) : '0.00')"></dd>
                </div>

                <!-- Fallback server-side rendered pricing for no-JS users -->
                <noscript>
                    @if($medicine->retail_price_box)
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Retail Price (Box)</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">৳ {{ number_format($medicine->retail_price_box, 2) }}</dd>
                    </div>
                    @endif

                    @if($medicine->retail_price_strip)
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Retail Price (Strip)</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">৳ {{ number_format($medicine->retail_price_strip, 2) }}</dd>
                    </div>
                    @endif

                    @if($medicine->retail_price_unit)
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Retail Price (Unit)</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">৳ {{ number_format($medicine->retail_price_unit, 2) }}</dd>
                    </div>
                    @endif

                    @if($medicine->supplier_price_box)
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Supplier Price (Box)</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">৳ {{ number_format($medicine->supplier_price_box, 2) }}</dd>
                    </div>
                    @endif

                    @if($medicine->supplier_price_strip)
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Supplier Price (Strip)</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">৳ {{ number_format($medicine->supplier_price_strip, 2) }}</dd>
                    </div>
                    @endif

                    @if($medicine->supplier_price_unit)
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Supplier Price (Unit)</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">৳ {{ number_format($medicine->supplier_price_unit, 2) }}</dd>
                    </div>
                    @endif
                </noscript>
            </dl>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Medicine Detail Offline Functionality
function medicineDetailOffline(medicineId) {
    return {
        // State variables
        medicineId: medicineId,
        isOnline: navigator.onLine,
        isOffline: false,
        isLoading: true,
        isDataStale: false,
        lastSync: null,
        lastSyncFormatted: 'Never',

        // Data
        medicineData: @json($medicine),
        batchesData: @json($batches),
        totalStock: {{ $total_stock }},

        // Initialize the component
        async init() {
            console.log('Initializing medicine detail offline functionality...');

            // Set initial online status
            this.updateOnlineStatus();

            // Listen for online/offline events
            window.addEventListener('online', () => this.updateOnlineStatus());
            window.addEventListener('offline', () => this.updateOnlineStatus());

            // Load cached data if offline
            if (!this.isOnline) {
                await this.loadCachedData();
            } else {
                // Cache current data for offline use
                await this.cacheCurrentData();
            }

            // Update sync status
            this.updateSyncStatus();

            this.isLoading = false;
        },

        // Update online/offline status
        updateOnlineStatus() {
            this.isOnline = navigator.onLine;
            this.isOffline = !this.isOnline;

            if (this.isOnline) {
                console.log('Medicine detail: Online mode');
                // Optionally refresh data when coming back online
                this.refreshDataIfStale();
            } else {
                console.log('Medicine detail: Offline mode');
                this.loadCachedData();
            }
        },

        // Load cached medicine data from IndexedDB
        async loadCachedData() {
            try {
                if (typeof window.OfflineDB === 'undefined') {
                    console.warn('OfflineDB not available, using server data');
                    return;
                }

                // Get cached medicine data
                const cachedMedicine = await window.OfflineDB.getMedicineById(this.medicineId);
                if (cachedMedicine) {
                    this.medicineData = cachedMedicine;
                    console.log('Loaded cached medicine data:', cachedMedicine);
                }

                // Get cached inventory/batches data
                const cachedInventory = await window.OfflineDB.getInventoryByMedicineId(this.medicineId);
                if (cachedInventory && cachedInventory.length > 0) {
                    this.batchesData = cachedInventory;
                    this.totalStock = cachedInventory.reduce((sum, batch) => sum + (batch.quantity || 0), 0);
                    console.log('Loaded cached inventory data:', cachedInventory);
                }

            } catch (error) {
                console.error('Error loading cached medicine data:', error);
            }
        },

        // Cache current data for offline use
        async cacheCurrentData() {
            try {
                if (typeof window.OfflineDB === 'undefined') {
                    console.warn('OfflineDB not available, cannot cache data');
                    return;
                }

                // Cache medicine data
                await window.OfflineDB.cacheMedicines([this.medicineData]);

                // Cache inventory data
                if (this.batchesData && this.batchesData.length > 0) {
                    await window.OfflineDB.cacheInventory(this.batchesData);
                }

                console.log('Cached medicine detail data for offline use');

            } catch (error) {
                console.error('Error caching medicine data:', error);
            }
        },

        // Update sync status information
        updateSyncStatus() {
            const lastSyncTime = localStorage.getItem('lastSync');
            if (lastSyncTime && lastSyncTime !== 'Never') {
                this.lastSync = new Date(lastSyncTime);
                this.lastSyncFormatted = this.formatRelativeTime(this.lastSync);

                // Check if data is stale (older than 1 hour)
                const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
                this.isDataStale = this.lastSync < oneHourAgo;
            } else {
                this.lastSyncFormatted = 'Never';
                this.isDataStale = true;
            }
        },

        // Refresh data if it's stale and we're online
        async refreshDataIfStale() {
            if (this.isDataStale && this.isOnline) {
                try {
                    // Trigger a sync to get fresh data
                    if (typeof window.forceSync === 'function') {
                        await window.forceSync();
                        // Reload the page to get fresh data
                        window.location.reload();
                    }
                } catch (error) {
                    console.error('Error refreshing stale data:', error);
                }
            }
        },

        // Format relative time
        formatRelativeTime(date) {
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) {
                return 'just now';
            } else if (diffInSeconds < 3600) {
                const minutes = Math.floor(diffInSeconds / 60);
                return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
            } else if (diffInSeconds < 86400) {
                const hours = Math.floor(diffInSeconds / 3600);
                return `${hours} hour${hours > 1 ? 's' : ''} ago`;
            } else {
                const days = Math.floor(diffInSeconds / 86400);
                return `${days} day${days > 1 ? 's' : ''} ago`;
            }
        },

        // Format expiry date
        formatExpiryDate(expiryDate) {
            if (!expiryDate) return 'N/A';

            try {
                const date = new Date(expiryDate);
                return date.toLocaleDateString('en-GB', {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric'
                });
            } catch (error) {
                return 'N/A';
            }
        },

        // Check if batch is expired
        isExpired(expiryDate) {
            if (!expiryDate) return false;

            try {
                const date = new Date(expiryDate);
                return date < new Date();
            } catch (error) {
                return false;
            }
        },

        // Check if batch is expiring soon (within 3 months)
        isExpiringSoon(expiryDate) {
            if (!expiryDate || this.isExpired(expiryDate)) return false;

            try {
                const date = new Date(expiryDate);
                const threeMonthsFromNow = new Date();
                threeMonthsFromNow.setMonth(threeMonthsFromNow.getMonth() + 3);

                return date <= threeMonthsFromNow;
            } catch (error) {
                return false;
            }
        }
    };
}
</script>
@endpush