@extends('inventory.medicines.layout')

@section('medicine-content')
    <div class="w-full">
        <div class="flex flex-col space-y-6">
            <!-- Header -->
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
                <div class="flex items-center space-x-4">
                    <h2 class="text-2xl font-semibold text-gray-800">{{ __('Medicines') }}</h2>
                    @can('view archived inventory')
                    <a href="{{ route('inventory.medicines.archived') }}" 
                       class="ml-4 text-gray-600 hover:text-gray-900 flex items-center space-x-1">
                        <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                        </svg>
                        <span>{{ __('View Archived') }}</span>
                    </a>
                    @endcan
                </div>
                <div class="flex items-center space-x-4">
                    <!-- Export/Import Buttons -->
                    <div class="flex items-center space-x-2">
                        <!-- Export Dropdown -->
                        <div class="relative" x-data="{ exportOpen: false }">
                            <button @click="exportOpen = !exportOpen"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                <svg class="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                {{ __('Export') }}
                                <svg class="ml-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>

                            <div x-show="exportOpen"
                                 @click.away="exportOpen = false"
                                 x-transition:enter="transition ease-out duration-100"
                                 x-transition:enter-start="transform opacity-0 scale-95"
                                 x-transition:enter-end="transform opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="transform opacity-100 scale-100"
                                 x-transition:leave-end="transform opacity-0 scale-95"
                                 class="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
                                <div class="py-1">
                                    <a href="{{ route('inventory.medicines.export', array_merge(request()->query(), ['format' => 'xlsx'])) }}"
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <svg class="mr-3 h-4 w-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                        {{ __('Excel (.xlsx)') }}
                                    </a>
                                    <a href="{{ route('inventory.medicines.export', array_merge(request()->query(), ['format' => 'xls'])) }}"
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <svg class="mr-3 h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                        {{ __('Excel (.xls)') }}
                                    </a>
                                    <a href="{{ route('inventory.medicines.export', array_merge(request()->query(), ['format' => 'csv'])) }}"
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <svg class="mr-3 h-4 w-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                        {{ __('CSV (.csv)') }}
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Import Button -->
                        <button type="button"
                                id="import-button"
                       class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" fill="currentColor">
                                <path d="M603.886933 0c17.6128 0 32.221867 5.3248 45.056 15.837867l6.280534 5.666133 222.890666 220.842667c13.038933 11.946667 20.616533 26.624 22.528 43.690666l0.477867 8.874667-0.068267 182.8864h-81.988266l0.068266-140.151467H669.764267a102.4 102.4 0 0 1-101.9904-92.501333l-0.4096-9.8304-0.068267-153.463467-361.8816 0.068267c-29.696 0-51.6096 19.387733-54.818133 46.830933l-0.4096 6.9632v697.412267c0 28.945067 19.8656 50.722133 47.991466 53.930667L205.482667 887.466667H546.133333v81.92H205.4144C131.959467 969.386667 73.728 915.2512 68.608 843.502933L68.266667 833.1264V135.714133C68.266667 62.6688 122.88 5.461333 194.9696 0.341333L205.482667 0h398.472533z m69.632 543.9488c22.254933 0 40.3456 19.114667 40.3456 42.666667v1.024h0.2048v57.480533h222.208v139.0592H714.069333v55.978667h-0.341333l0.136533 2.4576c0 23.552-18.090667 42.666667-40.413866 42.666666a38.980267 38.980267 0 0 1-26.965334-11.0592l-134.826666-127.863466a44.100267 44.100267 0 0 1 0.136533-63.2832l134.621867-128.2048a40.004267 40.004267 0 0 1 27.0336-10.922667zM635.562667 117.418667v117.9648a34.133333 34.133333 0 0 0 27.989333 33.5872l6.144 0.546133 119.330133-0.068267L635.562667 117.3504z"/>
                            </svg>
                            {{ __('Import') }}
                        </button>
                    </div>

                    <a href="{{ route('inventory.medicines.create') }}"
                       class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        {{ __('Add New Medicine') }}
                    </a>
                </div>
            </div>

            @if (session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
            @endif

            @if (session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                    <span class="block sm:inline">{{ session('error') }}</span>
                </div>
            @endif

            <!-- Search and Filters -->
            <div class="bg-white shadow rounded-lg mb-6" x-data="{ 
                showAdvancedFilters: false,
                searchTerm: '{{ request('search') }}',
                searchResults: [],
                loading: false,
                open: false,
                init() {
                    this.$watch('searchTerm', (value) => {
                        this.searchMedicines(value);
                    });
                },
                async searchMedicines(query) {
                    if (query.length < 3) {
                        this.searchResults = [];
                        this.open = false;
                        return;
                    }

                    this.loading = true;
                    try {
                        const response = await fetch(`{{ route('inventory.medicines.search') }}?query=${encodeURIComponent(query)}`);
                        if (!response.ok) throw new Error('Search failed');
                        const data = await response.json();
                        this.searchResults = data;
                        this.open = true;
                    } catch (error) {
                        console.error('Search error:', error);
                        this.searchResults = [];
                    } finally {
                        this.loading = false;
                    }
                },
                selectMedicine(medicine) {
                    this.searchTerm = medicine.name + (medicine.dosage ? ' ' + medicine.dosage : '');
                    this.open = false;
                    // Submit the form when a medicine is selected
                    this.$refs.searchForm.submit();
                }
            }">
                <div class="px-4 py-5 sm:p-6">
                    <form x-ref="searchForm" action="{{ route('inventory.medicines.index') }}" method="GET" class="space-y-4">
                        <!-- Primary Filters Row -->
                        <div class="flex flex-col sm:flex-row flex-wrap gap-4 items-start sm:items-end">
                            <!-- Search Input -->
                            <div class="w-full sm:flex-1 min-w-[200px]">
                                <label for="search" class="block text-sm font-medium text-gray-700">Search Medicines</label>
                                <div class="mt-1 relative">
                                    <input type="text" 
                                           name="search" 
                                           id="search"
                                           x-model="searchTerm"
                                           @focus="open = searchTerm.length >= 3"
                                           @click.away="open = false"
                                           placeholder="Search by name, generic name..." 
                                           class="focus:ring-primary focus:border-primary block w-full pr-10 sm:text-sm border-gray-300 rounded-md">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <template x-if="!loading">
                                            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                            </svg>
                                        </template>
                                        <template x-if="loading">
                                            <svg class="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                        </template>
                                    </div>

                                    <!-- Search Results Dropdown -->
                                    <div class="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md py-1 text-sm" 
                                        x-show="open && searchResults.length > 0" 
                                        x-transition:enter="transition ease-out duration-100" 
                                        x-transition:enter-start="transform opacity-0 scale-95" 
                                        x-transition:enter-end="transform opacity-100 scale-100" 
                                        x-transition:leave="transition ease-in duration-75" 
                                        x-transition:leave-start="transform opacity-100 scale-100" 
                                        x-transition:leave-end="transform opacity-0 scale-95"
                                        style="max-height: 300px; overflow-y: auto;">
                                        <template x-for="(result, idx) in searchResults" :key="idx">
                                            <a href="#" 
                                                class="block px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                                @click.prevent="selectMedicine(result)">
                                                <div class="font-medium" x-text="result.name + (result.dosage ? ' ' + result.dosage : '')"></div>
                                                <div class="flex justify-between items-center text-xs text-gray-500">
                                                    <div>
                                                        <span x-text="result.generic_name"></span>
                                                        <span x-show="result.manufacturer" x-text="' | ' + result.manufacturer"></span>
                                                    </div>
                                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium" 
                                                        :class="{
                                                            'bg-green-100 text-green-800': result.total_stock > 10,
                                                            'bg-yellow-100 text-yellow-800': result.total_stock > 0 && result.total_stock <= 10,
                                                            'bg-red-100 text-red-800': result.total_stock === 0
                                                        }">
                                                        <span x-text="'Stock: ' + result.total_stock"></span>
                                                    </span>
                                                </div>
                                            </a>
                                        </template>
                                    </div>

                                    <!-- No Results Message -->
                                    <div class="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md py-2 px-4 text-sm text-gray-500" 
                                        x-show="open && searchTerm.length >= 3 && searchResults.length === 0 && !loading">
                                        No medicines found
                                    </div>
                                </div>
                            </div>

                            <!-- Category Filter -->
                            <div class="w-full sm:w-auto min-w-[200px]">
                                <label for="category_id" class="block text-sm font-medium text-gray-700">Category</label>
                                <select name="category_id" 
                                        id="category_id" 
                                        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                    <option value="">All Categories</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ request('category_id') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Manufacturer Filter -->
                            <div class="w-full sm:w-auto min-w-[200px]">
                                <label for="manufacturer_id" class="block text-sm font-medium text-gray-700">Manufacturer</label>
                                <select name="manufacturer_id" 
                                        id="manufacturer_id" 
                                        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                    <option value="">All Manufacturers</option>
                                    @foreach($manufacturers as $manufacturer)
                                        <option value="{{ $manufacturer->id }}" {{ request('manufacturer_id') == $manufacturer->id ? 'selected' : '' }}>
                                            {{ $manufacturer->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Search Button -->
                            <div class="flex flex-wrap gap-2">
                                <button type="submit" 
                                class="inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                    <svg class="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                    </svg>
                                    Search
                                </button>
                                <button type="button" 
                                        @click="showAdvancedFilters = !showAdvancedFilters"
                                        class="inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                    <svg class="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                                    </svg>
                                    Advanced Filters
                                </button>
                            </div>
                        </div>

                        <!-- Advanced Filters (Collapsible) -->
                        <div x-show="showAdvancedFilters" 
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform -translate-y-2"
                             x-transition:enter-end="opacity-100 transform translate-y-0"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 transform translate-y-0"
                             x-transition:leave-end="opacity-0 transform -translate-y-2"
                             class="pt-4 border-t">
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                                <!-- Controlled Substance Filter -->
                                <div>
                                    <label for="controlled_substance" class="block text-sm font-medium text-gray-700">Controlled Substance</label>
                                    <select name="controlled_substance" 
                                            id="controlled_substance" 
                                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                        <option value="">All</option>
                                        <option value="1" {{ request('controlled_substance') === '1' ? 'selected' : '' }}>Yes</option>
                                        <option value="0" {{ request('controlled_substance') === '0' ? 'selected' : '' }}>No</option>
                                    </select>
                                </div>

                                <!-- Prescription Required Filter -->
                                <div>
                                    <label for="prescription_required" class="block text-sm font-medium text-gray-700">Prescription Required</label>
                                    <select name="prescription_required" 
                                            id="prescription_required" 
                                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                        <option value="">All</option>
                                        <option value="1" {{ request('prescription_required') === '1' ? 'selected' : '' }}>Yes</option>
                                        <option value="0" {{ request('prescription_required') === '0' ? 'selected' : '' }}>No</option>
                                    </select>
                                </div>

                                <!-- Sort Order -->
                                <div>
                                    <label for="sort" class="block text-sm font-medium text-gray-700">Sort Order</label>
                                    <select name="sort" 
                                            id="sort" 
                                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                        <option value="asc" {{ request('sort') === 'asc' ? 'selected' : '' }}>Ascending</option>
                                        <option value="desc" {{ request('sort') === 'desc' ? 'selected' : '' }}>Descending</option>
                                    </select>
                                </div>

                                <!-- Status Filter -->
                                <div>
                                    <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                                    <select name="status" 
                                            id="status" 
                                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                        <option value="">All Status</option>
                                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Active Filters -->
                        @if(array_filter(request()->only(['search', 'category_id', 'manufacturer_id', 'status', 'controlled_substance', 'prescription_required'])))
                            <div class="flex flex-wrap gap-2 pt-2">
                                @foreach(request()->only(['search', 'category_id', 'manufacturer_id', 'status', 'controlled_substance', 'prescription_required']) as $filter => $value)
                                    @if($value)
                                        <span class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                            {{ ucfirst(str_replace('_', ' ', $filter)) }}: {{ $value }}
                                            <a href="{{ request()->fullUrlWithQuery(array_merge(request()->query(), [$filter => null])) }}" 
                                               class="ml-1.5 inline-flex items-center justify-center text-blue-800 hover:text-blue-900">
                                                <span class="sr-only">Remove filter for {{ $filter }}</span>
                                                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                                </svg>
                                            </a>
                                        </span>
                                    @endif
                                @endforeach
                                <a href="{{ route('inventory.medicines.index') }}" 
                                   class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-gray-100 text-gray-800 hover:bg-gray-200">
                                    Clear All Filters
                                </a>
                            </div>
                        @endif
                    </form>
                </div>
            </div>

            <!-- Medicines Table -->
            <div class="overflow-x-auto bg-white rounded-lg shadow">
                <table class="w-full table-auto">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-2 sm:px-4 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <div class="flex items-center space-x-1">
                                    <span>{{ __('ID') }}</span>
                                    <div class="flex flex-col">
                                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'asc']) }}" class="text-gray-400 hover:text-gray-600 {{ $sort === 'asc' ? 'text-blue-600' : '' }}">
                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                                            </svg>
                                        </a>
                                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'desc']) }}" class="text-gray-400 hover:text-gray-600 {{ $sort === 'desc' ? 'text-blue-600' : '' }}">
                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </th>
                            <th scope="col" class="px-2 sm:px-4 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Name') }}</th>
                            <th scope="col" class="px-2 sm:px-4 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">{{ __('Generic Name') }}</th>
                            <th scope="col" class="px-2 sm:px-4 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">{{ __('Manufacturer') }}</th>
                            <th scope="col" class="px-2 sm:px-4 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Stock') }}</th>
                            <th scope="col" class="px-2 sm:px-4 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">{{ __('Unit Price') }}</th>
                            <th scope="col" class="px-2 sm:px-4 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">{{ __('Expiry') }}</th>
                            <th scope="col" class="px-2 sm:px-4 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Status') }}</th>
                            <th scope="col" class="px-2 sm:px-4 md:px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Actions') }}</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($medicines as $medicine)
                            <tr class="hover:bg-gray-50">
                                <td class="px-2 sm:px-4 md:px-6 py-2 md:py-4 whitespace-nowrap">
                                    {{ $medicine->id }}
                                </td>
                                <td class="px-2 sm:px-4 md:px-6 py-2 md:py-4 whitespace-nowrap">
                                    <div class="font-medium text-gray-900 truncate max-w-[100px] sm:max-w-[200px]" title="{{ $medicine->name }} {{ $medicine->dosage ?? '' }}">
                                        {{ $medicine->name }} {{ $medicine->dosage ?? '' }}
                                    </div>
                                </td>
                                <td class="px-2 sm:px-4 md:px-6 py-2 md:py-4 whitespace-nowrap hidden md:table-cell">
                                    <div class="text-gray-900 truncate max-w-[150px] md:max-w-[200px]" title="{{ $medicine->generic_name }}">
                                        {{ $medicine->generic_name }}
                                    </div>
                                </td>
                                <td class="px-2 sm:px-4 md:px-6 py-2 md:py-4 whitespace-nowrap hidden sm:table-cell">
                                    <div class="text-gray-900 truncate max-w-[100px] md:max-w-[150px]" title="{{ $medicine->manufacturer->name ?? $medicine->manufacturer ?? 'N/A' }}">
                                        {{ $medicine->manufacturer->name ?? $medicine->manufacturer ?? ($medicine->manufacturer_id ? 'Unknown Manufacturer' : 'N/A') }}
                                    </div>
                                </td>
                                <td class="px-2 sm:px-4 md:px-6 py-2 md:py-4 whitespace-nowrap">
                                    {{ $medicine->inventories->sum('quantity') ?? 0 }}
                                </td>
                                <td class="px-2 sm:px-4 md:px-6 py-2 md:py-4 whitespace-nowrap hidden sm:table-cell">
                                    {{ number_format($medicine->inventories->max('unit_price') ?? 0, 2) }}
                                </td>
                                <td class="px-2 sm:px-4 md:px-6 py-2 md:py-4 whitespace-nowrap hidden md:table-cell">
                                    @php
                                        $latestExpiry = $medicine->inventories->max('expiry_date');
                                        $expiryClass = '';
                                        if ($latestExpiry) {
                                            $expiryDate = \Carbon\Carbon::parse($latestExpiry);
                                            if ($expiryDate->isPast()) {
                                                $expiryClass = 'text-red-600';
                                            } elseif ($expiryDate->diffInMonths(now()) <= 3) {
                                                $expiryClass = 'text-yellow-600';
                                            }
                                        }
                                    @endphp
                                    <div class="{{ $expiryClass }}">
                                        {{ $latestExpiry ? date('Y-m-d', strtotime($latestExpiry)) : 'N/A' }}
                                    </div>
                                </td>
                                <td class="px-2 sm:px-4 md:px-6 py-2 md:py-4 whitespace-nowrap">
                                    @php
                                        $stock = $medicine->inventories->sum('quantity');
                                        $statusClass = $stock > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                                        $statusText = $stock > 0 ? 'In Stock' : 'Out of Stock';
                                    @endphp
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $statusClass }}">
                                        {{ $statusText }}
                                    </span>
                                </td>
                                <td class="px-2 sm:px-4 md:px-6 py-2 md:py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex justify-end space-x-1 sm:space-x-2">
                                        <a href="{{ route('inventory.medicines.history', $medicine) }}" 
                                           class="inline-flex text-blue-600 hover:text-blue-900"
                                           title="View History">
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </a>
                                        <a href="{{ route('inventory.medicines.edit', $medicine) }}" 
                                           class="inline-flex text-indigo-600 hover:text-indigo-900"
                                           title="Edit">
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                            </svg>
                                        </a>
                                        <a href="{{ route('inventory.medicines.duplicate', $medicine) }}" 
                                           class="inline-flex text-green-600 hover:text-green-900"
                                           title="Duplicate">
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                            </svg>
                                        </a>                                        
                                        <form action="{{ route('inventory.medicines.destroy', $medicine) }}" 
                                              method="POST" 
                                              class="inline-flex"
                                              onsubmit="return confirm('Are you sure you want to delete this medicine?');">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" 
                                                    class="text-red-600 hover:text-red-900"
                                                    title="Delete">
                                                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-4">
                {{ $medicines->links() }}
            </div>
        </div>
    </div>

    <!-- Import Modal -->
    <div x-data="importModalData()"
    @open-import-modal.window="showImportModal = true"
    @keydown.escape.window="showImportModal = false">

        <!-- Modal Backdrop -->
        <div x-show="showImportModal"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity z-50"
             @click="showImportModal = false">
        </div>

        <!-- Modal Content -->
        <div x-show="showImportModal"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             class="fixed inset-0 z-50 overflow-y-auto">

            <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                <div class="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg"
                     @click.stop>

                    <!-- Modal Header -->
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium leading-6 text-gray-900">
                                {{ __('Import Medicines') }}
                            </h3>
                            <button type="button"
                                    @click="showImportModal = false"
                                    class="text-gray-400 hover:text-gray-600">
                                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        <!-- Import Form -->
                        <form id="importForm"
                              action="{{ route('inventory.medicines.import') }}"
                              method="POST"
                              enctype="multipart/form-data"
                              @submit.prevent="submitImport">
                            @csrf

                            <!-- File Upload Area -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ __('Select Excel/CSV File') }}
                                </label>

                                <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-dashed rounded-md"
                                     :class="dragOver ? 'border-indigo-400 bg-indigo-50' : 'border-gray-300'"
                                     @dragover.prevent="dragOver = true"
                                     @dragleave.prevent="dragOver = false"
                                     @drop.prevent="handleFileDrop($event)">

                                    <div class="space-y-1 text-center">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>

                                        <div class="flex text-sm text-gray-600">
                                            <label for="import_file" class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500">
                                                <span>{{ __('Upload a file') }}</span>
                                                <input id="import_file"
                                                       name="import_file"
                                                       type="file"
                                                       accept=".xlsx,.xls,.csv"
                                                       class="sr-only"
                                                       @change="handleFileSelect($event)">
                                            </label>
                                            <p class="pl-1">{{ __('or drag and drop') }}</p>
                                        </div>

                                        <p class="text-xs text-gray-500">
                                            {{ __('Supported formats: .xlsx, .xls, .csv (Max 10MB)') }}
                                        </p>

                                        <div x-show="importFile" class="mt-3 p-3 bg-green-50 border border-green-200 rounded-md">
                                            <div class="flex items-center">
                                                <svg class="h-5 w-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                </svg>
                                                <p class="text-sm font-medium text-green-800">File selected:</p>
                                            </div>
                                            <p class="text-sm text-green-700 mt-1" x-text="importFile ? importFile.name : ''"></p>
                                            <p class="text-xs text-green-600 mt-1" x-text="importFile ? `Size: ${(importFile.size / 1024 / 1024).toFixed(2)} MB` : ''"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Import Options -->
                            <div class="mb-4">
                                <label class="flex items-center">
                                    <input type="checkbox"
                                           name="update_existing"
                                           value="1"
                                           class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700">
                                        {{ __('Update existing medicines (match by name)') }}
                                    </span>
                                </label>
                            </div>

                            <!-- Progress Bar -->
                            <div x-show="importing" class="mb-4">
                                <div class="bg-gray-200 rounded-full h-2">
                                    <div class="bg-indigo-600 h-2 rounded-full transition-all duration-300"
                                         :style="`width: ${importProgress}%`"></div>
                                </div>
                                <p class="text-sm text-gray-600 mt-1">{{ __('Importing...') }}</p>
                            </div>

                            <!-- Import Results -->
                            <div x-show="importResults" class="mb-4">
                                <div class="rounded-md p-4"
                                     :class="importResults && importResults.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg x-show="importResults && importResults.success" class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                            </svg>
                                            <svg x-show="importResults && !importResults.success" class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium"
                                               :class="importResults && importResults.success ? 'text-green-800' : 'text-red-800'"
                                               x-text="importResults ? importResults.message : ''">
                                            </p>
                                            <div x-show="importResults && importResults.details" class="mt-2 text-sm"
                                                 :class="importResults && importResults.success ? 'text-green-700' : 'text-red-700'">
                                                <ul class="list-disc list-inside space-y-1">
                                                    <template x-for="detail in (importResults ? importResults.details : [])" :key="detail">
                                                        <li x-text="detail"></li>
                                                    </template>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Modal Footer -->
                    <div class="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                        <button type="button"
                                @click="submitImport"
                                :disabled="!importFile || importing"
                                class="inline-flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed sm:ml-3 sm:w-auto sm:text-sm">
                            <span x-show="!importing">{{ __('Import') }}</span>
                            <span x-show="importing">{{ __('Importing...') }}</span>
                        </button>

                        <button type="button"
                                @click="showImportModal = false"
                                class="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            {{ __('Cancel') }}
                        </button>

                        <a href="{{ route('inventory.medicines.template') }}"
                           class="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            {{ __('Download Template') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Alpine.js component globally
        window.importModalData = function() {
            return {
                showImportModal: false,
                importFile: null,
                importing: false,
                importProgress: 0,
                importResults: null,
                dragOver: false,

                handleFileSelect(event) {
                    console.log('File select triggered', event);
                    const file = event.target.files[0];
                    if (file) {
                        console.log('File selected:', file.name, file.type, file.size);

                        // Validate file type
                        const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                            'application/vnd.ms-excel',
                                            'text/csv',
                                            'application/csv'];
                        const allowedExtensions = ['.xlsx', '.xls', '.csv'];
                        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

                        if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
                            this.importResults = {
                                success: false,
                                message: 'Invalid file format',
                                details: ['Please select a valid Excel (.xlsx, .xls) or CSV file.'],
                                errors: []
                            };
                            event.target.value = ''; // Clear the input
                            return;
                        }

                        // Validate file size (10MB = 10 * 1024 * 1024 bytes)
                        if (file.size > 10 * 1024 * 1024) {
                            this.importResults = {
                                success: false,
                                message: 'File too large',
                                details: ['File size must not exceed 10MB.'],
                                errors: []
                            };
                            event.target.value = ''; // Clear the input
                            return;
                        }

                        this.importFile = file;
                        this.importResults = null;
                        console.log('File set successfully:', this.importFile.name);
                    }
                },

                handleFileDrop(event) {
                    console.log('File drop triggered', event);
                    this.dragOver = false;
                    const files = event.dataTransfer.files;
                    if (files.length > 0) {
                        const file = files[0];
                        console.log('File dropped:', file.name, file.type, file.size);

                        // Validate file type
                        const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                            'application/vnd.ms-excel',
                                            'text/csv',
                                            'application/csv'];
                        const allowedExtensions = ['.xlsx', '.xls', '.csv'];
                        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

                        if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
                            this.importResults = {
                                success: false,
                                message: 'Invalid file format',
                                details: ['Please select a valid Excel (.xlsx, .xls) or CSV file.'],
                                errors: []
                            };
                            return;
                        }

                        // Validate file size (10MB = 10 * 1024 * 1024 bytes)
                        if (file.size > 10 * 1024 * 1024) {
                            this.importResults = {
                                success: false,
                                message: 'File too large',
                                details: ['File size must not exceed 10MB.'],
                                errors: []
                            };
                            return;
                        }

                        this.importFile = file;
                        // Set the file input element's files property
                        const fileInput = document.getElementById('import_file');
                        const dataTransfer = new DataTransfer();
                        dataTransfer.items.add(file);
                        fileInput.files = dataTransfer.files;
                        this.importResults = null;
                        console.log('File set successfully via drop:', this.importFile.name);
                    }
                },

                async submitImport() {
                    console.log('Submit import triggered', this.importFile);

                    if (!this.importFile) {
                        this.importResults = {
                            success: false,
                            message: 'No file selected',
                            details: ['Please select a file to import.']
                        };
                        return;
                    }

                    this.importing = true;
                    this.importProgress = 0;
                    this.importResults = null;

                    const formData = new FormData(document.getElementById('importForm'));

                    // Ensure the file is added to FormData
                    if (this.importFile) {
                        formData.set('import_file', this.importFile);
                    }

                    // Debug FormData contents
                    console.log('FormData contents:');
                    for (let [key, value] of formData.entries()) {
                        console.log(key, value);
                    }

                    try {
                        // Simulate progress
                        const progressInterval = setInterval(() => {
                            if (this.importProgress < 90) {
                                this.importProgress += 10;
                            }
                        }, 200);

                        const response = await fetch('{{ route('inventory.medicines.import') }}', {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            }
                        });

                        clearInterval(progressInterval);
                        this.importProgress = 100;

                        console.log('Response status:', response.status);
                        console.log('Response headers:', response.headers);

                        if (!response.ok) {
                            const errorText = await response.text();
                            console.error('Response error text:', errorText);
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        const result = await response.json();
                        console.log('Import result:', result);

                        this.importResults = result;

                        if (result.success) {
                            setTimeout(() => {
                                window.location.reload();
                            }, 2000);
                        }
                    } catch (error) {
                        console.error('Import error:', error);
                        this.importResults = {
                            success: false,
                            message: 'An error occurred during import',
                            details: [error.message]
                        };
                    } finally {
                        this.importing = false;
                    }
                }
            };
        };

        // Fix for the import modal button
        document.addEventListener('DOMContentLoaded', function() {
            const importButton = document.getElementById('import-button');
            if (importButton) {
                importButton.addEventListener('click', function() {
                    // Try multiple methods to open the modal to ensure compatibility
                    try {
                        // Method 1: Direct event dispatch
                        window.dispatchEvent(new CustomEvent('open-import-modal'));
                        
                        // Method 2: Direct Alpine data manipulation for v2
                        const importModal = document.querySelector('[x-data="importModalData()"]');
                        if (importModal && typeof importModal.__x !== 'undefined') {
                            importModal.__x.$data.showImportModal = true;
                        }
                        
                        // Method 3: Alpine v3 API
                        if (typeof Alpine !== 'undefined' && Alpine.store) {
                            const modal = document.querySelector('[x-data="importModalData()"]');
                            if (modal) {
                                Alpine.evaluate(modal, 'showImportModal = true');
                            }
                        }
                    } catch (e) {
                        console.error('Error opening import modal:', e);
                    }
                });
            }
        });
    </script>

@endsection
