@extends('inventory.medicines.layout')

@section('title', 'Edit Medicine')

@section('medicine-content')
<div class="max-w-[1400px] mx-auto px-6">
    @if (session('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
            <span class="block sm:inline">{{ session('error') }}</span>
        </div>
    @endif

    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-semibold">{{ __('Edit Medicine') }}</h1>
        <x-button-link href="{{ route('inventory.medicines.index') }}" class="bg-gray-600 hover:bg-gray-700">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
            </svg>
            Back to Medicines
        </x-button-link>
    </div>

    <!-- Medicine Info Form -->
    <div class="mb-6">
            <form method="POST" action="{{ route('inventory.medicines.update-medicine-info', $medicine->id) }}">
                @csrf
                @method('PUT')
                <div class="grid gap-6 mb-6">
                    <!-- Basic Information -->
                    <div class="bg-white rounded-lg border border-gray-200 p-6">
                        <h2 class="text-lg font-medium text-gray-900 mb-6">{{ __('Basic Information') }}</h2>
                        
                        <!-- Medicine Name and Generic Name -->
                        <div class="grid grid-cols-3 gap-6 mb-6">
                            <div>
                                <label for="name" class="block text-sm text-gray-600 mb-2">{{ __('Medicine Name') }}</label>
                                <input type="text" name="name" id="name" value="{{ old('name', $medicine->name) }}"
                                    placeholder="{{ __('Enter medicine name') }}"
                                    class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent">
                                @error('name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="dosage" class="block text-sm text-gray-600 mb-2">{{ __('Dosage') }}</label>
                                <input type="text" name="dosage" id="dosage" value="{{ old('dosage', $medicine->dosage) }}"
                                    placeholder="{{ __('Enter dosage (e.g. 40 mg, 500 mcg, 5 ml, etc.)') }}"
                                    class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent">
                                @error('dosage')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="generic_name" class="block text-sm text-gray-600 mb-2">{{ __('Generic Name') }}</label>
                                <input type="text" name="generic_name" id="generic_name" value="{{ old('generic_name', $medicine->generic_name) }}"
                                    placeholder="{{ __('Enter generic name') }}"
                                    class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent">
                                @error('generic_name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Manufacturer, Supplier, Category in one line -->
                        <div class="flex grid-cols-3 gap-6">
                            <div class="flex-1">
                                <label for="manufacturer_id" class="block text-sm text-gray-600 mb-2">
                                    {{ __('Manufacturer') }}<span class="text-red-500 ml-0.5">*</span>
                                </label>
                                <select name="manufacturer_id" id="manufacturer_id"
                                        class="w-full px-3 py-2 bg-white border @error('manufacturer_id') border-red-500 @else border-gray-200 @enderror rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent appearance-none"
                                        required>
                                    <option value="">{{ __('Select manufacturer') }}</option>
                                    @foreach($manufacturers as $manufacturer)
                                        <option value="{{ $manufacturer->id }}" {{ old('manufacturer_id', $medicine->manufacturer_id) == $manufacturer->id ? 'selected' : '' }}>
                                            {{ $manufacturer->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('manufacturer_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="flex-1">
                                <label for="supplier_id" class="block text-sm text-gray-600 mb-2">{{ __('Suppliers') }}</label>
                                <select name="supplier_id" id="supplier_id"
                                        class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent appearance-none">
                                    <option value="">{{ __('Select suppliers') }}</option>
                                    @foreach($suppliers as $supplier)
                                        <option value="{{ $supplier->id }}"
                                            {{ old('supplier_id', $medicine->supplier_id) == $supplier->id ? 'selected' : '' }}>
                                            {{ $supplier->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('supplier_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="flex-1">
                                <label for="category_id" class="block text-sm text-gray-600 mb-2">{{ __('Category') }}</label>
                                <select name="category_id" id="category_id"
                                        class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent appearance-none">
                                    <option value="">{{ __('Select category') }}</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ old('category_id', $medicine->category_id) == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('category_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Unit Type -->
                        <div class="mt-6">
                            <label for="unit_type_id" class="block text-sm text-gray-600 mb-2">{{ __('Unit Type') }}</label>
                            <select name="unit_type_id" id="unit_type_id"
                                    class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent appearance-none">
                                <option value="">{{ __('Select unit type') }}</option>
                                @foreach($unitTypes as $unitType)
                                    <option value="{{ $unitType->id }}" {{ old('unit_type_id', $medicine->unit_type_id) == $unitType->id ? 'selected' : '' }}>
                                        {{ $unitType->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('unit_type_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Pricing Information -->
                    <div class="bg-white rounded-lg border border-gray-200 p-4 md:p-6" x-data="{
                        supplierMarginPercentage: {{ old('supplier_margin_percentage', $medicine->supplier_margin_percentage ?? 12) }},
                        isSliderActive: false,
                        
                        calculateSupplierPrices() {
                            // Calculate supplier prices based on retail prices and margin percentage
                            const types = ['carton', 'box', 'strip', 'unit'];
                            
                            types.forEach(type => {
                                // Get the checkbox and price input elements
                                const retailCheckbox = document.getElementById('retail-' + type + '-check');
                                const retailPriceInput = document.getElementById('retail-' + type);
                                const supplierCheckbox = document.getElementById('supplier-' + type + '-check');
                                const supplierPriceInput = document.getElementById('supplier-' + type);
                                
                                // Only calculate if retail price is set and enabled
                                if (retailCheckbox && retailCheckbox.checked) {
                                    const retailPrice = parseFloat(retailPriceInput.value);
                                    if (!isNaN(retailPrice) && retailPrice > 0) {
                                        // Calculate supplier price by subtracting margin percentage
                                        const marginAmount = retailPrice * (this.supplierMarginPercentage / 100);
                                        supplierPriceInput.value = (retailPrice - marginAmount).toFixed(2);
                                        
                                        // Enable the corresponding supplier unit checkbox
                                        if (supplierCheckbox) {
                                            supplierCheckbox.checked = true;
                                        }
                                    }
                                }
                            });
                        }
                    }" x-init="
                        // Set up watcher for changes
                        $watch('supplierMarginPercentage', value => {
                            calculateSupplierPrices();
                        });
                    ">
                        <h2 class="text-lg font-medium text-gray-900 mb-4">{{ __('Pricing Information') }}</h2>
                        
                        <!-- Supplier Margin Percentage -->
                        <div class="mb-3 bg-blue-50 p-2 rounded-lg">
                            <div class="flex flex-wrap items-center">
                                <div class="flex items-center justify-between w-full mb-1">
                                    <label for="supplier_margin_percentage" class="text-sm font-medium text-gray-700">
                                        {{ __('Supplier Margin Percentage') }}:
                                    </label>
                                    <p class="text-xs text-gray-500 ml-2">{{ __('Automatically calculate supplier prices from retail prices') }}</p>
                                </div>
                                <div class="flex-1 flex flex-wrap sm:flex-nowrap items-center gap-2 w-full">
                                    <div class="relative flex-1 flex items-center w-full sm:w-auto min-w-0">
                                        <div class="relative h-2 bg-blue-100 rounded-lg w-full">
                                            <!-- Progress bar that fills based on current value -->
                                            <div class="absolute top-0 left-0 h-full bg-indigo-500 rounded-lg"
                                                :style="'width: ' + (supplierMarginPercentage / 25 * 100) + '%'"></div>
                                            
                                            <!-- Thumb indicator -->
                                            <div class="absolute top-0 w-3 h-3 bg-indigo-600 rounded-full shadow-sm transform -translate-x-1/2 -translate-y-1/4"
                                                :class="{ 'ring-1 ring-indigo-300': isSliderActive }"
                                                :style="'left: ' + (supplierMarginPercentage / 25 * 100) + '%'"></div>
                                        </div>
                                        <input type="range" 
                                            id="supplier_margin_percentage_range" 
                                            name="supplier_margin_percentage_range"
                                            min="0" 
                                            max="25" 
                                            step="0.5"
                                            x-model.number="supplierMarginPercentage"
                                            @mousedown="isSliderActive = true"
                                            @mouseup="isSliderActive = false"
                                            @touchstart="isSliderActive = true"
                                            @touchend="isSliderActive = false"
                                            class="w-full h-6 absolute top-[-8px] left-0 opacity-0 cursor-pointer z-10"
                                            style="margin: 0; padding: 0;"
                                        >
                                        <div class="flex justify-between text-xs text-gray-500 w-full absolute -bottom-4">
                                            <span>0%</span>
                                            <span class="mx-auto">12%</span>
                                            <span>25%</span>
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-2 mt-3 sm:mt-0">
                                        <div class="relative w-40 flex-shrink-0">
                                            <div class="flex rounded-lg overflow-hidden border border-gray-200 shadow-sm h-9">
                                                <input type="number" 
                                                    id="supplier_margin_percentage" 
                                                    name="supplier_margin_percentage"
                                                    min="0" 
                                                    max="25" 
                                                    step="0.5"
                                                    x-model.number="supplierMarginPercentage"
                                                    class="w-2/3 h-full px-3 bg-white focus:ring-0 focus:outline-none text-center text-base font-normal border-0">
                                                <div class="w-1/3 h-full flex items-center justify-center bg-gray-100 border-l border-gray-200">
                                                    <span class="text-gray-500 text-base">%</span>
                                                </div>
                                            </div>
                                        </div>
                                        <button type="button"
                                                @click="calculateSupplierPrices()"
                                                class="flex-shrink-0 inline-flex items-center justify-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-1 focus:ring-indigo-500">
                                            {{ __('Apply') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                            <!-- Supplier Prices -->
                            <div class="mb-4 md:mb-0">
                                <h3 class="font-medium mb-3 text-sm md:text-base">{{ __('Supplier Prices') }}</h3>
                                <div class="space-y-3">
                                    @foreach(['carton' => 'Carton Price', 'box' => 'Box Price', 'strip' => 'Strip Price', 'unit' => 'Unit Price'] as $type => $label)
                                        <div class="flex flex-wrap md:flex-nowrap items-center gap-2">
                                            <div class="flex items-center min-w-[120px]">
                                            <input type="checkbox" name="enabled_units[]" value="{{ $type }}"
                                                id="supplier-{{ $type }}-check"
                                                class="w-4 h-4 border-gray-200 rounded text-indigo-600 focus:ring-1 focus:ring-indigo-500"
                                                {{ in_array($type, old('enabled_units', is_array($medicine->enabled_units) ? $medicine->enabled_units : json_decode($medicine->enabled_units ?? '[]'))) ? 'checked' : '' }}>
                                                <label for="supplier-{{ $type }}-check" class="text-sm text-gray-600 ml-2">{{ __($label) }}</label>
                                            </div>
                                            <input type="number" step="0.01" name="supplier_price_{{ $type }}" id="supplier-{{ $type }}"
                                                value="{{ old('supplier_price_'.$type, $medicine->{'supplier_price_'.$type}) }}" placeholder="0"
                                                class="flex-1 w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent">
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                            <!-- Retail Prices -->
                            <div>
                                <h3 class="font-medium mb-3 text-sm md:text-base">{{ __('Retail Prices') }}</h3>
                                <div class="space-y-3">
                                    @foreach(['carton' => 'Carton Price', 'box' => 'Box Price', 'strip' => 'Strip Price', 'unit' => 'Unit Price'] as $type => $label)
                                        <div class="flex flex-wrap md:flex-nowrap items-center gap-2">
                                            <div class="flex items-center min-w-[120px]">
                                            <input type="checkbox" name="enabled_retail_units[]" value="{{ $type }}"
                                                id="retail-{{ $type }}-check"
                                                class="w-4 h-4 border-gray-200 rounded text-indigo-600 focus:ring-1 focus:ring-indigo-500"
                                                    {{ in_array($type, old('enabled_retail_units', $medicine->enabled_retail_units ?? [])) ? 'checked' : '' }}
                                                    @change="calculateSupplierPrices()">
                                                <label for="retail-{{ $type }}-check" class="text-sm text-gray-600 ml-2">{{ __($label) }}</label>
                                            </div>
                                            <input type="number" step="0.01" name="retail_price_{{ $type }}" id="retail-{{ $type }}"
                                                value="{{ old('retail_price_'.$type, $medicine->{'retail_price_'.$type}) }}" placeholder="0"
                                                class="flex-1 w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent"
                                                @input="calculateSupplierPrices()">
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div class="bg-white overflow-hidden shadow-sm rounded-lg">
                        <div class="p-6">
                            <h2 class="text-lg font-medium mb-6">{{ __('Additional Information') }}</h2>
                            <div class="space-y-4">
                                <div class="flex items-center gap-2">
                                    <input type="checkbox" name="controlled_substance" id="controlled_substance"
                                        class="w-4 h-4 border-gray-200 rounded text-gray-900 focus:ring-1 focus:ring-gray-400"
                                        {{ old('controlled_substance', $medicine->controlled_substance) ? 'checked' : '' }}>
                                    <label for="controlled_substance" class="text-sm text-gray-600">
                                        {{ __('Controlled Substance') }}
                                    </label>
                                </div>

                                <div class="flex items-center gap-2">
                                    <input type="checkbox" name="prescription_required" id="prescription_required"
                                        class="w-4 h-4 border-gray-200 rounded text-gray-900 focus:ring-1 focus:ring-gray-400"
                                        {{ old('prescription_required', $medicine->prescription_required) ? 'checked' : '' }}>
                                    <label for="prescription_required" class="text-sm text-gray-600">
                                        {{ __('Prescription Required') }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end gap-4 mt-8">
                        <button type="submit"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <svg class="-ml-1 mr-2 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span>{{ __('Update Medicine') }}</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
