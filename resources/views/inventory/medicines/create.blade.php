@extends('inventory.medicines.layout')

@section('title', 'Add New Medicine')

@section('medicine-content')
<div class="max-w-[1400px] mx-auto px-6" x-data="medicineCreationForm">
        @if (session('error'))
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                <span class="block sm:inline">{{ session('error') }}</span>
            </div>
        @endif

        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-semibold">{{ __('Add New Medicine') }}</h1>
            <x-button-link href="{{ route('inventory.medicines.index') }}" class="bg-gray-600 hover:bg-gray-700">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                    Back to Medicines
                </x-button-link>
        </div>

    <!-- Medicine Info Form -->
    <div class="mb-6">
        <form method="POST" action="{{ route('inventory.medicines.store') }}" id="medicine-info-form" name="medicine-info-form">
            @csrf
            <input type="hidden" name="activeTab" :value="activeTab">
            
            <!-- Hidden fields for boolean checkbox values with default values -->
            <input type="hidden" name="controlled_substance" :value="formData.controlled_substance ? 1 : 0">
            <input type="hidden" name="prescription_required" :value="formData.prescription_required ? 1 : 0">
            
            <div class="grid gap-6 mb-6">
                <!-- Basic Information -->
                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <h2 class="text-lg font-medium text-gray-900 mb-6">{{ __('Basic Information') }}</h2>
                    
                    <!-- Medicine Name and Generic Name -->
                    <div class="grid grid-cols-3 gap-6 mb-6">
                        <div>
                            <label for="name" class="block text-sm text-gray-600 mb-2">{{ __('Medicine Name') }}</label>
                            <input type="text" name="name" id="name" 
                                x-model="formData.name"
                                   placeholder="{{ __('Enter medicine name') }}"
                                   class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent">
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="dosage" class="block text-sm text-gray-600 mb-2">{{ __('Dosage') }}</label>
                            <input type="text" name="dosage" id="dosage" 
                                x-model="formData.dosage"
                                placeholder="{{ __('Enter dosage (e.g. 40 mg, 500 mcg, 5 ml, etc.)') }}"
                                class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent">
                            @error('dosage')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="generic_name" class="block text-sm text-gray-600 mb-2">{{ __('Generic Name') }}</label>
                            <input type="text" name="generic_name" id="generic_name" 
                                x-model="formData.generic_name"
                                   placeholder="{{ __('Enter generic name') }}"
                                   class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent">
                            @error('generic_name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Manufacturer, Supplier, Category in one line -->
                    <div class="flex grid-cols-3 gap-6">
                        <div class="flex-1">
                            <label for="manufacturer_id" class="block text-sm text-gray-600 mb-2">
                                {{ __('Manufacturer') }}<span class="text-red-500 ml-0.5">*</span>
                            </label>
                            <select name="manufacturer_id" id="manufacturer_id"
                                    x-model="formData.manufacturer_id"
                                    class="w-full px-3 py-2 bg-white border rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent appearance-none
                                           {{ $errors->has('manufacturer_id') ? 'border-red-500' : 'border-gray-200' }}"
                                    required>
                                <option value="">{{ __('Select manufacturer') }}</option>
                                @foreach($manufacturers as $manufacturer)
                                    <option value="{{ $manufacturer->id }}">
                                        {{ $manufacturer->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('manufacturer_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="flex-1">
                            <label for="supplier_id" class="block text-sm text-gray-600 mb-2">{{ __('Suppliers') }}</label>
                            <select name="supplier_id" id="supplier_id"
                                    x-model="formData.supplier_id"
                                    class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent appearance-none">
                                <option value="">{{ __('Select suppliers') }}</option>
                                @foreach($suppliers as $supplier)
                                    <option value="{{ $supplier->id }}">
                                        {{ $supplier->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('supplier_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="flex-1">
                            <label for="category_id" class="block text-sm text-gray-600 mb-2">{{ __('Category') }}</label>
                            <select name="category_id" id="category_id"
                                    x-model="formData.category_id"
                                    class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent appearance-none">
                                <option value="">{{ __('Select category') }}</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}">
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('category_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Unit Type -->
                    <div class="mt-6">
                        <label for="unit_type_id" class="block text-sm text-gray-600 mb-2">{{ __('Unit Type') }}</label>
                        <select name="unit_type_id" id="unit_type_id"
                                x-model="formData.unit_type_id"
                                class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent appearance-none">
                            <option value="">{{ __('Select unit type') }}</option>
                            @foreach($unitTypes as $unitType)
                                <option value="{{ $unitType->id }}">
                                    {{ $unitType->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('unit_type_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Pricing Information -->
                <div class="bg-white rounded-lg border border-gray-200 p-4 md:p-6" x-data="{
                    supplierMarginPercentage: {{ old('supplier_margin_percentage', 12) }},
                    isSliderActive: false,
                    
                    calculateSupplierPrices() {
                        // Calculate supplier prices based on retail prices and margin percentage
                        const types = ['carton', 'box', 'strip', 'unit'];
                        
                        types.forEach(type => {
                            // Only calculate if retail price is set and enabled
                            if (this.formData.enabled_retail_units[type]) {
                                const retailPrice = parseFloat(this.formData['retail_price_' + type]);
                                if (!isNaN(retailPrice) && retailPrice > 0) {
                                    // Calculate supplier price by subtracting margin percentage
                                    const marginAmount = retailPrice * (this.supplierMarginPercentage / 100);
                                    this.formData['supplier_price_' + type] = (retailPrice - marginAmount).toFixed(2);
                                    
                                    // Enable the corresponding supplier unit checkbox
                                    this.formData.enabled_units[type] = true;
                                }
                            }
                        });
                    }
                }" x-init="
                    // Initialize supplierMarginPercentage from formData
                    supplierMarginPercentage = formData.supplier_margin_percentage;
                    
                    // Set up watcher for changes
                    $watch('supplierMarginPercentage', value => {
                        // Update the formData value
                        formData.supplier_margin_percentage = value;
                        calculateSupplierPrices();
                    });
                    
                    // Initial calculation
                    calculateSupplierPrices();
                ">
                    <h2 class="text-lg font-medium text-gray-900 mb-4">{{ __('Pricing Information') }}</h2>
                    
                    <!-- Supplier Margin Percentage -->
                    <div class="mb-3 bg-blue-50 p-2 rounded-lg">
                        <div class="flex flex-wrap items-center">
                            <div class="flex items-center justify-between w-full mb-1">
                                <label for="supplier_margin_percentage" class="text-sm font-medium text-gray-700">
                                    {{ __('Supplier Margin Percentage') }}:
                                </label>
                                <p class="text-xs text-gray-500 ml-2">{{ __('Automatically calculate supplier prices from retail prices') }}</p>
                            </div>
                            <div class="flex-1 flex flex-wrap sm:flex-nowrap items-center gap-2 w-full">
                                <div class="relative flex-1 flex items-center w-full sm:w-auto min-w-0">
                                    <div class="relative h-2 bg-blue-100 rounded-lg w-full">
                                        <!-- Progress bar that fills based on current value -->
                                        <div class="absolute top-0 left-0 h-full bg-indigo-500 rounded-lg"
                                             :style="'width: ' + (supplierMarginPercentage / 25 * 100) + '%'"></div>
                                        
                                        <!-- Thumb indicator -->
                                        <div class="absolute top-0 w-3 h-3 bg-indigo-600 rounded-full shadow-sm transform -translate-x-1/2 -translate-y-1/4"
                                             :class="{ 'ring-1 ring-indigo-300': isSliderActive }"
                                             :style="'left: ' + (supplierMarginPercentage / 25 * 100) + '%'"></div>
                                    </div>
                                    <input type="range" 
                                           id="supplier_margin_percentage_range" 
                                           name="supplier_margin_percentage_range"
                                           min="0" 
                                           max="25" 
                                           step="0.5"
                                           x-model.number="supplierMarginPercentage"
                                           @mousedown="isSliderActive = true"
                                           @mouseup="isSliderActive = false"
                                           @touchstart="isSliderActive = true"
                                           @touchend="isSliderActive = false"
                                           class="w-full h-6 absolute top-[-8px] left-0 opacity-0 cursor-pointer z-10"
                                           style="margin: 0; padding: 0;"
                                    >
                                    <div class="flex justify-between text-xs text-gray-500 w-full absolute -bottom-4">
                                        <span>0%</span>
                                        <span class="mx-auto">12%</span>
                                        <span>25%</span>
                                    </div>
                                </div>
                                <div class="flex items-center gap-2 mt-3 sm:mt-0">
                                    <div class="relative w-40 flex-shrink-0">
                                        <div class="flex rounded-lg overflow-hidden border border-gray-200 shadow-sm h-9">
                                            <input type="number" 
                                                   id="supplier_margin_percentage" 
                                                   name="supplier_margin_percentage"
                                                   min="0" 
                                                   max="25" 
                                                   step="0.5"
                                                   x-model.number="supplierMarginPercentage"
                                                   class="w-2/3 h-full px-3 bg-white focus:ring-0 focus:outline-none text-center text-base font-normal border-0">
                                            <div class="w-1/3 h-full flex items-center justify-center bg-gray-100 border-l border-gray-200">
                                                <span class="text-gray-500 text-base">%</span>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="button"
                                            @click="calculateSupplierPrices()"
                                            class="flex-shrink-0 inline-flex items-center justify-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-1 focus:ring-indigo-500">
                                        {{ __('Apply') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                        <!-- Supplier Prices -->
                        <div class="mb-4 md:mb-0">
                            <h3 class="font-medium mb-3 text-sm md:text-base">{{ __('Supplier Prices') }}</h3>
                            <div class="space-y-3">
                                @foreach(['carton' => 'Carton Price', 'box' => 'Box Price', 'strip' => 'Strip Price', 'unit' => 'Unit Price'] as $type => $label)
                                    <div class="flex flex-wrap md:flex-nowrap items-center gap-2">
                                        <div class="flex items-center min-w-[120px]">
                                        <input type="checkbox" 
                                            id="supplier-{{ $type }}-check"
                                            x-model="formData.enabled_units.{{ $type }}"
                                            class="w-4 h-4 border-gray-200 rounded text-indigo-600 focus:ring-1 focus:ring-indigo-500">
                                            <label for="supplier-{{ $type }}-check" class="text-sm text-gray-600 ml-2">{{ __($label) }}</label>
                                        </div>
                                        <input type="number" step="0.01" name="supplier_price_{{ $type }}" id="supplier-{{ $type }}"
                                            x-model.number="formData.supplier_price_{{ $type }}"
                                            placeholder="0"
                                            class="flex-1 w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent">
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- Retail Prices -->
                        <div>
                            <h3 class="font-medium mb-3 text-sm md:text-base">{{ __('Retail Prices') }}</h3>
                            <div class="space-y-3">
                                @foreach(['carton' => 'Carton Price', 'box' => 'Box Price', 'strip' => 'Strip Price', 'unit' => 'Unit Price'] as $type => $label)
                                    <div class="flex flex-wrap md:flex-nowrap items-center gap-2">
                                        <div class="flex items-center min-w-[120px]">
                                        <input type="checkbox" 
                                            id="retail-{{ $type }}-check"
                                            x-model="formData.enabled_retail_units.{{ $type }}"
                                                @change="calculateSupplierPrices"
                                            class="w-4 h-4 border-gray-200 rounded text-indigo-600 focus:ring-1 focus:ring-indigo-500">
                                            <label for="retail-{{ $type }}-check" class="text-sm text-gray-600 ml-2">{{ __($label) }}</label>
                                        </div>
                                        <input type="number" step="0.01" name="retail_price_{{ $type }}" id="retail-{{ $type }}"
                                            x-model.number="formData.retail_price_{{ $type }}"
                                            @input="calculateSupplierPrices"
                                            placeholder="0"
                                            class="flex-1 w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent">
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="bg-white overflow-hidden shadow-sm rounded-lg">
                    <div class="p-6">
                        <h2 class="text-lg font-medium mb-6">{{ __('Additional Information') }}</h2>
                        <div class="space-y-4">
                            <div class="flex items-center gap-2">
                                <input type="checkbox" name="controlled_substance" id="controlled_substance"
                                    x-model="formData.controlled_substance"
                                    value="1"
                                    class="w-4 h-4 border-gray-200 rounded text-gray-900 focus:ring-1 focus:ring-gray-400">
                                <label for="controlled_substance" class="text-sm text-gray-600">
                                    {{ __('Controlled Substance') }}
                                </label>
                            </div>

                            <div class="flex items-center gap-2">
                                <input type="checkbox" name="prescription_required" id="prescription_required"
                                    x-model="formData.prescription_required"
                                    value="1"
                                    class="w-4 h-4 border-gray-200 rounded text-gray-900 focus:ring-1 focus:ring-gray-400">
                                <label for="prescription_required" class="text-sm text-gray-600">
                                    {{ __('Prescription Required') }}
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end gap-4 mt-8">
                    <button type="submit" name="save_medicine_info" value="1"
                        @click.prevent="validateMedicineInfoOnly($event)"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="-ml-1 mr-2 h-5 w-5" viewBox="0 0 488.446 488.446" fill="currentColor" aria-hidden="true" role="img">
                            <title>Save icon</title>
                            <desc>Icon indicating save functionality</desc>
                            <g>
                                <g>
                                    <g>
                                        <path d="M153.029,90.223h182.404c5.427,0,9.873-4.43,9.873-9.869V0H143.137v80.354C143.137,85.793,147.571,90.223,153.029,90.223z"/>
                                        <path d="M480.817,122.864L377.88,19.494v60.859c0,23.404-19.043,42.447-42.447,42.447H153.029c-23.409,0-42.447-19.043-42.447-42.447V0H44.823C20.068,0,0.002,20.07,0.002,44.808v398.831c0,24.736,20.066,44.808,44.821,44.808h398.813c24.74,0,44.808-20.068,44.808-44.808V141.325C488.444,134.392,485.698,127.758,480.817,122.864z M412.461,385.666c0,14.434-11.703,26.154-26.168,26.154H102.137c-14.451,0-26.153-11.722-26.153-26.154V249.303c0-14.43,11.702-26.148,26.153-26.148h284.156c14.465,0,26.168,11.72,26.168,26.148V385.666z"/>
                                        <path d="M356.497,265.131H131.949c-9.008,0-16.294,7.273-16.294,16.28s7.286,16.28,16.294,16.28h224.549c8.988,0,16.277-7.273,16.277-16.28S365.487,265.131,356.497,265.131z"/>
                                        <path d="M323.936,330.264H164.508c-8.994,0-16.28,7.273-16.28,16.28c0,8.989,7.286,16.28,16.28,16.28h159.427c8.994,0,16.281-7.291,16.281-16.28C340.217,337.537,332.93,330.264,323.936,330.264z"/>
                                    </g>
                                </g>
                            </g>
                        </svg>
                        <span>{{ __('Save Medicine') }}</span>
                    </button>
                </div>
            </div>
        </form>
    </div>

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('medicineCreationForm', () => ({
            // Store manufacturer data for display
            manufacturers: @json($manufacturers),
            categories: @json($categories),
            activeTab: '{{ old('activeTab', 'medicine-info') }}',
            formData: {
                // Medicine Information fields
                name: '{{ old('name', '') }}',
                generic_name: '{{ old('generic_name', '') }}',
                dosage: '{{ old('dosage', '') }}',
                manufacturer_id: '{{ old('manufacturer_id', '') }}',
                supplier_id: '{{ old('supplier_id', '') }}',
                category_id: '{{ old('category_id', '') }}' || 'uncategorized',
                unit_type_id: '{{ old('unit_type_id', '') }}',
                controlled_substance: {{ old('controlled_substance') ? 'true' : 'false' }},
                prescription_required: {{ old('prescription_required') ? 'true' : 'false' }},
                
                // Pricing fields
                supplier_margin_percentage: {{ old('supplier_margin_percentage', 12) }},
                enabled_units: {
                    carton: {{ in_array('carton', old('enabled_units', [])) ? 'true' : 'false' }},
                    box: {{ in_array('box', old('enabled_units', [])) ? 'true' : 'false' }},
                    strip: {{ in_array('strip', old('enabled_units', [])) ? 'true' : 'false' }},
                    unit: {{ in_array('unit', old('enabled_units', [])) ? 'true' : 'false' }}
                },
                supplier_price_carton: {{ old('supplier_price_carton', 0) }},
                supplier_price_box: {{ old('supplier_price_box', 0) }},
                supplier_price_strip: {{ old('supplier_price_strip', 0) }},
                supplier_price_unit: {{ old('supplier_price_unit', 0) }},
                
                enabled_retail_units: {
                    carton: {{ in_array('carton', old('enabled_retail_units', [])) ? 'true' : 'false' }},
                    box: {{ in_array('box', old('enabled_retail_units', [])) ? 'true' : 'false' }},
                    strip: {{ in_array('strip', old('enabled_retail_units', [])) ? 'true' : 'false' }},
                    unit: {{ in_array('unit', old('enabled_retail_units', [])) ? 'true' : 'false' }}
                },
                retail_price_carton: {{ old('retail_price_carton', 0) }},
                retail_price_box: {{ old('retail_price_box', 0) }},
                retail_price_strip: {{ old('retail_price_strip', 0) }},
                retail_price_unit: {{ old('retail_price_unit', 0) }},
                

            },
            
            validateAndSubmit(event, saveAndAdd) {
                console.log('Validating form submission...', this.formData);

                // Set default category to 'uncategorized' if none is selected
                if (!this.formData.category_id) {
                    this.formData.category_id = 'uncategorized';
                }

                // Check for required medicine info
                if (!this.formData.name || !this.formData.manufacturer_id) {
                    console.error('Missing required fields:', {
                        name: this.formData.name,
                        manufacturer_id: this.formData.manufacturer_id
                    });

                    // Create alert div with error details
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6';
                    alertDiv.setAttribute('role', 'alert');

                    const title = document.createElement('p');
                    title.className = 'font-bold';
                    title.textContent = 'Missing Required Information';

                    const message = document.createElement('p');
                    message.textContent = 'Please fill in the following required fields:';

                    const list = document.createElement('ul');
                    list.className = 'list-disc pl-5 mt-2';

                    if (!this.formData.name) {
                        const item = document.createElement('li');
                        item.textContent = 'Medicine Name is required';
                        list.appendChild(item);
                    }

                    if (!this.formData.manufacturer_id) {
                        const item = document.createElement('li');
                        item.textContent = 'Manufacturer is required';
                        list.appendChild(item);
                    }

                    alertDiv.appendChild(title);
                    alertDiv.appendChild(message);
                    alertDiv.appendChild(list);

                    // Insert at the top of the form
                    const form = event.target.closest('form');
                    form.insertBefore(alertDiv, form.firstChild);

                    // Scroll to alert
                    alertDiv.scrollIntoView({ behavior: 'smooth' });

                    return false;
                }

                const form = event.target.closest('form');

                // Clear any existing enabled_units and enabled_retail_units hidden fields
                // to prevent duplicates
                form.querySelectorAll('input[name="enabled_units[]"]').forEach(el => el.remove());
                form.querySelectorAll('input[name="enabled_retail_units[]"]').forEach(el => el.remove());

                // Process enabled_units
                for (const [unit, checked] of Object.entries(this.formData.enabled_units)) {
                    if (checked) {
                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = 'enabled_units[]';
                        input.value = unit;
                        form.appendChild(input);
                    }
                }

                // Process enabled_retail_units
                for (const [unit, checked] of Object.entries(this.formData.enabled_retail_units)) {
                    if (checked) {
                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = 'enabled_retail_units[]';
                        input.value = unit;
                        form.appendChild(input);
                    }
                }

                // Update the hidden fields for boolean checkbox values
                const controlledSubstanceInput = form.querySelector('input[name="controlled_substance"]');
                controlledSubstanceInput.value = this.formData.controlled_substance ? '1' : '0';

                const prescriptionRequiredInput = form.querySelector('input[name="prescription_required"]');
                prescriptionRequiredInput.value = this.formData.prescription_required ? '1' : '0';

                // Add the hidden field for save_and_add if needed
                if (saveAndAdd) {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'save_and_add';
                    input.value = '1';
                    form.appendChild(input);
                }

                // Submit the form
                form.submit();
            },

            getManufacturerName(id) {
                // Convert id to string for comparison if it exists
                const manufacturerId = id ? id.toString() : '';
                
                // Debug the manufacturer lookup
                console.log('Looking up manufacturer:', {
                    providedId: id,
                    manufacturerId: manufacturerId,
                    manufacturers: this.manufacturers
                });
                
                // Find the manufacturer by ID
                const manufacturer = this.manufacturers.find(m => m.id.toString() === manufacturerId);
                
                // Return the name or default text
                return manufacturer ? manufacturer.name : 'Not selected';
            },
            
            getCategoryName(id) {
                // Convert id to string for comparison if it exists
                const categoryId = id ? id.toString() : '';

                // Debug the category lookup
                console.log('Looking up category:', {
                    providedId: id,
                    categoryId: categoryId,
                    categories: this.categories
                });

                // Find the category by ID
                const category = this.categories.find(c => c.id.toString() === categoryId);

                // Return the name or 'Uncategorized' as default
                return category ? category.name : 'Uncategorized';
            },

            validateMedicineInfoOnly(event) {
                // Prevent the default form submission
                event.preventDefault();

                // Get the form element
                const form = event.target.closest('form');
                if (!form) {
                    console.error('Form not found');
                    return;
                }

                // Basic validation - check required fields
                const requiredFields = [
                    'name',
                    'manufacturer_id'
                ];

                let isValid = true;
                let firstInvalidField = null;

                requiredFields.forEach(fieldName => {
                    const field = form.querySelector(`[name="${fieldName}"]`);
                    if (field && (!field.value || field.value.trim() === '')) {
                        isValid = false;
                        if (!firstInvalidField) {
                            firstInvalidField = field;
                        }
                        // Add error styling
                        field.classList.add('border-red-500');
                    } else if (field) {
                        // Remove error styling
                        field.classList.remove('border-red-500');
                    }
                });

                if (!isValid) {
                    // Focus on the first invalid field
                    if (firstInvalidField) {
                        firstInvalidField.focus();
                    }

                    // Show error message
                    alert('Please fill in all required fields (Medicine Name and Manufacturer).');
                    return;
                }

                // If validation passes, submit the form
                form.submit();
            }
        }))
    });
</script>

@endsection