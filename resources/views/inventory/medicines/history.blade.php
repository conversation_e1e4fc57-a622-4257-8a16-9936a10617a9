@extends('inventory.medicines.layout')

@section('medicine-content')
    <div class="w-full">
        <div class="flex flex-col space-y-6">
            {{-- Header --}}
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="text-2xl font-bold text-gray-900">
                        {{ $medicine->name }}{{ $medicine->dosage ? ' ' . $medicine->dosage : '' }} - Inventory History
                    </h2>
                    <p class="mt-1 text-sm text-gray-600">
                        Generic Name: {{ $medicine->generic_name }}
                    </p>
                </div>
                <x-button-link href="{{ route('inventory.medicines.index') }}" class="bg-gray-600 hover:bg-gray-700">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                    Back to Medicines
                </x-button-link>
            </div>

            {{-- Current Stock Summary --}}
            <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Current Stock Summary</h3>
                </div>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 p-6">
                    {{-- Total Quantity --}}
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex flex-col">
                            <dt class="text-sm font-medium text-gray-500 mb-1">Total Quantity</dt>
                            <dd class="text-2xl font-bold text-gray-900">
                                {{ number_format($totalQuantity) }}
                                <span class="text-base font-medium text-gray-500 ml-1">{{ $medicine->unit_type }}</span>
                            </dd>
                        </div>
                    </div>

                    {{-- Active Batches --}}
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex flex-col">
                            <dt class="text-sm font-medium text-gray-500 mb-1">Active Batches</dt>
                            <dd class="text-2xl font-bold text-gray-900">
                                {{ number_format($activeBatches) }}
                            </dd>
                        </div>
                    </div>

                    {{-- Locations --}}
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex flex-col">
                            <dt class="text-sm font-medium text-gray-500 mb-1">Unique Locations</dt>
                            <dd class="text-2xl font-bold text-gray-900">
                                {{ number_format($uniqueLocations) }}
                            </dd>
                        </div>
                    </div>

                    {{-- Average Price --}}
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex flex-col">
                            <dt class="text-sm font-medium text-gray-500 mb-1">Average Unit Price</dt>
                            <dd class="text-2xl font-bold text-gray-900">
                                {{ number_format($averagePrice, 2) }}
                            </dd>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Current Batches --}}
            <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Current Batches</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Batch Number</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiry Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @forelse($batches as $batch)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $batch->batch_number }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $batch->location_name }}
                                        <span class="text-gray-500">({{ $batch->location_type }})</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ number_format($batch->quantity) }}
                                        <span class="text-gray-500">{{ $medicine->unit_type }}</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ number_format($batch->unit_price, 2) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $batch->expiry_date ? $batch->expiry_date->format('M d, Y') : 'N/A' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                            @if($batch->status === 'Valid') bg-green-100 text-green-800
                                            @elseif($batch->status === 'Expiring Soon') bg-yellow-100 text-yellow-800
                                            @elseif($batch->status === 'Expiry Not Set') bg-blue-100 text-blue-800
                                            @else bg-red-100 text-red-800
                                            @endif">
                                            {{ $batch->status }}
                                        </span>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">No active batches found</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>

            {{-- Movement History --}}
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Movement History</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">To</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Batch</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">By</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @forelse($movements as $movement)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $movement->created_at->format('M d, Y H:i') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                            @if($movement->movement_type === 'addition') bg-green-100 text-green-800
                                            @else bg-red-100 text-red-800
                                            @endif">
                                            {{ ucfirst($movement->movement_type) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ number_format(abs($movement->quantity)) }}
                                        <span class="text-gray-500">{{ $medicine->unit_type }}</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ optional($movement->sourceLocation)->name ?? 'N/A' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ optional($movement->destinationLocation)->name ?? 'N/A' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $movement->batch_number }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ optional($movement->creator)->name ?? 'System' }}
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900">
                                        {{ $movement->notes }}
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="8" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">No movement history found</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                @if($movements->hasPages())
                    <div class="px-4 py-3 border-t border-gray-200 sm:px-6">
                        {{ $movements->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection
