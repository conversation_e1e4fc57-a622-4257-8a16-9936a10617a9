@extends('layouts.admin')

@section('content')
<div class="py-6">
    <div class="max-w-full mx-auto px-4 sm:px-6 md:px-8">
        <div class="flex flex-col space-y-6">
            {{-- Header --}}
            <div class="md:flex md:items-center md:justify-between">
                <div class="flex-1 min-w-0">
                    <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:leading-9 sm:truncate">
                        Create New Location
                    </h2>
                </div>
                <div class="mt-4 flex md:mt-0 md:ml-4">
                    <a href="{{ route('inventory.locations.index') }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                        </svg>
                        Back to Locations
                    </a>
                </div>
            </div>

            {{-- Form --}}
            <div class="bg-white shadow rounded-lg">
                <form action="{{ route('inventory.locations.store') }}" method="POST" class="space-y-6">
                    @csrf
                    <div class="p-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                        {{-- Basic Information --}}
                        <div class="col-span-6">
                            <h3 class="text-lg font-medium leading-6 text-gray-900">Basic Information</h3>
                            <p class="mt-1 text-sm text-gray-500">Provide the basic details for the new location.</p>
                        </div>

                        {{-- Name --}}
                        <div class="col-span-6 sm:col-span-4">
                            <label for="name" class="block text-sm font-medium text-gray-700">Location Name</label>
                            <input type="text" name="name" id="name" value="{{ old('name') }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                   required>
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        {{-- Type --}}
                        <div class="col-span-6 sm:col-span-3">
                            <label for="type" class="block text-sm font-medium text-gray-700">Location Type</label>
                            <select id="type" name="type" 
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                <option value="warehouse" {{ old('type') == 'warehouse' ? 'selected' : '' }}>Warehouse</option>
                                <option value="store" {{ old('type') == 'store' ? 'selected' : '' }}>Store</option>
                                <option value="shelf" {{ old('type') == 'shelf' ? 'selected' : '' }}>Shelf</option>
                            </select>
                            @error('type')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        {{-- Parent Location --}}
                        <div class="col-span-6 sm:col-span-3">
                            <label for="parent_id" class="block text-sm font-medium text-gray-700">Parent Location</label>
                            <select id="parent_id" name="parent_id" 
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                <option value="">None</option>
                                @foreach($parents as $parent)
                                    <option value="{{ $parent->id }}" {{ old('parent_id') == $parent->id ? 'selected' : '' }}>
                                        {{ $parent->name }} ({{ ucfirst($parent->type) }})
                                    </option>
                                @endforeach
                            </select>
                            @error('parent_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        {{-- Location Details --}}
                        <div class="col-span-6 pt-6">
                            <h3 class="text-lg font-medium leading-6 text-gray-900">Location Details</h3>
                            <p class="mt-1 text-sm text-gray-500">Specify the organizational details for this location.</p>
                        </div>

                        {{-- Section --}}
                        <div class="col-span-6 sm:col-span-3">
                            <label for="section" class="block text-sm font-medium text-gray-700">Section</label>
                            <input type="text" name="section" id="section" value="{{ old('section') }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            @error('section')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        {{-- Zone --}}
                        <div class="col-span-6 sm:col-span-3">
                            <label for="zone" class="block text-sm font-medium text-gray-700">Zone</label>
                            <input type="text" name="zone" id="zone" value="{{ old('zone') }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            @error('zone')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        {{-- Aisle Number --}}
                        <div class="col-span-6 sm:col-span-2">
                            <label for="aisle_number" class="block text-sm font-medium text-gray-700">Aisle Number</label>
                            <input type="text" name="aisle_number" id="aisle_number" value="{{ old('aisle_number') }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            @error('aisle_number')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        {{-- Rack Number --}}
                        <div class="col-span-6 sm:col-span-2">
                            <label for="rack_number" class="block text-sm font-medium text-gray-700">Rack Number</label>
                            <input type="text" name="rack_number" id="rack_number" value="{{ old('rack_number') }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            @error('rack_number')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        {{-- Bin Location --}}
                        <div class="col-span-6 sm:col-span-2">
                            <label for="bin_location" class="block text-sm font-medium text-gray-700">Bin Location</label>
                            <input type="text" name="bin_location" id="bin_location" value="{{ old('bin_location') }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            @error('bin_location')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        {{-- Storage Conditions --}}
                        <div class="col-span-6 pt-6">
                            <h3 class="text-lg font-medium leading-6 text-gray-900">Storage Conditions</h3>
                            <p class="mt-1 text-sm text-gray-500">Define the storage conditions for this location.</p>
                        </div>

                        {{-- Temperature Requirement --}}
                        <div class="col-span-6 sm:col-span-3">
                            <label for="temperature_requirement" class="block text-sm font-medium text-gray-700">Temperature Requirement</label>
                            <input type="text" name="temperature_requirement" id="temperature_requirement" 
                                   value="{{ old('temperature_requirement') }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                   placeholder="e.g., 20-25°C">
                            @error('temperature_requirement')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        {{-- Status --}}
                        <div class="col-span-6 sm:col-span-3">
                            <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                            <select id="status" name="status" 
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                <option value="active" {{ old('status', 'active') == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                            @error('status')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div class="px-4 py-3 bg-gray-50 text-right sm:px-6 rounded-b-lg border-t border-gray-200">
                        <button type="submit" 
                                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Create Location
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
