@extends('layouts.admin')

@section('content')
<div class="w-full">
    <div class="flex flex-col space-y-6">
        <div class="flex flex-col space-y-6">
            {{-- Header --}}
            <div class="md:flex md:items-center md:justify-between">
                <div class="flex-1 min-w-0">
                    <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:leading-9 sm:truncate">
                        Storage Locations
                    </h2>
                </div>
                <div class="mt-4 flex md:mt-0 md:ml-4">
                    <a href="{{ route('inventory.locations.create') }}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        Add New Location
                    </a>
                </div>
            </div>

            {{-- Filters --}}
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <form action="{{ route('inventory.locations.index') }}" method="GET" id="filterForm">
                        <!-- Filter Labels -->
                        <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-2">
                            <div>
                                <label for="section" class="block text-sm font-medium text-gray-700">Section</label>
                            </div>
                            <div>
                                <label for="zone" class="block text-sm font-medium text-gray-700">Zone</label>
                            </div>
                            <div>
                                <label for="temperature" class="block text-sm font-medium text-gray-700">Temperature</label>
                            </div>
                            <div>
                                <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                            </div>
                            <div class="hidden md:block">
                                <!-- Empty label for Filter button alignment -->
                            </div>
                        </div>
                        
                        <!-- Filter Controls -->
                        <div class="flex flex-wrap items-center space-y-2 md:space-y-0">
                            {{-- Section Filter --}}
                            <div class="w-full md:w-1/5 md:pr-2">
                                <select id="section" name="section" class="block w-full rounded-md border-gray-300 shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" onchange="document.getElementById('filterForm').submit()">
                                    <option value="">All Sections</option>
                                    <option value="OTC Medicines" {{ request('section') === 'OTC Medicines' ? 'selected' : '' }}>OTC Medicines</option>
                                    <option value="Prescription Medicines" {{ request('section') === 'Prescription Medicines' ? 'selected' : '' }}>Prescription Medicines</option>
                                    <option value="Controlled Substances" {{ request('section') === 'Controlled Substances' ? 'selected' : '' }}>Controlled Substances</option>
                                </select>
                            </div>

                            {{-- Zone Filter --}}
                            <div class="w-full md:w-1/5 md:px-2">
                                <select id="zone" name="zone" class="block w-full rounded-md border-gray-300 shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" onchange="document.getElementById('filterForm').submit()">
                                    <option value="">All Zones</option>
                                    <option value="Normal Storage" {{ request('zone') === 'Normal Storage' ? 'selected' : '' }}>Normal Storage</option>
                                    <option value="Cool Storage" {{ request('zone') === 'Cool Storage' ? 'selected' : '' }}>Cool Storage</option>
                                    <option value="Cold Storage" {{ request('zone') === 'Cold Storage' ? 'selected' : '' }}>Cold Storage</option>
                                    <option value="Secure Storage" {{ request('zone') === 'Secure Storage' ? 'selected' : '' }}>Secure Storage</option>
                                </select>
                            </div>

                            {{-- Temperature Filter --}}
                            <div class="w-full md:w-1/5 md:px-2">
                                <select id="temperature" name="temperature" class="block w-full rounded-md border-gray-300 shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" onchange="document.getElementById('filterForm').submit()">
                                    <option value="">All Temperatures</option>
                                    <option value="20-25°C" {{ request('temperature') === '20-25°C' ? 'selected' : '' }}>Room Temperature (20-25°C)</option>
                                    <option value="8-15°C" {{ request('temperature') === '8-15°C' ? 'selected' : '' }}>Cool Storage (8-15°C)</option>
                                    <option value="2-8°C" {{ request('temperature') === '2-8°C' ? 'selected' : '' }}>Cold Storage (2-8°C)</option>
                                </select>
                            </div>

                            {{-- Search with Suggestions --}}
                            <div class="w-full md:w-1/5 md:px-2">
                                <div class="relative" x-data="{ 
                                    searchTerm: '{{ request('search') }}',
                                    searchResults: [],
                                    loading: false,
                                    open: false,
                                    noResults: false,
                                    
                                    init() {
                                        this.$watch('searchTerm', (value) => {
                                            if (value.length >= 3) {
                                                this.fetchResults(value);
                                            } else {
                                                this.searchResults = [];
                                                this.open = false;
                                                this.noResults = false;
                                            }
                                        });
                                    },
                                    
                                    async fetchResults(query) {
                                        this.loading = true;
                                        this.noResults = false;
                                        try {
                                            const response = await fetch(`{{ route('inventory.locations.index') }}?search=${encodeURIComponent(query)}&format=json`, {
                                                headers: {
                                                    'Accept': 'application/json',
                                                    'X-Requested-With': 'XMLHttpRequest'
                                                }
                                            });
                                            if (!response.ok) throw new Error('Search failed');
                                            this.searchResults = await response.json();
                                            this.open = this.searchResults.length > 0;
                                            this.noResults = this.searchResults.length === 0;
                                        } catch (error) {
                                            console.error('Search error:', error);
                                            this.searchResults = [];
                                            this.noResults = true;
                                        } finally {
                                            this.loading = false;
                                        }
                                    },
                                    
                                    selectLocation(name) {
                                        this.searchTerm = name;
                                        this.open = false;
                                        this.noResults = false;
                                        setTimeout(() => {
                                            document.getElementById('filterForm').submit();
                                        }, 100);
                                    }
                                }">
                                    <input type="text" 
                                           name="search" 
                                           id="search" 
                                           x-model="searchTerm"
                                           @focus="searchTerm.length >= 3 && fetchResults(searchTerm)"
                                           @click.away="open = false"
                                           placeholder="Search locations..."
                                           class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                    
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                        <template x-if="!loading">
                                            <button type="submit" class="p-1 focus:outline-none focus:shadow-outline">
                                                <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                                </svg>
                                            </button>
                                        </template>
                                        <template x-if="loading">
                                            <svg class="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                        </template>
                                    </div>

                                    <!-- Search Results Dropdown -->
                                    <div class="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md py-1 text-sm" 
                                        x-show="open" 
                                        x-transition:enter="transition ease-out duration-100" 
                                        x-transition:enter-start="transform opacity-0 scale-95" 
                                        x-transition:enter-end="transform opacity-100 scale-100" 
                                        x-transition:leave="transition ease-in duration-75" 
                                        x-transition:leave-start="transform opacity-100 scale-100" 
                                        x-transition:leave-end="transform opacity-0 scale-95"
                                        style="display: none; max-height: 300px; overflow-y: auto;">
                                        <template x-for="result in searchResults" :key="result.id">
                                            <a href="#" 
                                                class="block px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                                @click.prevent="selectLocation(result.name)">
                                                <div class="flex justify-between items-start">
                                                    <div>
                                                        <div class="font-medium" x-text="result.name"></div>
                                                        <div class="text-xs text-gray-500" x-text="'Code: ' + result.code + ' | Type: ' + result.type"></div>
                                                        <div class="text-xs text-gray-500" x-show="result.section !== '-' || result.zone !== '-'">
                                                            <span x-show="result.section !== '-'" x-text="'Section: ' + result.section"></span>
                                                            <span x-show="result.section !== '-' && result.zone !== '-'"> | </span>
                                                            <span x-show="result.zone !== '-'" x-text="'Zone: ' + result.zone"></span>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" 
                                                              :class="result.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                                                              x-text="result.is_active ? 'Active' : 'Inactive'"></span>
                                                    </div>
                                                </div>
                                            </a>
                                        </template>
                                    </div>

                                    <!-- No Results Message -->
                                    <div class="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md py-2 px-4 text-sm text-gray-500" 
                                        x-show="noResults"
                                        style="display: none;">
                                        No locations found
                                    </div>
                                </div>
                            </div>

                            <!-- Filter Button and Clear Filters -->
                            <div class="w-full md:w-1/5 md:pl-2 flex justify-end">
                                @if(request()->hasAny(['search', 'section', 'zone', 'temperature']))
                                    <a href="{{ route('inventory.locations.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 whitespace-nowrap">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="-ml-1 mr-2 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                        Clear All Filters
                                    </a>
                                @endif
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            {{-- Locations List --}}
            <x-location-list :locations="$locations" />

            {{-- Pagination --}}
            @if($locations->hasPages())
                <div class="mt-5">
                    {{ $locations->links() }}
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
