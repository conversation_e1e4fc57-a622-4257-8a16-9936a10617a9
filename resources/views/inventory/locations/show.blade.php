@extends('layouts.admin')

@section('content')
    {{-- Header with Buttons --}}
    <div class="flex justify-between items-center mb-4">
        <div>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Location Details') }}: {{ $location->name }}
            </h2>
            @if($location->parent)
            <p class="mt-1 text-sm text-gray-600">
                Parent Location: {{ $location->parent->name }}
                <span class="text-gray-400">(Level {{ $location->level }})</span>
            </p>
            @endif
        </div>
        <div class="flex items-center space-x-3">
            <x-button-link href="{{ route('inventory.locations.index') }}" 
                class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition ease-in-out duration-150">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                {{ __('Back to List') }}
            </x-button-link>
            
            <x-button-link href="{{ route('inventory.locations.edit', $location) }}" 
                class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition ease-in-out duration-150">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                </svg>
                {{ __('Edit Location') }}
            </x-button-link>
        </div>
    </div>

    <div class="py-6">
        <div class="max-w-7xl mx-auto">
            {{-- Location Information Card --}}
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {{-- Basic Information --}}
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                            <div class="space-y-3">
                                <div class="flex items-center">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $location->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ ucfirst($location->status) }}
                                    </span>
                                    @if($location->is_default)
                                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Default Location
                                    </span>
                                    @endif
                                </div>
                                <p class="text-sm text-gray-600">
                                    <span class="font-medium">Type:</span> {{ ucfirst($location->type) }}
                                </p>
                                @if($location->section)
                                <p class="text-sm text-gray-600">
                                    <span class="font-medium">Section:</span> {{ $location->section }}
                                </p>
                                @endif
                                @if($location->zone)
                                <p class="text-sm text-gray-600">
                                    <span class="font-medium">Zone:</span> {{ $location->zone }}
                                </p>
                                @endif
                                @if($location->location_code)
                                <p class="text-sm text-gray-600">
                                    <span class="font-medium">Location Code:</span>
                                    <span class="font-mono">{{ $location->location_code }}</span>
                                </p>
                                @endif
                                @if($location->path)
                                <p class="text-sm text-gray-600">
                                    <span class="font-medium">Location Path:</span>
                                    <span class="font-mono">{{ $location->path }}</span>
                                </p>
                                @endif
                            </div>
                        </div>
                        
                        {{-- Storage Details --}}
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Storage Details</h3>
                            <div class="space-y-3">
                                @if($location->temperature_requirement)
                                <div class="bg-blue-50 rounded-lg p-4 mb-4">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-blue-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                        </svg>
                                        <span class="text-sm font-medium text-blue-700">Temperature Requirement:</span>
                                        <span class="ml-2 text-sm text-blue-700">{{ $location->temperature_requirement }}</span>
                                    </div>
                                </div>
                                @endif
                                
                                @if($location->type === 'shelf')
                                <div class="space-y-2">
                                    @if($location->aisle_number)
                                    <p class="text-sm text-gray-600">
                                        <span class="font-medium">Aisle:</span> {{ $location->aisle_number }}
                                    </p>
                                    @endif
                                    @if($location->rack_number)
                                    <p class="text-sm text-gray-600">
                                        <span class="font-medium">Rack:</span> {{ $location->rack_number }}
                                    </p>
                                    @endif
                                    @if($location->bin_location)
                                    <p class="text-sm text-gray-600">
                                        <span class="font-medium">Bin:</span> {{ $location->bin_location }}
                                    </p>
                                    @endif
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    @if($location->address)
                    <div class="mt-6 border-t border-gray-200 pt-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Address</h3>
                        <p class="text-gray-600 whitespace-pre-line">{{ $location->address }}</p>
                    </div>
                    @endif

                    <div class="mt-6 border-t border-gray-200 pt-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Additional Information</h3>
                        <div class="grid grid-cols-2 gap-4 text-sm text-gray-600">
                            <div>
                                <p><span class="font-medium">Created By:</span> {{ $location->creator->name ?? 'System' }}</p>
                                <p><span class="font-medium">Created At:</span> {{ $location->created_at->format('M d, Y H:i') }}</p>
                            </div>
                            <div>
                                <p><span class="font-medium">Last Updated By:</span> {{ $location->updater->name ?? 'System' }}</p>
                                <p><span class="font-medium">Last Updated At:</span> {{ $location->updated_at->format('M d, Y H:i') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Stock Information --}}
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Stock Information</h3>
                </div>
                <div class="p-6 bg-white">
                    @forelse($location->batchHistories ?? [] as $batch)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead>
                                <tr>
                                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Batch</th>
                                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiry</th>
                                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">{{ $batch->medicine->name ?? 'N/A' }}</div>
                                        <div class="text-sm text-gray-500">{{ $batch->medicine->generic_name ?? '' }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $batch->batch_number }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ $batch->quantity }}</div>
                                        <div class="text-xs text-gray-500">{{ $batch->medicine->unit_type ?? 'units' }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @if($batch->expiry_date)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            {{ $batch->expiry_date->isPast() ? 'bg-red-100 text-red-800' : 
                                               ($batch->expiry_date->diffInDays(now()) < 30 ? 'bg-yellow-100 text-yellow-800' : 
                                               'bg-green-100 text-green-800') }}">
                                            {{ $batch->expiry_date->format('M d, Y') }}
                                        </span>
                                        @else
                                        <span class="text-gray-400">N/A</span>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $batch->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ ucfirst($batch->status ?? 'Unknown') }}
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    @empty
                    <div class="text-center py-4 text-gray-500">
                        No stock items found in this location.
                    </div>
                    @endforelse
                </div>
            </div>

            {{-- Movement History --}}
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Movement History</h3>
                </div>
                <div class="p-6 bg-white">
                    @forelse($location->movements ?? [] as $movement)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead>
                                <tr>
                                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ $movement->movement_date ? $movement->movement_date->format('M d, Y H:i') : $movement->created_at->format('M d, Y H:i') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">{{ $movement->medicine->name ?? 'N/A' }}</div>
                                        <div class="text-sm text-gray-500">{{ $movement->medicine->generic_name ?? '' }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            {{ $movement->movement_type === 'in' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ ucfirst($movement->movement_type ?? 'Unknown') }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ $movement->quantity }}</div>
                                        <div class="text-xs text-gray-500">{{ $movement->medicine->unit_type ?? 'units' }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ $movement->reference_number ?? 'N/A' }}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    @empty
                    <div class="text-center py-4 text-gray-500">
                        No movement history found for this location.
                    </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
@endsection
