[x-cloak] {
    display: none !important;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
    .sidebar-item {
        @apply flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150 ease-in-out;
    }

    .sidebar-item-active {
        @apply bg-gray-100 text-gray-900;
    }

    .sidebar-item-inactive {
        @apply text-gray-600 hover:bg-gray-50 hover:text-gray-900;
    }

    .sidebar-icon {
        @apply mr-3 h-6 w-6 text-gray-400 transition-colors duration-150 ease-in-out;
    }

    .sidebar-item:hover .sidebar-icon {
        @apply text-gray-500;
    }

    .sidebar-item-active .sidebar-icon {
        @apply text-gray-500;
    }

    /* Admin Layout Stability */
    .admin-layout-container {
        @apply overflow-x-hidden max-w-full;
    }

    .admin-main-content {
        @apply max-w-full overflow-hidden;
        transition-property: padding-left;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 300ms;
    }

    .admin-nav-container {
        @apply max-w-full overflow-hidden;
    }

    .admin-nav-flex {
        @apply min-w-0;
    }
}
