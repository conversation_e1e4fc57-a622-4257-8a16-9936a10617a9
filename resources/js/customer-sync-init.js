/**
 * Customer Sync Initialization Script
 * Initializes and coordinates all customer sync components
 */

class CustomerSyncManager {
    constructor() {
        this.db = null;
        this.syncService = null;
        this.scheduler = null;
        this.progressComponent = null;
        this.isInitialized = false;
        this.config = {
            autoStart: true,
            showProgress: false,
            enableScheduler: true,
            syncInterval: 5 * 60 * 1000, // 5 minutes
            enableNotifications: true
        };
    }

    /**
     * Initialize the complete customer sync system
     */
    async init(options = {}) {
        try {
            console.log('Initializing Customer Sync System...');
            
            // Merge configuration
            this.config = { ...this.config, ...options };
            
            // Initialize IndexedDB
            await this.initDatabase();
            
            // Initialize sync service
            await this.initSyncService();
            
            // Initialize scheduler if enabled
            if (this.config.enableScheduler) {
                await this.initScheduler();
            }
            
            // Initialize progress component if enabled
            if (this.config.showProgress) {
                await this.initProgressComponent();
            }
            
            // Set up global event listeners
            this.setupGlobalEventListeners();
            
            // Auto-start if configured
            if (this.config.autoStart && this.scheduler) {
                await this.scheduler.start();
            }
            
            this.isInitialized = true;
            console.log('Customer Sync System initialized successfully');
            
            // Dispatch initialization event
            this.dispatchEvent('customer_sync_system_initialized', {
                config: this.config,
                components: {
                    database: !!this.db,
                    syncService: !!this.syncService,
                    scheduler: !!this.scheduler,
                    progressComponent: !!this.progressComponent
                }
            });
            
            return true;
        } catch (error) {
            console.error('Failed to initialize Customer Sync System:', error);
            this.dispatchEvent('customer_sync_system_error', { error: error.message });
            throw error;
        }
    }

    /**
     * Initialize IndexedDB
     */
    async initDatabase() {
        console.log('Initializing Customer Database...');
        this.db = new CustomerOfflineDB();
        await this.db.init();
        console.log('Customer Database initialized');
    }

    /**
     * Initialize sync service
     */
    async initSyncService() {
        console.log('Initializing Customer Sync Service...');
        this.syncService = new CustomerSyncService();
        await this.syncService.init();
        console.log('Customer Sync Service initialized');
    }

    /**
     * Initialize scheduler
     */
    async initScheduler() {
        console.log('Initializing Customer Sync Scheduler...');
        this.scheduler = new CustomerSyncScheduler();
        await this.scheduler.init(this.syncService);
        
        // Update scheduler config if provided
        if (this.config.syncInterval !== 5 * 60 * 1000) {
            await this.scheduler.updateConfig({
                syncInterval: this.config.syncInterval
            });
        }
        
        console.log('Customer Sync Scheduler initialized');
    }

    /**
     * Initialize progress component
     */
    async initProgressComponent() {
        console.log('Initializing Customer Sync Progress Component...');
        
        // Check if component exists in DOM
        if (typeof CustomerSyncProgressComponent !== 'undefined') {
            this.progressComponent = CustomerSyncProgressComponent;
            await this.progressComponent.init(this.syncService, this.scheduler);
            
            // Show progress component if configured
            if (this.config.showProgress) {
                this.progressComponent.show();
            }
            
            console.log('Customer Sync Progress Component initialized');
        } else {
            console.warn('Customer Sync Progress Component not found in DOM');
        }
    }

    /**
     * Set up global event listeners
     */
    setupGlobalEventListeners() {
        // Listen for sync events and show notifications if enabled
        if (this.config.enableNotifications) {
            window.addEventListener('customer_sync_completed', (event) => {
                this.showNotification('Customer sync completed successfully', 'success');
            });

            window.addEventListener('customer_sync_error', (event) => {
                this.showNotification('Customer sync failed: ' + event.detail.error, 'error');
            });

            window.addEventListener('customer_scheduler_sync_started', (event) => {
                if (event.detail.type === 'manual') {
                    this.showNotification('Customer sync started...', 'info');
                }
            });
        }

        // Listen for connection changes
        window.addEventListener('online', () => {
            this.dispatchEvent('customer_sync_connection_restored');
        });

        window.addEventListener('offline', () => {
            this.dispatchEvent('customer_sync_connection_lost');
        });

        // Listen for page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                this.dispatchEvent('customer_sync_page_visible');
            }
        });
    }

    /**
     * Show notification to user
     */
    showNotification(message, type = 'info') {
        if (!this.config.enableNotifications) return;

        // Try to use browser notifications if available
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification('Customer Sync', {
                body: message,
                icon: '/favicon.ico',
                tag: 'customer-sync'
            });
        } else {
            // Fallback to console or custom notification system
            console.log(`[Customer Sync ${type.toUpperCase()}] ${message}`);
            
            // If there's a custom notification system, use it
            if (typeof window.showToast === 'function') {
                window.showToast(message, type);
            }
        }
    }

    /**
     * Request notification permission
     */
    async requestNotificationPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            const permission = await Notification.requestPermission();
            return permission === 'granted';
        }
        return false;
    }

    /**
     * Get system status
     */
    async getStatus() {
        if (!this.isInitialized) {
            return { initialized: false };
        }

        const status = {
            initialized: true,
            online: navigator.onLine,
            components: {
                database: !!this.db,
                syncService: !!this.syncService,
                scheduler: !!this.scheduler,
                progressComponent: !!this.progressComponent
            }
        };

        if (this.syncService) {
            status.syncStats = await this.syncService.getSyncStats();
        }

        if (this.scheduler) {
            status.schedulerStatus = this.scheduler.getStatus();
        }

        return status;
    }

    /**
     * Trigger manual sync
     */
    async triggerSync(options = {}) {
        if (!this.syncService) {
            throw new Error('Sync service not initialized');
        }

        return this.syncService.syncCustomers(options);
    }

    /**
     * Get customers from local storage
     */
    async getCustomers(options = {}) {
        if (!this.syncService) {
            throw new Error('Sync service not initialized');
        }

        return this.syncService.getCustomers(options);
    }

    /**
     * Get customer by ID
     */
    async getCustomerById(customerId) {
        if (!this.syncService) {
            throw new Error('Sync service not initialized');
        }

        return this.syncService.getCustomerById(customerId);
    }

    /**
     * Search customers
     */
    async searchCustomers(searchTerm, limit = 20) {
        if (!this.syncService) {
            throw new Error('Sync service not initialized');
        }

        return this.syncService.searchCustomers(searchTerm, limit);
    }

    /**
     * Save customer
     */
    async saveCustomer(customerData) {
        if (!this.syncService) {
            throw new Error('Sync service not initialized');
        }

        return this.syncService.saveCustomer(customerData);
    }

    /**
     * Update sync configuration
     */
    async updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        
        if (this.scheduler && newConfig.syncInterval) {
            await this.scheduler.updateConfig({
                syncInterval: newConfig.syncInterval
            });
        }

        this.dispatchEvent('customer_sync_config_updated', { config: this.config });
    }

    /**
     * Show/hide progress component
     */
    toggleProgress(show = null) {
        if (!this.progressComponent) return;

        if (show === null) {
            // Toggle current state
            show = !this.config.showProgress;
        }

        this.config.showProgress = show;
        
        if (show) {
            this.progressComponent.show();
        } else {
            this.progressComponent.hide();
        }
    }

    /**
     * Clear all customer data
     */
    async clearAllData() {
        if (!this.db) {
            throw new Error('Database not initialized');
        }

        const confirmed = confirm('Are you sure you want to clear all customer data? This action cannot be undone.');
        if (!confirmed) return false;

        await this.db.clearAllData();
        this.dispatchEvent('customer_sync_data_cleared');
        return true;
    }

    /**
     * Dispatch custom events
     */
    dispatchEvent(eventType, data = {}) {
        const event = new CustomEvent(eventType, {
            detail: { ...data, timestamp: new Date().toISOString() }
        });
        window.dispatchEvent(event);
    }

    /**
     * Destroy the sync system
     */
    async destroy() {
        console.log('Destroying Customer Sync System...');

        if (this.scheduler) {
            this.scheduler.destroy();
        }

        if (this.progressComponent) {
            this.progressComponent.hide();
        }

        this.isInitialized = false;
        this.db = null;
        this.syncService = null;
        this.scheduler = null;
        this.progressComponent = null;

        console.log('Customer Sync System destroyed');
    }
}

// Initialize the customer sync system when DOM is ready
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // Create global instance
        window.CustomerSyncManager = new CustomerSyncManager();
        
        // Initialize with default configuration
        // You can customize this based on your application needs
        await window.CustomerSyncManager.init({
            autoStart: true,
            showProgress: false, // Set to true to show progress component by default
            enableScheduler: true,
            syncInterval: 5 * 60 * 1000, // 5 minutes
            enableNotifications: true
        });

        // Request notification permission if needed
        if ('Notification' in window && Notification.permission === 'default') {
            await window.CustomerSyncManager.requestNotificationPermission();
        }

        console.log('Customer Sync System ready for use');
        
    } catch (error) {
        console.error('Failed to initialize Customer Sync System:', error);
    }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CustomerSyncManager;
}
