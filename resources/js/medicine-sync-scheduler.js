/**
 * Medicine Sync Scheduler for PharmaDesk
 * 
 * This module handles automatic background synchronization of medicines data
 * to ensure comprehensive offline sales functionality.
 */

import * as OfflineDB from './offline-db.js';

// Sync configuration
const SYNC_CONFIG = {
  // Sync intervals
  FULL_SYNC_INTERVAL: 6 * 60 * 60 * 1000, // 6 hours
  INCREMENTAL_SYNC_INTERVAL: 30 * 60 * 1000, // 30 minutes
  RETRY_INTERVAL: 5 * 60 * 1000, // 5 minutes
  
  // Batch sizes
  MEDICINE_BATCH_SIZE: 50,
  INVENTORY_BATCH_SIZE: 100,
  
  // Retry configuration
  MAX_RETRIES: 3,
  RETRY_BACKOFF_MULTIPLIER: 2,
  
  // Storage thresholds
  MAX_STORAGE_USAGE: 0.8, // 80% of available storage
  CLEANUP_THRESHOLD: 0.9, // 90% of available storage
};

// Sync state
let syncState = {
  isRunning: false,
  lastFullSync: null,
  lastIncrementalSync: null,
  nextScheduledSync: null,
  retryCount: 0,
  currentOperation: null,
  progress: {
    total: 0,
    processed: 0,
    stage: 'idle'
  }
};

// Timers
let fullSyncTimer = null;
let incrementalSyncTimer = null;
let retryTimer = null;

/**
 * Initialize the medicine sync scheduler
 */
export function initMedicineSyncScheduler() {
  console.log('Initializing medicine sync scheduler...');
  
  // Load previous sync state
  loadSyncState();
  
  // Set up event listeners
  setupEventListeners();
  
  // Start scheduling if online
  if (navigator.onLine) {
    startSyncScheduling();
  }
  
  console.log('Medicine sync scheduler initialized');
}

/**
 * Load sync state from storage
 */
async function loadSyncState() {
  try {
    const lastFullSync = await OfflineDB.getSetting('lastFullMedicineSync');
    const lastIncrementalSync = await OfflineDB.getSetting('lastIncrementalMedicineSync');
    
    syncState.lastFullSync = lastFullSync ? new Date(lastFullSync) : null;
    syncState.lastIncrementalSync = lastIncrementalSync ? new Date(lastIncrementalSync) : null;
    
    console.log('Loaded sync state:', {
      lastFullSync: syncState.lastFullSync,
      lastIncrementalSync: syncState.lastIncrementalSync
    });
  } catch (error) {
    console.error('Error loading sync state:', error);
  }
}

/**
 * Save sync state to storage
 */
async function saveSyncState() {
  try {
    if (syncState.lastFullSync) {
      await OfflineDB.setSetting('lastFullMedicineSync', syncState.lastFullSync.toISOString());
    }
    if (syncState.lastIncrementalSync) {
      await OfflineDB.setSetting('lastIncrementalMedicineSync', syncState.lastIncrementalSync.toISOString());
    }
  } catch (error) {
    console.error('Error saving sync state:', error);
  }
}

/**
 * Set up event listeners for online/offline status
 */
function setupEventListeners() {
  window.addEventListener('online', () => {
    console.log('Connection restored - starting medicine sync scheduling');
    startSyncScheduling();
  });
  
  window.addEventListener('offline', () => {
    console.log('Connection lost - pausing medicine sync scheduling');
    pauseSyncScheduling();
  });
  
  // Listen for visibility changes to sync when app becomes active
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden && navigator.onLine && !syncState.isRunning) {
      checkAndTriggerSync();
    }
  });
}

/**
 * Start sync scheduling
 */
function startSyncScheduling() {
  if (!navigator.onLine) return;
  
  // Clear existing timers
  pauseSyncScheduling();
  
  // Check if immediate sync is needed
  checkAndTriggerSync();
  
  // Schedule periodic syncs
  fullSyncTimer = setInterval(() => {
    if (navigator.onLine && !syncState.isRunning) {
      triggerFullSync();
    }
  }, SYNC_CONFIG.FULL_SYNC_INTERVAL);
  
  incrementalSyncTimer = setInterval(() => {
    if (navigator.onLine && !syncState.isRunning) {
      triggerIncrementalSync();
    }
  }, SYNC_CONFIG.INCREMENTAL_SYNC_INTERVAL);
  
  console.log('Medicine sync scheduling started');
}

/**
 * Pause sync scheduling
 */
function pauseSyncScheduling() {
  if (fullSyncTimer) {
    clearInterval(fullSyncTimer);
    fullSyncTimer = null;
  }
  
  if (incrementalSyncTimer) {
    clearInterval(incrementalSyncTimer);
    incrementalSyncTimer = null;
  }
  
  if (retryTimer) {
    clearTimeout(retryTimer);
    retryTimer = null;
  }
  
  console.log('Medicine sync scheduling paused');
}

/**
 * Check if sync is needed and trigger appropriate sync type
 */
async function checkAndTriggerSync() {
  if (syncState.isRunning || !navigator.onLine) return;
  
  const now = new Date();
  const needsFullSync = !syncState.lastFullSync || 
    (now - syncState.lastFullSync) > SYNC_CONFIG.FULL_SYNC_INTERVAL;
  
  const needsIncrementalSync = !syncState.lastIncrementalSync || 
    (now - syncState.lastIncrementalSync) > SYNC_CONFIG.INCREMENTAL_SYNC_INTERVAL;
  
  if (needsFullSync) {
    await triggerFullSync();
  } else if (needsIncrementalSync) {
    await triggerIncrementalSync();
  }
}

/**
 * Trigger full medicine sync
 */
export async function triggerFullSync() {
  if (syncState.isRunning) {
    console.log('Sync already in progress, skipping full sync');
    return;
  }
  
  console.log('Starting full medicine sync...');
  
  syncState.isRunning = true;
  syncState.currentOperation = 'full_sync';
  syncState.retryCount = 0;
  
  try {
    // Dispatch sync started event
    dispatchSyncEvent('medicine-sync-started', {
      type: 'full',
      timestamp: new Date().toISOString()
    });
    
    // Perform full sync
    await performFullMedicineSync();
    
    // Update sync state
    syncState.lastFullSync = new Date();
    syncState.lastIncrementalSync = new Date();
    await saveSyncState();
    
    console.log('Full medicine sync completed successfully');
    
    // Dispatch completion event
    dispatchSyncEvent('medicine-sync-completed', {
      type: 'full',
      success: true,
      timestamp: new Date().toISOString(),
      processed: syncState.progress.processed
    });
    
  } catch (error) {
    console.error('Full medicine sync failed:', error);
    await handleSyncError(error, 'full_sync');
  } finally {
    syncState.isRunning = false;
    syncState.currentOperation = null;
    resetProgress();
  }
}

/**
 * Trigger incremental medicine sync
 */
export async function triggerIncrementalSync() {
  if (syncState.isRunning) {
    console.log('Sync already in progress, skipping incremental sync');
    return;
  }
  
  console.log('Starting incremental medicine sync...');
  
  syncState.isRunning = true;
  syncState.currentOperation = 'incremental_sync';
  syncState.retryCount = 0;
  
  try {
    // Dispatch sync started event
    dispatchSyncEvent('medicine-sync-started', {
      type: 'incremental',
      timestamp: new Date().toISOString()
    });
    
    // Perform incremental sync
    await performIncrementalMedicineSync();
    
    // Update sync state
    syncState.lastIncrementalSync = new Date();
    await saveSyncState();
    
    console.log('Incremental medicine sync completed successfully');
    
    // Dispatch completion event
    dispatchSyncEvent('medicine-sync-completed', {
      type: 'incremental',
      success: true,
      timestamp: new Date().toISOString(),
      processed: syncState.progress.processed
    });
    
  } catch (error) {
    console.error('Incremental medicine sync failed:', error);
    await handleSyncError(error, 'incremental_sync');
  } finally {
    syncState.isRunning = false;
    syncState.currentOperation = null;
    resetProgress();
  }
}

/**
 * Get current sync status
 */
export function getSyncStatus() {
  return {
    ...syncState,
    isOnline: navigator.onLine,
    nextFullSync: syncState.lastFullSync ? 
      new Date(syncState.lastFullSync.getTime() + SYNC_CONFIG.FULL_SYNC_INTERVAL) : null,
    nextIncrementalSync: syncState.lastIncrementalSync ? 
      new Date(syncState.lastIncrementalSync.getTime() + SYNC_CONFIG.INCREMENTAL_SYNC_INTERVAL) : null
  };
}

/**
 * Dispatch sync events for UI updates
 */
function dispatchSyncEvent(eventType, data) {
  window.dispatchEvent(new CustomEvent(eventType, { detail: data }));
}

/**
 * Reset progress tracking
 */
function resetProgress() {
  syncState.progress = {
    total: 0,
    processed: 0,
    stage: 'idle'
  };
}

/**
 * Perform full medicine sync
 */
async function performFullMedicineSync() {
  syncState.progress.stage = 'fetching_reference_data';

  // First, sync reference data (categories, manufacturers, etc.)
  await syncReferenceData();

  syncState.progress.stage = 'fetching_medicines';

  // Then sync all medicines with comprehensive data
  await syncAllMedicines();

  syncState.progress.stage = 'optimizing_storage';

  // Optimize storage and clean up old data
  await optimizeStorage();

  syncState.progress.stage = 'completed';
}

/**
 * Perform incremental medicine sync
 */
async function performIncrementalMedicineSync() {
  const lastSync = syncState.lastIncrementalSync || syncState.lastFullSync;
  const sinceParam = lastSync ? `?since=${encodeURIComponent(lastSync.toISOString())}` : '';

  syncState.progress.stage = 'fetching_updates';

  // Sync only updated medicines
  await syncUpdatedMedicines(sinceParam);

  syncState.progress.stage = 'completed';
}

/**
 * Sync reference data (categories, manufacturers, etc.)
 */
async function syncReferenceData() {
  try {
    // Sync categories
    const categoriesResponse = await fetch('/api/offline/categories');
    if (categoriesResponse.ok) {
      const categoriesData = await categoriesResponse.json();
      await OfflineDB.cacheCategories(categoriesData.data || []);
    }

    // Sync manufacturers
    const manufacturersResponse = await fetch('/api/offline/manufacturers');
    if (manufacturersResponse.ok) {
      const manufacturersData = await manufacturersResponse.json();
      await OfflineDB.cacheManufacturers(manufacturersData.data || []);
    }

    // Sync locations
    const locationsResponse = await fetch('/api/offline/locations');
    if (locationsResponse.ok) {
      const locationsData = await locationsResponse.json();
      await OfflineDB.cacheLocations(locationsData.data || []);
    }

    console.log('Reference data sync completed');
  } catch (error) {
    console.error('Error syncing reference data:', error);
    throw error;
  }
}

/**
 * Sync all medicines with comprehensive data
 */
async function syncAllMedicines() {
  let offset = 0;
  const limit = SYNC_CONFIG.MEDICINE_BATCH_SIZE;
  let hasMore = true;
  let totalProcessed = 0;

  while (hasMore) {
    try {
      const response = await fetch(`/api/offline/medicines?limit=${limit}&offset=${offset}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch medicines: ${response.status}`);
      }

      const data = await response.json();

      if (data.data && data.data.length > 0) {
        // Process medicines with enhancement for offline sales
        const enhancedMedicines = await Promise.all(
          data.data.map(medicine => enhanceMedicineForOfflineUse(medicine))
        );

        // Cache medicines
        await OfflineDB.cacheMedicines(enhancedMedicines);

        // Cache related inventory data
        for (const medicine of enhancedMedicines) {
          if (medicine.inventories && medicine.inventories.length > 0) {
            await OfflineDB.cacheInventory(medicine.inventories);
          }
        }

        totalProcessed += data.data.length;
        syncState.progress.processed = totalProcessed;

        // Dispatch progress event
        dispatchSyncEvent('medicine-sync-progress', {
          stage: 'medicines',
          processed: totalProcessed,
          message: `Synced ${totalProcessed} medicines...`
        });

        console.log(`Processed ${data.data.length} medicines (total: ${totalProcessed})`);
      }

      hasMore = data.meta?.has_more || false;
      offset += limit;

      // Small delay to prevent overwhelming the browser
      await new Promise(resolve => setTimeout(resolve, 10));

    } catch (error) {
      console.error('Error syncing medicines batch:', error);
      throw error;
    }
  }

  console.log(`All medicines sync completed: ${totalProcessed} medicines processed`);
}

/**
 * Sync only updated medicines (incremental sync)
 */
async function syncUpdatedMedicines(sinceParam) {
  let offset = 0;
  const limit = SYNC_CONFIG.MEDICINE_BATCH_SIZE;
  let hasMore = true;
  let totalProcessed = 0;

  while (hasMore) {
    try {
      const response = await fetch(`/api/offline/medicines${sinceParam}${sinceParam ? '&' : '?'}limit=${limit}&offset=${offset}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch updated medicines: ${response.status}`);
      }

      const data = await response.json();

      if (data.data && data.data.length > 0) {
        // Process and cache updated medicines
        const enhancedMedicines = await Promise.all(
          data.data.map(medicine => enhanceMedicineForOfflineUse(medicine))
        );

        await OfflineDB.cacheMedicines(enhancedMedicines);

        // Update related inventory data
        for (const medicine of enhancedMedicines) {
          if (medicine.inventories && medicine.inventories.length > 0) {
            await OfflineDB.cacheInventory(medicine.inventories);
          }
        }

        totalProcessed += data.data.length;
        syncState.progress.processed = totalProcessed;

        console.log(`Updated ${data.data.length} medicines (total: ${totalProcessed})`);
      }

      hasMore = data.meta?.has_more || false;
      offset += limit;

    } catch (error) {
      console.error('Error syncing updated medicines:', error);
      throw error;
    }
  }

  console.log(`Incremental medicines sync completed: ${totalProcessed} medicines updated`);
}

/**
 * Enhance medicine data for offline sales operations
 */
async function enhanceMedicineForOfflineUse(medicine) {
  // Calculate total stock from inventories
  const totalStock = medicine.inventories ?
    medicine.inventories.reduce((sum, inv) => sum + (inv.quantity || 0), 0) : 0;

  // Calculate available stock (non-expired)
  const now = new Date();
  const availableStock = medicine.inventories ?
    medicine.inventories
      .filter(inv => !inv.expiry_date || new Date(inv.expiry_date) > now)
      .reduce((sum, inv) => sum + (inv.quantity || 0), 0) : 0;

  // Determine stock status
  const stockStatus = getStockStatus(totalStock, availableStock, medicine.minimum_stock);

  // Create searchable text for offline search
  const searchableText = [
    medicine.name,
    medicine.generic_name,
    medicine.manufacturer?.name,
    medicine.category?.name
  ].filter(Boolean).join(' ').toLowerCase();

  return {
    ...medicine,
    // Enhanced fields for offline operations
    total_stock: totalStock,
    available_stock: availableStock,
    stock_status: stockStatus,
    searchable_text: searchableText,
    last_synced: new Date().toISOString(),
    sync_status: 'synced',

    // Sales validation flags
    can_sell: availableStock > 0 && medicine.status === 'active',
    requires_prescription: medicine.prescription_required || false,
    is_controlled: medicine.controlled_substance || false,
  };
}

/**
 * Determine stock status for validation
 */
function getStockStatus(totalStock, availableStock, minimumStock = 0) {
  if (availableStock === 0) return 'out_of_stock';
  if (availableStock <= minimumStock) return 'low_stock';
  if (totalStock > availableStock) return 'has_expired_stock';
  return 'in_stock';
}

/**
 * Optimize storage and clean up old data
 */
async function optimizeStorage() {
  try {
    // Check storage usage
    const storageInfo = await OfflineDB.getStorageInfo();
    const usageRatio = storageInfo.used / storageInfo.quota;

    if (usageRatio > SYNC_CONFIG.CLEANUP_THRESHOLD) {
      console.log('Storage usage high, performing cleanup...');

      // Clean up old sync queue items
      await OfflineDB.cleanupSyncQueue({ maxAge: 7 * 24 * 60 * 60 * 1000 }); // 7 days

      // Remove old medicine data (keep last 30 days)
      const cutoffDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      // Implementation would depend on your specific cleanup needs

      console.log('Storage cleanup completed');
    }
  } catch (error) {
    console.error('Error optimizing storage:', error);
    // Don't throw - this is not critical for sync success
  }
}

/**
 * Handle sync errors with retry logic
 */
async function handleSyncError(error, operation) {
  syncState.retryCount++;

  if (syncState.retryCount <= SYNC_CONFIG.MAX_RETRIES) {
    const retryDelay = SYNC_CONFIG.RETRY_INTERVAL *
      Math.pow(SYNC_CONFIG.RETRY_BACKOFF_MULTIPLIER, syncState.retryCount - 1);

    console.log(`Sync failed, retrying in ${retryDelay / 1000} seconds (attempt ${syncState.retryCount}/${SYNC_CONFIG.MAX_RETRIES})`);

    // Dispatch retry event
    dispatchSyncEvent('medicine-sync-retry', {
      operation,
      attempt: syncState.retryCount,
      maxRetries: SYNC_CONFIG.MAX_RETRIES,
      retryDelay,
      error: error.message
    });

    retryTimer = setTimeout(() => {
      if (operation === 'full_sync') {
        triggerFullSync();
      } else if (operation === 'incremental_sync') {
        triggerIncrementalSync();
      }
    }, retryDelay);
  } else {
    console.error(`Sync failed after ${SYNC_CONFIG.MAX_RETRIES} attempts:`, error);

    // Dispatch failure event
    dispatchSyncEvent('medicine-sync-failed', {
      operation,
      error: error.message,
      attempts: syncState.retryCount
    });

    // Reset retry count for next sync
    syncState.retryCount = 0;
  }
}

// Make functions available globally
window.MedicineSyncScheduler = {
  init: initMedicineSyncScheduler,
  triggerFullSync,
  triggerIncrementalSync,
  getSyncStatus,
  pauseSyncScheduling,
  startSyncScheduling: () => {
    if (navigator.onLine) startSyncScheduling();
  }
};
