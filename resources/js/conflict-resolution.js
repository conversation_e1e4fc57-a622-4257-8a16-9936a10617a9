/**
 * Conflict Resolution Module for PharmaDesk Offline Sync
 * 
 * This module handles data conflicts that arise during synchronization
 * between offline and online data.
 */

import * as OfflineDB from './offline-db.js';

// Conflict resolution strategies
export const RESOLUTION_STRATEGIES = {
  SERVER_WINS: 'server_wins',
  CLIENT_WINS: 'client_wins',
  MERGE: 'merge',
  USER_CHOICE: 'user_choice',
  TIMESTAMP_BASED: 'timestamp_based'
};

// Conflict types
export const CONFLICT_TYPES = {
  UPDATE_UPDATE: 'update_update',
  UPDATE_DELETE: 'update_delete',
  DELETE_UPDATE: 'delete_update',
  DUPLICATE_CREATE: 'duplicate_create'
};

/**
 * Detect conflicts between local and server data
 * @param {Object} localData - Local data object
 * @param {Object} serverData - Server data object
 * @param {string} entityType - Type of entity (sales, customers, etc.)
 * @returns {Object} Conflict detection result
 */
export function detectConflict(localData, serverData, entityType) {
  if (!localData && !serverData) {
    return { hasConflict: false };
  }
  
  // No conflict if only one version exists
  if (!localData || !serverData) {
    return { hasConflict: false };
  }
  
  // Check for timestamp conflicts
  const localTimestamp = new Date(localData.updated_at);
  const serverTimestamp = new Date(serverData.updated_at);
  
  // If timestamps are the same, no conflict
  if (localTimestamp.getTime() === serverTimestamp.getTime()) {
    return { hasConflict: false };
  }
  
  // Detect conflict type
  let conflictType = CONFLICT_TYPES.UPDATE_UPDATE;
  
  if (localData.deleted_at && !serverData.deleted_at) {
    conflictType = CONFLICT_TYPES.DELETE_UPDATE;
  } else if (!localData.deleted_at && serverData.deleted_at) {
    conflictType = CONFLICT_TYPES.UPDATE_DELETE;
  }
  
  // Check for data differences
  const differences = findDataDifferences(localData, serverData, entityType);
  
  return {
    hasConflict: true,
    conflictType,
    localData,
    serverData,
    localTimestamp,
    serverTimestamp,
    differences,
    entityType
  };
}

/**
 * Find differences between local and server data
 * @param {Object} localData - Local data object
 * @param {Object} serverData - Server data object
 * @param {string} entityType - Type of entity
 * @returns {Array} Array of differences
 */
function findDataDifferences(localData, serverData, entityType) {
  const differences = [];
  const fieldsToCompare = getComparableFields(entityType);
  
  for (const field of fieldsToCompare) {
    const localValue = localData[field];
    const serverValue = serverData[field];
    
    if (localValue !== serverValue) {
      differences.push({
        field,
        localValue,
        serverValue,
        type: typeof localValue !== typeof serverValue ? 'type_mismatch' : 'value_mismatch'
      });
    }
  }
  
  return differences;
}

/**
 * Get fields that should be compared for conflicts
 * @param {string} entityType - Type of entity
 * @returns {Array} Array of field names
 */
function getComparableFields(entityType) {
  const commonFields = ['id', 'created_at', 'updated_at'];
  
  const entityFields = {
    sales: ['customer_id', 'total_amount', 'paid_amount', 'due_amount', 'discount_amount', 'tax_amount', 'payment_method', 'payment_status'],
    customers: ['name', 'email', 'phone', 'address', 'loyalty_points', 'status'],
    inventory: ['medicine_id', 'batch_number', 'quantity', 'unit_price', 'purchase_price', 'expiry_date'],
    medicines: ['name', 'generic_name', 'manufacturer_id', 'category_id', 'unit_price', 'status']
  };
  
  return [...commonFields, ...(entityFields[entityType] || [])];
}

/**
 * Resolve conflict using specified strategy
 * @param {Object} conflict - Conflict object
 * @param {string} strategy - Resolution strategy
 * @param {Object} userChoices - User choices for manual resolution
 * @returns {Object} Resolution result
 */
export async function resolveConflict(conflict, strategy = RESOLUTION_STRATEGIES.TIMESTAMP_BASED, userChoices = null) {
  try {
    switch (strategy) {
      case RESOLUTION_STRATEGIES.SERVER_WINS:
        return resolveServerWins(conflict);
        
      case RESOLUTION_STRATEGIES.CLIENT_WINS:
        return resolveClientWins(conflict);
        
      case RESOLUTION_STRATEGIES.TIMESTAMP_BASED:
        return resolveTimestampBased(conflict);
        
      case RESOLUTION_STRATEGIES.MERGE:
        return resolveMerge(conflict);
        
      case RESOLUTION_STRATEGIES.USER_CHOICE:
        return resolveUserChoice(conflict, userChoices);
        
      default:
        throw new Error(`Unknown resolution strategy: ${strategy}`);
    }
  } catch (error) {
    console.error('Error resolving conflict:', error);
    return {
      success: false,
      error: error.message,
      resolvedData: null
    };
  }
}

/**
 * Resolve conflict by choosing server data
 * @param {Object} conflict - Conflict object
 * @returns {Object} Resolution result
 */
function resolveServerWins(conflict) {
  return {
    success: true,
    strategy: RESOLUTION_STRATEGIES.SERVER_WINS,
    resolvedData: conflict.serverData,
    message: 'Conflict resolved: Server data chosen'
  };
}

/**
 * Resolve conflict by choosing client data
 * @param {Object} conflict - Conflict object
 * @returns {Object} Resolution result
 */
function resolveClientWins(conflict) {
  return {
    success: true,
    strategy: RESOLUTION_STRATEGIES.CLIENT_WINS,
    resolvedData: conflict.localData,
    message: 'Conflict resolved: Client data chosen'
  };
}

/**
 * Resolve conflict based on timestamps (most recent wins)
 * @param {Object} conflict - Conflict object
 * @returns {Object} Resolution result
 */
function resolveTimestampBased(conflict) {
  const useServerData = conflict.serverTimestamp > conflict.localTimestamp;
  
  return {
    success: true,
    strategy: RESOLUTION_STRATEGIES.TIMESTAMP_BASED,
    resolvedData: useServerData ? conflict.serverData : conflict.localData,
    message: `Conflict resolved: ${useServerData ? 'Server' : 'Client'} data chosen (most recent)`
  };
}

/**
 * Resolve conflict by merging data
 * @param {Object} conflict - Conflict object
 * @returns {Object} Resolution result
 */
function resolveMerge(conflict) {
  const mergedData = { ...conflict.localData };
  
  // Use server data for system fields
  const systemFields = ['id', 'created_at'];
  systemFields.forEach(field => {
    if (conflict.serverData[field] !== undefined) {
      mergedData[field] = conflict.serverData[field];
    }
  });
  
  // Use most recent timestamp
  if (conflict.serverTimestamp > conflict.localTimestamp) {
    mergedData.updated_at = conflict.serverData.updated_at;
  }
  
  // For numeric fields, use the higher value (assuming it's more recent)
  const numericFields = ['total_amount', 'paid_amount', 'quantity', 'loyalty_points'];
  numericFields.forEach(field => {
    if (conflict.serverData[field] !== undefined && conflict.localData[field] !== undefined) {
      mergedData[field] = Math.max(conflict.serverData[field], conflict.localData[field]);
    }
  });
  
  return {
    success: true,
    strategy: RESOLUTION_STRATEGIES.MERGE,
    resolvedData: mergedData,
    message: 'Conflict resolved: Data merged'
  };
}

/**
 * Resolve conflict based on user choices
 * @param {Object} conflict - Conflict object
 * @param {Object} userChoices - User choices for each field
 * @returns {Object} Resolution result
 */
function resolveUserChoice(conflict, userChoices) {
  if (!userChoices) {
    throw new Error('User choices required for manual resolution');
  }
  
  const resolvedData = { ...conflict.localData };
  
  // Apply user choices for each field
  Object.keys(userChoices).forEach(field => {
    const choice = userChoices[field];
    if (choice === 'server') {
      resolvedData[field] = conflict.serverData[field];
    } else if (choice === 'client') {
      resolvedData[field] = conflict.localData[field];
    } else if (choice === 'custom') {
      resolvedData[field] = userChoices[`${field}_custom_value`];
    }
  });
  
  return {
    success: true,
    strategy: RESOLUTION_STRATEGIES.USER_CHOICE,
    resolvedData,
    message: 'Conflict resolved: User choices applied'
  };
}

/**
 * Save conflict resolution result
 * @param {Object} conflict - Original conflict
 * @param {Object} resolution - Resolution result
 * @returns {Promise<void>}
 */
export async function saveConflictResolution(conflict, resolution) {
  if (!resolution.success) {
    throw new Error(`Cannot save failed resolution: ${resolution.error}`);
  }
  
  // Save the resolved data to the appropriate store
  const db = await OfflineDB.initDB();
  const storeName = getStoreNameForEntity(conflict.entityType);
  
  if (storeName) {
    const tx = db.transaction([storeName, OfflineDB.STORES.SYNC_QUEUE], 'readwrite');
    
    // Update the main record
    resolution.resolvedData.sync_status = 'pending';
    resolution.resolvedData.updated_at = new Date().toISOString();
    await tx.objectStore(storeName).put(resolution.resolvedData);
    
    // Add to sync queue for server update
    await tx.objectStore(OfflineDB.STORES.SYNC_QUEUE).add({
      entity_type: conflict.entityType,
      operation: 'update',
      data: resolution.resolvedData,
      entity_id: resolution.resolvedData.id,
      status: 'pending',
      retry_count: 0,
      created_at: new Date().toISOString(),
      last_attempt: null,
      conflict_resolved: true,
      resolution_strategy: resolution.strategy
    });
    
    await tx.done;
  }
}

/**
 * Get store name for entity type
 * @param {string} entityType - Entity type
 * @returns {string} Store name
 */
function getStoreNameForEntity(entityType) {
  const storeMap = {
    sales: OfflineDB.STORES.SALES,
    customers: OfflineDB.STORES.CUSTOMERS,
    inventory: OfflineDB.STORES.INVENTORY,
    medicines: OfflineDB.STORES.MEDICINES
  };
  
  return storeMap[entityType];
}
