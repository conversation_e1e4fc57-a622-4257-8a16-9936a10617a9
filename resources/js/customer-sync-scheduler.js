/**
 * Customer Sync Scheduler
 * Handles automatic background synchronization of customer data
 */

class CustomerSyncScheduler {
    constructor() {
        this.syncService = null;
        this.isRunning = false;
        this.intervalId = null;
        this.config = {
            syncInterval: 5 * 60 * 1000, // 5 minutes in milliseconds
            autoSyncEnabled: true,
            syncOnStartup: true,
            syncOnVisibilityChange: true,
            syncOnConnectionChange: true,
            maxRetries: 3,
            retryDelay: 30000, // 30 seconds
            quietHours: {
                enabled: false,
                start: '22:00',
                end: '06:00'
            }
        };
        this.lastSyncAttempt = null;
        this.consecutiveFailures = 0;
        this.listeners = new Map();
        
        // Bind methods
        this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
        this.handleConnectionChange = this.handleConnectionChange.bind(this);
        this.handleBeforeUnload = this.handleBeforeUnload.bind(this);
    }

    /**
     * Initialize the scheduler
     */
    async init(syncService) {
        try {
            this.syncService = syncService;
            
            // Load configuration from localStorage
            await this.loadConfig();
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Start automatic sync if enabled
            if (this.config.autoSyncEnabled) {
                await this.start();
            }
            
            // Perform initial sync if configured
            if (this.config.syncOnStartup && navigator.onLine) {
                this.scheduleImmediateSync();
            }
            
            console.log('Customer sync scheduler initialized successfully');
            this.dispatchEvent('scheduler_initialized', { config: this.config });
            
            return true;
        } catch (error) {
            console.error('Failed to initialize customer sync scheduler:', error);
            throw error;
        }
    }

    /**
     * Load configuration from localStorage
     */
    async loadConfig() {
        try {
            const savedConfig = localStorage.getItem('customer_sync_config');
            if (savedConfig) {
                const parsedConfig = JSON.parse(savedConfig);
                this.config = { ...this.config, ...parsedConfig };
            }
        } catch (error) {
            console.warn('Failed to load sync config, using defaults:', error);
        }
    }

    /**
     * Save configuration to localStorage
     */
    async saveConfig() {
        try {
            localStorage.setItem('customer_sync_config', JSON.stringify(this.config));
            this.dispatchEvent('config_updated', { config: this.config });
        } catch (error) {
            console.error('Failed to save sync config:', error);
        }
    }

    /**
     * Update configuration
     */
    async updateConfig(newConfig) {
        const oldConfig = { ...this.config };
        this.config = { ...this.config, ...newConfig };
        
        await this.saveConfig();
        
        // Restart scheduler if interval changed
        if (oldConfig.syncInterval !== this.config.syncInterval && this.isRunning) {
            await this.restart();
        }
        
        // Enable/disable auto sync
        if (oldConfig.autoSyncEnabled !== this.config.autoSyncEnabled) {
            if (this.config.autoSyncEnabled) {
                await this.start();
            } else {
                await this.stop();
            }
        }
        
        console.log('Customer sync configuration updated:', this.config);
        return this.config;
    }

    /**
     * Start the automatic sync scheduler
     */
    async start() {
        if (this.isRunning) {
            console.log('Customer sync scheduler already running');
            return;
        }

        if (!this.syncService) {
            throw new Error('Sync service not initialized');
        }

        this.isRunning = true;
        this.scheduleNextSync();
        
        console.log(`Customer sync scheduler started with ${this.config.syncInterval / 1000}s interval`);
        this.dispatchEvent('scheduler_started', { 
            interval: this.config.syncInterval,
            nextSync: this.getNextSyncTime()
        });
    }

    /**
     * Stop the automatic sync scheduler
     */
    async stop() {
        if (!this.isRunning) {
            console.log('Customer sync scheduler not running');
            return;
        }

        this.isRunning = false;
        
        if (this.intervalId) {
            clearTimeout(this.intervalId);
            this.intervalId = null;
        }
        
        console.log('Customer sync scheduler stopped');
        this.dispatchEvent('scheduler_stopped');
    }

    /**
     * Restart the scheduler
     */
    async restart() {
        await this.stop();
        await this.start();
    }

    /**
     * Schedule the next sync
     */
    scheduleNextSync() {
        if (!this.isRunning) return;

        if (this.intervalId) {
            clearTimeout(this.intervalId);
        }

        const delay = this.calculateNextSyncDelay();
        
        this.intervalId = setTimeout(async () => {
            await this.performScheduledSync();
            this.scheduleNextSync(); // Schedule the next one
        }, delay);

        this.dispatchEvent('sync_scheduled', { 
            nextSync: new Date(Date.now() + delay).toISOString(),
            delay: delay
        });
    }

    /**
     * Calculate delay until next sync
     */
    calculateNextSyncDelay() {
        let delay = this.config.syncInterval;

        // Check quiet hours
        if (this.config.quietHours.enabled && this.isInQuietHours()) {
            const quietEndTime = this.getQuietHoursEndTime();
            delay = Math.max(delay, quietEndTime - Date.now());
        }

        // Add exponential backoff for consecutive failures
        if (this.consecutiveFailures > 0) {
            const backoffMultiplier = Math.min(Math.pow(2, this.consecutiveFailures), 8);
            delay *= backoffMultiplier;
        }

        return Math.max(delay, 30000); // Minimum 30 seconds
    }

    /**
     * Check if current time is in quiet hours
     */
    isInQuietHours() {
        if (!this.config.quietHours.enabled) return false;

        const now = new Date();
        const currentTime = now.getHours() * 60 + now.getMinutes();
        
        const [startHour, startMin] = this.config.quietHours.start.split(':').map(Number);
        const [endHour, endMin] = this.config.quietHours.end.split(':').map(Number);
        
        const startTime = startHour * 60 + startMin;
        const endTime = endHour * 60 + endMin;

        if (startTime <= endTime) {
            // Same day quiet hours
            return currentTime >= startTime && currentTime <= endTime;
        } else {
            // Overnight quiet hours
            return currentTime >= startTime || currentTime <= endTime;
        }
    }

    /**
     * Get quiet hours end time
     */
    getQuietHoursEndTime() {
        const now = new Date();
        const [endHour, endMin] = this.config.quietHours.end.split(':').map(Number);
        
        const endTime = new Date(now);
        endTime.setHours(endHour, endMin, 0, 0);
        
        // If end time is before current time, it's tomorrow
        if (endTime <= now) {
            endTime.setDate(endTime.getDate() + 1);
        }
        
        return endTime.getTime();
    }

    /**
     * Perform scheduled sync
     */
    async performScheduledSync() {
        if (!navigator.onLine) {
            console.log('Skipping scheduled customer sync: offline');
            return;
        }

        if (!this.syncService) {
            console.error('Sync service not available');
            return;
        }

        try {
            this.lastSyncAttempt = new Date().toISOString();
            this.dispatchEvent('sync_started', { 
                type: 'scheduled',
                timestamp: this.lastSyncAttempt
            });

            console.log('Performing scheduled customer sync...');
            const result = await this.syncService.syncCustomers();
            
            this.consecutiveFailures = 0;
            this.dispatchEvent('sync_completed', { 
                type: 'scheduled',
                result: result,
                timestamp: new Date().toISOString()
            });

            console.log('Scheduled customer sync completed successfully');
        } catch (error) {
            this.consecutiveFailures++;
            console.error('Scheduled customer sync failed:', error);
            
            this.dispatchEvent('sync_failed', { 
                type: 'scheduled',
                error: error.message,
                consecutiveFailures: this.consecutiveFailures,
                timestamp: new Date().toISOString()
            });

            // If too many consecutive failures, increase delay
            if (this.consecutiveFailures >= this.config.maxRetries) {
                console.warn(`Customer sync failed ${this.consecutiveFailures} times consecutively`);
            }
        }
    }

    /**
     * Schedule immediate sync
     */
    scheduleImmediateSync() {
        if (!this.isRunning || !navigator.onLine) return;

        // Cancel current scheduled sync
        if (this.intervalId) {
            clearTimeout(this.intervalId);
        }

        // Schedule immediate sync
        this.intervalId = setTimeout(async () => {
            await this.performScheduledSync();
            this.scheduleNextSync(); // Resume normal schedule
        }, 1000); // 1 second delay to avoid overwhelming

        this.dispatchEvent('immediate_sync_scheduled');
    }

    /**
     * Trigger manual sync
     */
    async triggerManualSync() {
        if (!this.syncService) {
            throw new Error('Sync service not available');
        }

        try {
            this.dispatchEvent('sync_started', { 
                type: 'manual',
                timestamp: new Date().toISOString()
            });

            console.log('Performing manual customer sync...');
            const result = await this.syncService.syncCustomers({ forceFullSync: false });
            
            this.consecutiveFailures = 0;
            this.dispatchEvent('sync_completed', { 
                type: 'manual',
                result: result,
                timestamp: new Date().toISOString()
            });

            console.log('Manual customer sync completed successfully');
            return result;
        } catch (error) {
            console.error('Manual customer sync failed:', error);
            
            this.dispatchEvent('sync_failed', { 
                type: 'manual',
                error: error.message,
                timestamp: new Date().toISOString()
            });
            
            throw error;
        }
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Page visibility change
        if (this.config.syncOnVisibilityChange) {
            document.addEventListener('visibilitychange', this.handleVisibilityChange);
        }

        // Connection change
        if (this.config.syncOnConnectionChange) {
            window.addEventListener('online', this.handleConnectionChange);
            window.addEventListener('offline', this.handleConnectionChange);
        }

        // Before page unload
        window.addEventListener('beforeunload', this.handleBeforeUnload);

        // Listen to sync service events
        window.addEventListener('customer_sync_completed', (event) => {
            this.consecutiveFailures = 0;
        });

        window.addEventListener('customer_sync_error', (event) => {
            this.consecutiveFailures++;
        });
    }

    /**
     * Handle page visibility change
     */
    handleVisibilityChange() {
        if (document.visibilityState === 'visible' && this.isRunning && navigator.onLine) {
            // Page became visible, schedule immediate sync if it's been a while
            const timeSinceLastSync = this.lastSyncAttempt ? 
                Date.now() - new Date(this.lastSyncAttempt).getTime() : 
                Infinity;

            if (timeSinceLastSync > this.config.syncInterval) {
                this.scheduleImmediateSync();
            }
        }
    }

    /**
     * Handle connection change
     */
    handleConnectionChange() {
        if (navigator.onLine && this.isRunning) {
            console.log('Connection restored, scheduling immediate customer sync');
            this.scheduleImmediateSync();
        }
    }

    /**
     * Handle before page unload
     */
    handleBeforeUnload() {
        // Save any pending state
        this.saveConfig();
    }

    /**
     * Get next sync time
     */
    getNextSyncTime() {
        if (!this.isRunning) return null;
        
        const delay = this.calculateNextSyncDelay();
        return new Date(Date.now() + delay).toISOString();
    }

    /**
     * Get scheduler status
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            config: this.config,
            lastSyncAttempt: this.lastSyncAttempt,
            nextSync: this.getNextSyncTime(),
            consecutiveFailures: this.consecutiveFailures,
            isOnline: navigator.onLine,
            isInQuietHours: this.isInQuietHours()
        };
    }

    /**
     * Dispatch custom events
     */
    dispatchEvent(eventType, data = {}) {
        const event = new CustomEvent(`customer_scheduler_${eventType}`, {
            detail: { ...data, timestamp: new Date().toISOString() }
        });
        window.dispatchEvent(event);
    }

    /**
     * Add event listener
     */
    addEventListener(eventType, callback) {
        const fullEventType = `customer_scheduler_${eventType}`;
        window.addEventListener(fullEventType, callback);
        
        if (!this.listeners.has(fullEventType)) {
            this.listeners.set(fullEventType, new Set());
        }
        this.listeners.get(fullEventType).add(callback);
    }

    /**
     * Remove event listener
     */
    removeEventListener(eventType, callback) {
        const fullEventType = `customer_scheduler_${eventType}`;
        window.removeEventListener(fullEventType, callback);
        
        if (this.listeners.has(fullEventType)) {
            this.listeners.get(fullEventType).delete(callback);
        }
    }

    /**
     * Clean up resources
     */
    destroy() {
        this.stop();
        
        // Remove all event listeners
        document.removeEventListener('visibilitychange', this.handleVisibilityChange);
        window.removeEventListener('online', this.handleConnectionChange);
        window.removeEventListener('offline', this.handleConnectionChange);
        window.removeEventListener('beforeunload', this.handleBeforeUnload);
        
        // Remove custom event listeners
        this.listeners.forEach((callbacks, eventType) => {
            callbacks.forEach(callback => {
                window.removeEventListener(eventType, callback);
            });
        });
        this.listeners.clear();
        
        console.log('Customer sync scheduler destroyed');
    }
}

// Export for use in other modules
window.CustomerSyncScheduler = CustomerSyncScheduler;
