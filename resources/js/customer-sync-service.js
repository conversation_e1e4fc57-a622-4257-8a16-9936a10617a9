/**
 * Customer Sync Service
 * Handles synchronization of customer data between server and IndexedDB
 */

class CustomerSyncService {
    constructor() {
        this.db = new CustomerOfflineDB();
        this.apiBaseUrl = '/api/offline';
        this.isOnline = navigator.onLine;
        this.syncInProgress = false;
        this.retryAttempts = 3;
        this.retryDelay = 2000; // 2 seconds
        this.batchSize = 50;
        
        // Event listeners for online/offline status
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.onConnectionChange('online');
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.onConnectionChange('offline');
        });
    }

    /**
     * Initialize the sync service
     */
    async init() {
        try {
            await this.db.init();
            console.log('Customer sync service initialized successfully');
            
            // Perform initial sync if online
            if (this.isOnline) {
                this.performInitialSync();
            }
            
            return true;
        } catch (error) {
            console.error('Failed to initialize customer sync service:', error);
            throw error;
        }
    }

    /**
     * Perform initial sync on startup
     */
    async performInitialSync() {
        try {
            const lastSync = await this.db.getSyncMetadata('last_customer_sync');
            const shouldSync = !lastSync || this.shouldPerformFullSync(lastSync);
            
            if (shouldSync) {
                console.log('Performing initial customer sync...');
                await this.syncCustomers();
            }
        } catch (error) {
            console.error('Initial customer sync failed:', error);
        }
    }

    /**
     * Check if full sync is needed
     */
    shouldPerformFullSync(lastSync) {
        if (!lastSync) return true;
        
        const lastSyncTime = new Date(lastSync);
        const hoursSinceLastSync = (Date.now() - lastSyncTime.getTime()) / (1000 * 60 * 60);
        
        // Perform full sync if more than 24 hours since last sync
        return hoursSinceLastSync > 24;
    }

    /**
     * Sync customers from server
     */
    async syncCustomers(options = {}) {
        if (this.syncInProgress) {
            console.log('Customer sync already in progress');
            return { status: 'already_running' };
        }

        if (!this.isOnline) {
            console.log('Cannot sync customers: offline');
            return { status: 'offline' };
        }

        this.syncInProgress = true;
        this.dispatchSyncEvent('sync_started', { type: 'customers' });

        try {
            const syncResult = await this.performCustomerSync(options);
            
            // Update last sync timestamp
            await this.db.setSyncMetadata('last_customer_sync', new Date().toISOString());
            
            this.dispatchSyncEvent('sync_completed', { 
                type: 'customers',
                result: syncResult 
            });
            
            return syncResult;
        } catch (error) {
            console.error('Customer sync failed:', error);
            this.dispatchSyncEvent('sync_error', { 
                type: 'customers',
                error: error.message 
            });
            throw error;
        } finally {
            this.syncInProgress = false;
        }
    }

    /**
     * Perform the actual customer sync
     */
    async performCustomerSync(options = {}) {
        const { forceFullSync = false, since = null } = options;
        let totalSynced = 0;
        let offset = 0;
        let hasMore = true;
        
        // Determine sync timestamp
        let syncSince = since;
        if (!forceFullSync && !syncSince) {
            syncSince = await this.db.getSyncMetadata('last_customer_sync');
        }

        console.log('Starting customer sync...', { forceFullSync, syncSince });

        while (hasMore) {
            try {
                const response = await this.fetchCustomersFromServer({
                    limit: this.batchSize,
                    offset: offset,
                    since: syncSince
                });

                if (!response.ok) {
                    throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                const customers = data.data || [];
                
                if (customers.length > 0) {
                    // Store customers in IndexedDB
                    await this.db.storeCustomers(customers);
                    
                    // Store related loyalty transactions
                    await this.storeLoyaltyTransactions(customers);
                    
                    totalSynced += customers.length;
                    
                    this.dispatchSyncEvent('sync_progress', {
                        type: 'customers',
                        processed: totalSynced,
                        batch: customers.length
                    });
                }

                hasMore = data.meta?.has_more || false;
                offset += this.batchSize;

                // Add small delay between batches to prevent overwhelming the server
                if (hasMore) {
                    await this.delay(100);
                }

            } catch (error) {
                console.error(`Failed to sync customer batch at offset ${offset}:`, error);
                
                // Retry logic
                const retrySuccess = await this.retryOperation(
                    () => this.fetchCustomersFromServer({
                        limit: this.batchSize,
                        offset: offset,
                        since: syncSince
                    }),
                    this.retryAttempts
                );

                if (!retrySuccess) {
                    throw new Error(`Failed to sync customers after ${this.retryAttempts} retries`);
                }
            }
        }

        console.log(`Customer sync completed. Total synced: ${totalSynced}`);
        return {
            status: 'completed',
            totalSynced: totalSynced,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Fetch customers from server
     */
    async fetchCustomersFromServer(params) {
        const url = new URL(`${this.apiBaseUrl}/customers`, window.location.origin);
        
        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined) {
                url.searchParams.append(key, params[key]);
            }
        });

        return fetch(url.toString(), {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin'
        });
    }

    /**
     * Store loyalty transactions from customer data
     */
    async storeLoyaltyTransactions(customers) {
        const allTransactions = [];
        
        customers.forEach(customer => {
            if (customer.loyalty_transactions && Array.isArray(customer.loyalty_transactions)) {
                customer.loyalty_transactions.forEach(transaction => {
                    allTransactions.push({
                        ...transaction,
                        customer_id: customer.id
                    });
                });
            }
        });

        if (allTransactions.length > 0) {
            await this.db.storeLoyaltyTransactions(allTransactions);
        }
    }

    /**
     * Get customers from local storage
     */
    async getCustomers(options = {}) {
        try {
            return await this.db.getCustomers(options);
        } catch (error) {
            console.error('Failed to get customers from local storage:', error);
            throw error;
        }
    }

    /**
     * Get customer by ID from local storage
     */
    async getCustomerById(customerId) {
        try {
            return await this.db.getCustomerById(customerId);
        } catch (error) {
            console.error('Failed to get customer by ID:', error);
            throw error;
        }
    }

    /**
     * Search customers in local storage
     */
    async searchCustomers(searchTerm, limit = 20) {
        try {
            return await this.db.searchCustomers(searchTerm, limit);
        } catch (error) {
            console.error('Failed to search customers:', error);
            throw error;
        }
    }

    /**
     * Save customer to local storage and sync to server if online
     */
    async saveCustomer(customerData) {
        try {
            // Save to local storage first
            const savedCustomer = await this.db.saveCustomer(customerData);
            
            // If online, try to sync to server
            if (this.isOnline) {
                try {
                    await this.syncCustomerToServer(savedCustomer);
                } catch (error) {
                    console.warn('Failed to sync customer to server, will retry later:', error);
                    // Mark for later sync
                    await this.markForSync(savedCustomer.id);
                }
            } else {
                // Mark for sync when online
                await this.markForSync(savedCustomer.id);
            }
            
            return savedCustomer;
        } catch (error) {
            console.error('Failed to save customer:', error);
            throw error;
        }
    }

    /**
     * Sync customer to server
     */
    async syncCustomerToServer(customer) {
        const url = `${this.apiBaseUrl}/customers`;
        
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin',
            body: JSON.stringify(customer)
        });

        if (!response.ok) {
            throw new Error(`Failed to sync customer to server: ${response.status}`);
        }

        return response.json();
    }

    /**
     * Mark customer for sync
     */
    async markForSync(customerId) {
        const pendingSync = await this.db.getSyncMetadata('pending_customer_sync') || [];
        if (!pendingSync.includes(customerId)) {
            pendingSync.push(customerId);
            await this.db.setSyncMetadata('pending_customer_sync', pendingSync);
        }
    }

    /**
     * Sync pending customers to server
     */
    async syncPendingCustomers() {
        if (!this.isOnline) return;

        const pendingSync = await this.db.getSyncMetadata('pending_customer_sync') || [];
        if (pendingSync.length === 0) return;

        console.log(`Syncing ${pendingSync.length} pending customers...`);

        const synced = [];
        for (const customerId of pendingSync) {
            try {
                const customer = await this.db.getCustomerById(customerId);
                if (customer) {
                    await this.syncCustomerToServer(customer);
                    synced.push(customerId);
                }
            } catch (error) {
                console.error(`Failed to sync customer ${customerId}:`, error);
            }
        }

        // Remove synced customers from pending list
        const remaining = pendingSync.filter(id => !synced.includes(id));
        await this.db.setSyncMetadata('pending_customer_sync', remaining);

        console.log(`Synced ${synced.length} pending customers`);
    }

    /**
     * Handle connection change
     */
    async onConnectionChange(status) {
        console.log(`Customer sync service: Connection ${status}`);
        
        if (status === 'online') {
            // Sync pending customers
            await this.syncPendingCustomers();
            
            // Perform incremental sync
            await this.syncCustomers();
        }
        
        this.dispatchSyncEvent('connection_change', { status });
    }

    /**
     * Retry operation with exponential backoff
     */
    async retryOperation(operation, maxRetries) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const result = await operation();
                return result;
            } catch (error) {
                if (attempt === maxRetries) {
                    console.error(`Operation failed after ${maxRetries} attempts:`, error);
                    return false;
                }
                
                const delay = this.retryDelay * Math.pow(2, attempt - 1);
                console.log(`Retry attempt ${attempt} failed, retrying in ${delay}ms...`);
                await this.delay(delay);
            }
        }
        return false;
    }

    /**
     * Utility delay function
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Dispatch sync events
     */
    dispatchSyncEvent(eventType, data) {
        const event = new CustomEvent(`customer_${eventType}`, {
            detail: data
        });
        window.dispatchEvent(event);
    }

    /**
     * Get sync statistics
     */
    async getSyncStats() {
        const stats = await this.db.getStats();
        const lastSync = await this.db.getSyncMetadata('last_customer_sync');
        const pendingSync = await this.db.getSyncMetadata('pending_customer_sync') || [];

        return {
            ...stats,
            lastSync: lastSync,
            pendingSync: pendingSync.length,
            isOnline: this.isOnline,
            syncInProgress: this.syncInProgress
        };
    }

    /**
     * Clear all customer data
     */
    async clearAllData() {
        return this.db.clearAllData();
    }
}

// Export for use in other modules
window.CustomerSyncService = CustomerSyncService;
