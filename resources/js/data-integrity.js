/**
 * Data Integrity Module for PharmaDesk Offline Operations
 * 
 * This module provides validation and integrity checks for offline operations,
 * including inventory constraints, business rule validation, and data consistency verification.
 */

import * as OfflineDB from './offline-db.js';

// Validation error types
export const VALIDATION_ERRORS = {
  INSUFFICIENT_STOCK: 'insufficient_stock',
  INVALID_QUANTITY: 'invalid_quantity',
  INVALID_PRICE: 'invalid_price',
  MISSING_REQUIRED_FIELD: 'missing_required_field',
  INVALID_CUSTOMER: 'invalid_customer',
  EXPIRED_MEDICINE: 'expired_medicine',
  INVALID_BATCH: 'invalid_batch',
  BUSINESS_RULE_VIOLATION: 'business_rule_violation',
  DATA_CONSISTENCY_ERROR: 'data_consistency_error'
};

// Business rules configuration
const BUSINESS_RULES = {
  MIN_SALE_AMOUNT: 0.01,
  MAX_SALE_AMOUNT: 100000,
  MAX_DISCOUNT_PERCENTAGE: 50,
  MIN_LOYALTY_POINTS_REDEMPTION: 10,
  MAX_LOYALTY_POINTS_REDEMPTION_PERCENTAGE: 80,
  EXPIRY_WARNING_DAYS: 30,
  NEGATIVE_STOCK_ALLOWED: false
};

/**
 * Validate sale data before processing
 * @param {Object} saleData - Sale data to validate
 * @returns {Promise<Object>} Validation result
 */
export async function validateSale(saleData) {
  const errors = [];
  const warnings = [];
  
  try {
    // Basic sale validation
    const basicValidation = validateBasicSaleData(saleData);
    errors.push(...basicValidation.errors);
    warnings.push(...basicValidation.warnings);
    
    // Customer validation
    if (saleData.customer_id) {
      const customerValidation = await validateCustomer(saleData.customer_id);
      if (!customerValidation.valid) {
        errors.push(...customerValidation.errors);
      }
    }
    
    // Items validation
    if (saleData.items && saleData.items.length > 0) {
      for (let i = 0; i < saleData.items.length; i++) {
        const itemValidation = await validateSaleItem(saleData.items[i], i);
        errors.push(...itemValidation.errors);
        warnings.push(...itemValidation.warnings);
      }
    } else {
      errors.push({
        type: VALIDATION_ERRORS.MISSING_REQUIRED_FIELD,
        field: 'items',
        message: 'Sale must have at least one item'
      });
    }
    
    // Business rules validation
    const businessRulesValidation = validateBusinessRules(saleData);
    errors.push(...businessRulesValidation.errors);
    warnings.push(...businessRulesValidation.warnings);
    
    // Loyalty points validation
    if (saleData.loyalty_points_to_redeem > 0) {
      const loyaltyValidation = await validateLoyaltyRedemption(saleData);
      errors.push(...loyaltyValidation.errors);
      warnings.push(...loyaltyValidation.warnings);
    }
    
    return {
      valid: errors.length === 0,
      errors,
      warnings,
      canProceedWithWarnings: errors.length === 0 && warnings.length > 0
    };
    
  } catch (error) {
    return {
      valid: false,
      errors: [{
        type: VALIDATION_ERRORS.DATA_CONSISTENCY_ERROR,
        message: `Validation error: ${error.message}`
      }],
      warnings: []
    };
  }
}

/**
 * Validate basic sale data
 * @param {Object} saleData - Sale data
 * @returns {Object} Validation result
 */
function validateBasicSaleData(saleData) {
  const errors = [];
  const warnings = [];
  
  // Required fields
  if (!saleData.payment_method) {
    errors.push({
      type: VALIDATION_ERRORS.MISSING_REQUIRED_FIELD,
      field: 'payment_method',
      message: 'Payment method is required'
    });
  }
  
  // Amount validations
  if (saleData.total_amount < BUSINESS_RULES.MIN_SALE_AMOUNT) {
    errors.push({
      type: VALIDATION_ERRORS.INVALID_PRICE,
      field: 'total_amount',
      message: `Total amount must be at least ${BUSINESS_RULES.MIN_SALE_AMOUNT}`
    });
  }
  
  if (saleData.total_amount > BUSINESS_RULES.MAX_SALE_AMOUNT) {
    warnings.push({
      type: VALIDATION_ERRORS.BUSINESS_RULE_VIOLATION,
      field: 'total_amount',
      message: `Large sale amount: ${saleData.total_amount}. Please verify.`
    });
  }
  
  if (saleData.paid_amount < 0) {
    errors.push({
      type: VALIDATION_ERRORS.INVALID_PRICE,
      field: 'paid_amount',
      message: 'Paid amount cannot be negative'
    });
  }
  
  if (saleData.discount_amount < 0) {
    errors.push({
      type: VALIDATION_ERRORS.INVALID_PRICE,
      field: 'discount_amount',
      message: 'Discount amount cannot be negative'
    });
  }
  
  // Discount percentage check
  if (saleData.discount_amount > 0 && saleData.total_amount > 0) {
    const discountPercentage = (saleData.discount_amount / saleData.total_amount) * 100;
    if (discountPercentage > BUSINESS_RULES.MAX_DISCOUNT_PERCENTAGE) {
      warnings.push({
        type: VALIDATION_ERRORS.BUSINESS_RULE_VIOLATION,
        field: 'discount_amount',
        message: `High discount percentage: ${discountPercentage.toFixed(1)}%. Please verify.`
      });
    }
  }
  
  return { errors, warnings };
}

/**
 * Validate customer
 * @param {number} customerId - Customer ID
 * @returns {Promise<Object>} Validation result
 */
async function validateCustomer(customerId) {
  try {
    const customer = await OfflineDB.getCustomer(customerId);
    
    if (!customer) {
      return {
        valid: false,
        errors: [{
          type: VALIDATION_ERRORS.INVALID_CUSTOMER,
          field: 'customer_id',
          message: 'Customer not found'
        }]
      };
    }
    
    if (customer.status === 'inactive') {
      return {
        valid: false,
        errors: [{
          type: VALIDATION_ERRORS.INVALID_CUSTOMER,
          field: 'customer_id',
          message: 'Customer account is inactive'
        }]
      };
    }
    
    return { valid: true, errors: [] };
  } catch (error) {
    return {
      valid: false,
      errors: [{
        type: VALIDATION_ERRORS.DATA_CONSISTENCY_ERROR,
        message: `Error validating customer: ${error.message}`
      }]
    };
  }
}

/**
 * Validate sale item
 * @param {Object} item - Sale item
 * @param {number} index - Item index
 * @returns {Promise<Object>} Validation result
 */
async function validateSaleItem(item, index) {
  const errors = [];
  const warnings = [];
  const itemPrefix = `Item ${index + 1}`;
  
  try {
    // Required fields
    if (!item.medicine_id) {
      errors.push({
        type: VALIDATION_ERRORS.MISSING_REQUIRED_FIELD,
        field: `items[${index}].medicine_id`,
        message: `${itemPrefix}: Medicine is required`
      });
      return { errors, warnings };
    }
    
    if (!item.batch_number) {
      errors.push({
        type: VALIDATION_ERRORS.MISSING_REQUIRED_FIELD,
        field: `items[${index}].batch_number`,
        message: `${itemPrefix}: Batch number is required`
      });
    }
    
    if (!item.quantity || item.quantity <= 0) {
      errors.push({
        type: VALIDATION_ERRORS.INVALID_QUANTITY,
        field: `items[${index}].quantity`,
        message: `${itemPrefix}: Quantity must be greater than zero`
      });
    }
    
    if (!item.unit_price || item.unit_price < 0) {
      errors.push({
        type: VALIDATION_ERRORS.INVALID_PRICE,
        field: `items[${index}].unit_price`,
        message: `${itemPrefix}: Unit price must be greater than or equal to zero`
      });
    }
    
    // Get medicine and inventory data
    const medicine = await OfflineDB.getMedicine(item.medicine_id);
    if (!medicine) {
      errors.push({
        type: VALIDATION_ERRORS.INVALID_BATCH,
        field: `items[${index}].medicine_id`,
        message: `${itemPrefix}: Medicine not found`
      });
      return { errors, warnings };
    }
    
    // Check inventory availability
    const inventory = await OfflineDB.getInventoryByMedicineBatch(
      item.medicine_id, 
      item.batch_number, 
      item.location_id || 1
    );
    
    if (!inventory) {
      errors.push({
        type: VALIDATION_ERRORS.INVALID_BATCH,
        field: `items[${index}].batch_number`,
        message: `${itemPrefix}: Batch not found in inventory`
      });
      return { errors, warnings };
    }
    
    // Stock availability check
    if (inventory.quantity < item.quantity) {
      if (BUSINESS_RULES.NEGATIVE_STOCK_ALLOWED) {
        warnings.push({
          type: VALIDATION_ERRORS.INSUFFICIENT_STOCK,
          field: `items[${index}].quantity`,
          message: `${itemPrefix}: Insufficient stock (Available: ${inventory.quantity}, Required: ${item.quantity})`
        });
      } else {
        errors.push({
          type: VALIDATION_ERRORS.INSUFFICIENT_STOCK,
          field: `items[${index}].quantity`,
          message: `${itemPrefix}: Insufficient stock (Available: ${inventory.quantity}, Required: ${item.quantity})`
        });
      }
    }
    
    // Expiry date check
    if (inventory.expiry_date) {
      const expiryDate = new Date(inventory.expiry_date);
      const today = new Date();
      const daysUntilExpiry = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));
      
      if (daysUntilExpiry < 0) {
        errors.push({
          type: VALIDATION_ERRORS.EXPIRED_MEDICINE,
          field: `items[${index}].batch_number`,
          message: `${itemPrefix}: Medicine expired on ${expiryDate.toLocaleDateString()}`
        });
      } else if (daysUntilExpiry <= BUSINESS_RULES.EXPIRY_WARNING_DAYS) {
        warnings.push({
          type: VALIDATION_ERRORS.EXPIRED_MEDICINE,
          field: `items[${index}].batch_number`,
          message: `${itemPrefix}: Medicine expires in ${daysUntilExpiry} days`
        });
      }
    }
    
    return { errors, warnings };
  } catch (error) {
    return {
      errors: [{
        type: VALIDATION_ERRORS.DATA_CONSISTENCY_ERROR,
        message: `${itemPrefix}: Error validating item - ${error.message}`
      }],
      warnings: []
    };
  }
}

/**
 * Validate business rules
 * @param {Object} saleData - Sale data
 * @returns {Object} Validation result
 */
function validateBusinessRules(saleData) {
  const errors = [];
  const warnings = [];
  
  // Payment validation
  if (saleData.paid_amount > saleData.total_amount) {
    warnings.push({
      type: VALIDATION_ERRORS.BUSINESS_RULE_VIOLATION,
      field: 'paid_amount',
      message: 'Paid amount exceeds total amount. Change will be given.'
    });
  }
  
  // Due date validation for partial payments
  if (saleData.due_amount > 0 && !saleData.due_date) {
    warnings.push({
      type: VALIDATION_ERRORS.BUSINESS_RULE_VIOLATION,
      field: 'due_date',
      message: 'Due date recommended for partial payments'
    });
  }
  
  return { errors, warnings };
}

/**
 * Validate loyalty points redemption
 * @param {Object} saleData - Sale data
 * @returns {Promise<Object>} Validation result
 */
async function validateLoyaltyRedemption(saleData) {
  const errors = [];
  const warnings = [];
  
  try {
    if (!saleData.customer_id) {
      errors.push({
        type: VALIDATION_ERRORS.BUSINESS_RULE_VIOLATION,
        field: 'loyalty_points_to_redeem',
        message: 'Customer required for loyalty points redemption'
      });
      return { errors, warnings };
    }
    
    const customer = await OfflineDB.getCustomer(saleData.customer_id);
    if (!customer) {
      errors.push({
        type: VALIDATION_ERRORS.INVALID_CUSTOMER,
        field: 'customer_id',
        message: 'Customer not found for loyalty redemption'
      });
      return { errors, warnings };
    }
    
    if (saleData.loyalty_points_to_redeem < BUSINESS_RULES.MIN_LOYALTY_POINTS_REDEMPTION) {
      errors.push({
        type: VALIDATION_ERRORS.BUSINESS_RULE_VIOLATION,
        field: 'loyalty_points_to_redeem',
        message: `Minimum ${BUSINESS_RULES.MIN_LOYALTY_POINTS_REDEMPTION} points required for redemption`
      });
    }
    
    if (saleData.loyalty_points_to_redeem > customer.loyalty_points) {
      errors.push({
        type: VALIDATION_ERRORS.BUSINESS_RULE_VIOLATION,
        field: 'loyalty_points_to_redeem',
        message: `Insufficient loyalty points (Available: ${customer.loyalty_points}, Requested: ${saleData.loyalty_points_to_redeem})`
      });
    }
    
    // Check redemption percentage
    const maxRedemptionAmount = saleData.total_amount * (BUSINESS_RULES.MAX_LOYALTY_POINTS_REDEMPTION_PERCENTAGE / 100);
    const redemptionAmount = saleData.loyalty_points_to_redeem * 0.01; // Assuming 1 point = 0.01 currency
    
    if (redemptionAmount > maxRedemptionAmount) {
      warnings.push({
        type: VALIDATION_ERRORS.BUSINESS_RULE_VIOLATION,
        field: 'loyalty_points_to_redeem',
        message: `High loyalty redemption percentage. Maximum recommended: ${BUSINESS_RULES.MAX_LOYALTY_POINTS_REDEMPTION_PERCENTAGE}%`
      });
    }
    
    return { errors, warnings };
  } catch (error) {
    return {
      errors: [{
        type: VALIDATION_ERRORS.DATA_CONSISTENCY_ERROR,
        message: `Error validating loyalty redemption: ${error.message}`
      }],
      warnings: []
    };
  }
}

/**
 * Perform data consistency check
 * @returns {Promise<Object>} Consistency check result
 */
export async function performDataConsistencyCheck() {
  const issues = [];
  
  try {
    // Check for orphaned sale items
    const orphanedItems = await findOrphanedSaleItems();
    if (orphanedItems.length > 0) {
      issues.push({
        type: 'orphaned_sale_items',
        count: orphanedItems.length,
        message: `Found ${orphanedItems.length} sale items without corresponding sales`
      });
    }
    
    // Check for negative inventory
    const negativeInventory = await findNegativeInventory();
    if (negativeInventory.length > 0) {
      issues.push({
        type: 'negative_inventory',
        count: negativeInventory.length,
        message: `Found ${negativeInventory.length} inventory items with negative quantities`
      });
    }
    
    // Check for duplicate sync queue items
    const duplicateSyncItems = await findDuplicateSyncItems();
    if (duplicateSyncItems.length > 0) {
      issues.push({
        type: 'duplicate_sync_items',
        count: duplicateSyncItems.length,
        message: `Found ${duplicateSyncItems.length} duplicate sync queue items`
      });
    }
    
    return {
      consistent: issues.length === 0,
      issues,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      consistent: false,
      issues: [{
        type: 'consistency_check_error',
        message: `Error performing consistency check: ${error.message}`
      }],
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Find orphaned sale items
 * @returns {Promise<Array>} Array of orphaned sale items
 */
async function findOrphanedSaleItems() {
  // Implementation would check for sale items without corresponding sales
  // This is a placeholder for the actual implementation
  return [];
}

/**
 * Find negative inventory items
 * @returns {Promise<Array>} Array of negative inventory items
 */
async function findNegativeInventory() {
  try {
    const allInventory = await OfflineDB.getAllInventory();
    return allInventory.filter(item => item.quantity < 0);
  } catch (error) {
    console.error('Error finding negative inventory:', error);
    return [];
  }
}

/**
 * Find duplicate sync queue items
 * @returns {Promise<Array>} Array of duplicate sync items
 */
async function findDuplicateSyncItems() {
  // Implementation would check for duplicate sync queue entries
  // This is a placeholder for the actual implementation
  return [];
}
