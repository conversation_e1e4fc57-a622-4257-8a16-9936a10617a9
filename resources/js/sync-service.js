import * as OfflineDB from './offline-db';
import * as ConflictResolution from './conflict-resolution';

/**
 * PharmaDesk Synchronization Service
 * 
 * This module provides functionality to synchronize data between the client's
 * IndexedDB and the server when the connection is restored.
 */

// Sync status event name
const SYNC_STATUS_EVENT = 'sync-status-changed';

// Sync interval in milliseconds (5 minutes)
const SYNC_INTERVAL = 5 * 60 * 1000;

// Maximum number of retry attempts
const MAX_RETRY_ATTEMPTS = 3;

// Sync status
let isSyncing = false;
let syncTimer = null;

/**
 * Initialize the synchronization service
 */
export function initSyncService() {
  try {
    // Initialize the database
    OfflineDB.initDB().then(() => {
      console.log('Offline database initialized');
      
      // Set last sync time if not set
      OfflineDB.getSetting('lastSyncAttempt').then(lastSync => {
        if (!lastSync) {
          OfflineDB.saveSetting('lastSyncAttempt', new Date().toISOString());
        }
      });
    });
    
    // Add event listeners for online/offline status
    window.addEventListener('online', () => {
      console.log('Connection restored. Starting sync...');
      startSync();
    });
    
    window.addEventListener('offline', () => {
      console.log('Connection lost. Pausing sync...');
      pauseSync();
    });
    
    // Start periodic sync if online
    if (navigator.onLine) {
      startPeriodicSync();
    }
  } catch (error) {
    console.error('Error initializing sync service:', error);
  }
}

/**
 * Start the synchronization process
 * @returns {Promise<void>}
 */
export async function startSync() {
  if (isSyncing || !navigator.onLine) {
    return;
  }
  
  isSyncing = true;
  dispatchSyncStatusEvent(true);
  
  try {
    // First, fetch and cache any new data from the server
    await fetchAndCacheData();
    
    // Then, process the sync queue
    await processSyncQueue();
    
    // Update last sync time
    const now = new Date().toISOString();
    await OfflineDB.saveSetting('lastSyncAttempt', now);
    await OfflineDB.saveSetting('lastSuccessfulSync', now);
    localStorage.setItem('lastSync', new Date().toLocaleString());
    
    console.log('Sync completed successfully');
    dispatchSyncStatusEvent(false);
  } catch (error) {
    console.error('Sync failed:', error);
    dispatchSyncStatusEvent(false, true, error.message);
    
    // Save error for debugging
    await OfflineDB.saveSetting('lastSyncError', {
      message: error.message,
      timestamp: new Date().toISOString()
    });
  } finally {
    isSyncing = false;
  }
}

/**
 * Start periodic synchronization
 */
export function startPeriodicSync() {
  if (syncTimer) {
    clearInterval(syncTimer);
  }
  
  // Perform initial sync
  startSync();
  
  // Set up interval for periodic sync
  syncTimer = setInterval(() => {
    if (navigator.onLine && !isSyncing) {
      startSync();
    }
  }, SYNC_INTERVAL);
}

/**
 * Pause synchronization
 */
export function pauseSync() {
  if (syncTimer) {
    clearInterval(syncTimer);
    syncTimer = null;
  }
}

/**
 * Process the sync queue
 * @returns {Promise<void>}
 */
async function processSyncQueue() {
  const pendingItems = await OfflineDB.getPendingSyncItems();
  
  if (pendingItems.length === 0) {
    console.log('No pending items to sync');
    return;
  }
  
  console.log(`Processing ${pendingItems.length} pending sync items`);
  
  for (const item of pendingItems) {
    // Skip items that have exceeded retry attempts
    if (item.retry_count >= MAX_RETRY_ATTEMPTS) {
      console.warn(`Skipping item ${item.id} (${item.entity_type}) - max retries exceeded`);
      continue;
    }
    
    try {
      // Update status to processing
      await OfflineDB.updateSyncItemStatus(item.id, 'processing');
      
      // Process based on entity type and operation
      switch (item.entity_type) {
        case 'sales':
          await syncSale(item);
          break;
        case 'customers':
          await syncCustomer(item);
          break;
        case 'inventory':
          await syncInventory(item);
          break;
        default:
          console.warn(`Unknown entity type: ${item.entity_type}`);
      }
      
      // Mark as completed
      await OfflineDB.updateSyncItemStatus(item.id, 'completed');
    } catch (error) {
      console.error(`Error processing sync item ${item.id}:`, error);
      await OfflineDB.updateSyncItemStatus(item.id, 'failed', error.message);
    }
  }
}

/**
 * Sync a sale to the server with conflict resolution
 * @param {Object} item - The sync queue item
 * @returns {Promise<void>}
 */
async function syncSale(item) {
  const { data, operation } = item;

  // Get sale items if not included
  if (!data.items) {
    data.items = await OfflineDB.getSaleItemsBySale(data.id);
  }

  // For updates, check for conflicts first
  if (operation === 'update' && data.server_id) {
    try {
      const serverResponse = await fetch(`/api/offline/sales/${data.server_id}`);
      if (serverResponse.ok) {
        const serverData = await serverResponse.json();

        // Detect conflicts
        const conflict = ConflictResolution.detectConflict(data, serverData, 'sales');

        if (conflict.hasConflict) {
          console.log('Conflict detected for sale:', data.id);

          // Resolve conflict using timestamp-based strategy
          const resolution = await ConflictResolution.resolveConflict(
            conflict,
            ConflictResolution.RESOLUTION_STRATEGIES.TIMESTAMP_BASED
          );

          if (resolution.success) {
            // Save the resolved data
            await ConflictResolution.saveConflictResolution(conflict, resolution);
            console.log('Sale conflict resolved:', resolution.message);

            // Update the data to sync with resolved version
            Object.assign(data, resolution.resolvedData);
          } else {
            throw new Error(`Conflict resolution failed: ${resolution.error}`);
          }
        }
      }
    } catch (error) {
      console.warn('Error checking for conflicts:', error.message);
      // Continue with sync even if conflict check fails
    }
  }

  // Prepare the request
  const endpoint = operation === 'create' ? '/api/offline/sales' : `/api/offline/sales/${data.server_id || data.id}`;
  const method = operation === 'create' ? 'POST' : operation === 'update' ? 'PUT' : 'DELETE';

  // Get CSRF token safely
  let csrfToken = '';
  try {
    const metaElement = document.querySelector('meta[name="csrf-token"]');
    if (metaElement) {
      csrfToken = metaElement.getAttribute('content') || '';
    }
  } catch (e) {
    console.warn('Could not get CSRF token:', e);
  }

  // Send to server
  const response = await fetch(endpoint, {
    method,
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-TOKEN': csrfToken
    },
    body: JSON.stringify(data)
  });

  if (!response.ok) {
    const errorText = await response.text();

    // Check if it's a conflict error (409)
    if (response.status === 409) {
      console.log('Server conflict detected for sale:', data.id);

      try {
        const conflictData = JSON.parse(errorText);
        const conflict = ConflictResolution.detectConflict(data, conflictData.serverData, 'sales');

        if (conflict.hasConflict) {
          // Resolve conflict and retry
          const resolution = await ConflictResolution.resolveConflict(
            conflict,
            ConflictResolution.RESOLUTION_STRATEGIES.TIMESTAMP_BASED
          );

          if (resolution.success) {
            await ConflictResolution.saveConflictResolution(conflict, resolution);

            // Retry the sync with resolved data
            return syncSale({ ...item, data: resolution.resolvedData });
          }
        }
      } catch (parseError) {
        console.error('Error parsing conflict response:', parseError);
      }
    }

    throw new Error(`Server responded with ${response.status}: ${errorText}`);
  }

  // If successful, update the local record
  const result = await response.json();

  if (operation === 'create' || operation === 'update') {
    // Update the local sale with server ID and data
    const db = await OfflineDB.initDB();
    const tx = db.transaction(['sales', 'sale_items'], 'readwrite');

    // Update sale
    const sale = await tx.objectStore('sales').get(data.id);
    if (sale) {
      sale.sync_status = 'synced';
      sale.server_id = result.id || sale.server_id; // Store server ID
      sale.updated_at = result.updated_at || sale.updated_at;
      await tx.objectStore('sales').put(sale);
    }

    // Update sale items
    const saleItems = await OfflineDB.getSaleItemsBySale(data.id);
    for (const item of saleItems) {
      item.sync_status = 'synced';
      item.server_id = result.items?.find(i => i.medicine_id === item.medicine_id)?.id || item.server_id;
      await tx.objectStore('sale_items').put(item);
    }

    await tx.done;
  }
}

/**
 * Sync a customer to the server
 * @param {Object} item - The sync queue item
 * @returns {Promise<void>}
 */
async function syncCustomer(item) {
  const { data, operation } = item;
  
  // Prepare the request
  const endpoint = operation === 'create' ? '/api/offline/customers' : `/api/offline/customers/${data.id}`;
  const method = operation === 'create' ? 'POST' : operation === 'update' ? 'PUT' : 'DELETE';
  
  // Get CSRF token safely
  let csrfToken = '';
  try {
    const metaElement = document.querySelector('meta[name="csrf-token"]');
    if (metaElement) {
      csrfToken = metaElement.getAttribute('content') || '';
    }
  } catch (e) {
    console.warn('Could not get CSRF token:', e);
  }
  
  // Send to server
  const response = await fetch(endpoint, {
    method,
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-TOKEN': csrfToken
    },
    body: JSON.stringify(data)
  });
  
  if (!response.ok) {
    throw new Error(`Server responded with ${response.status}: ${await response.text()}`);
  }
  
  // If successful, update the local record
  const result = await response.json();
  
  if (operation === 'create' || operation === 'update') {
    const db = await OfflineDB.initDB();
    const customer = await db.get('customers', data.id);
    if (customer) {
      customer.sync_status = 'synced';
      customer.server_id = result.id; // Store server ID
      await db.put('customers', customer);
    }
  }
}

/**
 * Sync inventory to the server
 * @param {Object} item - The sync queue item
 * @returns {Promise<void>}
 */
async function syncInventory(item) {
  const { data, operation } = item;
  
  // Prepare the request
  const endpoint = operation === 'create' ? '/api/offline/inventory' : `/api/offline/inventory/${data.id}`;
  const method = operation === 'create' ? 'POST' : operation === 'update' ? 'PUT' : 'DELETE';
  
  // Get CSRF token safely
  let csrfToken = '';
  try {
    const metaElement = document.querySelector('meta[name="csrf-token"]');
    if (metaElement) {
      csrfToken = metaElement.getAttribute('content') || '';
    }
  } catch (e) {
    console.warn('Could not get CSRF token:', e);
  }
  
  // Send to server
  const response = await fetch(endpoint, {
    method,
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-TOKEN': csrfToken
    },
    body: JSON.stringify(data)
  });
  
  if (!response.ok) {
    throw new Error(`Server responded with ${response.status}: ${await response.text()}`);
  }
  
  // If successful, update the local record
  const result = await response.json();
  
  if (operation === 'create' || operation === 'update') {
    const db = await OfflineDB.initDB();
    const inventory = await db.get('inventory', data.id);
    if (inventory) {
      inventory.sync_status = 'synced';
      inventory.server_id = result.id; // Store server ID
      await db.put('inventory', inventory);
    }
  }
}

/**
 * Fetch and cache data from the server
 * @returns {Promise<void>}
 */
async function fetchAndCacheData() {
  try {
    console.log('Starting comprehensive data sync...');

    // Get last sync timestamp for incremental sync
    const lastSync = await OfflineDB.getSetting('lastSuccessfulSync');
    const sinceParam = lastSync ? `?since=${encodeURIComponent(lastSync)}` : '';

    // Dispatch sync started event
    dispatchSyncEvent('sync-started', {
      type: 'comprehensive',
      timestamp: new Date().toISOString()
    });

    // Fetch reference data first (categories, manufacturers, etc.)
    await fetchReferenceData(sinceParam);

    // Fetch medicines with comprehensive data for offline sales
    await fetchComprehensiveMedicinesData(sinceParam);

    // Fetch inventory with pagination
    await fetchInventoryData(sinceParam);

    // Fetch customers with pagination
    await fetchCustomersData(sinceParam);

    console.log('Comprehensive data caching completed');
  } catch (error) {
    console.warn('Error in fetchAndCacheData:', error.message);
    // Don't throw the error to prevent sync failure
  }
}

/**
 * Fetch reference data (categories, manufacturers, etc.)
 * @param {string} sinceParam - Query parameter for incremental sync
 * @returns {Promise<void>}
 */
async function fetchReferenceData(sinceParam) {
  const referenceEndpoints = [
    { endpoint: 'categories', cacheFunction: 'cacheCategories' },
    { endpoint: 'manufacturers', cacheFunction: 'cacheManufacturers' },
    { endpoint: 'unit-types', cacheFunction: 'cacheUnitTypes' },
    { endpoint: 'locations', cacheFunction: 'cacheLocations' },
    { endpoint: 'warehouses', cacheFunction: 'cacheWarehouses' },
    { endpoint: 'suppliers', cacheFunction: 'cacheSuppliers' },
    { endpoint: 'unit-conversions', cacheFunction: 'cacheUnitConversions' },
    { endpoint: 'users', cacheFunction: 'cacheUsers' }
  ];

  for (const { endpoint, cacheFunction } of referenceEndpoints) {
    try {
      const response = await fetch(`/api/offline/${endpoint}${sinceParam}`);
      if (response.ok) {
        const result = await response.json();
        const data = result.data || result;

        if (data && data.length > 0) {
          await OfflineDB[cacheFunction](data);
          console.log(`Cached ${data.length} ${endpoint}`);
        }
      } else if (response.status === 404) {
        console.log(`${endpoint} API endpoint not found, skipping cache`);
      }
    } catch (error) {
      console.log(`Error fetching ${endpoint}, skipping cache:`, error.message);
    }
  }
}

/**
 * Fetch medicines data with pagination
 * @param {string} sinceParam - Query parameter for incremental sync
 * @returns {Promise<void>}
 */
async function fetchComprehensiveMedicinesData(sinceParam) {
  let offset = 0;
  const limit = 50; // Smaller batches for comprehensive data
  let hasMore = true;
  let totalProcessed = 0;

  console.log('Fetching comprehensive medicines data for offline sales...');

  // Dispatch initial progress
  dispatchSyncEvent('sync-progress', {
    entity: 'medicines',
    stage: 'starting',
    processed: 0,
    message: 'Initializing medicine sync...'
  });

  while (hasMore) {
    try {
      const response = await fetch(`/api/offline/medicines${sinceParam}${sinceParam ? '&' : '?'}limit=${limit}&offset=${offset}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch medicines: ${response.status}`);
      }

      const data = await response.json();

      if (data.data && data.data.length > 0) {
        // Process medicines in smaller chunks to avoid blocking UI
        await processMedicinesInChunks(data.data);
        totalProcessed += data.data.length;

        console.log(`Processed ${data.data.length} medicines (total: ${totalProcessed})`);
      }

      hasMore = data.meta?.has_more || false;
      offset += limit;

      // Dispatch progress event with detailed information
      dispatchSyncEvent('sync-progress', {
        entity: 'medicines',
        stage: 'processing',
        processed: totalProcessed,
        hasMore,
        message: `Synced ${totalProcessed} medicines...`
      });

      // Small delay to prevent overwhelming the browser
      await new Promise(resolve => setTimeout(resolve, 10));

    } catch (error) {
      console.error('Error fetching comprehensive medicines data:', error);
      dispatchSyncEvent('sync-error', {
        entity: 'medicines',
        error: error.message,
        offset
      });
      throw error;
    }
  }

  console.log(`Completed medicines sync: ${totalProcessed} medicines processed`);

  // Dispatch completion event
  dispatchSyncEvent('sync-progress', {
    entity: 'medicines',
    stage: 'completed',
    processed: totalProcessed,
    message: `Medicine sync completed: ${totalProcessed} medicines`
  });
}

/**
 * Process medicines in small chunks to avoid blocking the UI
 * @param {Array} medicines - Array of medicine objects
 * @returns {Promise<void>}
 */
async function processMedicinesInChunks(medicines) {
  const chunkSize = 10;

  for (let i = 0; i < medicines.length; i += chunkSize) {
    const chunk = medicines.slice(i, i + chunkSize);

    // Process each medicine with full validation and enhancement
    const processedChunk = await Promise.all(chunk.map(async (medicine) => {
      return await enhanceMedicineForOfflineUse(medicine);
    }));

    // Cache the processed chunk
    await OfflineDB.cacheMedicines(processedChunk);

    // Cache related inventory data
    for (const medicine of processedChunk) {
      if (medicine.inventories && medicine.inventories.length > 0) {
        await OfflineDB.cacheInventory(medicine.inventories);
      }
    }

    // Small delay between chunks
    await new Promise(resolve => setTimeout(resolve, 5));
  }
}

/**
 * Enhance medicine data for offline sales operations
 * @param {Object} medicine - Medicine object from API
 * @returns {Promise<Object>} Enhanced medicine object
 */
async function enhanceMedicineForOfflineUse(medicine) {
  // Calculate total stock from inventories
  const totalStock = medicine.inventories ?
    medicine.inventories.reduce((sum, inv) => sum + (inv.quantity || 0), 0) : 0;

  // Calculate available stock (non-expired)
  const now = new Date();
  const availableStock = medicine.inventories ?
    medicine.inventories
      .filter(inv => !inv.expiry_date || new Date(inv.expiry_date) > now)
      .reduce((sum, inv) => sum + (inv.quantity || 0), 0) : 0;

  // Determine stock status for offline sales validation
  const stockStatus = getStockStatus(totalStock, availableStock, medicine.minimum_stock);

  // Add search-friendly fields for offline search
  const searchableText = [
    medicine.name,
    medicine.generic_name,
    medicine.manufacturer?.name,
    medicine.category?.name
  ].filter(Boolean).join(' ').toLowerCase();

  return {
    ...medicine,
    // Enhanced fields for offline operations
    total_stock: totalStock,
    available_stock: availableStock,
    stock_status: stockStatus,
    searchable_text: searchableText,
    last_synced: new Date().toISOString(),
    sync_status: 'synced',

    // Ensure pricing data is available for sales
    pricing: {
      retail_price_unit: medicine.retail_price_unit || 0,
      retail_price_strip: medicine.retail_price_strip || 0,
      retail_price_box: medicine.retail_price_box || 0,
      supplier_price_unit: medicine.supplier_price_unit || 0,
      supplier_price_strip: medicine.supplier_price_strip || 0,
      supplier_price_box: medicine.supplier_price_box || 0,
    },

    // Sales validation flags
    can_sell: availableStock > 0 && medicine.status === 'active',
    requires_prescription: medicine.prescription_required || false,
    is_controlled: medicine.controlled_substance || false,
  };
}

/**
 * Determine stock status for offline validation
 * @param {number} totalStock - Total stock quantity
 * @param {number} availableStock - Available (non-expired) stock
 * @param {number} minimumStock - Minimum stock threshold
 * @returns {string} Stock status
 */
function getStockStatus(totalStock, availableStock, minimumStock = 0) {
  if (availableStock === 0) return 'out_of_stock';
  if (availableStock <= minimumStock) return 'low_stock';
  if (totalStock > availableStock) return 'has_expired_stock';
  return 'in_stock';
}

/**
 * Fetch inventory data with pagination
 * @param {string} sinceParam - Query parameter for incremental sync
 * @returns {Promise<void>}
 */
async function fetchInventoryData(sinceParam) {
  try {
    let offset = 0;
    const limit = 200;
    let hasMore = true;
    let totalCached = 0;

    while (hasMore) {
      const url = `/api/offline/inventory${sinceParam}${sinceParam ? '&' : '?'}limit=${limit}&offset=${offset}`;
      const response = await fetch(url);

      if (response.ok) {
        const result = await response.json();
        const inventory = result.data || result;

        if (inventory && inventory.length > 0) {
          await OfflineDB.cacheInventory(inventory);
          totalCached += inventory.length;
          hasMore = result.meta?.has_more || false;
          offset += limit;
        } else {
          hasMore = false;
        }
      } else {
        console.log('Inventory API error:', response.status);
        hasMore = false;
      }
    }

    console.log(`Cached ${totalCached} inventory items`);
  } catch (error) {
    console.log('Error fetching inventory:', error.message);
  }
}

/**
 * Fetch customers data with pagination
 * @param {string} sinceParam - Query parameter for incremental sync
 * @returns {Promise<void>}
 */
async function fetchCustomersData(sinceParam) {
  try {
    let offset = 0;
    const limit = 100;
    let hasMore = true;
    let totalCached = 0;

    while (hasMore) {
      const url = `/api/offline/customers${sinceParam}${sinceParam ? '&' : '?'}limit=${limit}&offset=${offset}&recent=true`;
      const response = await fetch(url);

      if (response.ok) {
        const result = await response.json();
        const customers = result.data || result;

        if (customers && customers.length > 0) {
          const db = await OfflineDB.initDB();
          const tx = db.transaction('customers', 'readwrite');

          for (const customer of customers) {
            customer.sync_status = 'synced';
            await tx.store.put(customer);
          }

          await tx.done;
          totalCached += customers.length;
          hasMore = result.meta?.has_more || false;
          offset += limit;
        } else {
          hasMore = false;
        }
      } else {
        console.log('Customers API error:', response.status);
        hasMore = false;
      }
    }

    console.log(`Cached ${totalCached} customers`);
  } catch (error) {
    console.log('Error fetching customers:', error.message);
  }
}

/**
 * Dispatch sync status event
 * @param {boolean} syncing - Whether sync is in progress
 * @param {boolean} error - Whether there was an error
 * @param {string} errorMessage - Error message
 */
function dispatchSyncStatusEvent(syncing, error = false, errorMessage = null) {
  try {
    // Only dispatch the event without any DOM manipulation
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      // Create a custom event with the sync status details
      const syncEvent = new CustomEvent(SYNC_STATUS_EVENT, {
        detail: {
          syncing,
          error,
          errorMessage,
          timestamp: new Date().toISOString()
        }
      });
      
      // Dispatch the event
      window.dispatchEvent(syncEvent);
      
      // Log status for debugging
      if (syncing) {
        console.log('Sync status: In progress');
      } else if (error) {
        console.log('Sync status: Error -', errorMessage || 'Unknown error');
      } else {
        console.log('Sync status: Completed');
      }
    }
  } catch (e) {
    console.warn('Error in dispatchSyncStatusEvent:', e);
  }
}

/**
 * Dispatch sync event for progress tracking and debugging
 * @param {string} eventType - Type of sync event (sync-started, sync-progress, sync-error, etc.)
 * @param {Object} data - Event data
 */
function dispatchSyncEvent(eventType, data = {}) {
  try {
    // Only dispatch the event without any DOM manipulation
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      // Create a custom event with the sync details
      const syncEvent = new CustomEvent(`sync-${eventType}`, {
        detail: {
          ...data,
          timestamp: new Date().toISOString()
        }
      });

      // Dispatch the event
      window.dispatchEvent(syncEvent);

      // Log event for debugging
      console.log(`Sync event: ${eventType}`, data);
    }
  } catch (e) {
    console.warn('Error in dispatchSyncEvent:', e);
  }
}

/**
 * Force a sync
 * @returns {Promise<void>}
 */
export async function forceSync() {
  if (navigator.onLine) {
    return startSync();
  } else {
    throw new Error('Cannot sync while offline');
  }
}

/**
 * Get sync status
 * @returns {Promise<Object>} Sync status object
 */
export async function getSyncStatus() {
  const lastAttempt = await OfflineDB.getSetting('lastSyncAttempt');
  const lastSuccess = await OfflineDB.getSetting('lastSuccessfulSync');
  const lastError = await OfflineDB.getSetting('lastSyncError');
  const pendingItems = await OfflineDB.getPendingSyncItems();
  
  return {
    isSyncing,
    lastSyncAttempt: lastAttempt,
    lastSuccessfulSync: lastSuccess,
    lastSyncError: lastError,
    pendingItemsCount: pendingItems.length
  };
} 