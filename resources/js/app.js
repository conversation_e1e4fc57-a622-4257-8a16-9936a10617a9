// ClassList Patch - Apply early to prevent errors
(function() {
  try {
    // Only apply if Element exists
    if (typeof Element !== 'undefined') {
      const originalClassListDescriptor = Object.getOwnPropertyDescriptor(Element.prototype, 'classList');
      if (originalClassListDescriptor) {
        Object.defineProperty(Element.prototype, 'classList', {
          get: function() {
            try {
              return originalClassListDescriptor.get.call(this);
            } catch (e) {
              // Return a dummy DOMTokenList-like object
              return {
                add: function() {},
                remove: function() {},
                toggle: function() {},
                contains: function() { return false; }
              };
            }
          }
        });
        console.log('ClassList patch applied globally');
      }
    }
  } catch (e) {
    console.warn('Error applying classList patch:', e);
  }
})();

import './bootstrap';
// Import Alpine plugins only - Livewire already includes Alpine
import focus from '@alpinejs/focus';
import intersect from '@alpinejs/intersect';
import locationModal from './components/location-modal';
import dateRangeDropdown from './components/date-range-dropdown';
import { initSyncUIHandler } from './sync-ui-handler';

// Create fallback functions in case offline modules fail to load
window.getSyncStatus = window.getSyncStatus || (() => Promise.resolve({ pendingItemsCount: 0 }));
window.forceSync = window.forceSync || (() => Promise.resolve());

// Wait for Alpine to be loaded by Livewire before registering plugins
document.addEventListener('alpine:init', () => {
    // Register Alpine plugins
    Alpine.plugin(focus);
    Alpine.plugin(intersect);

    // Register Alpine components
    Alpine.data('locationModal', locationModal);
    Alpine.data('dateRangeDropdown', dateRangeDropdown);
    
    // Register offline data handlers with fallbacks
    Alpine.data('offlineManager', () => ({
        isSyncing: false,
        pendingItems: 0,
        lastSync: 'Never',
        
        init() {
            // Initialize
            this.updateSyncInfo();
            
            // Listen for sync status changes
            window.addEventListener('sync-status-changed', (event) => {
                this.isSyncing = event.detail.syncing;
                this.updateSyncInfo();
                
                // Show notification if there was an error
                if (event.detail.error && typeof Livewire !== 'undefined') {
                    Livewire.dispatch('notify', { 
                        type: 'error',
                        message: `Sync failed: ${event.detail.errorMessage || 'Unknown error'}`
                    });
                }
            });
            
            // Listen for service worker messages
            if (navigator.serviceWorker) {
                navigator.serviceWorker.addEventListener('message', (event) => {
                    if (event.data.type === 'sync-completed') {
                        this.isSyncing = false;
                        this.updateSyncInfo();
                        
                        if (typeof Livewire !== 'undefined') {
                            if (event.data.data.success) {
                                Livewire.dispatch('notify', { 
                                    type: 'success',
                                    message: `Sync completed: ${event.data.data.successCount} items synchronized`
                                });
                            } else {
                                Livewire.dispatch('notify', { 
                                    type: 'warning',
                                    message: `Sync completed with issues: ${event.data.data.failureCount} failures`
                                });
                            }
                        }
                    } else if (event.data.type === 'sync-started') {
                        this.isSyncing = true;
                    } else if (event.data.type === 'sync-error') {
                        this.isSyncing = false;
                        if (typeof Livewire !== 'undefined') {
                            Livewire.dispatch('notify', { 
                                type: 'error',
                                message: `Sync error: ${event.data.data.error || 'Unknown error'}`
                            });
                        }
                    }
                });
            }
        },
        
        async updateSyncInfo() {
            try {
                if (typeof window.getSyncStatus === 'function') {
                    const status = await window.getSyncStatus();
                    this.pendingItems = status.pendingItemsCount;
                    this.lastSync = localStorage.getItem('lastSync') || 'Never';
                }
            } catch (error) {
                console.error('Error getting sync status:', error);
            }
        },
        
        async triggerSync() {
            if (this.isSyncing) return;
            
            if (!navigator.onLine) {
                if (typeof Livewire !== 'undefined') {
                    Livewire.dispatch('notify', { 
                        type: 'warning',
                        message: 'Cannot sync while offline'
                    });
                }
                return;
            }
            
            try {
                this.isSyncing = true;
                if (typeof window.forceSync === 'function') {
                    await window.forceSync();
                    if (typeof Livewire !== 'undefined') {
                        Livewire.dispatch('notify', { 
                            type: 'success',
                            message: 'Sync completed successfully'
                        });
                    }
                }
            } catch (error) {
                if (typeof Livewire !== 'undefined') {
                    Livewire.dispatch('notify', { 
                        type: 'error',
                        message: `Sync failed: ${error.message}`
                    });
                }
            } finally {
                this.isSyncing = false;
                this.updateSyncInfo();
            }
        }
    }));
});

// Dynamically import idb to ensure it's available
let idbModule;
try {
  idbModule = import('idb').then(module => {
    window.idb = module;
    return module;
  }).catch(err => {
    console.error('Failed to load idb module:', err);
    // Try loading from CDN as fallback
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/idb@7.1.1/build/umd.js';
    script.onload = () => console.log('idb loaded from CDN');
    script.onerror = (e) => console.error('Failed to load idb from CDN:', e);
    document.head.appendChild(script);
    return new Promise(resolve => {
      script.onload = () => resolve(window.idb);
    });
  });
} catch (e) {
  console.error('Error setting up idb:', e);
}

// Import offline modules after idb setup
const importOfflineModules = async () => {
  try {
    // Wait for idb to be available if possible
    if (idbModule) await idbModule;
    
    // Now import the modules that depend on idb
    // Use static imports instead of dynamic imports to avoid path issues
    const OfflineDB = await import('./offline-db.js');
    const SyncService = await import('./sync-service.js');
    const MedicineSyncScheduler = await import('./medicine-sync-scheduler.js');
    
    // Initialize offline database
    OfflineDB.initDB().catch(error => {
      console.error('Failed to initialize offline database:', error);
    });
    
    // Export for global use
    window.OfflineDB = OfflineDB;
    window.initSyncService = SyncService.initSyncService;
    window.forceSync = SyncService.forceSync;
    window.getSyncStatus = SyncService.getSyncStatus;
    window.MedicineSyncScheduler = MedicineSyncScheduler;

    // Initialize sync service
    SyncService.initSyncService();

    // Initialize medicine sync scheduler for comprehensive offline sales support
    MedicineSyncScheduler.initMedicineSyncScheduler();

    // Initialize sync UI handler
    initSyncUIHandler();
    
    console.log('Offline modules loaded successfully');
  } catch (error) {
    console.error('Error importing offline modules:', error);
  }
};

// Start importing offline modules
importOfflineModules();

// Register Service Worker
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/service-worker.js', { scope: '/' })
            .then(registration => {
                console.log('ServiceWorker registration successful with scope:', registration.scope);
                
                // Check for updates
                registration.addEventListener('updatefound', () => {
                    console.log('New service worker being installed');
                    const newWorker = registration.installing;
                    
                    newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            console.log('New service worker installed and will be activated on reload');
                            // Optionally notify the user about the update
                            if (typeof Livewire !== 'undefined') {
                                Livewire.dispatch('notify', { 
                                    type: 'info',
                                    message: 'App updated. Refresh to apply changes.'
                                });
                            }
                        }
                    });
                });
                
                // Request notification permission with a better UX approach
                if ('Notification' in window) {
                    // Only request permission if not already denied
                    if (Notification.permission !== 'denied' && Notification.permission !== 'granted') {
                        // Store whether we've asked before
                        const hasAskedBefore = localStorage.getItem('notificationPermissionAsked');
                        
                        if (!hasAskedBefore) {
                            // Mark that we've asked
                            localStorage.setItem('notificationPermissionAsked', 'true');
                            
                            // Ask for permission
                            Notification.requestPermission().then(permission => {
                                if (permission === 'granted') {
                                    console.log('Notification permission granted');
                                    // Could show a welcome notification here
                                }
                            });
                        }
                    }
                }
                
                // Update last sync time
                const updateLastSync = () => {
                    const now = new Date().toLocaleString();
                    localStorage.setItem('lastSync', now);
                };
                
                // Listen for sync events
                window.addEventListener('online', () => {
                    registration.sync.register('sync-data')
                        .catch(error => {
                            console.error('Failed to register background sync:', error);
                            // Fallback to manual sync if background sync fails
                            if (typeof window.forceSync === 'function') {
                                window.forceSync();
                            }
                        });
                    updateLastSync();
                });
            })
            .catch(error => {
                console.error('ServiceWorker registration failed:', error);
            });
    });
}

// Add offline/online status handlers
window.addEventListener('online', () => {
    document.body.classList.remove('offline');
    // Dispatch custom event
    window.dispatchEvent(new CustomEvent('connection-changed', { detail: { online: true } }));
    console.log('Application is online');
});

window.addEventListener('offline', () => {
    document.body.classList.add('offline');
    // Dispatch custom event
    window.dispatchEvent(new CustomEvent('connection-changed', { detail: { online: false } }));
    console.log('Application is offline');
    
    // Show notification if Livewire is available
    if (typeof Livewire !== 'undefined') {
        Livewire.dispatch('notify', { 
            type: 'warning',
            message: 'You are offline. Some features may be limited.'
        });
    }
    
    // Store the current URL before going offline
    sessionStorage.setItem('lastOnlineUrl', window.location.href);
});

// Initial offline status check
if (!navigator.onLine) {
    document.body.classList.add('offline');
    console.log('Application started in offline mode');
}

// Add a fallback for offline navigation when service worker isn't available
document.addEventListener('DOMContentLoaded', () => {
    // Only add this if service worker isn't controlling the page
    if (!navigator.serviceWorker || !navigator.serviceWorker.controller) {
        console.log('Adding fallback offline navigation handler');
        
        // Intercept clicks on links
        document.addEventListener('click', (event) => {
            // Check if we're offline
            if (!navigator.onLine) {
                // Find if the click was on a link
                let link = event.target;
                while (link && link.tagName !== 'A') {
                    link = link.parentElement;
                }
                
                if (link && link.href && link.href.startsWith(window.location.origin)) {
                    // It's an internal link
                    event.preventDefault();
                    
                    // Check if we have this page cached in localStorage
                    const cachedPage = localStorage.getItem(`page_cache_${link.pathname}`);
                    if (cachedPage) {
                        // We have a cached version, use it
                        console.log('Using cached version of', link.pathname);
                        document.documentElement.innerHTML = cachedPage;
                        document.body.classList.add('offline');
                        window.history.pushState(null, '', link.href);
                    } else {
                        // No cached version, redirect to offline page
                        console.log('No cached version of', link.pathname, 'redirecting to offline page');
                        window.location.href = '/offline';
                    }
                }
            }
        });
        
        // Cache current page for offline use
        if (document.location.pathname !== '/offline') {
            try {
                localStorage.setItem(`page_cache_${document.location.pathname}`, document.documentElement.innerHTML);
                console.log('Cached current page for offline use:', document.location.pathname);
            } catch (error) {
                console.warn('Failed to cache current page:', error);
            }
        }
    }
    
    // Add a global error handler for fetch operations
    const originalFetch = window.fetch;
    window.fetch = function(input, init) {
        return originalFetch(input, init)
            .catch(error => {
                if (!navigator.onLine) {
                    console.log('Fetch failed while offline:', input);
                    // For API requests, return a custom offline response
                    const url = typeof input === 'string' ? new URL(input, window.location.href) : input.url;
                    if (url.pathname.startsWith('/api/')) {
                        return new Response(
                            JSON.stringify({ 
                                error: 'offline',
                                message: 'You are currently offline'
                            }),
                            { 
                                status: 503, 
                                headers: { 'Content-Type': 'application/json' } 
                            }
                        );
                    }
                }
                throw error;
            });
    };
});
