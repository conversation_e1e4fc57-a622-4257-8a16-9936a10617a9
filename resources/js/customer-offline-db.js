/**
 * Customer Offline Database Manager
 * Handles IndexedDB operations for customer data storage and retrieval
 */

class CustomerOfflineDB {
    constructor() {
        this.dbName = 'PharmaCustomerDB';
        this.dbVersion = 1;
        this.db = null;
        this.stores = {
            customers: 'customers',
            loyaltyTransactions: 'loyalty_transactions',
            customerSales: 'customer_sales',
            syncMetadata: 'sync_metadata'
        };
    }

    /**
     * Initialize the database
     */
    async init() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = () => {
                console.error('Failed to open customer database:', request.error);
                reject(request.error);
            };

            request.onsuccess = () => {
                this.db = request.result;
                console.log('Customer database opened successfully');
                resolve(this.db);
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                this.createStores(db);
            };
        });
    }

    /**
     * Create object stores
     */
    createStores(db) {
        // Customers store
        if (!db.objectStoreNames.contains(this.stores.customers)) {
            const customerStore = db.createObjectStore(this.stores.customers, { keyPath: 'id' });
            customerStore.createIndex('name', 'name', { unique: false });
            customerStore.createIndex('email', 'email', { unique: false });
            customerStore.createIndex('phone', 'phone', { unique: false });
            customerStore.createIndex('status', 'status', { unique: false });
            customerStore.createIndex('loyalty_tier', 'loyalty_tier', { unique: false });
            customerStore.createIndex('updated_at', 'updated_at', { unique: false });
        }

        // Loyalty transactions store
        if (!db.objectStoreNames.contains(this.stores.loyaltyTransactions)) {
            const loyaltyStore = db.createObjectStore(this.stores.loyaltyTransactions, { keyPath: 'id' });
            loyaltyStore.createIndex('customer_id', 'customer_id', { unique: false });
            loyaltyStore.createIndex('type', 'type', { unique: false });
            loyaltyStore.createIndex('created_at', 'created_at', { unique: false });
        }

        // Customer sales store (for recent sales history)
        if (!db.objectStoreNames.contains(this.stores.customerSales)) {
            const salesStore = db.createObjectStore(this.stores.customerSales, { keyPath: 'id' });
            salesStore.createIndex('customer_id', 'customer_id', { unique: false });
            salesStore.createIndex('created_at', 'created_at', { unique: false });
            salesStore.createIndex('payment_status', 'payment_status', { unique: false });
        }

        // Sync metadata store
        if (!db.objectStoreNames.contains(this.stores.syncMetadata)) {
            const metaStore = db.createObjectStore(this.stores.syncMetadata, { keyPath: 'key' });
        }

        console.log('Customer database stores created successfully');
    }

    /**
     * Store customers in bulk
     */
    async storeCustomers(customers) {
        if (!this.db) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.stores.customers], 'readwrite');
            const store = transaction.objectStore(this.stores.customers);
            let processed = 0;

            transaction.oncomplete = () => {
                console.log(`Stored ${processed} customers successfully`);
                resolve(processed);
            };

            transaction.onerror = () => {
                console.error('Failed to store customers:', transaction.error);
                reject(transaction.error);
            };

            customers.forEach(customer => {
                // Ensure customer has computed fields for offline use
                const customerData = {
                    ...customer,
                    loyalty_tier: customer.loyalty_tier || (customer.loyalty_points >= 1000 ? 'Gold' : 'Regular'),
                    points_to_next_tier: customer.points_to_next_tier || (customer.loyalty_points >= 1000 ? 0 : (1000 - customer.loyalty_points)),
                    total_points_earned: customer.total_points_earned || 0,
                    total_points_redeemed: customer.total_points_redeemed || 0,
                    synced_at: new Date().toISOString()
                };

                const request = store.put(customerData);
                request.onsuccess = () => processed++;
            });
        });
    }

    /**
     * Store loyalty transactions
     */
    async storeLoyaltyTransactions(transactions) {
        if (!this.db) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.stores.loyaltyTransactions], 'readwrite');
            const store = transaction.objectStore(this.stores.loyaltyTransactions);
            let processed = 0;

            transaction.oncomplete = () => {
                console.log(`Stored ${processed} loyalty transactions successfully`);
                resolve(processed);
            };

            transaction.onerror = () => {
                console.error('Failed to store loyalty transactions:', transaction.error);
                reject(transaction.error);
            };

            transactions.forEach(loyaltyTransaction => {
                const request = store.put({
                    ...loyaltyTransaction,
                    synced_at: new Date().toISOString()
                });
                request.onsuccess = () => processed++;
            });
        });
    }

    /**
     * Get all customers with optional filtering
     */
    async getCustomers(options = {}) {
        if (!this.db) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.stores.customers], 'readonly');
            const store = transaction.objectStore(this.stores.customers);
            const customers = [];

            let cursor;
            if (options.status) {
                const index = store.index('status');
                cursor = index.openCursor(IDBKeyRange.only(options.status));
            } else {
                cursor = store.openCursor();
            }

            cursor.onsuccess = (event) => {
                const result = event.target.result;
                if (result) {
                    const customer = result.value;
                    
                    // Apply search filter if provided
                    if (options.search) {
                        const searchTerm = options.search.toLowerCase();
                        const matchesSearch = 
                            customer.name.toLowerCase().includes(searchTerm) ||
                            (customer.email && customer.email.toLowerCase().includes(searchTerm)) ||
                            (customer.phone && customer.phone.includes(searchTerm));
                        
                        if (matchesSearch) {
                            customers.push(customer);
                        }
                    } else {
                        customers.push(customer);
                    }
                    
                    result.continue();
                } else {
                    // Apply sorting
                    if (options.sortBy === 'name') {
                        customers.sort((a, b) => a.name.localeCompare(b.name));
                    } else if (options.sortBy === 'updated_at') {
                        customers.sort((a, b) => new Date(b.updated_at) - new Date(a.updated_at));
                    }

                    // Apply pagination
                    const start = options.offset || 0;
                    const end = start + (options.limit || customers.length);
                    const paginatedCustomers = customers.slice(start, end);

                    resolve({
                        data: paginatedCustomers,
                        total: customers.length,
                        hasMore: end < customers.length
                    });
                }
            };

            cursor.onerror = () => {
                console.error('Failed to get customers:', cursor.error);
                reject(cursor.error);
            };
        });
    }

    /**
     * Get customer by ID with related data
     */
    async getCustomerById(customerId) {
        if (!this.db) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([
                this.stores.customers, 
                this.stores.loyaltyTransactions,
                this.stores.customerSales
            ], 'readonly');

            const customerStore = transaction.objectStore(this.stores.customers);
            const loyaltyStore = transaction.objectStore(this.stores.loyaltyTransactions);
            const salesStore = transaction.objectStore(this.stores.customerSales);

            const customerRequest = customerStore.get(customerId);

            customerRequest.onsuccess = () => {
                const customer = customerRequest.result;
                if (!customer) {
                    resolve(null);
                    return;
                }

                // Get loyalty transactions
                const loyaltyIndex = loyaltyStore.index('customer_id');
                const loyaltyRequest = loyaltyIndex.getAll(customerId);

                loyaltyRequest.onsuccess = () => {
                    customer.loyalty_transactions = loyaltyRequest.result || [];

                    // Get recent sales
                    const salesIndex = salesStore.index('customer_id');
                    const salesRequest = salesIndex.getAll(customerId);

                    salesRequest.onsuccess = () => {
                        customer.sales = salesRequest.result || [];
                        resolve(customer);
                    };

                    salesRequest.onerror = () => {
                        console.error('Failed to get customer sales:', salesRequest.error);
                        customer.sales = [];
                        resolve(customer);
                    };
                };

                loyaltyRequest.onerror = () => {
                    console.error('Failed to get loyalty transactions:', loyaltyRequest.error);
                    customer.loyalty_transactions = [];
                    resolve(customer);
                };
            };

            customerRequest.onerror = () => {
                console.error('Failed to get customer:', customerRequest.error);
                reject(customerRequest.error);
            };
        });
    }

    /**
     * Search customers by name, email, or phone
     */
    async searchCustomers(searchTerm, limit = 20) {
        const options = {
            search: searchTerm,
            limit: limit,
            status: 'active'
        };
        return this.getCustomers(options);
    }

    /**
     * Add or update a customer
     */
    async saveCustomer(customer) {
        if (!this.db) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.stores.customers], 'readwrite');
            const store = transaction.objectStore(this.stores.customers);

            const customerData = {
                ...customer,
                synced_at: new Date().toISOString(),
                updated_at: customer.updated_at || new Date().toISOString()
            };

            const request = store.put(customerData);

            request.onsuccess = () => {
                console.log('Customer saved successfully:', customer.id);
                resolve(customerData);
            };

            request.onerror = () => {
                console.error('Failed to save customer:', request.error);
                reject(request.error);
            };
        });
    }

    /**
     * Get sync metadata
     */
    async getSyncMetadata(key) {
        if (!this.db) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.stores.syncMetadata], 'readonly');
            const store = transaction.objectStore(this.stores.syncMetadata);
            const request = store.get(key);

            request.onsuccess = () => {
                resolve(request.result ? request.result.value : null);
            };

            request.onerror = () => {
                console.error('Failed to get sync metadata:', request.error);
                reject(request.error);
            };
        });
    }

    /**
     * Set sync metadata
     */
    async setSyncMetadata(key, value) {
        if (!this.db) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.stores.syncMetadata], 'readwrite');
            const store = transaction.objectStore(this.stores.syncMetadata);
            const request = store.put({ key, value });

            request.onsuccess = () => {
                resolve(value);
            };

            request.onerror = () => {
                console.error('Failed to set sync metadata:', request.error);
                reject(request.error);
            };
        });
    }

    /**
     * Clear all customer data
     */
    async clearAllData() {
        if (!this.db) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([
                this.stores.customers,
                this.stores.loyaltyTransactions,
                this.stores.customerSales,
                this.stores.syncMetadata
            ], 'readwrite');

            let cleared = 0;
            const storesToClear = Object.values(this.stores);

            storesToClear.forEach(storeName => {
                const store = transaction.objectStore(storeName);
                const request = store.clear();
                
                request.onsuccess = () => {
                    cleared++;
                    if (cleared === storesToClear.length) {
                        console.log('All customer data cleared successfully');
                        resolve();
                    }
                };
            });

            transaction.onerror = () => {
                console.error('Failed to clear customer data:', transaction.error);
                reject(transaction.error);
            };
        });
    }

    /**
     * Get database statistics
     */
    async getStats() {
        if (!this.db) await this.init();

        const stats = {};
        
        for (const storeName of Object.values(this.stores)) {
            stats[storeName] = await this.getStoreCount(storeName);
        }

        return stats;
    }

    /**
     * Get count of records in a store
     */
    async getStoreCount(storeName) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.count();

            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = () => {
                console.error(`Failed to count ${storeName}:`, request.error);
                reject(request.error);
            };
        });
    }
}

// Export for use in other modules
window.CustomerOfflineDB = CustomerOfflineDB;
