// Try to import from different paths to handle various bundling scenarios
let openDB;
try {
  // First try the standard import
  openDB = require('idb').openDB;
  console.log('Successfully imported idb using standard import');
} catch (e) {
  try {
    // Then try the async iterator version
    openDB = require('idb/with-async-ittr.js').openDB;
    console.log('Successfully imported idb using with-async-ittr path');
  } catch (e2) {
    try {
      // Try dynamic import for ESM environments
      import('idb').then(idb => {
        openDB = idb.openDB;
        console.log('Successfully imported idb using dynamic ESM import');
      }).catch(e3 => {
        console.error('Failed to dynamically import idb:', e3);
      });
    } catch (e3) {
      // Fallback to window.idb if loaded from CDN
      if (typeof window !== 'undefined' && window.idb) {
        openDB = window.idb.openDB;
        console.log('Using window.idb from CDN');
      } else {
        console.error('Failed to import idb library. Trying to load from CDN as fallback.');
        
        // Try to load from CDN as a last resort
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/idb@7.1.1/build/umd.js';
        script.async = true;
        script.onload = () => {
          if (window.idb) {
            openDB = window.idb.openDB;
            console.log('Successfully loaded idb from CDN');
          } else {
            console.error('Failed to load idb from CDN');
          }
        };
        script.onerror = (err) => {
          console.error('Error loading idb from CDN:', err);
        };
        document.head.appendChild(script);
        
        // Create a temporary implementation until the CDN script loads
        openDB = (...args) => {
          if (window.idb && window.idb.openDB) {
            return window.idb.openDB(...args);
          }
          console.error('IndexedDB is not available yet. Trying again in 1 second...');
          return new Promise((resolve, reject) => {
            setTimeout(() => {
              if (window.idb && window.idb.openDB) {
                resolve(window.idb.openDB(...args));
              } else {
                reject(new Error('IndexedDB not available after timeout'));
              }
            }, 1000);
          });
        };
      }
    }
  }
}

/**
 * PharmaDesk Offline Database
 *
 * This module provides a comprehensive IndexedDB implementation for offline data storage
 * and synchronization in the PharmaDesk application.
 */

// Import data integrity module (will be loaded dynamically to avoid circular dependencies)
let DataIntegrity = null;
try {
  import('./data-integrity.js').then(module => {
    DataIntegrity = module;
  });
} catch (error) {
  console.warn('Data integrity module not available:', error);
}

// Database version - increment when schema changes
const DB_VERSION = 2;
const DB_NAME = 'pharmadesk-offline-db';

// Store names
const STORES = {
  SALES: 'sales',
  SALE_ITEMS: 'sale_items',
  INVENTORY: 'inventory',
  CUSTOMERS: 'customers',
  SYNC_QUEUE: 'sync_queue',
  MEDICINES: 'medicines',
  SETTINGS: 'settings',
  CATEGORIES: 'categories',
  MANUFACTURERS: 'manufacturers',
  UNIT_TYPES: 'unit_types',
  LOCATIONS: 'locations',
  WAREHOUSES: 'warehouses',
  SUPPLIERS: 'suppliers',
  LOYALTY_TRANSACTIONS: 'loyalty_transactions',
  PROFIT_LOSS: 'profit_loss',
  PRESCRIPTIONS: 'prescriptions',
  USERS: 'users',
  UNIT_CONVERSIONS: 'unit_conversions'
};

/**
 * Initialize the IndexedDB database
 * @returns {Promise<IDBDatabase>} The database instance
 */
export async function initDB() {
  return openDB(DB_NAME, DB_VERSION, {
    upgrade(db, oldVersion, newVersion, transaction) {
      console.log(`Upgrading database from version ${oldVersion} to ${newVersion}`);
      
      // Create stores if they don't exist
      if (!db.objectStoreNames.contains(STORES.SALES)) {
        const salesStore = db.createObjectStore(STORES.SALES, { keyPath: 'id', autoIncrement: true });
        salesStore.createIndex('sync_status', 'sync_status');
        salesStore.createIndex('created_at', 'created_at');
        salesStore.createIndex('invoice_number', 'invoice_number', { unique: true });
      }
      
      if (!db.objectStoreNames.contains(STORES.SALE_ITEMS)) {
        const saleItemsStore = db.createObjectStore(STORES.SALE_ITEMS, { keyPath: 'id', autoIncrement: true });
        saleItemsStore.createIndex('sale_id', 'sale_id');
        saleItemsStore.createIndex('medicine_id', 'medicine_id');
        saleItemsStore.createIndex('batch_number', 'batch_number');
      }
      
      if (!db.objectStoreNames.contains(STORES.INVENTORY)) {
        const inventoryStore = db.createObjectStore(STORES.INVENTORY, { keyPath: 'id', autoIncrement: true });
        inventoryStore.createIndex('medicine_id', 'medicine_id');
        inventoryStore.createIndex('batch_number', 'batch_number');
        inventoryStore.createIndex('location_id', 'location_id');
        inventoryStore.createIndex('sync_status', 'sync_status');
        inventoryStore.createIndex('medicine_batch_location', ['medicine_id', 'batch_number', 'location_id'], { unique: true });
      }
      
      if (!db.objectStoreNames.contains(STORES.CUSTOMERS)) {
        const customersStore = db.createObjectStore(STORES.CUSTOMERS, { keyPath: 'id', autoIncrement: true });
        customersStore.createIndex('sync_status', 'sync_status');
        customersStore.createIndex('email', 'email', { unique: true });
        customersStore.createIndex('phone', 'phone');
      }
      
      if (!db.objectStoreNames.contains(STORES.SYNC_QUEUE)) {
        const syncQueueStore = db.createObjectStore(STORES.SYNC_QUEUE, { keyPath: 'id', autoIncrement: true });
        syncQueueStore.createIndex('entity_type', 'entity_type');
        syncQueueStore.createIndex('operation', 'operation');
        syncQueueStore.createIndex('created_at', 'created_at');
        syncQueueStore.createIndex('status', 'status');
        syncQueueStore.createIndex('priority', 'priority');
        syncQueueStore.createIndex('retry_count', 'retry_count');
        syncQueueStore.createIndex('next_retry_at', 'next_retry_at');
        syncQueueStore.createIndex('batch_id', 'batch_id');
        syncQueueStore.createIndex('status_priority', ['status', 'priority']);
      }
      
      if (!db.objectStoreNames.contains(STORES.MEDICINES)) {
        const medicinesStore = db.createObjectStore(STORES.MEDICINES, { keyPath: 'id' });
        medicinesStore.createIndex('name', 'name');
        medicinesStore.createIndex('category_id', 'category_id');
        medicinesStore.createIndex('sync_status', 'sync_status');
      }
      
      if (!db.objectStoreNames.contains(STORES.SETTINGS)) {
        const settingsStore = db.createObjectStore(STORES.SETTINGS, { keyPath: 'key' });
      }

      // Categories store
      if (!db.objectStoreNames.contains(STORES.CATEGORIES)) {
        const categoriesStore = db.createObjectStore(STORES.CATEGORIES, { keyPath: 'id', autoIncrement: true });
        categoriesStore.createIndex('name', 'name');
        categoriesStore.createIndex('slug', 'slug', { unique: true });
        categoriesStore.createIndex('parent_id', 'parent_id');
        categoriesStore.createIndex('is_active', 'is_active');
        categoriesStore.createIndex('sync_status', 'sync_status');
      }

      // Manufacturers store
      if (!db.objectStoreNames.contains(STORES.MANUFACTURERS)) {
        const manufacturersStore = db.createObjectStore(STORES.MANUFACTURERS, { keyPath: 'id', autoIncrement: true });
        manufacturersStore.createIndex('name', 'name');
        manufacturersStore.createIndex('slug', 'slug', { unique: true });
        manufacturersStore.createIndex('is_active', 'is_active');
        manufacturersStore.createIndex('sync_status', 'sync_status');
      }

      // Unit types store
      if (!db.objectStoreNames.contains(STORES.UNIT_TYPES)) {
        const unitTypesStore = db.createObjectStore(STORES.UNIT_TYPES, { keyPath: 'id', autoIncrement: true });
        unitTypesStore.createIndex('name', 'name');
        unitTypesStore.createIndex('code', 'code', { unique: true });
        unitTypesStore.createIndex('category', 'category');
        unitTypesStore.createIndex('is_base_unit', 'is_base_unit');
        unitTypesStore.createIndex('status', 'status');
        unitTypesStore.createIndex('sync_status', 'sync_status');
      }

      // Locations store
      if (!db.objectStoreNames.contains(STORES.LOCATIONS)) {
        const locationsStore = db.createObjectStore(STORES.LOCATIONS, { keyPath: 'id', autoIncrement: true });
        locationsStore.createIndex('name', 'name');
        locationsStore.createIndex('code', 'code');
        locationsStore.createIndex('parent_id', 'parent_id');
        locationsStore.createIndex('is_active', 'is_active');
        locationsStore.createIndex('is_default', 'is_default');
        locationsStore.createIndex('sync_status', 'sync_status');
      }

      // Warehouses store
      if (!db.objectStoreNames.contains(STORES.WAREHOUSES)) {
        const warehousesStore = db.createObjectStore(STORES.WAREHOUSES, { keyPath: 'id', autoIncrement: true });
        warehousesStore.createIndex('name', 'name');
        warehousesStore.createIndex('code', 'code');
        warehousesStore.createIndex('is_active', 'is_active');
        warehousesStore.createIndex('sync_status', 'sync_status');
      }

      // Suppliers store
      if (!db.objectStoreNames.contains(STORES.SUPPLIERS)) {
        const suppliersStore = db.createObjectStore(STORES.SUPPLIERS, { keyPath: 'id', autoIncrement: true });
        suppliersStore.createIndex('name', 'name');
        suppliersStore.createIndex('email', 'email');
        suppliersStore.createIndex('phone', 'phone');
        suppliersStore.createIndex('is_active', 'is_active');
        suppliersStore.createIndex('sync_status', 'sync_status');
      }

      // Loyalty transactions store
      if (!db.objectStoreNames.contains(STORES.LOYALTY_TRANSACTIONS)) {
        const loyaltyTransactionsStore = db.createObjectStore(STORES.LOYALTY_TRANSACTIONS, { keyPath: 'id', autoIncrement: true });
        loyaltyTransactionsStore.createIndex('customer_id', 'customer_id');
        loyaltyTransactionsStore.createIndex('sale_id', 'sale_id');
        loyaltyTransactionsStore.createIndex('transaction_type', 'transaction_type');
        loyaltyTransactionsStore.createIndex('transaction_date', 'transaction_date');
        loyaltyTransactionsStore.createIndex('sync_status', 'sync_status');
      }

      // Profit loss store
      if (!db.objectStoreNames.contains(STORES.PROFIT_LOSS)) {
        const profitLossStore = db.createObjectStore(STORES.PROFIT_LOSS, { keyPath: 'id', autoIncrement: true });
        profitLossStore.createIndex('medicine_id', 'medicine_id');
        profitLossStore.createIndex('sale_id', 'sale_id');
        profitLossStore.createIndex('reference_number', 'reference_number');
        profitLossStore.createIndex('transaction_type', 'transaction_type');
        profitLossStore.createIndex('transaction_date', 'transaction_date');
        profitLossStore.createIndex('sync_status', 'sync_status');
      }

      // Prescriptions store
      if (!db.objectStoreNames.contains(STORES.PRESCRIPTIONS)) {
        const prescriptionsStore = db.createObjectStore(STORES.PRESCRIPTIONS, { keyPath: 'id', autoIncrement: true });
        prescriptionsStore.createIndex('sale_id', 'sale_id');
        prescriptionsStore.createIndex('doctor_name', 'doctor_name');
        prescriptionsStore.createIndex('prescription_date', 'prescription_date');
        prescriptionsStore.createIndex('sync_status', 'sync_status');
      }

      // Users store (for offline user info)
      if (!db.objectStoreNames.contains(STORES.USERS)) {
        const usersStore = db.createObjectStore(STORES.USERS, { keyPath: 'id', autoIncrement: true });
        usersStore.createIndex('email', 'email', { unique: true });
        usersStore.createIndex('name', 'name');
        usersStore.createIndex('role', 'role');
        usersStore.createIndex('is_active', 'is_active');
        usersStore.createIndex('sync_status', 'sync_status');
      }

      // Unit conversions store
      if (!db.objectStoreNames.contains(STORES.UNIT_CONVERSIONS)) {
        const unitConversionsStore = db.createObjectStore(STORES.UNIT_CONVERSIONS, { keyPath: 'id', autoIncrement: true });
        unitConversionsStore.createIndex('from_unit_id', 'from_unit_id');
        unitConversionsStore.createIndex('to_unit_id', 'to_unit_id');
        unitConversionsStore.createIndex('from_to_units', ['from_unit_id', 'to_unit_id'], { unique: true });
        unitConversionsStore.createIndex('status', 'status');
        unitConversionsStore.createIndex('sync_status', 'sync_status');
      }
    }
  });
}

// Sync priorities
export const SYNC_PRIORITIES = {
  CRITICAL: 1,    // Sales, payments
  HIGH: 2,        // Customer updates, inventory changes
  MEDIUM: 3,      // Medicine updates, reference data
  LOW: 4          // Logs, analytics
};

// Sync statuses
export const SYNC_STATUSES = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  RETRY: 'retry'
};

/**
 * Add an item to the sync queue with enhanced options
 * @param {string} entityType - The type of entity (sales, inventory, etc.)
 * @param {string} operation - The operation (create, update, delete)
 * @param {Object} data - The data to sync
 * @param {number} entityId - The ID of the entity
 * @param {Object} options - Additional options
 * @returns {Promise<number>} The ID of the new sync queue item
 */
export async function addToSyncQueue(entityType, operation, data, entityId, options = {}) {
  const db = await initDB();

  // Determine priority based on entity type
  let priority = options.priority || getPriorityForEntity(entityType);

  // Generate batch ID if not provided
  const batchId = options.batchId || generateBatchId();

  const syncItem = {
    entity_type: entityType,
    operation: operation,
    data: data,
    entity_id: entityId,
    status: SYNC_STATUSES.PENDING,
    priority: priority,
    retry_count: 0,
    max_retries: options.maxRetries || 3,
    retry_delay: options.retryDelay || 1000, // milliseconds
    batch_id: batchId,
    dependencies: options.dependencies || [], // Array of sync item IDs this depends on
    created_at: new Date().toISOString(),
    last_attempt: null,
    next_retry_at: null,
    error_message: null,
    metadata: options.metadata || {}
  };

  return db.add(STORES.SYNC_QUEUE, syncItem);
}

/**
 * Get priority for entity type
 * @param {string} entityType - Entity type
 * @returns {number} Priority level
 */
function getPriorityForEntity(entityType) {
  const priorityMap = {
    sales: SYNC_PRIORITIES.CRITICAL,
    sale_items: SYNC_PRIORITIES.CRITICAL,
    customers: SYNC_PRIORITIES.HIGH,
    inventory: SYNC_PRIORITIES.HIGH,
    medicines: SYNC_PRIORITIES.MEDIUM,
    categories: SYNC_PRIORITIES.LOW,
    manufacturers: SYNC_PRIORITIES.LOW,
    suppliers: SYNC_PRIORITIES.LOW
  };

  return priorityMap[entityType] || SYNC_PRIORITIES.MEDIUM;
}

/**
 * Generate a batch ID for grouping related sync operations
 * @returns {string} Batch ID
 */
function generateBatchId() {
  return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Get pending sync items with priority ordering and dependency resolution
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Array of pending sync items
 */
export async function getPendingSyncItems(options = {}) {
  const db = await initDB();
  const tx = db.transaction(STORES.SYNC_QUEUE, 'readonly');
  const store = tx.objectStore(STORES.SYNC_QUEUE);

  // Get all pending and retry items
  const pendingItems = await store.index('status').getAll(SYNC_STATUSES.PENDING);
  const retryItems = await store.index('status').getAll(SYNC_STATUSES.RETRY);

  // Filter retry items that are ready for retry
  const now = new Date();
  const readyRetryItems = retryItems.filter(item => {
    return !item.next_retry_at || new Date(item.next_retry_at) <= now;
  });

  // Combine and sort by priority and creation time
  const allItems = [...pendingItems, ...readyRetryItems];

  // Filter by batch if specified
  if (options.batchId) {
    return allItems.filter(item => item.batch_id === options.batchId);
  }

  // Filter by entity type if specified
  if (options.entityType) {
    return allItems.filter(item => item.entity_type === options.entityType);
  }

  // Sort by priority (lower number = higher priority) and creation time
  allItems.sort((a, b) => {
    if (a.priority !== b.priority) {
      return a.priority - b.priority;
    }
    return new Date(a.created_at) - new Date(b.created_at);
  });

  // Resolve dependencies
  const resolvedItems = resolveDependencies(allItems);

  // Apply limit if specified
  if (options.limit) {
    return resolvedItems.slice(0, options.limit);
  }

  return resolvedItems;
}

/**
 * Resolve dependencies between sync items
 * @param {Array} items - Array of sync items
 * @returns {Array} Array of items with dependencies resolved
 */
function resolveDependencies(items) {
  const itemMap = new Map(items.map(item => [item.id, item]));
  const resolved = [];
  const processing = new Set();

  function canProcess(item) {
    if (!item.dependencies || item.dependencies.length === 0) {
      return true;
    }

    return item.dependencies.every(depId => {
      const depItem = itemMap.get(depId);
      return !depItem || depItem.status === SYNC_STATUSES.COMPLETED;
    });
  }

  function processItem(item) {
    if (processing.has(item.id) || resolved.includes(item)) {
      return;
    }

    if (canProcess(item)) {
      processing.add(item.id);
      resolved.push(item);
    }
  }

  // Process items until no more can be processed
  let lastResolvedCount = -1;
  while (resolved.length !== lastResolvedCount && resolved.length < items.length) {
    lastResolvedCount = resolved.length;

    for (const item of items) {
      processItem(item);
    }
  }

  return resolved;
}

/**
 * Get sync items by batch ID
 * @param {string} batchId - Batch ID
 * @returns {Promise<Array>} Array of sync items in the batch
 */
export async function getSyncItemsByBatch(batchId) {
  const db = await initDB();
  return db.getAllFromIndex(STORES.SYNC_QUEUE, 'batch_id', batchId);
}

/**
 * Get failed sync items
 * @returns {Promise<Array>} Array of failed sync items
 */
export async function getFailedSyncItems() {
  const db = await initDB();
  return db.getAllFromIndex(STORES.SYNC_QUEUE, 'status', SYNC_STATUSES.FAILED);
}

/**
 * Update sync item status with enhanced retry logic
 * @param {number} id - The ID of the sync item
 * @param {string} status - The new status
 * @param {string} error - Optional error message
 * @param {Object} options - Additional options
 * @returns {Promise<void>}
 */
export async function updateSyncItemStatus(id, status, error = null, options = {}) {
  const db = await initDB();
  const item = await db.get(STORES.SYNC_QUEUE, id);

  if (!item) {
    console.warn(`Sync item ${id} not found`);
    return;
  }

  const now = new Date().toISOString();
  item.last_attempt = now;

  switch (status) {
    case SYNC_STATUSES.PROCESSING:
      item.status = status;
      break;

    case SYNC_STATUSES.COMPLETED:
      item.status = status;
      item.completed_at = now;
      item.error_message = null;
      break;

    case SYNC_STATUSES.FAILED:
      item.retry_count += 1;
      item.error_message = error;

      // Determine if we should retry or mark as permanently failed
      if (item.retry_count < item.max_retries) {
        item.status = SYNC_STATUSES.RETRY;

        // Calculate next retry time with exponential backoff
        const baseDelay = item.retry_delay || 1000;
        const backoffMultiplier = Math.pow(2, item.retry_count - 1);
        const jitter = Math.random() * 0.1; // Add 10% jitter
        const delay = baseDelay * backoffMultiplier * (1 + jitter);

        const nextRetryTime = new Date(Date.now() + delay);
        item.next_retry_at = nextRetryTime.toISOString();

        console.log(`Sync item ${id} scheduled for retry ${item.retry_count}/${item.max_retries} at ${nextRetryTime.toLocaleString()}`);
      } else {
        item.status = SYNC_STATUSES.FAILED;
        item.next_retry_at = null;
        console.error(`Sync item ${id} permanently failed after ${item.retry_count} attempts: ${error}`);
      }
      break;

    case SYNC_STATUSES.RETRY:
      item.status = status;
      if (options.nextRetryAt) {
        item.next_retry_at = options.nextRetryAt;
      }
      break;

    default:
      item.status = status;
  }

  // Add metadata if provided
  if (options.metadata) {
    item.metadata = { ...item.metadata, ...options.metadata };
  }

  await db.put(STORES.SYNC_QUEUE, item);
}

/**
 * Batch update sync item statuses
 * @param {Array} updates - Array of {id, status, error, options} objects
 * @returns {Promise<void>}
 */
export async function batchUpdateSyncItemStatus(updates) {
  const db = await initDB();
  const tx = db.transaction(STORES.SYNC_QUEUE, 'readwrite');
  const store = tx.objectStore(STORES.SYNC_QUEUE);

  for (const update of updates) {
    const item = await store.get(update.id);
    if (item) {
      // Apply the same logic as updateSyncItemStatus
      const now = new Date().toISOString();
      item.last_attempt = now;

      switch (update.status) {
        case SYNC_STATUSES.COMPLETED:
          item.status = update.status;
          item.completed_at = now;
          item.error_message = null;
          break;

        case SYNC_STATUSES.FAILED:
          item.retry_count += 1;
          item.error_message = update.error;

          if (item.retry_count < item.max_retries) {
            item.status = SYNC_STATUSES.RETRY;
            const baseDelay = item.retry_delay || 1000;
            const backoffMultiplier = Math.pow(2, item.retry_count - 1);
            const delay = baseDelay * backoffMultiplier;
            item.next_retry_at = new Date(Date.now() + delay).toISOString();
          } else {
            item.status = SYNC_STATUSES.FAILED;
          }
          break;

        default:
          item.status = update.status;
      }

      if (update.options?.metadata) {
        item.metadata = { ...item.metadata, ...update.options.metadata };
      }

      await store.put(item);
    }
  }

  await tx.done;
}

/**
 * Clean up completed and old failed sync items
 * @param {Object} options - Cleanup options
 * @returns {Promise<number>} Number of items cleaned up
 */
export async function cleanupSyncQueue(options = {}) {
  const db = await initDB();
  const tx = db.transaction(STORES.SYNC_QUEUE, 'readwrite');
  const store = tx.objectStore(STORES.SYNC_QUEUE);

  const maxAge = options.maxAge || 7 * 24 * 60 * 60 * 1000; // 7 days
  const cutoffDate = new Date(Date.now() - maxAge);

  let cleanedCount = 0;

  // Get all items
  const allItems = await store.getAll();

  for (const item of allItems) {
    const itemDate = new Date(item.completed_at || item.last_attempt || item.created_at);

    // Clean up completed items older than maxAge
    if (item.status === SYNC_STATUSES.COMPLETED && itemDate < cutoffDate) {
      await store.delete(item.id);
      cleanedCount++;
    }

    // Clean up permanently failed items older than maxAge
    if (item.status === SYNC_STATUSES.FAILED && itemDate < cutoffDate) {
      await store.delete(item.id);
      cleanedCount++;
    }
  }

  await tx.done;

  console.log(`Cleaned up ${cleanedCount} sync queue items`);
  return cleanedCount;
}

/**
 * Save a sale for offline use with comprehensive business logic
 * @param {Object} sale - The sale object
 * @returns {Promise<Object>} Result object with sale ID and validation results
 */
export async function saveSale(sale) {
  const db = await initDB();
  const tx = db.transaction([
    STORES.SALES,
    STORES.SALE_ITEMS,
    STORES.INVENTORY,
    STORES.CUSTOMERS,
    STORES.LOYALTY_TRANSACTIONS,
    STORES.PROFIT_LOSS,
    STORES.PRESCRIPTIONS,
    STORES.SYNC_QUEUE
  ], 'readwrite');

  try {
    // Use data integrity validation if available, otherwise use basic validation
    let validationResult;
    if (DataIntegrity && DataIntegrity.validateSale) {
      validationResult = await DataIntegrity.validateSale(sale);
    } else {
      validationResult = await validateSaleData(sale, tx);
    }

    if (!validationResult.valid) {
      const errorMessages = validationResult.errors.map(e => e.message || e).join(', ');
      throw new Error(`Sale validation failed: ${errorMessages}`);
    }

    // Log warnings if any
    if (validationResult.warnings && validationResult.warnings.length > 0) {
      console.warn('Sale validation warnings:', validationResult.warnings);
    }

    // Add offline flag and timestamp
    sale.sync_status = 'pending';
    sale.created_at = new Date().toISOString();
    sale.updated_at = new Date().toISOString();

    // Generate a temporary invoice number for offline use
    if (!sale.invoice_number) {
      sale.invoice_number = `OFFLINE-${Date.now()}`;
    }

    // Process loyalty points redemption if applicable
    if (sale.customer_id && sale.loyalty_points_to_redeem > 0) {
      const loyaltyResult = await processLoyaltyRedemption(sale, tx);
      if (!loyaltyResult.success) {
        throw new Error(`Loyalty redemption failed: ${loyaltyResult.message}`);
      }
      sale.discount_amount = (sale.discount_amount || 0) + loyaltyResult.discount;
    }

    // Recalculate totals with all discounts and taxes
    const calculatedTotals = calculateSaleTotals(sale);
    Object.assign(sale, calculatedTotals);

    // Save the sale
    const saleId = await tx.objectStore(STORES.SALES).add(sale);

    // Save the sale items and update inventory
    if (sale.items && Array.isArray(sale.items)) {
      for (const item of sale.items) {
        item.sale_id = saleId;
        item.sync_status = 'pending';
        await tx.objectStore(STORES.SALE_ITEMS).add(item);

        // Update inventory with proper validation
        const inventoryResult = await updateInventoryQuantity(
          tx,
          item.medicine_id,
          item.batch_number,
          item.location_id,
          -item.quantity
        );

        if (!inventoryResult.success) {
          throw new Error(`Inventory update failed: ${inventoryResult.message}`);
        }

        // Create profit/loss record
        await createProfitLossRecord(sale, item, saleId, tx);
      }
    }

    // Award loyalty points if customer is present
    if (sale.customer_id) {
      await awardLoyaltyPoints(sale, saleId, tx);
    }

    // Save prescription if provided
    if (sale.prescription) {
      await savePrescriptionData(sale.prescription, saleId, tx);
    }

    // Add to sync queue
    await tx.objectStore(STORES.SYNC_QUEUE).add({
      entity_type: STORES.SALES,
      operation: 'create',
      data: { ...sale, id: saleId },
      entity_id: saleId,
      status: 'pending',
      retry_count: 0,
      created_at: new Date().toISOString(),
      last_attempt: null
    });

    await tx.done;

    return {
      success: true,
      saleId: saleId,
      invoiceNumber: sale.invoice_number,
      totalAmount: sale.total_amount,
      message: 'Sale saved successfully for offline sync'
    };
  } catch (error) {
    console.error('Error saving offline sale:', error);
    throw error;
  }
}

/**
 * Update inventory quantity with validation
 * @param {IDBTransaction} tx - The transaction
 * @param {number} medicineId - The medicine ID
 * @param {string} batchNumber - The batch number
 * @param {number} locationId - The location ID
 * @param {number} quantityChange - The quantity change (positive for increase, negative for decrease)
 * @returns {Promise<Object>} Result object with success status
 */
async function updateInventoryQuantity(tx, medicineId, batchNumber, locationId, quantityChange) {
  try {
    const inventoryStore = tx.objectStore(STORES.INVENTORY);
    const index = inventoryStore.index('medicine_batch_location');
    const key = IDBKeyRange.only([medicineId, batchNumber, locationId]);

    const inventory = await index.get(key);

    if (!inventory) {
      return {
        success: false,
        message: `Inventory not found for medicine ${medicineId}, batch ${batchNumber}, location ${locationId}`
      };
    }

    const newQuantity = inventory.quantity + quantityChange;

    // Validate sufficient stock for sales (negative quantity change)
    if (quantityChange < 0 && newQuantity < 0) {
      return {
        success: false,
        message: `Insufficient stock. Available: ${inventory.quantity}, Required: ${Math.abs(quantityChange)}`
      };
    }

    inventory.quantity = newQuantity;
    inventory.sync_status = 'pending';
    inventory.updated_at = new Date().toISOString();
    await inventoryStore.put(inventory);

    return {
      success: true,
      newQuantity: newQuantity,
      message: 'Inventory updated successfully'
    };
  } catch (error) {
    return {
      success: false,
      message: `Error updating inventory: ${error.message}`
    };
  }
}

/**
 * Validate sale data before processing
 * @param {Object} sale - The sale object
 * @param {IDBTransaction} tx - The transaction
 * @returns {Promise<Object>} Validation result
 */
async function validateSaleData(sale, tx) {
  const errors = [];

  // Basic validation
  if (!sale.items || !Array.isArray(sale.items) || sale.items.length === 0) {
    errors.push('Sale must have at least one item');
  }

  if (!sale.payment_method) {
    errors.push('Payment method is required');
  }

  if (sale.total_amount <= 0) {
    errors.push('Total amount must be greater than zero');
  }

  // Validate customer if provided
  if (sale.customer_id) {
    const customerStore = tx.objectStore(STORES.CUSTOMERS);
    const customer = await customerStore.get(sale.customer_id);
    if (!customer) {
      errors.push('Customer not found');
    }
  }

  // Validate items and inventory
  if (sale.items) {
    for (let i = 0; i < sale.items.length; i++) {
      const item = sale.items[i];

      if (!item.medicine_id || !item.batch_number || !item.quantity || !item.unit_price) {
        errors.push(`Item ${i + 1}: Missing required fields`);
        continue;
      }

      if (item.quantity <= 0) {
        errors.push(`Item ${i + 1}: Quantity must be greater than zero`);
      }

      if (item.unit_price < 0) {
        errors.push(`Item ${i + 1}: Unit price cannot be negative`);
      }

      // Check inventory availability
      const inventoryStore = tx.objectStore(STORES.INVENTORY);
      const index = inventoryStore.index('medicine_batch_location');
      const key = IDBKeyRange.only([item.medicine_id, item.batch_number, item.location_id || 1]);
      const inventory = await index.get(key);

      if (!inventory) {
        errors.push(`Item ${i + 1}: Inventory not found for this medicine and batch`);
      } else if (inventory.quantity < item.quantity) {
        errors.push(`Item ${i + 1}: Insufficient stock (Available: ${inventory.quantity}, Required: ${item.quantity})`);
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors: errors
  };
}

/**
 * Calculate sale totals with discounts and taxes
 * @param {Object} sale - The sale object
 * @returns {Object} Calculated totals
 */
function calculateSaleTotals(sale) {
  let subtotal = 0;
  let totalTax = 0;
  let totalDiscount = sale.discount_amount || 0;

  if (sale.items) {
    for (const item of sale.items) {
      const itemTotal = item.quantity * item.unit_price;
      const itemDiscount = item.discount || 0;
      const itemTax = (itemTotal - itemDiscount) * (item.tax_rate || 0) / 100;

      subtotal += itemTotal - itemDiscount;
      totalTax += itemTax;
    }
  }

  const totalAmount = subtotal + totalTax - totalDiscount;
  const paidAmount = Math.min(sale.paid_amount || 0, totalAmount);
  const dueAmount = Math.max(0, totalAmount - paidAmount);

  return {
    total_amount: parseFloat(totalAmount.toFixed(2)),
    tax_amount: parseFloat(totalTax.toFixed(2)),
    discount_amount: parseFloat(totalDiscount.toFixed(2)),
    paid_amount: parseFloat(paidAmount.toFixed(2)),
    due_amount: parseFloat(dueAmount.toFixed(2)),
    payment_status: paidAmount >= totalAmount ? 'completed' : (paidAmount > 0 ? 'partial' : 'pending')
  };
}

/**
 * Process loyalty points redemption
 * @param {Object} sale - The sale object
 * @param {IDBTransaction} tx - The transaction
 * @returns {Promise<Object>} Redemption result
 */
async function processLoyaltyRedemption(sale, tx) {
  try {
    const customerStore = tx.objectStore(STORES.CUSTOMERS);
    const customer = await customerStore.get(sale.customer_id);

    if (!customer) {
      return { success: false, message: 'Customer not found' };
    }

    const pointsToRedeem = sale.loyalty_points_to_redeem || 0;

    if (pointsToRedeem <= 0) {
      return { success: true, discount: 0 };
    }

    if (customer.loyalty_points < pointsToRedeem) {
      return {
        success: false,
        message: `Insufficient loyalty points. Available: ${customer.loyalty_points}, Required: ${pointsToRedeem}`
      };
    }

    // Calculate discount (assuming 1 point = 0.01 currency unit)
    const discount = pointsToRedeem * 0.01;

    // Update customer loyalty points
    customer.loyalty_points -= pointsToRedeem;
    customer.sync_status = 'pending';
    customer.updated_at = new Date().toISOString();
    await customerStore.put(customer);

    return {
      success: true,
      discount: discount,
      pointsRedeemed: pointsToRedeem,
      remainingPoints: customer.loyalty_points
    };
  } catch (error) {
    return { success: false, message: `Error processing loyalty redemption: ${error.message}` };
  }
}

/**
 * Award loyalty points for a sale
 * @param {Object} sale - The sale object
 * @param {number} saleId - The sale ID
 * @param {IDBTransaction} tx - The transaction
 * @returns {Promise<void>}
 */
async function awardLoyaltyPoints(sale, saleId, tx) {
  try {
    const customerStore = tx.objectStore(STORES.CUSTOMERS);
    const customer = await customerStore.get(sale.customer_id);

    if (!customer) {
      console.warn('Customer not found for loyalty points award');
      return;
    }

    // Calculate points to award (1% of total amount)
    const pointsToAward = Math.floor(sale.total_amount * 0.01);

    if (pointsToAward > 0) {
      // Update customer loyalty points
      customer.loyalty_points = (customer.loyalty_points || 0) + pointsToAward;
      customer.sync_status = 'pending';
      customer.updated_at = new Date().toISOString();
      await customerStore.put(customer);

      // Create loyalty transaction record
      const loyaltyTransaction = {
        customer_id: sale.customer_id,
        sale_id: saleId,
        transaction_type: 'earned',
        points: pointsToAward,
        description: `Points earned from sale ${sale.invoice_number}`,
        transaction_date: new Date().toISOString(),
        sync_status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      await tx.objectStore(STORES.LOYALTY_TRANSACTIONS).add(loyaltyTransaction);
    }
  } catch (error) {
    console.error('Error awarding loyalty points:', error);
  }
}

/**
 * Create profit/loss record for sale item
 * @param {Object} sale - The sale object
 * @param {Object} item - The sale item
 * @param {number} saleId - The sale ID
 * @param {IDBTransaction} tx - The transaction
 * @returns {Promise<void>}
 */
async function createProfitLossRecord(sale, item, saleId, tx) {
  try {
    // Get medicine cost from inventory
    const inventoryStore = tx.objectStore(STORES.INVENTORY);
    const index = inventoryStore.index('medicine_batch_location');
    const key = IDBKeyRange.only([item.medicine_id, item.batch_number, item.location_id || 1]);
    const inventory = await index.get(key);

    const unitCost = inventory?.purchase_price || 0;
    const totalCost = unitCost * item.quantity;
    const itemDiscount = item.discount || 0;
    const itemTax = (item.quantity * item.unit_price - itemDiscount) * (item.tax_rate || 0) / 100;
    const totalRevenue = (item.quantity * item.unit_price) - itemDiscount + itemTax;
    const grossProfit = totalRevenue - totalCost;
    const netProfit = grossProfit; // Could include other costs
    const profitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;

    const profitLossRecord = {
      medicine_id: item.medicine_id,
      sale_id: saleId,
      reference_number: sale.invoice_number,
      transaction_type: 'sale',
      quantity: item.quantity,
      unit_cost: unitCost,
      unit_price: item.unit_price,
      total_cost: totalCost,
      total_revenue: totalRevenue,
      discount_amount: itemDiscount,
      tax_amount: itemTax,
      gross_profit: grossProfit,
      net_profit: netProfit,
      profit_margin: profitMargin,
      transaction_date: new Date().toISOString().split('T')[0],
      sync_status: 'pending',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    await tx.objectStore(STORES.PROFIT_LOSS).add(profitLossRecord);
  } catch (error) {
    console.error('Error creating profit/loss record:', error);
  }
}

/**
 * Save prescription data
 * @param {Object} prescription - The prescription object
 * @param {number} saleId - The sale ID
 * @param {IDBTransaction} tx - The transaction
 * @returns {Promise<void>}
 */
async function savePrescriptionData(prescription, saleId, tx) {
  try {
    const prescriptionData = {
      sale_id: saleId,
      doctor_name: prescription.doctor_name,
      hospital_name: prescription.hospital_name,
      prescription_date: prescription.date || new Date().toISOString().split('T')[0],
      prescription_image: prescription.image || null,
      sync_status: 'pending',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    await tx.objectStore(STORES.PRESCRIPTIONS).add(prescriptionData);
  } catch (error) {
    console.error('Error saving prescription data:', error);
  }
}

/**
 * Save customer data
 * @param {Object} customer - The customer object
 * @returns {Promise<number>} The ID of the new customer
 */
export async function saveCustomer(customer) {
  const db = await initDB();
  const tx = db.transaction([STORES.CUSTOMERS, STORES.SYNC_QUEUE], 'readwrite');
  
  customer.sync_status = 'pending';
  customer.created_at = new Date().toISOString();
  customer.updated_at = new Date().toISOString();
  
  const customerId = await tx.objectStore(STORES.CUSTOMERS).add(customer);
  
  await tx.objectStore(STORES.SYNC_QUEUE).add({
    entity_type: STORES.CUSTOMERS,
    operation: 'create',
    data: { ...customer, id: customerId },
    entity_id: customerId,
    status: 'pending',
    retry_count: 0,
    created_at: new Date().toISOString(),
    last_attempt: null
  });
  
  await tx.done;
  return customerId;
}

/**
 * Cache medicines data from API
 * @param {Array} medicines - Array of medicine objects
 * @returns {Promise<void>}
 */
export async function cacheMedicines(medicines) {
  const db = await initDB();
  const tx = db.transaction(STORES.MEDICINES, 'readwrite');
  
  for (const medicine of medicines) {
    medicine.sync_status = 'synced';
    await tx.store.put(medicine);
  }
  
  await tx.done;
}

/**
 * Cache inventory data from API
 * @param {Array} inventory - Array of inventory objects
 * @returns {Promise<void>}
 */
export async function cacheInventory(inventory) {
  const db = await initDB();
  const tx = db.transaction(STORES.INVENTORY, 'readwrite');
  
  for (const item of inventory) {
    item.sync_status = 'synced';
    await tx.store.put(item);
  }
  
  await tx.done;
}

/**
 * Get all medicines
 * @returns {Promise<Array>} Array of medicines
 */
export async function getAllMedicines() {
  const db = await initDB();
  return db.getAll(STORES.MEDICINES);
}

/**
 * Get a specific medicine by ID
 * @param {number} medicineId - The medicine ID
 * @returns {Promise<Object|null>} Medicine object or null if not found
 */
export async function getMedicineById(medicineId) {
  const db = await initDB();
  return db.get(STORES.MEDICINES, medicineId);
}

/**
 * Get inventory/batches for a specific medicine
 * @param {number} medicineId - The medicine ID
 * @returns {Promise<Array>} Array of inventory batches
 */
export async function getInventoryByMedicineId(medicineId) {
  const db = await initDB();
  const tx = db.transaction(STORES.INVENTORY, 'readonly');
  const index = tx.store.index('medicine_id');
  return index.getAll(medicineId);
}

/**
 * Search medicines for offline sales operations
 * @param {string} searchTerm - Search term
 * @param {Object} options - Search options
 * @returns {Promise<Array>} Array of matching medicines
 */
export async function searchMedicinesForSales(searchTerm, options = {}) {
  const db = await initDB();
  const medicines = await db.getAll(STORES.MEDICINES);

  if (!searchTerm || searchTerm.trim() === '') {
    // Return all active medicines with stock if no search term
    return medicines
      .filter(medicine =>
        medicine.status === 'active' &&
        (medicine.available_stock > 0 || !options.requireStock)
      )
      .slice(0, options.limit || 50);
  }

  const term = searchTerm.toLowerCase().trim();

  return medicines
    .filter(medicine => {
      // Basic filters
      if (medicine.status !== 'active') return false;
      if (options.requireStock && (medicine.available_stock || 0) <= 0) return false;

      // Search in multiple fields
      const searchableFields = [
        medicine.name,
        medicine.generic_name,
        medicine.manufacturer?.name,
        medicine.category?.name,
        medicine.searchable_text
      ].filter(Boolean);

      return searchableFields.some(field =>
        field.toLowerCase().includes(term)
      );
    })
    .sort((a, b) => {
      // Sort by relevance - exact matches first, then partial matches
      const aName = (a.name || '').toLowerCase();
      const bName = (b.name || '').toLowerCase();

      if (aName.startsWith(term) && !bName.startsWith(term)) return -1;
      if (!aName.startsWith(term) && bName.startsWith(term)) return 1;

      return aName.localeCompare(bName);
    })
    .slice(0, options.limit || 50);
}

/**
 * Get medicines by category for offline browsing
 * @param {number} categoryId - Category ID
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Array of medicines in category
 */
export async function getMedicinesByCategory(categoryId, options = {}) {
  const db = await initDB();
  const medicines = await db.getAll(STORES.MEDICINES);

  return medicines
    .filter(medicine => {
      if (medicine.category_id !== categoryId) return false;
      if (medicine.status !== 'active') return false;
      if (options.requireStock && (medicine.available_stock || 0) <= 0) return false;

      return true;
    })
    .sort((a, b) => (a.name || '').localeCompare(b.name || ''))
    .slice(0, options.limit || 100);
}

/**
 * Get medicines with low stock for offline monitoring
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Array of medicines with low stock
 */
export async function getLowStockMedicines(options = {}) {
  const db = await initDB();
  const medicines = await db.getAll(STORES.MEDICINES);

  return medicines
    .filter(medicine => {
      if (medicine.status !== 'active') return false;

      const availableStock = medicine.available_stock || 0;
      const minimumStock = medicine.minimum_stock || 0;

      return availableStock <= minimumStock && availableStock > 0;
    })
    .sort((a, b) => (a.available_stock || 0) - (b.available_stock || 0))
    .slice(0, options.limit || 100);
}

/**
 * Get medicines requiring prescription for offline validation
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Array of prescription medicines
 */
export async function getPrescriptionMedicines(options = {}) {
  const db = await initDB();
  const medicines = await db.getAll(STORES.MEDICINES);

  return medicines
    .filter(medicine => {
      if (medicine.status !== 'active') return false;
      if (options.requireStock && (medicine.available_stock || 0) <= 0) return false;

      return medicine.requires_prescription || medicine.prescription_required;
    })
    .sort((a, b) => (a.name || '').localeCompare(b.name || ''))
    .slice(0, options.limit || 100);
}

/**
 * Validate medicine availability for offline sales
 * @param {number} medicineId - Medicine ID
 * @param {number} requestedQuantity - Requested quantity
 * @returns {Promise<Object>} Validation result
 */
export async function validateMedicineAvailability(medicineId, requestedQuantity) {
  const medicine = await getMedicineById(medicineId);

  if (!medicine) {
    return {
      valid: false,
      error: 'Medicine not found',
      code: 'MEDICINE_NOT_FOUND'
    };
  }

  if (medicine.status !== 'active') {
    return {
      valid: false,
      error: 'Medicine is not active',
      code: 'MEDICINE_INACTIVE'
    };
  }

  const availableStock = medicine.available_stock || 0;

  if (availableStock < requestedQuantity) {
    return {
      valid: false,
      error: `Insufficient stock. Available: ${availableStock}, Requested: ${requestedQuantity}`,
      code: 'INSUFFICIENT_STOCK',
      availableStock,
      requestedQuantity
    };
  }

  return {
    valid: true,
    medicine,
    availableStock,
    requestedQuantity,
    canSell: true
  };
}

/**
 * Get inventory by medicine ID
 * @param {number} medicineId - The medicine ID
 * @returns {Promise<Array>} Array of inventory items
 */
export async function getInventoryByMedicine(medicineId) {
  const db = await initDB();
  return db.getAllFromIndex(STORES.INVENTORY, 'medicine_id', medicineId);
}

/**
 * Get all pending sales
 * @returns {Promise<Array>} Array of pending sales
 */
export async function getPendingSales() {
  const db = await initDB();
  return db.getAllFromIndex(STORES.SALES, 'sync_status', 'pending');
}

/**
 * Get sale items by sale ID
 * @param {number} saleId - The sale ID
 * @returns {Promise<Array>} Array of sale items
 */
export async function getSaleItemsBySale(saleId) {
  const db = await initDB();
  return db.getAllFromIndex(STORES.SALE_ITEMS, 'sale_id', saleId);
}

/**
 * Save a setting
 * @param {string} key - The setting key
 * @param {any} value - The setting value
 * @returns {Promise<void>}
 */
export async function saveSetting(key, value) {
  const db = await initDB();
  await db.put(STORES.SETTINGS, { key, value });
}

/**
 * Get a setting
 * @param {string} key - The setting key
 * @returns {Promise<any>} The setting value
 */
export async function getSetting(key) {
  const db = await initDB();
  const setting = await db.get(STORES.SETTINGS, key);
  return setting ? setting.value : null;
}

/**
 * Clear all data from the database
 * @returns {Promise<void>}
 */
export async function clearDatabase() {
  const db = await initDB();
  const tx = db.transaction(Object.values(STORES), 'readwrite');

  for (const storeName of Object.values(STORES)) {
    await tx.objectStore(storeName).clear();
  }

  await tx.done;
}

/**
 * Perform data integrity check
 * @returns {Promise<Object>} Integrity check result
 */
export async function performIntegrityCheck() {
  try {
    if (DataIntegrity && DataIntegrity.performDataConsistencyCheck) {
      return await DataIntegrity.performDataConsistencyCheck();
    } else {
      // Basic integrity check if data integrity module is not available
      return await performBasicIntegrityCheck();
    }
  } catch (error) {
    console.error('Error performing integrity check:', error);
    return {
      consistent: false,
      issues: [{
        type: 'integrity_check_error',
        message: `Error performing integrity check: ${error.message}`
      }],
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Perform basic integrity check
 * @returns {Promise<Object>} Basic integrity check result
 */
async function performBasicIntegrityCheck() {
  const issues = [];

  try {
    // Check for negative inventory
    const allInventory = await getAllInventory();
    const negativeInventory = allInventory.filter(item => item.quantity < 0);

    if (negativeInventory.length > 0) {
      issues.push({
        type: 'negative_inventory',
        count: negativeInventory.length,
        message: `Found ${negativeInventory.length} inventory items with negative quantities`
      });
    }

    // Check for orphaned sale items
    const allSaleItems = await getAllSaleItems();
    const allSales = await getAllSales();
    const saleIds = new Set(allSales.map(sale => sale.id));

    const orphanedItems = allSaleItems.filter(item => !saleIds.has(item.sale_id));

    if (orphanedItems.length > 0) {
      issues.push({
        type: 'orphaned_sale_items',
        count: orphanedItems.length,
        message: `Found ${orphanedItems.length} sale items without corresponding sales`
      });
    }

    return {
      consistent: issues.length === 0,
      issues,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      consistent: false,
      issues: [{
        type: 'basic_integrity_check_error',
        message: `Error in basic integrity check: ${error.message}`
      }],
      timestamp: new Date().toISOString()
    };
  }
}

// ===== REFERENCE DATA CACHING FUNCTIONS =====

/**
 * Cache categories data from API
 * @param {Array} categories - Array of category objects
 * @returns {Promise<void>}
 */
export async function cacheCategories(categories) {
  const db = await initDB();
  const tx = db.transaction(STORES.CATEGORIES, 'readwrite');

  for (const category of categories) {
    category.sync_status = 'synced';
    await tx.store.put(category);
  }

  await tx.done;
}

/**
 * Cache manufacturers data from API
 * @param {Array} manufacturers - Array of manufacturer objects
 * @returns {Promise<void>}
 */
export async function cacheManufacturers(manufacturers) {
  const db = await initDB();
  const tx = db.transaction(STORES.MANUFACTURERS, 'readwrite');

  for (const manufacturer of manufacturers) {
    manufacturer.sync_status = 'synced';
    await tx.store.put(manufacturer);
  }

  await tx.done;
}

/**
 * Cache unit types data from API
 * @param {Array} unitTypes - Array of unit type objects
 * @returns {Promise<void>}
 */
export async function cacheUnitTypes(unitTypes) {
  const db = await initDB();
  const tx = db.transaction(STORES.UNIT_TYPES, 'readwrite');

  for (const unitType of unitTypes) {
    unitType.sync_status = 'synced';
    await tx.store.put(unitType);
  }

  await tx.done;
}

/**
 * Cache locations data from API
 * @param {Array} locations - Array of location objects
 * @returns {Promise<void>}
 */
export async function cacheLocations(locations) {
  const db = await initDB();
  const tx = db.transaction(STORES.LOCATIONS, 'readwrite');

  for (const location of locations) {
    location.sync_status = 'synced';
    await tx.store.put(location);
  }

  await tx.done;
}

/**
 * Cache warehouses data from API
 * @param {Array} warehouses - Array of warehouse objects
 * @returns {Promise<void>}
 */
export async function cacheWarehouses(warehouses) {
  const db = await initDB();
  const tx = db.transaction(STORES.WAREHOUSES, 'readwrite');

  for (const warehouse of warehouses) {
    warehouse.sync_status = 'synced';
    await tx.store.put(warehouse);
  }

  await tx.done;
}

/**
 * Cache suppliers data from API
 * @param {Array} suppliers - Array of supplier objects
 * @returns {Promise<void>}
 */
export async function cacheSuppliers(suppliers) {
  const db = await initDB();
  const tx = db.transaction(STORES.SUPPLIERS, 'readwrite');

  for (const supplier of suppliers) {
    supplier.sync_status = 'synced';
    await tx.store.put(supplier);
  }

  await tx.done;
}

// ===== REFERENCE DATA GETTER FUNCTIONS =====

/**
 * Get all categories
 * @returns {Promise<Array>} Array of categories
 */
export async function getAllCategories() {
  const db = await initDB();
  return db.getAll(STORES.CATEGORIES);
}

/**
 * Get active categories
 * @returns {Promise<Array>} Array of active categories
 */
export async function getActiveCategories() {
  const db = await initDB();
  return db.getAllFromIndex(STORES.CATEGORIES, 'is_active', true);
}

/**
 * Get all manufacturers
 * @returns {Promise<Array>} Array of manufacturers
 */
export async function getAllManufacturers() {
  const db = await initDB();
  return db.getAll(STORES.MANUFACTURERS);
}

/**
 * Get active manufacturers
 * @returns {Promise<Array>} Array of active manufacturers
 */
export async function getActiveManufacturers() {
  const db = await initDB();
  return db.getAllFromIndex(STORES.MANUFACTURERS, 'is_active', true);
}

/**
 * Get all unit types
 * @returns {Promise<Array>} Array of unit types
 */
export async function getAllUnitTypes() {
  const db = await initDB();
  return db.getAll(STORES.UNIT_TYPES);
}

/**
 * Get active unit types
 * @returns {Promise<Array>} Array of active unit types
 */
export async function getActiveUnitTypes() {
  const db = await initDB();
  return db.getAllFromIndex(STORES.UNIT_TYPES, 'status', 'active');
}

/**
 * Get all locations
 * @returns {Promise<Array>} Array of locations
 */
export async function getAllLocations() {
  const db = await initDB();
  return db.getAll(STORES.LOCATIONS);
}

/**
 * Get active locations
 * @returns {Promise<Array>} Array of active locations
 */
export async function getActiveLocations() {
  const db = await initDB();
  return db.getAllFromIndex(STORES.LOCATIONS, 'is_active', true);
}

/**
 * Get default location
 * @returns {Promise<Object|null>} Default location or null
 */
export async function getDefaultLocation() {
  const db = await initDB();
  const locations = await db.getAllFromIndex(STORES.LOCATIONS, 'is_default', true);
  return locations.length > 0 ? locations[0] : null;
}

/**
 * Get all warehouses
 * @returns {Promise<Array>} Array of warehouses
 */
export async function getAllWarehouses() {
  const db = await initDB();
  return db.getAll(STORES.WAREHOUSES);
}

/**
 * Get active warehouses
 * @returns {Promise<Array>} Array of active warehouses
 */
export async function getActiveWarehouses() {
  const db = await initDB();
  return db.getAllFromIndex(STORES.WAREHOUSES, 'is_active', true);
}

/**
 * Get all suppliers
 * @returns {Promise<Array>} Array of suppliers
 */
export async function getAllSuppliers() {
  const db = await initDB();
  return db.getAll(STORES.SUPPLIERS);
}

/**
 * Get active suppliers
 * @returns {Promise<Array>} Array of active suppliers
 */
export async function getActiveSuppliers() {
  const db = await initDB();
  return db.getAllFromIndex(STORES.SUPPLIERS, 'is_active', true);
}

// ===== BUSINESS DATA FUNCTIONS =====

/**
 * Save loyalty transaction
 * @param {Object} transaction - The loyalty transaction object
 * @returns {Promise<number>} The ID of the new transaction
 */
export async function saveLoyaltyTransaction(transaction) {
  const db = await initDB();
  const tx = db.transaction([STORES.LOYALTY_TRANSACTIONS, STORES.SYNC_QUEUE], 'readwrite');

  transaction.sync_status = 'pending';
  transaction.created_at = new Date().toISOString();
  transaction.updated_at = new Date().toISOString();

  const transactionId = await tx.objectStore(STORES.LOYALTY_TRANSACTIONS).add(transaction);

  await tx.objectStore(STORES.SYNC_QUEUE).add({
    entity_type: STORES.LOYALTY_TRANSACTIONS,
    operation: 'create',
    data: { ...transaction, id: transactionId },
    entity_id: transactionId,
    status: 'pending',
    retry_count: 0,
    created_at: new Date().toISOString(),
    last_attempt: null
  });

  await tx.done;
  return transactionId;
}

/**
 * Get loyalty transactions by customer ID
 * @param {number} customerId - The customer ID
 * @returns {Promise<Array>} Array of loyalty transactions
 */
export async function getLoyaltyTransactionsByCustomer(customerId) {
  const db = await initDB();
  return db.getAllFromIndex(STORES.LOYALTY_TRANSACTIONS, 'customer_id', customerId);
}

/**
 * Save profit loss record
 * @param {Object} profitLoss - The profit loss object
 * @returns {Promise<number>} The ID of the new record
 */
export async function saveProfitLoss(profitLoss) {
  const db = await initDB();
  const tx = db.transaction([STORES.PROFIT_LOSS, STORES.SYNC_QUEUE], 'readwrite');

  profitLoss.sync_status = 'pending';
  profitLoss.created_at = new Date().toISOString();
  profitLoss.updated_at = new Date().toISOString();

  const profitLossId = await tx.objectStore(STORES.PROFIT_LOSS).add(profitLoss);

  await tx.objectStore(STORES.SYNC_QUEUE).add({
    entity_type: STORES.PROFIT_LOSS,
    operation: 'create',
    data: { ...profitLoss, id: profitLossId },
    entity_id: profitLossId,
    status: 'pending',
    retry_count: 0,
    created_at: new Date().toISOString(),
    last_attempt: null
  });

  await tx.done;
  return profitLossId;
}

/**
 * Save prescription
 * @param {Object} prescription - The prescription object
 * @returns {Promise<number>} The ID of the new prescription
 */
export async function savePrescription(prescription) {
  const db = await initDB();
  const tx = db.transaction([STORES.PRESCRIPTIONS, STORES.SYNC_QUEUE], 'readwrite');

  prescription.sync_status = 'pending';
  prescription.created_at = new Date().toISOString();
  prescription.updated_at = new Date().toISOString();

  const prescriptionId = await tx.objectStore(STORES.PRESCRIPTIONS).add(prescription);

  await tx.objectStore(STORES.SYNC_QUEUE).add({
    entity_type: STORES.PRESCRIPTIONS,
    operation: 'create',
    data: { ...prescription, id: prescriptionId },
    entity_id: prescriptionId,
    status: 'pending',
    retry_count: 0,
    created_at: new Date().toISOString(),
    last_attempt: null
  });

  await tx.done;
  return prescriptionId;
}

/**
 * Get prescription by sale ID
 * @param {number} saleId - The sale ID
 * @returns {Promise<Object|null>} Prescription object or null
 */
export async function getPrescriptionBySale(saleId) {
  const db = await initDB();
  const prescriptions = await db.getAllFromIndex(STORES.PRESCRIPTIONS, 'sale_id', saleId);
  return prescriptions.length > 0 ? prescriptions[0] : null;
}

/**
 * Cache users data from API
 * @param {Array} users - Array of user objects
 * @returns {Promise<void>}
 */
export async function cacheUsers(users) {
  const db = await initDB();
  const tx = db.transaction(STORES.USERS, 'readwrite');

  for (const user of users) {
    user.sync_status = 'synced';
    await tx.store.put(user);
  }

  await tx.done;
}

/**
 * Get user by ID
 * @param {number} userId - The user ID
 * @returns {Promise<Object|null>} User object or null
 */
export async function getUserById(userId) {
  const db = await initDB();
  return db.get(STORES.USERS, userId);
}

/**
 * Cache unit conversions data from API
 * @param {Array} conversions - Array of unit conversion objects
 * @returns {Promise<void>}
 */
export async function cacheUnitConversions(conversions) {
  const db = await initDB();
  const tx = db.transaction(STORES.UNIT_CONVERSIONS, 'readwrite');

  for (const conversion of conversions) {
    conversion.sync_status = 'synced';
    await tx.store.put(conversion);
  }

  await tx.done;
}

/**
 * Get unit conversion factor
 * @param {number} fromUnitId - The from unit ID
 * @param {number} toUnitId - The to unit ID
 * @returns {Promise<Object|null>} Conversion object or null
 */
export async function getUnitConversion(fromUnitId, toUnitId) {
  const db = await initDB();
  const conversions = await db.getAllFromIndex(STORES.UNIT_CONVERSIONS, 'from_to_units', [fromUnitId, toUnitId]);
  return conversions.length > 0 ? conversions[0] : null;
}

/**
 * Get all inventory items
 * @returns {Promise<Array>} Array of all inventory items
 */
export async function getAllInventory() {
  const db = await initDB();
  return db.getAll(STORES.INVENTORY);
}

/**
 * Get all sale items
 * @returns {Promise<Array>} Array of all sale items
 */
export async function getAllSaleItems() {
  const db = await initDB();
  return db.getAll(STORES.SALE_ITEMS);
}

/**
 * Get all sales
 * @returns {Promise<Array>} Array of all sales
 */
export async function getAllSales() {
  const db = await initDB();
  return db.getAll(STORES.SALES);
}

/**
 * Get a customer by ID
 * @param {number} customerId - The customer ID
 * @returns {Promise<Object|null>} Customer object or null if not found
 */
export async function getCustomer(customerId) {
  const db = await initDB();
  return db.get(STORES.CUSTOMERS, customerId);
}

/**
 * Get a medicine by ID (alias for getMedicineById for API consistency)
 * @param {number} medicineId - The medicine ID
 * @returns {Promise<Object|null>} Medicine object or null if not found
 */
export async function getMedicine(medicineId) {
  return getMedicineById(medicineId);
}

/**
 * Get inventory by medicine ID and batch number
 * @param {number} medicineId - The medicine ID
 * @param {string} batchNumber - The batch number
 * @param {number} locationId - Optional location ID
 * @returns {Promise<Object|null>} Inventory object or null if not found
 */
export async function getInventoryByMedicineBatch(medicineId, batchNumber, locationId = null) {
  const db = await initDB();
  
  if (locationId) {
    // If location is specified, use the compound index
    const index = db.transaction(STORES.INVENTORY).store.index('medicine_batch_location');
    return index.get([medicineId, batchNumber, locationId]);
  } else {
    // Otherwise, get all matching inventory and return the first one
    const index = db.transaction(STORES.INVENTORY).store.index('medicine_id');
    const items = await index.getAll(medicineId);
    return items.find(item => item.batch_number === batchNumber) || null;
  }
}

/**
 * Set a setting (alias for saveSetting for API consistency)
 * @param {string} key - The setting key
 * @param {any} value - The setting value
 * @returns {Promise<void>}
 */
export async function setSetting(key, value) {
  return saveSetting(key, value);
}

/**
 * Get storage information including size usage
 * @returns {Promise<Object>} Storage information
 */
export async function getStorageInfo() {
  const db = await initDB();
  const storageInfo = {
    stores: {},
    totalSize: 0,
    quotaUsage: null,
    timestamp: new Date().toISOString()
  };

  // Try to get browser storage estimate
  try {
    if (navigator.storage && navigator.storage.estimate) {
      const estimate = await navigator.storage.estimate();
      storageInfo.quotaUsage = {
        usage: estimate.usage,
        quota: estimate.quota,
        percentUsed: Math.round((estimate.usage / estimate.quota) * 100)
      };
    }
  } catch (e) {
    console.warn('Failed to get storage estimate:', e);
  }

  // Get store sizes
  try {
    for (const storeName of Object.values(STORES)) {
      const count = await db.count(storeName);
      const items = await db.getAll(storeName);
      
      // Roughly estimate size based on JSON string length
      const size = items.reduce((total, item) => {
        return total + new Blob([JSON.stringify(item)]).size;
      }, 0);
      
      storageInfo.stores[storeName] = {
        count,
        sizeBytes: size,
        sizeKB: Math.round(size / 1024 * 100) / 100
      };
      
      storageInfo.totalSize += size;
    }
    
    storageInfo.totalSizeKB = Math.round(storageInfo.totalSize / 1024 * 100) / 100;
    storageInfo.totalSizeMB = Math.round(storageInfo.totalSize / (1024 * 1024) * 100) / 100;
  } catch (e) {
    console.error('Error calculating store sizes:', e);
  }
  
  return storageInfo;
}

// Export STORES to make it available to other modules
export { STORES };

// Make functions available globally for use in Blade templates
window.OfflineDB = {
  // Core functions
  initDB,

  // Sales functions
  saveSale,
  getAllSales,
  getPendingSales,
  getSaleItemsBySale,

  // Customer functions
  saveCustomer,
  getCustomer,

  // Inventory functions
  cacheInventory,
  getInventoryByMedicineId,
  getInventoryByMedicine,
  getAllInventory,

  // Medicine functions
  cacheMedicines,
  getAllMedicines,
  getMedicineById,
  getMedicine,
  searchMedicinesForSales,
  getMedicinesByCategory,
  getLowStockMedicines,
  getPrescriptionMedicines,
  validateMedicineAvailability,

  // Sync functions
  addToSyncQueue,
  getPendingSyncItems,
  updateSyncItemStatus,
  batchUpdateSyncItemStatus,
  cleanupSyncQueue,
  getSyncItemsByBatch,
  getFailedSyncItems,

  // Settings functions
  setSetting,
  getSetting,

  // Reference data functions
  cacheCategories,
  getAllCategories,
  getActiveCategories,
  cacheManufacturers,
  getAllManufacturers,
  getActiveManufacturers,
  cacheUnitTypes,
  getAllUnitTypes,
  getActiveUnitTypes,
  cacheLocations,
  getAllLocations,
  getActiveLocations,
  getDefaultLocation,
  cacheWarehouses,
  getAllWarehouses,
  getActiveWarehouses,
  cacheSuppliers,
  getAllSuppliers,
  getActiveSuppliers,

  // Utility functions
  clearDatabase,
  getStorageInfo,
  performIntegrityCheck,

  // Constants
  SYNC_PRIORITIES,
  SYNC_STATUSES
};