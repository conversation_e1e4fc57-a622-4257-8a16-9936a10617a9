// Date Range Dropdown Component for Alpine.js
export default function dateRangeDropdown(route, currentStartDate, currentEndDate) {
    return {
        open: false,
        
        selectDateRange(range) {
            console.log('Date range selected:', range);
            const today = new Date();
            let startDate, endDate;
            
            switch(range) {
                case 'today':
                    startDate = new Date(today);
                    endDate = new Date(today);
                    break;
                    
                case 'last3days':
                    startDate = new Date(today);
                    startDate.setDate(today.getDate() - 2);
                    endDate = new Date(today);
                    break;
                    
                case 'lastweek':
                    startDate = new Date(today);
                    startDate.setDate(today.getDate() - 6);
                    endDate = new Date(today);
                    break;
                    
                case 'lastmonth':
                    startDate = new Date(today);
                    startDate.setMonth(today.getMonth() - 1);
                    endDate = new Date(today);
                    break;
                    
                case 'thisyear':
                    startDate = new Date(today.getFullYear(), 0, 1);
                    endDate = new Date(today);
                    break;
                    
                case 'lastyear':
                    startDate = new Date(today.getFullYear() - 1, 0, 1);
                    endDate = new Date(today.getFullYear() - 1, 11, 31);
                    break;
            }
            
            // Format dates as YYYY-MM-DD
            const formatDate = (date) => {
                return date.getFullYear() + '-' + 
                       String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                       String(date.getDate()).padStart(2, '0');
            };
            
            console.log('Formatted dates:', formatDate(startDate), formatDate(endDate));
            
            // Update the form inputs
            const startInput = document.getElementById('start_date');
            const endInput = document.getElementById('end_date');
            
            if (startInput) {
                startInput.value = formatDate(startDate);
                console.log('Updated start input:', startInput.value);
            } else {
                console.warn('Start date input not found');
            }
            
            if (endInput) {
                endInput.value = formatDate(endDate);
                console.log('Updated end input:', endInput.value);
            } else {
                console.warn('End date input not found');
            }
            
            // Close dropdown
            this.open = false;
            
            // Submit the form automatically - try multiple selectors
            let form = null;
            
            // First try to find form by action containing the route
            if (route) {
                form = document.querySelector(`form[action*="${route}"]`);
                console.log('Form found by route:', form);
            }
            
            // If not found, try to find the closest form to the inputs
            if (!form && startInput) {
                form = startInput.closest('form');
                console.log('Form found by closest to start input:', form);
            }
            
            // If still not found, try generic GET form selector
            if (!form) {
                form = document.querySelector('form[method="GET"]');
                console.log('Form found by GET method:', form);
            }
            
            // Last resort: find any form on the page
            if (!form) {
                form = document.querySelector('form');
                console.log('Form found as fallback:', form);
            }
            
            if (form) {
                console.log('Submitting form:', form.action || form.getAttribute('action'));
                form.submit();
            } else {
                console.error('No form found to submit');
                // Manual redirect as fallback
                if (route && startInput && endInput) {
                    const url = new URL(route, window.location.origin);
                    url.searchParams.set('start_date', startInput.value);
                    url.searchParams.set('end_date', endInput.value);
                    console.log('Manual redirect to:', url.toString());
                    window.location.href = url.toString();
                }
            }
        }
    };
}
