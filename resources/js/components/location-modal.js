export default () => ({
    open: false,
    init() {
        this.$watch('open', value => {
            if (value) {
                document.body.classList.add('overflow-hidden');
            } else {
                document.body.classList.remove('overflow-hidden');
                // Reset form fields when modal is closed
                const form = this.$el.querySelector('form');
                if (form) form.reset();
            }
        });
    },
    toggle() {
        this.open = !this.open;
    },
    close() {
        this.open = false;
    }
});
