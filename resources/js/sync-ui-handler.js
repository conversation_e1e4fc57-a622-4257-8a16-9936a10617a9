// Enhanced Sync UI Handler
// This script handles comprehensive UI updates for offline status, sync progress, and data freshness

import * as OfflineDB from './offline-db.js';

/**
 * Create and initialize the enhanced sync UI handler
 * Note: This now works with the unified status component instead of creating separate indicators
 */
export function initSyncUIHandler() {
    // Event names
    const SYNC_STATUS_EVENT = 'sync-status-changed';
    const OFFLINE_STATUS_EVENT = 'offline-status-changed';

    // Set up event listeners for the unified component
    setupEventListeners();

    // Initial status check - the unified component handles its own initialization
    console.log('Sync UI Handler initialized - working with unified status component');
}

/**
 * Initialize offline status indicator
 * DEPRECATED: Now handled by unified status component
 */
function initOfflineStatusIndicator() {
    // This function is deprecated - the unified status component handles offline status
    console.log('initOfflineStatusIndicator: Deprecated - using unified status component');
}

/**
 * Initialize sync progress indicator
 * DEPRECATED: Now handled by unified status component
 */
function initSyncProgressIndicator() {
    // This function is deprecated - the unified status component handles sync progress
    console.log('initSyncProgressIndicator: Deprecated - using unified status component');
}

/**
 * Initialize data freshness indicator
 * DEPRECATED: Now handled by unified status component
 */
function initDataFreshnessIndicator() {
    // This function is deprecated - the unified status component handles data freshness
    console.log('initDataFreshnessIndicator: Deprecated - using unified status component');
}

/**
 * Initialize pending operations counter
 * DEPRECATED: Now handled by unified status component
 */
function initPendingOperationsCounter() {
    // This function is deprecated - the unified status component handles pending operations
    console.log('initPendingOperationsCounter: Deprecated - using unified status component');
}

/**
 * Set up event listeners for status changes
 * Note: The unified component handles most of its own event listening
 */
function setupEventListeners() {
    // Online/offline status - dispatch events for the unified component
    window.addEventListener('online', () => {
        dispatchOfflineStatusEvent(false);
    });

    window.addEventListener('offline', () => {
        dispatchOfflineStatusEvent(true);
    });

    // The unified component handles sync-status-changed events internally
    // No need for additional periodic updates as the component manages its own state
}

/**
 * Handle sync status change events
 * DEPRECATED: The unified status component handles its own sync status changes
 */
function handleSyncStatusChange(event) {
    // This function is deprecated - the unified status component handles sync status changes internally
    console.log('handleSyncStatusChange: Deprecated - unified status component handles this');
}

/**
 * Update offline status indicator
 * DEPRECATED: Now handled by unified status component
 */
function updateOfflineStatus() {
    // This function is deprecated - the unified status component handles offline status
    console.log('updateOfflineStatus: Deprecated - using unified status component');
}

/**
 * Update sync progress indicator
 * DEPRECATED: Now handled by unified status component
 */
function updateSyncProgress(syncing, progress = 0, error = false, errorMessage = null) {
    // This function is deprecated - the unified status component handles sync progress
    console.log('updateSyncProgress: Deprecated - using unified status component');
}

/**
 * Update data freshness indicator
 * DEPRECATED: Now handled by unified status component
 */
async function updateDataFreshness() {
    // This function is deprecated - the unified status component handles data freshness
    console.log('updateDataFreshness: Deprecated - using unified status component');
}

/**
 * Update pending operations counter
 * DEPRECATED: Now handled by unified status component
 */
async function updatePendingOperationsCount() {
    // This function is deprecated - the unified status component handles pending operations count
    console.log('updatePendingOperationsCount: Deprecated - using unified status component');
}

/**
 * Show detailed sync information
 */
async function showSyncDetails() {
    try {
        const lastSync = await OfflineDB.getSetting('lastSuccessfulSync');
        const lastError = await OfflineDB.getSetting('lastSyncError');
        const pendingItems = await OfflineDB.getPendingSyncItems();

        let message = 'Sync Status:\n\n';

        if (lastSync) {
            message += `Last successful sync: ${new Date(lastSync).toLocaleString()}\n`;
        } else {
            message += 'No successful sync yet\n';
        }

        if (lastError) {
            message += `Last error: ${lastError.message} at ${new Date(lastError.timestamp).toLocaleString()}\n`;
        }

        message += `Pending operations: ${pendingItems.length}\n`;
        message += `Online status: ${navigator.onLine ? 'Online' : 'Offline'}`;

        alert(message);
    } catch (error) {
        console.warn('Error showing sync details:', error);
        alert('Unable to load sync details');
    }
}

/**
 * Show pending operations details
 */
async function showPendingOperations() {
    try {
        const pendingItems = await OfflineDB.getPendingSyncItems();

        if (pendingItems.length === 0) {
            alert('No pending operations');
            return;
        }

        let message = `Pending Operations (${pendingItems.length}):\n\n`;

        const grouped = pendingItems.reduce((acc, item) => {
            const key = `${item.entity_type}_${item.operation}`;
            acc[key] = (acc[key] || 0) + 1;
            return acc;
        }, {});

        Object.entries(grouped).forEach(([key, count]) => {
            const [entityType, operation] = key.split('_');
            message += `${entityType} ${operation}: ${count}\n`;
        });

        alert(message);
    } catch (error) {
        console.warn('Error showing pending operations:', error);
        alert('Unable to load pending operations');
    }
}

/**
 * Dispatch offline status event
 */
function dispatchOfflineStatusEvent(isOffline) {
    try {
        if (typeof window !== 'undefined' && window.dispatchEvent) {
            const event = new CustomEvent('offline-status-changed', {
                detail: {
                    isOffline,
                    timestamp: new Date().toISOString()
                }
            });
            window.dispatchEvent(event);
        }
    } catch (error) {
        console.warn('Error dispatching offline status event:', error);
    }
}