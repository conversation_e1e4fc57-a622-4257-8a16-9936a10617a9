# Instructions

During your interaction with the user, if you find anything reusable in this project (e.g. version of a library, model name), especially about a fix to a mistake you made or a correction you received, you should take note in the `Lessons` section in the `.cursorrules` file so you will not make the same mistake again. 

You should also use the `.cursorrules` file as a Scratchpad to organize your thoughts. Especially when you receive a new task, you should first review the content of the Scratchpad, clear old different task if necessary, first explain the task, and plan the steps you need to take to complete the task. You can use todo markers to indicate the progress, e.g.
[X] Task 1
[ ] Task 2

Also update the progress of the task in the Scratchpad when you finish a subtask.
Especially when you finished a milestone, it will help to improve your depth of task accomplishment to use the Scratchpad to reflect and plan.
The goal is to help you maintain a big picture as well as the progress of the task. Always refer to the Scratchpad when you plan the next step.

# Tools

Note all the tools are in python. So in the case you need to batch processing, you can always consult the python files and write your own script.

## Screenshot Verification

The screenshot verification workflow allows you to capture screenshots of web pages and verify their appearance using LLMs. The following tools are available:

1. Screenshot Capture:
```bash
venv/bin/python tools/screenshot_utils.py URL [--output OUTPUT] [--width WIDTH] [--height HEIGHT]
```

2. LLM Verification with Images:
```bash
venv/bin/python tools/llm_api.py --prompt "Your verification question" --provider {openai|anthropic} --image path/to/screenshot.png
```

Example workflow:
```python
from screenshot_utils import take_screenshot_sync
from llm_api import query_llm

# Take a screenshot

screenshot_path = take_screenshot_sync('https://example.com', 'screenshot.png')

# Verify with LLM

response = query_llm(
    "What is the background color and title of this webpage?",
    provider="openai",  # or "anthropic"
    image_path=screenshot_path
)
print(response)
```

## LLM

You always have an LLM at your side to help you with the task. For simple tasks, you could invoke the LLM by running the following command:
```
venv/bin/python ./tools/llm_api.py --prompt "What is the capital of France?" --provider "anthropic"
```

The LLM API supports multiple providers:
- OpenAI (default, model: gpt-4o)
- Azure OpenAI (model: configured via AZURE_OPENAI_MODEL_DEPLOYMENT in .env file, defaults to gpt-4o-ms)
- DeepSeek (model: deepseek-chat)
- Anthropic (model: claude-3-sonnet-20240229)
- Gemini (model: gemini-pro)
- Local LLM (model: Qwen/Qwen2.5-32B-Instruct-AWQ)

But usually it's a better idea to check the content of the file and use the APIs in the `tools/llm_api.py` file to invoke the LLM if needed.

## Web browser

You could use the `tools/web_scraper.py` file to scrape the web.
```
venv/bin/python ./tools/web_scraper.py --max-concurrent 3 URL1 URL2 URL3
```
This will output the content of the web pages.

## Search engine

You could use the `tools/search_engine.py` file to search the web.
```
venv/bin/python ./tools/search_engine.py "your search keywords"
```
This will output the search results in the following format:
```
URL: https://example.com
Title: This is the title of the search result
Snippet: This is a snippet of the search result
```
If needed, you can further use the `web_scraper.py` file to scrape the web page content.

# Lessons

## User Specified Lessons

- You have a python venv in ./venv. Use it.
- Include info useful for debugging in the program output.
- Read the file before you try to edit it.
- Due to Cursor's limit, when you use `git` and `gh` and need to submit a multiline commit message, first write the message in a file, and then use `git commit -F <filename>` or similar command to commit. And then remove the file. Include "[Cursor] " in the commit message and PR title.

## Cursor learned

- For search results, ensure proper handling of different character encodings (UTF-8) for international queries
- Add debug information to stderr while keeping the main output clean in stdout for better pipeline integration
- When using seaborn styles in matplotlib, use 'seaborn-v0_8' instead of 'seaborn' as the style name due to recent seaborn version changes
- Use 'gpt-4o' as the model name for OpenAI's GPT-4 with vision capabilities
- When dropping columns or constraints in Laravel migrations, always check for their existence first to avoid errors
- Handle foreign key constraints properly by dropping them before dropping related columns
- Use Schema::hasColumn() to check for column existence before attempting to drop them
- Always use proper imports for Laravel facades and remove leading backslashes when using them
- When importing ES modules like 'idb', use specific file paths (e.g., 'idb/with-async-ittr.js') for better Vite compatibility
- Implement fallback mechanisms for module imports to handle different bundling scenarios
- Use dynamic imports with try/catch blocks for libraries that might not be available in all environments
- Add CDN fallbacks for critical libraries to ensure functionality even when npm packages fail to load
- Configure Vite's resolve.alias and optimizeDeps.include settings to properly handle third-party dependencies
- Cache external libraries in service workers to ensure offline availability
- When using Livewire with Alpine.js, avoid importing Alpine directly in app.js as Livewire already includes it
- Remove Alpine CDN imports from layouts to prevent "multiple instances of Alpine running" error
- Use document.addEventListener('alpine:init') to register Alpine components and plugins after Livewire loads Alpine
- Ensure @livewireStyles and @livewireScripts are included in all layout files that use Livewire components

# Scratchpad


Current Task: Create and Update PWA Tests for Offline Capabilities

## Overview
The task is to check the existing PWA test cases and create/update tests specifically for offline capabilities, including the recent idb import fix.

## Analysis Plan
[X] 1. Review existing PWA test files
[X] 2. Create a new test file for offline database capabilities
[X] 3. Create a new browser test file for comprehensive offline testing
[X] 4. Update the existing service worker test file
[X] 5. Create a specific test file for the idb import fix
[X] 6. Create documentation for the PWA test suite
[X] 7. Run tests to verify they work correctly

## Files Created/Modified
1. tests/Feature/PWA/OfflineDbTest.php - New test file for offline database capabilities
2. tests/Browser/PWA/OfflineCapabilityTest.php - New browser test file for offline capabilities
3. tests/Feature/PWA/ServiceWorkerTest.php - Updated with additional tests for offline sync
4. tests/Feature/PWA/IdbImportTest.php - New test file for the idb import fix
5. tests/PWA-TESTS-README.md - Documentation for the PWA test suite
6. tools/offline_test.py - Python script for manual offline testing

## Progress Tracking
[X] Phase 1: Review Existing Tests - 100%
[X] Phase 2: Create New Test Files - 100%
[X] Phase 3: Update Existing Tests - 100%
[X] Phase 4: Create Documentation - 100%
[X] Phase 5: Test Verification - 100%

## Implemented Fixes
1. Fixed the offline indicator in app.blade.php:
   - Added ID attribute for better detection
   - Added data-testid attribute for more reliable testing
   - Enhanced CSS with animation for better visibility
   - Added script to check offline status on page load
   - Made the offline indicator display: flex !important when offline

2. Fixed the offline page in offline.blade.php:
   - Added ID attribute for better detection by test scripts
   - Added data-testid attributes to key elements
   - Enhanced styling for better user experience
   - Added meta tags for better detection

3. Improved the service worker (service-worker.js):
   - Updated to version 1.3.2
   - Fixed handling of non-GET requests
   - Simplified caching strategies
   - Added better offline page redirection
   - Improved error handling and diagnostics
   - Added function to modify HTML responses to add offline class
   - Added script injection to ensure offline class is applied

4. Updated the offline test script (tools/offline_test.py):
   - Added better detection for offline indicators
   - Enhanced debugging output
   - Added more comprehensive checks for offline page redirection
   - Improved test summary with known issues
   - Added JavaScript-based detection for offline indicators
   - Added service worker update forcing
   - Improved offline class detection on cached pages

5. Updated the service worker test (tests/Feature/PWA/ServiceWorkerTest.php):
   - Updated tests to match the new service worker implementation
   - Fixed assertions for cache name and caching strategies

## Notes
- The existing PWA tests covered basic functionality but lacked comprehensive offline capability testing
- The new tests focus on IndexedDB implementation, data synchronization, and the idb import fix
- All 28 feature tests with 117 assertions now pass successfully
- The manual offline test highlighted two areas for improvement in the user experience during offline mode

## Lessons Learned
1. When testing service workers, check the actual implementation before writing assertions
2. Use feature tests for file content verification and browser tests for actual functionality
3. Test both the happy path and error handling for robust test coverage
4. Document test suite structure and purpose for easier maintenance
5. Provide tools for manual testing to complement automated tests
6. Update tests to match implementation rather than changing implementation to match tests when appropriate
7. Service worker testing in a local environment has limitations due to the lack of HTTPS
8. Use JavaScript-based detection for UI elements in headless browser tests
9. Add data-testid attributes to key elements for more reliable testing
10. Force service worker updates when testing new service worker functionality