# Laravel specific
/vendor/
node_modules/
npm-debug.log
yarn-error.log

# Laravel 4 specific
bootstrap/compiled.php
app/storage/

# Laravel 5 & above specific
public/storage
public/hot
storage/*.key
.env
.env.backup
.env.*.local
Homestead.yaml
Homestead.json
/.vagrant
.phpunit.result.cache

# IDE specific
/.idea
/.vscode
/.fleet
/.phpstorm.meta.php
/_ide_helper.php
/_ide_helper_models.php

# Dependencies
/public/build
/public/css
/public/js
/public/mix-manifest.json

# Compressed files
*.zip
*.rar
*.7z
*.tar
*.gz
*.bz2

# Logs and databases
*.log
*.sql
*.sqlite
*.sqlite-journal

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build files
/build/
/dist/
/out/

# Cache files
.php_cs.cache
.php-cs-fixer.cache

# Testing
/coverage/
.phpunit.cache/

# Composer
composer.phar
/.php-cs-fixer.php

# Application specific
/storage/app/public/*
/storage/framework/cache/*
/storage/framework/sessions/*
/storage/framework/testing/*
/storage/framework/views/*
/storage/logs/*
/bootstrap/cache/*

# Keep directories
!.gitkeep
!public/.gitkeep
!storage/app/public/.gitkeep
!storage/framework/cache/.gitkeep
!storage/framework/sessions/.gitkeep
!storage/framework/views/.gitkeep
!storage/logs/.gitkeep

# Windsurf Rules
.windsurfrules

# Node modules
/node_modules

# Database backups
database/backups/*.sql
database/backups/*.gz
