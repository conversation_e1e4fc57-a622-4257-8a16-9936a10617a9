// Alpine.js Debug Script
// This script helps debug Alpine.js components and data

console.log('Alpine.js Debug Script Loaded');

// Wait for Alpine to be ready
document.addEventListener('alpine:init', () => {
    console.log('Alpine.js initialized');
    
    // Debug function to inspect Alpine data
    window.debugAlpine = function(element) {
        if (!element) {
            console.log('Available Alpine components:', Object.keys(Alpine.store()));
            return;
        }
        
        const alpineData = Alpine.$data(element);
        console.log('Alpine data for element:', alpineData);
        return alpineData;
    };
    
    // Debug function to list all Alpine components
    window.listAlpineComponents = function() {
        const components = document.querySelectorAll('[x-data]');
        console.log(`Found ${components.length} Alpine components:`);
        components.forEach((component, index) => {
            console.log(`${index + 1}:`, component.getAttribute('x-data'), component);
        });
        return components;
    };
});

// Debug Alpine events
document.addEventListener('alpine:initialized', () => {
    console.log('Alpine.js fully initialized');
});

// Log Alpine errors
window.addEventListener('error', (event) => {
    if (event.error && event.error.message && event.error.message.includes('Alpine')) {
        console.error('Alpine.js Error:', event.error);
    }
});
