<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V1\Inventory\BatchHistoryController;
use App\Http\Controllers\Api\V1\Inventory\CategoryController;
use App\Http\Controllers\Api\V1\Inventory\LocationController;
use App\Http\Controllers\Api\V1\Inventory\ManufacturerController;
use App\Http\Controllers\Api\V1\Inventory\MedicineController;
use App\Http\Controllers\Api\V1\Inventory\StockController;
use App\Http\Controllers\Api\V1\Inventory\SupplierController;
use App\Http\Controllers\Api\V1\Inventory\UnitConversionController;
use App\Http\Controllers\Api\V1\Inventory\UnitTypeController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Offline Sync API Routes - No authentication required for PWA offline functionality
Route::prefix('offline')->group(function () {
    // Enhanced data download endpoints with pagination and incremental sync

    // Medicines with full relationships
    Route::get('/medicines', function (Request $request) {
        $query = \App\Models\Inventory\Medicine::with([
            'category',
            'manufacturer',
            'inventories' => function($q) {
                $q->where('quantity', '>', 0);
            }
        ])->active();

        // Support incremental sync
        if ($request->has('since')) {
            $query->where('updated_at', '>', $request->since);
        }

        // Pagination support with validation
        $limit = min(max((int) $request->input('limit', 100), 1), 500); // Between 1 and 500 items
        $offset = max((int) $request->input('offset', 0), 0); // Ensure non-negative

        // Only apply offset if it's greater than 0 to avoid SQL syntax errors
        if ($offset > 0) {
            $medicines = $query->offset($offset)->limit($limit)->get();
        } else {
            $medicines = $query->limit($limit)->get();
        }

        return response()->json([
            'data' => $medicines,
            'meta' => [
                'count' => $medicines->count(),
                'limit' => $limit,
                'offset' => $offset,
                'has_more' => $medicines->count() === $limit
            ]
        ]);
    });

    // Inventory with relationships
    Route::get('/inventory', function (Request $request) {
        $query = \App\Models\Inventory\Inventory::with(['medicine', 'location'])
            ->where('quantity', '>', 0);

        // Support incremental sync
        if ($request->has('since')) {
            $query->where('updated_at', '>', $request->since);
        }

        // Pagination support
        $limit = min($request->input('limit', 200), 1000);
        $offset = $request->input('offset', 0);

        $inventory = $query->offset($offset)->limit($limit)->get();

        return response()->json([
            'data' => $inventory,
            'meta' => [
                'count' => $inventory->count(),
                'limit' => $limit,
                'offset' => $offset,
                'has_more' => $inventory->count() === $limit
            ]
        ]);
    });

    // Enhanced customers endpoint with comprehensive data for offline sales
    Route::get('/customers', function (Request $request) {
        $query = \App\Models\Customers\Customer::with(['loyaltyTransactions' => function($q) {
            $q->orderBy('created_at', 'desc')->limit(10); // Last 10 transactions
        }]);

        // Support status filtering - default to active if no status specified
        if ($request->has('status')) {
            $query->where('status', $request->status);
        } else {
            $query->where('status', 'active');
        }

        // Support incremental sync
        if ($request->has('since')) {
            $query->where('updated_at', '>', $request->since);
        }

        // Support search functionality for offline use
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Support status filtering
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Pagination with validation
        $limit = min(max((int) $request->input('limit', 100), 1), 500);
        $offset = max((int) $request->input('offset', 0), 0);

        $total = $query->count();

        // Apply ordering
        if ($request->has('recent') && $request->recent) {
            $query->orderBy('updated_at', 'desc');
        } else {
            $query->orderBy('name', 'asc');
        }

        // Only apply offset if it's greater than 0 to avoid SQL syntax errors
        if ($offset > 0) {
            $customers = $query->offset($offset)->limit($limit)->get();
        } else {
            $customers = $query->limit($limit)->get();
        }

        // Add computed attributes for offline use
        $customers->each(function($customer) {
            $customer->loyalty_tier = $customer->loyalty_tier;
            $customer->points_to_next_tier = $customer->points_to_next_tier;
            $customer->total_points_earned = $customer->total_points_earned;
            $customer->total_points_redeemed = $customer->total_points_redeemed;
        });

        return response()->json([
            'data' => $customers,
            'meta' => [
                'count' => $customers->count(),
                'total' => $total,
                'limit' => $limit,
                'offset' => $offset,
                'has_more' => ($offset + $limit) < $total,
                'timestamp' => now()->toISOString()
            ]
        ]);
    });

    // Customer sync specific endpoints (must be before individual customer route)
    Route::get('/customers/sync-status', function () {
        $totalCustomers = \App\Models\Customers\Customer::where('status', 'active')->count();
        $recentlyUpdated = \App\Models\Customers\Customer::where('status', 'active')
            ->where('updated_at', '>', now()->subHours(24))
            ->count();

        return response()->json([
            'status' => 'idle',
            'last_sync' => cache()->get('customer_last_sync', now()->subMinutes(10)->toISOString()),
            'next_sync' => now()->addMinutes(5)->toISOString(),
            'sync_interval' => 300, // 5 minutes in seconds
            'total_customers' => $totalCustomers,
            'pending_updates' => $recentlyUpdated,
            'sync_enabled' => true
        ]);
    });

    Route::post('/customers/sync-trigger', function (Request $request) {
        // Check if there's already a customer sync operation running
        $runningCustomerSyncs = cache()->get('running_customer_syncs', []);
        if (!empty($runningCustomerSyncs)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Customer sync operation already in progress'
            ], 409);
        }

        $syncId = 'customer_sync_' . uniqid();
        $forceFullSync = $request->input('force_full_sync', false);

        // Calculate customers to sync
        $query = \App\Models\Customers\Customer::where('status', 'active');

        if (!$forceFullSync && $request->has('since')) {
            $query->where('updated_at', '>', $request->since);
        }

        $customersToSync = $query->count();

        // Mark customer sync as running
        cache()->put('running_customer_syncs', [$syncId], 3600);

        // Store sync operation in cache for tracking
        cache()->put("customer_sync_operation_{$syncId}", [
            'status' => 'running',
            'started_at' => now(),
            'customers_total' => $customersToSync,
            'customers_processed' => 0,
            'progress_percentage' => 0,
            'force_full_sync' => $forceFullSync
        ], 3600); // 1 hour

        return response()->json([
            'status' => 'started',
            'message' => 'Customer sync operation started',
            'sync_id' => $syncId,
            'estimated_duration' => ceil($customersToSync / 50) * 2, // Rough estimate: 2 seconds per 50 customers
            'customers_to_sync' => $customersToSync,
            'force_full_sync' => $forceFullSync
        ]);
    });

    Route::get('/customers/sync-progress/{syncId}', function ($syncId) {
        $syncData = cache()->get("customer_sync_operation_{$syncId}");

        if (!$syncData) {
            return response()->json([
                'status' => 'error',
                'message' => 'Customer sync operation not found'
            ], 404);
        }

        // Simulate progress for demo purposes
        $elapsedSeconds = now()->diffInSeconds($syncData['started_at']);
        $progressPercentage = min(100, max(0, ($elapsedSeconds / 60) * 100)); // Complete in 1 minute for demo

        if ($progressPercentage >= 100) {
            $syncData['status'] = 'completed';
            $syncData['progress_percentage'] = 100;
            $syncData['customers_processed'] = $syncData['customers_total'];

            // Remove from running syncs and update last sync time
            cache()->forget('running_customer_syncs');
            cache()->put('customer_last_sync', now()->toISOString(), 86400);
            cache()->put("customer_sync_operation_{$syncId}", $syncData, 3600);
        } else {
            $syncData['progress_percentage'] = $progressPercentage;
            $syncData['customers_processed'] = ceil(($progressPercentage / 100) * $syncData['customers_total']);
            cache()->put("customer_sync_operation_{$syncId}", $syncData, 3600);
        }

        return response()->json([
            'sync_id' => $syncId,
            'status' => $syncData['status'],
            'progress_percentage' => $syncData['progress_percentage'],
            'current_step' => 'Syncing customer data',
            'customers_processed' => $syncData['customers_processed'],
            'customers_total' => $syncData['customers_total'],
            'estimated_time_remaining' => max(0, 60 - $elapsedSeconds), // seconds
            'errors' => []
        ]);
    });

    Route::delete('/customers/sync-cancel/{syncId}', function ($syncId) {
        $syncData = cache()->get("customer_sync_operation_{$syncId}");

        if (!$syncData) {
            return response()->json([
                'status' => 'error',
                'message' => 'Customer sync operation not found'
            ], 404);
        }

        $syncData['status'] = 'cancelled';

        // Remove from running syncs
        cache()->forget('running_customer_syncs');
        cache()->put("customer_sync_operation_{$syncId}", $syncData, 3600);

        return response()->json([
            'status' => 'cancelled',
            'message' => 'Customer sync operation cancelled successfully'
        ]);
    });

    // Individual customer details for offline caching
    Route::get('/customers/{id}', function ($id) {
        $customer = \App\Models\Customers\Customer::with([
            'loyaltyTransactions' => function($q) {
                $q->orderBy('created_at', 'desc');
            },
            'sales' => function($q) {
                $q->orderBy('created_at', 'desc')->limit(20); // Last 20 sales
            }
        ])->find($id);

        if (!$customer) {
            return response()->json(['error' => 'Customer not found'], 404);
        }

        // Add computed attributes
        $customer->loyalty_tier = $customer->loyalty_tier;
        $customer->points_to_next_tier = $customer->points_to_next_tier;
        $customer->total_points_earned = $customer->total_points_earned;
        $customer->total_points_redeemed = $customer->total_points_redeemed;

        return response()->json([
            'data' => $customer,
            'meta' => ['timestamp' => now()->toISOString()]
        ]);
    });

    // Individual medicine details with batches for offline caching
    Route::get('/medicines/{id}', function ($id) {
        $medicine = \App\Models\Inventory\Medicine::with([
            'category',
            'manufacturer',
            'inventories' => function($q) {
                $q->with('location')->where('quantity', '>', 0);
            },
            'suppliers'
        ])->find($id);

        if (!$medicine) {
            return response()->json(['error' => 'Medicine not found'], 404);
        }

        return response()->json([
            'data' => $medicine,
            'meta' => ['timestamp' => now()->toISOString()]
        ]);
    });

    // Reference data endpoints
    Route::get('/categories', function (Request $request) {
        $query = \App\Models\Inventory\Category::where('is_active', true);

        if ($request->has('since')) {
            $query->where('updated_at', '>', $request->since);
        }

        return response()->json([
            'data' => $query->get(),
            'meta' => ['timestamp' => now()->toISOString()]
        ]);
    });

    Route::get('/manufacturers', function (Request $request) {
        $query = \App\Models\Inventory\Manufacturer::where('is_active', true);

        if ($request->has('since')) {
            $query->where('updated_at', '>', $request->since);
        }

        return response()->json([
            'data' => $query->get(),
            'meta' => ['timestamp' => now()->toISOString()]
        ]);
    });

    Route::get('/unit-types', function (Request $request) {
        $query = \App\Models\Inventory\UnitType::where('is_active', true);

        if ($request->has('since')) {
            $query->where('updated_at', '>', $request->since);
        }

        return response()->json([
            'data' => $query->get(),
            'meta' => ['timestamp' => now()->toISOString()]
        ]);
    });

    Route::get('/locations', function (Request $request) {
        $query = \App\Models\Inventory\Location::where('status', 'active');

        if ($request->has('since')) {
            $query->where('updated_at', '>', $request->since);
        }

        return response()->json([
            'data' => $query->get(),
            'meta' => ['timestamp' => now()->toISOString()]
        ]);
    });

    Route::get('/warehouses', function (Request $request) {
        $query = \App\Models\Inventory\Warehouse::where('is_active', true);

        if ($request->has('since')) {
            $query->where('updated_at', '>', $request->since);
        }

        return response()->json([
            'data' => $query->get(),
            'meta' => ['timestamp' => now()->toISOString()]
        ]);
    });

    Route::get('/suppliers', function (Request $request) {
        $query = \App\Models\Inventory\Supplier::where('is_active', true);

        if ($request->has('since')) {
            $query->where('updated_at', '>', $request->since);
        }

        return response()->json([
            'data' => $query->get(),
            'meta' => ['timestamp' => now()->toISOString()]
        ]);
    });

    Route::get('/unit-conversions', function (Request $request) {
        $query = \App\Models\Inventory\UnitConversion::with(['fromUnit', 'toUnit'])
            ->where('status', 'active');

        if ($request->has('since')) {
            $query->where('updated_at', '>', $request->since);
        }

        return response()->json([
            'data' => $query->get(),
            'meta' => ['timestamp' => now()->toISOString()]
        ]);
    });

    Route::get('/users', function (Request $request) {
        $query = \App\Models\Users\User::select(['id', 'name', 'email', 'status', 'updated_at'])
            ->where('status', 'active')
            ->with('roles:id,name'); // Include roles relationship

        if ($request->has('since')) {
            $query->where('updated_at', '>', $request->since);
        }

        $users = $query->get();

        // Transform the data to include role information
        $transformedUsers = $users->map(function ($user) {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'status' => $user->status,
                'is_active' => $user->status === 'active',
                'role' => $user->roles->first()?->name ?? null,
                'updated_at' => $user->updated_at,
            ];
        });

        return response()->json([
            'data' => $transformedUsers,
            'meta' => ['timestamp' => now()->toISOString()]
        ]);
    });

    // Enhanced sync endpoints for offline data
    Route::post('/sales', function (Request $request) {
        try {
            $saleData = $request->validate([
                'invoice_number' => 'required|string',
                'customer_id' => 'nullable|exists:customers,id',
                'total_amount' => 'required|numeric|min:0',
                'paid_amount' => 'required|numeric|min:0',
                'due_amount' => 'required|numeric|min:0',
                'discount_amount' => 'nullable|numeric|min:0',
                'tax_amount' => 'nullable|numeric|min:0',
                'payment_method' => 'required|string',
                'payment_status' => 'required|string',
                'due_date' => 'nullable|date',
                'items' => 'required|array|min:1',
                'items.*.medicine_id' => 'required|exists:medicines,id',
                'items.*.batch_number' => 'required|string',
                'items.*.quantity' => 'required|integer|min:1',
                'items.*.unit_price' => 'required|numeric|min:0',
                'items.*.discount' => 'nullable|numeric|min:0',
                'items.*.tax_rate' => 'nullable|numeric|min:0',
            ]);

            // Replace offline invoice number with proper one
            if (str_starts_with($saleData['invoice_number'], 'OFFLINE-')) {
                $saleData['invoice_number'] = \App\Http\Controllers\Sales\SaleController::generateInvoiceNumber();
            }

            $sale = \App\Models\Sales\Sale::create($saleData);

            // Create sale items
            foreach ($saleData['items'] as $itemData) {
                $sale->items()->create($itemData);
            }

            return response()->json([
                'id' => $sale->id,
                'invoice_number' => $sale->invoice_number,
                'status' => 'success'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 422);
        }
    });

    Route::post('/customers', function (Request $request) {
        try {
            $customerData = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'nullable|email|unique:customers,email',
                'phone' => 'nullable|string|max:20',
                'address' => 'nullable|string',
                'loyalty_points' => 'nullable|integer|min:0',
                'status' => 'nullable|string|in:active,inactive',
            ]);

            $customer = \App\Models\Customers\Customer::create($customerData);

            return response()->json([
                'id' => $customer->id,
                'status' => 'success'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 422);
        }
    });

    Route::post('/inventory', function (Request $request) {
        try {
            $inventoryData = $request->validate([
                'medicine_id' => 'required|exists:medicines,id',
                'batch_number' => 'required|string',
                'quantity' => 'required|integer',
                'unit_price' => 'required|numeric|min:0',
                'purchase_price' => 'nullable|numeric|min:0',
                'expiry_date' => 'nullable|date',
                'location_id' => 'nullable|exists:locations,id',
                'warehouse_id' => 'nullable|exists:warehouses,id',
            ]);

            $inventory = \App\Models\Inventory\Inventory::create($inventoryData);

            return response()->json([
                'id' => $inventory->id,
                'status' => 'success'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 422);
        }
    });

    // Sync management endpoints
    Route::get('/sync-status', function () {
        return response()->json([
            'status' => 'idle',
            'last_sync' => now()->subMinutes(5)->toISOString(),
            'next_sync' => now()->addMinutes(5)->toISOString(),
            'sync_interval' => 300, // 5 minutes in seconds
            'total_records' => \App\Models\Inventory\Medicine::where('status', 'active')->count(),
            'pending_updates' => 0
        ]);
    });

    Route::post('/sync-trigger', function (Request $request) {
        // Check if there's already a sync operation running
        $runningSyncs = cache()->get('running_syncs', []);
        if (!empty($runningSyncs)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Sync operation already in progress'
            ], 409);
        }

        $syncId = 'sync_' . uniqid();
        $entities = $request->input('entities', ['medicines', 'categories', 'manufacturers', 'locations']);

        // Calculate records to sync
        $recordsToSync = 0;
        if (in_array('medicines', $entities)) {
            $recordsToSync += \App\Models\Inventory\Medicine::where('status', 'active')->count();
        }
        if (in_array('categories', $entities)) {
            $recordsToSync += \App\Models\Inventory\Category::where('is_active', true)->count();
        }
        if (in_array('manufacturers', $entities)) {
            $recordsToSync += \App\Models\Inventory\Manufacturer::where('is_active', true)->count();
        }
        if (in_array('locations', $entities)) {
            $recordsToSync += \App\Models\Inventory\Location::where('status', 'active')->count();
        }

        // Mark sync as running
        cache()->put('running_syncs', [$syncId], 3600);

        // Store sync operation in cache/session for tracking
        cache()->put("sync_operation_{$syncId}", [
            'status' => 'running',
            'started_at' => now(),
            'entities' => $entities,
            'records_total' => $recordsToSync,
            'records_processed' => 0,
            'progress_percentage' => 0
        ], 3600); // 1 hour

        return response()->json([
            'status' => 'started',
            'message' => 'Sync operation started',
            'sync_id' => $syncId,
            'estimated_duration' => ceil($recordsToSync / 100) * 2, // Rough estimate: 2 seconds per 100 records
            'records_to_sync' => $recordsToSync,
            'entities_to_sync' => $entities
        ]);
    });

    Route::get('/sync-progress/{syncId}', function ($syncId) {
        $syncData = cache()->get("sync_operation_{$syncId}");

        if (!$syncData) {
            return response()->json([
                'status' => 'error',
                'message' => 'Sync operation not found'
            ], 404);
        }

        // Simulate progress for demo purposes
        $elapsedSeconds = now()->diffInSeconds($syncData['started_at']);
        $progressPercentage = min(100, max(0, ($elapsedSeconds / 120) * 100)); // Complete in 2 minutes for demo

        if ($progressPercentage >= 100) {
            $syncData['status'] = 'completed';
            $syncData['progress_percentage'] = 100;
            $syncData['records_processed'] = $syncData['records_total'];

            // Remove from running syncs
            cache()->forget('running_syncs');
            cache()->put("sync_operation_{$syncId}", $syncData, 3600);
        } else {
            $syncData['progress_percentage'] = $progressPercentage;
            $syncData['records_processed'] = ceil(($progressPercentage / 100) * $syncData['records_total']);
            cache()->put("sync_operation_{$syncId}", $syncData, 3600);
        }

        return response()->json([
            'sync_id' => $syncId,
            'status' => $syncData['status'],
            'progress_percentage' => $syncData['progress_percentage'],
            'current_step' => 'Syncing medicines',
            'total_steps' => count($syncData['entities']),
            'records_processed' => $syncData['records_processed'],
            'records_total' => $syncData['records_total'],
            'estimated_time_remaining' => max(0, 120 - $elapsedSeconds), // seconds
            'errors' => []
        ]);
    });

    Route::delete('/sync-cancel/{syncId}', function ($syncId) {
        $syncData = cache()->get("sync_operation_{$syncId}");

        if (!$syncData) {
            return response()->json([
                'status' => 'error',
                'message' => 'Sync operation not found'
            ], 404);
        }

        $syncData['status'] = 'cancelled';

        // Remove from running syncs
        cache()->forget('running_syncs');
        cache()->put("sync_operation_{$syncId}", $syncData, 3600);

        return response()->json([
            'status' => 'cancelled',
            'message' => 'Sync operation cancelled successfully'
        ]);
    });

    Route::get('/sync-config', function () {
        return response()->json([
            'sync_interval' => 300,
            'auto_sync_enabled' => true,
            'sync_on_startup' => true,
            'batch_size' => 100,
            'retry_attempts' => 3,
            'retry_delay' => 5000
        ]);
    });

    Route::put('/sync-config', function (Request $request) {
        $validated = $request->validate([
            'sync_interval' => 'required|integer|min:60|max:3600',
            'auto_sync_enabled' => 'required|boolean',
            'sync_on_startup' => 'sometimes|boolean',
            'batch_size' => 'required|integer|min:10|max:1000',
            'retry_attempts' => 'required|integer|min:0|max:10',
            'retry_delay' => 'sometimes|integer|min:1000|max:60000'
        ]);

        // In a real implementation, you would save this to database or config
        cache()->put('sync_config', $validated, 86400); // 24 hours

        return response()->json([
            'status' => 'success',
            'message' => 'Sync configuration updated'
        ]);
    });

    Route::get('/sync-history', function (Request $request) {
        $limit = min($request->input('limit', 10), 50);

        // Mock sync history data
        $history = collect();
        for ($i = 0; $i < $limit; $i++) {
            $history->push([
                'sync_id' => 'sync_' . uniqid(),
                'started_at' => now()->subHours($i * 2)->toISOString(),
                'completed_at' => now()->subHours($i * 2)->addMinutes(2)->toISOString(),
                'status' => $i === 0 ? 'completed' : (rand(0, 10) > 8 ? 'error' : 'completed'),
                'records_synced' => rand(50, 200),
                'errors_count' => $i === 0 ? 0 : rand(0, 3),
                'duration_seconds' => rand(60, 180),
                'trigger_type' => rand(0, 1) ? 'automatic' : 'manual'
            ]);
        }

        return response()->json([
            'data' => $history->toArray(),
            'meta' => [
                'total' => $history->count(),
                'per_page' => $limit,
                'current_page' => 1
            ]
        ]);
    });

    Route::get('/connectivity-check', function () {
        $startTime = microtime(true);

        // Simulate connectivity check
        $responseTime = (microtime(true) - $startTime) * 1000;

        return response()->json([
            'online' => true,
            'api_reachable' => true,
            'response_time_ms' => round($responseTime, 2),
            'last_check' => now()->toISOString(),
            'server_time' => now()->toISOString()
        ]);
    });


});

// API V1 Routes
Route::prefix('v1')->middleware('auth:sanctum')->group(function () {
    // Inventory Routes
    Route::prefix('inventory')->namespace('App\Http\Controllers\Api\V1\Inventory')->group(function () {
        // Medicines
        Route::apiResource('medicines', MedicineController::class);
        
        // Categories
        Route::apiResource('categories', CategoryController::class);
        
        // Locations
        Route::apiResource('locations', LocationController::class);
        
        // Manufacturers
        Route::apiResource('manufacturers', ManufacturerController::class);
        
        // Stocks
        Route::apiResource('stocks', StockController::class);
        Route::get('stocks/{medicine}/history', [StockController::class, 'medicineHistory']);
        Route::get('locations/{location}/medicines', [StockController::class, 'medicinesByLocation']);
        Route::get('locations/{location}/medicines/{medicine}/batches', [StockController::class, 'batchesByLocationAndMedicine']);
        Route::post('transfer', [StockController::class, 'transfer']);
        
        // Suppliers
        Route::apiResource('suppliers', SupplierController::class);
        
        // Unit Types
        Route::apiResource('unit-types', UnitTypeController::class);
        
        // Unit Conversions
        Route::apiResource('unit-conversions', UnitConversionController::class);
        Route::post('unit-conversions/convert', [UnitConversionController::class, 'convert']);
        
        // Batch Histories
        Route::apiResource('batch-histories', BatchHistoryController::class);
        Route::get('batch-histories/export', [BatchHistoryController::class, 'export']);
    });
});
